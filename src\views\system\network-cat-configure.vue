<template>
    <div>
        <el-card>
            <span style="margin-left: 15px">模式选择：</span>
            <el-select v-model="selModule" placeholder="请选择" clearable size="small" :disabled="isSelect">
                <el-option
                        v-for="item in selModuleArr"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                </el-option>
            </el-select>
            <span>网卡绑定：<el-switch v-model="NICBonding" @change="setNetworkFun" :disabled="selModule === ''"></el-switch></span>
<!--            <span class="tip_txt" style="color: red;font-weight: bold;">注：修改信息后需重启设备方可生效!</span>-->
        </el-card>
        <ul class="network_config_con">
            <li v-for="(item, index) in networkFormArr" :key="item.type">
                <div class="network_type">
                    <span v-if="item.type === 0">管理口</span>
                    <span v-if="item.type === 1">业务口一</span>
                    <span v-if="item.type === 2">业务口二</span>
                </div>
                <div class="config_item">
                    <el-form label-width="180px" class="comForm" ref="networkForm" :model="item" :disabled="(item.type === 1 || item.type === 2) && NICBonding" :rules="rules">
                        <el-form-item label="IP地址" prop="ip">
                            <el-input v-model="item.ip" placeholder="请输入IP地址"></el-input>
                        </el-form-item>
                        <el-form-item label="子网掩码" prop="mask">
                            <el-input v-model="item.mask" placeholder="请输入子网掩码"></el-input>
                        </el-form-item>
                        <el-form-item label="网关地址" prop="gw" style="margin-bottom: 0">
                            <el-input v-model="item.gw" placeholder="请输入网关地址"></el-input>
                        </el-form-item>
                    </el-form>
                </div>
                <div class="handle_con">
                    <span>
                        <el-button class="comBtn com_reset_btn" size="small" :disabled="(item.type === 1 || item.type === 2) && NICBonding" @click="cancelHandle(item.type, index)">取 消</el-button>
                        <el-button class="comBtn com_send_btn" size="small" type="primary" @click="networkSave(item, index)" :disabled="(item.type === 1 || item.type === 2) && NICBonding">保 存</el-button>
                    </span>
                </div>
            </li>
        </ul>

        <!--<ul class="content_box">-->
            <!--<li v-for="(item, index) in networkFormArr" :key="item.type">-->
                <!--<h4 v-if="item.type === 0">管理口</h4>-->
                <!--<h4 v-if="item.type === 1">业务口一</h4>-->
                <!--<h4 v-if="item.type === 2">业务口二</h4>-->
                <!--<el-form label-width="80px" class="testForm" :model="item" :rules="rules">-->
                    <!--<el-form-item label="IP地址" prop="ip">-->
                        <!--<el-input v-model="item.ip" placeholder="请输入IP地址"></el-input>-->
                    <!--</el-form-item>-->
                    <!--<el-form-item label="子网掩码" prop="mask">-->
                        <!--<el-input v-model="item.mask" placeholder="请输入子网掩码"></el-input>-->
                    <!--</el-form-item>-->
                    <!--<el-form-item label="网关地址" prop="gw" style="margin-bottom: 0">-->
                        <!--<el-input v-model="item.gw" placeholder="请输入网关地址"></el-input>-->
                    <!--</el-form-item>-->
                <!--</el-form>-->
                <!--<div class="test_btn">-->
                    <!--<el-button size="small" type="primary">保 存</el-button>-->
                    <!--<el-button size="small">取 消</el-button>-->
                <!--</div>-->
            <!--</li>-->
        <!--</ul>-->
    </div>
</template>

<script>
    import createTable from "@/utils/createTable";
    // import systemMG from "@/api/systemMG";

    export default {
        name: "network-cat-configure",
        comments: {createTable},
        data() {
            const validatorIp = (rule, value, callback) => {
                if (value !== "") {
                    let ip = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
                    if (!ip.test(value)) {
                        callback(new Error('请输入正确的IP!'))
                    } else {
                        callback()
                    }
                } else {
                    callback();
                }
            };
            const validatorMask = (rule, value, callback) => {
                if (value !== "") {
                    let mask = /^(254|252|248|240|224|192|128|0)\.0\.0\.0|255\.(254|252|248|240|224|192|128|0)\.0\.0|255\.255\.(254|252|248|240|224|192|128|0)\.0|255\.255\.255\.(254|252|248|240|224|192|128|0)$/;
                    if (!mask.test(value)) {
                        callback(new Error("请输入正确的子网掩码！"));
                    } else {
                        callback();
                    }
                } else {
                    callback();
                }
            };
            const validatorGW = (rule, value, callback) => {
                if (value !== "") {
                    let ip = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
                    if (!ip.test(value)) {
                        callback(new Error('请输入正确的网关地址!'))
                    } else {
                        callback()
                    }
                } else {
                    callback();
                }
            };
            return {
                NICBonding: false, // 网卡绑定
                isSelect: false,
                selModule: '', // 模式选择
                selModuleArr: [
                    {label: 'bond 0', value: 0},
                    {label: 'bond 1', value: 1},
                ],
                networkFormArr: [
                    {ip: '', mask: '', gw: '', type: 0 },
                    {ip: '', mask: '', gw: '', type: 1 },
                    {ip: '', mask: '', gw: '', type: 2 },
                ],
                disForm: true,
                rules: {
                    ip: [
                        {required: true, message: "请输入IP地址", trigger: "blur"},
                        {validator: validatorIp, trigger: 'blur'}
                    ],
                    mask: [
                        {required: true, message: "请输入子网掩码", trigger: "blur"},
                        {validator: validatorMask, trigger: 'blur'}
                    ],
                    gw: [
                        {required: true, message: "请输入网关地址", trigger: "blur"},
                        {validator: validatorGW, trigger: 'blur'}
                    ],
                }
            }
        },
        methods: {
            // 获取网口列表
            getNetworkListFun() {
                this.$http.systemMG.networkListApi().then(({ code, data, msg }) => {
                    console.log(code, data, msg);
                    if (!code) {
                        this.networkFormArr = data
                    } else {
                        // this.$message.warning(msg)
                    }
                })
            },
            // 网卡绑定
            setNetworkFun(isChange) {
                // console.log(isChange);
                this.$confirm('请确保网络配置的正确性, 配置后立刻生效!', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    closeOnClickModal: false,
                    closeOnPressEscape: false,
                    type: 'warning'
                }).then(() => {
                    let bondObj;
                    if (isChange) {
                        // 业务口一
                        bondObj = this.networkFormArr.find(item => item.type === 1);
                        // this.$refs['networkForm'][1].disabled = true
                    } else {
                        // 业务口二 NICBonding
                        bondObj = this.networkFormArr.find(item => item.type === 2);
                        this.selModule = ''
                    }
                    bondObj.switchBond = isChange;
                    this.$nextTick(() => {
                        this.isSelect = isChange;
                    });
                    bondObj.bond = this.selModule;
                    this.$http.systemMG.setBond(bondObj).then(({code, data, msg}) => {
                        if(!code) {
                            this.$message.success(msg);
                            this.getNetworkListFun()
                          // 调用重启设备方法
                          // this.restartDeviceFun();
                        } else {
                            this.NICBonding = false;
                            this.selModule = '';
                            // if (code !== 500) this.$message.error(msg)
                        }
                    })
                }).catch(() => {
                    this.getBondStatusFun()
                });
            },
            // 获取 bond 状态
            getBondStatusFun() {
                this.$http.systemMG.getBondStatus().then(({code, data, msg}) => {
                    console.log(code, data, msg);
                    if (!code) {
                        this.NICBonding = data.switchBond;
                        this.selModule = data.switchBond ? data.bond : '';
                        this.$nextTick(() => {
                            this.isSelect = data.switchBond;
                        });
                    } else {
                        // this.$message.warning(msg)
                    }
                    // !code ? this.NICBonding = data.switchBond : this.$message.warning(msg)
                })
            },
            // 保存配置(设置网络)
            networkSave(params, index) {
                this.$refs['networkForm'][index].validate(valid => {
                    if (valid) {
                        this.$confirm('请确保网络配置的正确性, 配置后立刻生效!', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            closeOnClickModal: false,
                            closeOnPressEscape: false,
                            type: 'warning'
                        }).then(() => {
                            // console.log(this.$refs['networkForm'][index]);
                            // params.type = type;
                            this.$http.systemMG.setNetwork(JSON.stringify(params)).then(({ code, data, msg }) => {
                                // console.log(code, data, msg);
                                if (!code) {
                                    this.$message.success('配置成功!');
                                    // 调用重启设备方法
                                    // this.restartDeviceFun();
                                    this.getNetworkListFun()
                                } else {
                                    // this.$message.warning(msg)
                                }
                            })
                        });
                    }
                })
            },
            // clearValidate
            cancelHandle(type, index) {
                this.$http.systemMG.networkListApi().then(({ code, data, msg }) => {
                    if (!code) {
                        this.$set(this.networkFormArr, index, data[index]);
                        this.$refs['networkForm'][index].clearValidate()
                    } else {
                        // this.$message.warning(msg)
                    }
                })
            },
            // 重启设备 (无调用)
            restartDeviceFun() {
                this.$confirm('网络设置成功，需重启设备生效', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    closeOnClickModal: false,
                    closeOnPressEscape: false,
                    showClose: false,
                    showCancelButton: false,
                    type: 'warning'
                }).then(() => {
                    this.$http.systemMG.restartDevApi().then(res => {
                        console.log(res)
                    });
                    // this.getNetworkListFun()
                })
            },


            // 查询所有接口
            allEth() {
                this.tableData = [];
                this.$http.systemMG.allEth().then((res) => {
                    const code = res.code;
                    if (code == 0) {
                        this.tableData = res.data;
                    } else {
                        this.$message.error(res.msg);
                    }
                })
            },
            queryGW() {
                this.$http.systemMG.queryDefGW().then((res) => {
                    const code = res.code;
                    if (code == 0) {
                        this.gw = res.data;
                    } else {
                        // this.$message.error(res.msg);
                    }
                })
            },
            editView(row) {
                this.ethForm.eth = row.eth;
                this.ethForm.status = row.status;
                this.ethForm.ip = row.ip;
                this.ethForm.netmask = row.netmask;
                this.ethForm.gw = row.gw;
                this.ethForm.trillion = row.trillion;
                this.ethForm.purpose = row.purpose;
                this.ethForm.showOnLink = row.showOnLink;
                this.ipVisible = true;
            },
            addView() {
                this.addVisible = true;
            },
            closeDialog() {
                this.$refs["ethForm"].clearValidate();
                this.ipVisible = false;
                this.clearEthForm();
            },
            closeAdd() {
                this.addVisible = false;
                this.clearEthForm();
            },
            setIpAddr() {
                this.$refs["ethForm"].validate((valid) => {
                    if (valid) {
                        this.$http.systemMG.setEth(this.ethForm).then((res) => {
                            const code = res.code;
                            if (code == 0) {
                                this.$message.success("修改成功！");
                                this.allEth();
                            } else {
                                // this.$message.error(res.msg)
                            }
                            this.ipVisible = false;
                            this.clearEthForm();
                        })
                    }
                })
            },
            setGw() {
                var opt = {
                    "gw": this.gw
                };
                this.$http.systemMG.setDefGW(opt).then((res) => {
                    const code = res.code;
                    if (code == 0) {
                        this.$message.success("设置成功！");
                    } else {
                        // this.$message.error(res.msg);
                    }
                })
            },
            addEth() {

                this.$refs["ethForm"].validate((valid) => {
                    if (valid) {
                        if (this.ethForm.status == 'false') {
                            this.ethForm.status = 'down'
                        } else {
                            this.ethForm.status = 'up'
                        }

                        this.$http.systemMG.setEth(this.ethForm).then((res) => {
                            const code = res.code;
                            if (code == 0) {
                                this.$message.success(res.msg)
                            } else {
                                // this.$message.error(res.msg)
                            }
                        });
                        this.addVisible = false;
                        this.clearEthForm();
                    }
                })
            },
            clearEthForm() {
                this.ethForm.eth = null;
                this.ethForm.status = null;
                this.ethForm.ip = null;
                this.ethForm.netmask = null;
                this.ethForm.gw = null;
                this.ethForm.trillion = null;
                this.ethForm.purpose = null;
            },
            getEmptyEth() {
                this.$http.systemMG.getEmptyEth().then((res) => {
                    const code = res.code;
                    if (code == 0) {
                        this.ethOptions = res.data;
                    } else {
                        // this.$message.error(res.msg)
                    }
                })
            },
            changeEnableEth(row) {
                if (row.ip == null || row.ip == "") {
                    this.$message.warning("未设置网卡!");
                    return;
                }

                if (row.showOnLink == null || row.showOnLink == "") {
                    this.$message.warning("未连接网线，网卡不能启动！")
                    return;
                }

                const status = row.status;
                var changeStatus = "up";
                if ("UP" == status) {
                    changeStatus = "down";
                }
                var opt = {
                    eth: row.eth,
                    status: changeStatus
                };

                this.$http.systemMG.setNetCardStatus(opt).then((res) => {
                    const code = res.code;
                    if (code == 0) {
                        this.$message.success("设置成功！");
                        this.allEth();
                    } else {
                        // this.$message.error(res.msg);
                    }
                })
            }
        },
        created() {
            this.getNetworkListFun();
            this.getBondStatusFun();
            // this.getEmptyEth();
            // this.queryGW();
        }
    }
</script>

<style lang="less" scoped>
    .comForm.el-form .el-form-item {
        padding: 0 10px;
        /*margin: 0 10px 10px 0;*/
        /*margin-bottom: 10px;*/
        border-radius: 5px;
        background-color: #f0f0f0;
        /*&:last-child {*/
        /*margin-right: 0;*/
        /*}*/
    }
    .tip_txt {
        font-size: 14px;
        color: #ccc;
        margin-left: 30px;
    }

    .network_config_con {
        list-style: none;
        padding: 0;

        li {
            display: flex;
            padding: 20px 10px;
            margin-bottom: 15px;
            /*border: 1px solid #ccc;*/
            border-radius: 5px;
            background-color: #fff;
            box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);

            .network_type, .handle_con {
                width: 240px;
            }

            .network_type {
                width: 30px;
                /*height: 100px;*/
                height: inherit;
                padding: 5px;
                margin: 0 80px;
                font-size: 18px;
                font-weight: 600;
                /*line-height: 20px;*/

                /*垂直方向文字*/
                writing-mode: vertical-rl;
                /*居中文字*/
                text-align: center;
                display: flex;
                flex-direction: column;
                justify-content: center;

                background-color: #EEEEEE;
            }

            .config_item {
                flex: 1;

                span {
                    display: flex;
                    padding: 4px;
                    margin-bottom: 10px;
                    vertical-align: middle;
                    border: 1px solid #ccc;
                    background-color: #EEEEEE;

                    &:last-child {
                        margin-bottom: 0;
                    }

                    i {
                        font-style: normal;
                        width: 120px;
                        line-height: 32px;
                    }
                }
            }

            .handle_con {
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }
    }


    .testForm.el-form {
        /*margin-top: 15px;*/
        padding: 15px 20px;
        .el-form-item {
            /*padding: 0 10px;*/
            /*margin: 0 10px 10px 0;*/
            /*margin-bottom: 10px;*/
            border-radius: 5px;
            /*background-color: #f0f0f0;*/
            /*&:last-child {*/
            /*margin-right: 0;*/
            /*}*/
        }
    }

    .content_box {
        list-style: none;
        padding: 0;
        display: flex;

        li {
            /*height: 100px;*/
            width: calc((100% - 30px) / 3);
            margin-right: 15px;
            /*padding: 10px;*/
            border-radius: 5px;
            background-color: #fff;
            box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);

            &:last-child {
                margin-right: 0;
            }
            h4 {
                padding: 10px;
                margin: 0;
                /*color: #fff;*/
                /*background-color: #EEEEEE;*/
                /*background-image: linear-gradient(to left, #022547, #26447a); !* 自右向左 *!*/
                border-bottom: 1px solid #ccc;
            }
            .test_btn {
                margin-top: 20px;
                text-align: right;
                padding: 10px 20px;
            }
        }
    }

</style>

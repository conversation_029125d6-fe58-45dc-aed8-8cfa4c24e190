<template>
  <!--为echarts准备一个具备大小的容器dom-->
  <div>
    <el-card>
      <el-row>
        <el-col :span="24">
          <div class="grid-content bg-purple-dark">
            <el-col :span="18">
              <div>
                {{ title }}
              </div>
            </el-col>
            <el-col :span="6" class="flex-end">
              <el-button size="mini" type="primary" @click="refresh()"
                >刷新
              </el-button>
              <el-button size="mini" type="primary" @click="onelevel"
                >返回上一级
              </el-button>
            </el-col>
          </div>
        </el-col>
        <el-col :span="24"
          ><div class="grid-content bg-purple-dark">
            <el-col :span="24">
              <div
                class="grid-content bg-purple"
                style="text-align: right; margin-top: 20px"
              >
                历史搜索：
                <el-select
                  v-model="value"
                  placeholder="请选择"
                  size="small"
                  @change="changehistory"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
                <el-button
                  size="small"
                  style="margin-left: 10px"
                  @click="resetting"
                  >重置</el-button
                >
              </div>
            </el-col>
          </div>
        </el-col>
      </el-row>
      <div style="margin-top: 20px">
        <div class="pie" id="pie" :style="{ height: '500px' }" />
      </div>
    </el-card>
    <!-- <el-card style="margin-top: 5px">
      <tabledata ref="tablename"></tabledata>
    </el-card> -->
  </div>
</template>
<script>
import tabledata from "./tabledata";
export default {
  components: { tabledata },
  name: "barGraph",
  props: ["id", "getData"], // 满足一个页面有多个饼图，建议传入不同id
  data() {
    return {
      title: "",
      nav: "",
      name: "",
      pie: this.id || "pie",
      flowdate: {
        opinion: [],
        opinionDatay: [],
      },
      options: [
        {
          value: "1",
          label: "近一天",
        },
        {
          value: "2",
          label: "近一周",
        },
        {
          value: "3",
          label: "近一个月",
        },
        {
          value: "4",
          label: "近一年",
        },
        {
          value: "5",
          label: "全部",
        },
      ],
      value: "1",
      API_CODES: {
        history: {
          "TAG.SIGN(d)": "N_037",
          "TAG.SIGN(w)": "N_038",
          "TAG.SIGN(m)": "N_039",
          "TAG.SIGN(y)": "N_040",
          "TAG.SIGN(all)": "N_041",

          "TAG.ASYM(d)": "N_048",
          "TAG.ASYM(w)": "N_049",
          "TAG.ASYM(m)": "N_050",
          "TAG.ASYM(y)": "N_051",
          "TAG.ASYM(all)": "N_052",

          "TAG.SYM(d)": "N_065",
          "TAG.SYM(w)": "N_066",
          "TAG.SYM(m)": "N_067",
          "TAG.SYM(y)": "N_068",
          "TAG.SYM(all)": "N_069",

          "TAG.TIMESTAMP(d)": "N_082",
          "TAG.TIMESTAMP(w)": "N_083",
          "TAG.TIMESTAMP(m)": "N_084",
          "TAG.TIMESTAMP(y)": "N_085",
          "TAG.TIMESTAMP(all)": "N_086",

          "TAG.ENVELOPE(d)": "N_093",
          "TAG.ENVELOPE(w)": "N_094",
          "TAG.ENVELOPE(m)": "N_095",
          "TAG.ENVELOPE(y)": "N_096",
          "TAG.ENVELOPE(all)": "N_097",

          "TAG.DIGEST(d)": "N_104",
          "TAG.DIGEST(w)": "N_105",
          "TAG.DIGEST(m)": "N_106",
          "TAG.DIGEST(y)": "N_107",
          "TAG.DIGEST(all)": "N_108",

          "TAG.MAC(d)": "N_115",
          "TAG.MAC(w)": "N_116",
          "TAG.MAC(m)": "N_117",
          "TAG.MAC(y)": "N_118",
          "TAG.MAC(all)": "N_119",

          "TAG.CSD.SIGN(d)": "N_126",
          "TAG.CSD.SIGN(w)": "N_127",
          "TAG.CSD.SIGN(m)": "N_128",
          "TAG.CSD.SIGN(y)": "N_129",
          "TAG.CSD.SIGN(all)": "N_130",

          "TAG.CSD.DEC(d)": "N_137",
          "TAG.CSD.DEC(w)": "N_138",
          "TAG.CSD.DEC(m)": "N_139",
          "TAG.CSD.DEC(y)": "N_140",
          "TAG.CSD.DEC(all)": "N_141",
        },
      },
    };
  },
  // 实时监听父组件传过来的值，进而执行drawBar方法，重绘柱状图
  // mounted() {
  //   this.drawPie(this.flowdate);
  // },
  methods: {
    Detail(val) {
      this.title = val.title;
      this.nav = val.nav;
      this.name = val.name;
      this.oneApi();
      // 需求变更-去掉底部表格渲染
      // if (this.title == "加解密历史业务量") {
      //   this.$refs.tablename.Detail("N_025");
      // } else if (this.title == "签名验签历史业务量") {
      //   this.$refs.tablename.Detail("N_025");
      // }
    },

    oneApi() {
      if (!this.nav || !this.name) {
        console.error("nav or title is not initialized");
        return;
      }
      const navApiCodes = this.API_CODES[this.nav];
      if (!navApiCodes) {
        console.error(`No API codes found for nav: ${this.nav}`);
        return;
      }
      let apiCode = (apiCode = navApiCodes[this.name + "(d)"]);
      if (!apiCode) {
        console.error(
          `No API code found for name: ${this.name} in nav: ${this.nav}`
        );
        return;
      }
      try {
        this.getApi(apiCode);
      } catch (error) {
        console.error(`Error calling API: ${error}`);
      }
    },
    getApi(item) {
      var model = {
        opinionData: [],
        opinionDatay: [],
        opinionDatax: [],
        unit: "",
      };
      let opt = {
        setCode: item,
      };
      this.$http.monitorApi.query(JSON.stringify(opt)).then((res) => {
        let detail = res.data.detail[0];
        model.opinionDatax = detail.xAxis;
        model.opinionDatay = detail.point;
        model.unit = res.data.unit;
        this.drawPie(model);
      });
    },
    drawPie(getData) {
      var charts = this.$echarts.init(document.getElementById(this.pie));
      var getDataList = getData.opinionDatay;
      var colorArray = [
        "#4D88FE",
        "#50CCCB",
        "#EE9201",
        "#29AAE3",
        "#B74AE5",
        "#0AAF9F",
        "#E89589",
        "#16A085",
        "#4A235A",
        "#C39BD3",
        "#F9E79F",
        "#BA4A00",
        "#ECF0F1",
        "#616A6B",
        "#EAF2F8",
        "#4A235A",
        "#3498DB",
      ];
      var series1 = [];
      for (var p = 0; p < getDataList.length; p++) {
        var item = {
          name: getDataList[p].name,
          type: "bar",
          itemStyle: {
            normal: {
              color: colorArray[p], //折线点的颜色
            },
          },
          stack: getDataList[p].stack,
          data: getDataList[p].value,
        };
        series1.push(item);
      }
      charts.setOption({
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "line",
            textStyle: {
              color: "#fff",
            },
          },
          formatter: function (params) {
            // 格式：数据组必须是一个成功跟一个失败。
            var res = params[0].name + "<br/>";
            var cache = 0;
            for (var i = 0, l = params.length; i < l; i++) {
              let lastTwoChars = params[i].seriesName.slice(-2);
              let totalName = params[i].seriesName.slice(0, -2);
              res +=
                "<span style='display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:" +
                params[i].color +
                ";'></span>" +
                params[i].seriesName +
                " : " +
                params[i].value +
                getData.unit +
                "<br/>";
              if (lastTwoChars === "失败") {
                res +=
                  "<span style='display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#FFFFFF;'></span>" +
                  totalName +
                  "总量 : " +
                  (params[i].value + cache) +
                  getData.unit +
                  "<br/>";
              } else {
                cache = params[i].value;
              }
            }
            return res;
          },
        },
        legend: {
          icon: "rect",
          bottom: 5,
          left: "center",
          // itemWidth: 20,
          // itemHeight: 5,
          itemGap: 60,
          textStyle: {
            color: "#666",
          },
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "8%",
          top: "18%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: getData.opinionDatax,
          },
        ],
        yAxis: [
          {
            type: "value",
            minInterval:1,
          },
        ],
        series: series1,
      });
      window.addEventListener("resize", () => {
        setTimeout(() => {
          charts.resize();
        }, 500);
      });
    },
    refresh() {
      if (this.value != "") {
        this.changehistory(this.value);
      } else {
        this.oneApi();
      }
    },
    changehistory(val) {
      const navApiCodes = this.API_CODES[this.nav];
      let apiCode = "";
      if (val == 1) {
        apiCode = navApiCodes[this.name + "(d)"];
      }
      if (val == 2) {
        apiCode = navApiCodes[this.name + "(w)"];
      }
      if (val == 3) {
        apiCode = navApiCodes[this.name + "(m)"];
      }
      if (val == 4) {
        apiCode = navApiCodes[this.name + "(y)"];
      }
      if (val == 5) {
        apiCode = navApiCodes[this.name + "(all)"];
      }
      this.getApi(apiCode);
    },
    onelevel() {
      this.$emit("personHandle");
    },
    resetting() {
      this.value = "1";
      this.oneApi();
    },
  },
};
</script>
<style lang="less" scoped>
.flex-end {
  display: flex;
  justify-content: flex-end;
}
</style>

<template>
  <div>
     <!-- 设置root级别 -->
     <el-card class="box-card" style="width: 70%;margin-bottom: 10px">
      <div slot="header" class="clearfix">
        <span style="font-size: 18; font-weight: 600;">设置日志级别</span>
      </div>
      <el-form label-width="160px" style="margin-top: 10px">
        <el-form-item label="日志级别：">
          <el-select v-model="rootLevel" placeholder="请选择" size="small">
            <el-option v-for="item in levelOptions" :key="item.value" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button class="comBtn com_send_btn" size="small" @click="serviceSetRootLevel">设置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <!-- 下载日志 -->
    <el-card class="box-card" style="width: 70%;margin-bottom: 10px">
      <div slot="header" class="clearfix">
        <span style="font-size: 18; font-weight: 600;">下载日志</span>
      </div>
      <el-form label-width="160px" ref="downloadForm" :model="downloadForm" :rules="rules">
        <el-form-item label="日志日期：" style="width: 50%" prop="searchTime">
          <el-date-picker v-model="downloadForm.searchTime" type="date" placeholder="选择日期" value-format="yyyyMMdd"
            size="small" :picker-options="pickerOptions">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button class="comBtn com_send_btn" :loading="downLoading" size="small"
            @click="downloadSvsGoLog">下载</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
// import oamMG from "@/api/oamMG";
export default {
  name: "service-log",
  data() {
    return {
      level: "info",
      downLoading: false,
      levelOptions: [
        { label: "DEBUG", value: "debug" },
        { label: "INFO", value: "info" },
        { label: "WARN", value: "warn" },
        { label: "ERROR", value: "error" }
      ],
      rootLevel: "",
      downloadForm: {
        logLevel: "",
        searchTime: ""
      },
      pickerOptions: {
        disabledDate: (time) => {
          return time.getTime() > Date.now()
        }
      },
      rules: {
        searchTime: [{ required: true, message: '请选择要下载日志的时间', trigger: 'blur' }],
        logLevel: [{ required: true, message: '请选择下载日志的级别', trigger: ['blur', 'change'] }]
      }
    }
  },
  methods: {
    getLogLevelFun() {
      this.$http.oamMG.getLogLevel().then(res => {
        let code = res.code;
        if (code == 0) this.rootLevel = res.data;
      })
    },
    // 设置日志级别
    serviceSetRootLevel() {
      this.$http.oamMG.setLogLevel(this.rootLevel).then(res => {
        let code = res.code;
        if (code == 0) this.$message.success("设置成功！");
      })
    },
    downloadSvsGoLog() {
      this.$refs["downloadForm"].validate(valid => {
        if (valid) {
          this.downLoading = true;
          this.$http.oamMG.downloadSvsGoLog(this.downloadForm).then(res => {
            let blob = new Blob([res], {
              type: 'application/zip'
            });
            let fileName = this.downloadForm.searchTime + ".zip";
            if (window.navigator.msSaveOrOpenBlob) {
              // console.log(2)
              navigator.msSaveBlob(blob, fileName)
            } else {
              // console.log(3)
              var link = document.createElement('a');
              link.href = window.URL.createObjectURL(blob);
              link.download = fileName;
              link.click();
              //释放内存
              window.URL.revokeObjectURL(link.href);
              this.downLoading = false;
            }
          },
            error => {
              this.$message.error("下载异常！");
              this.downLoading = false;
            })
        }
      })
    }
  }
}
</script>

<style scoped></style>

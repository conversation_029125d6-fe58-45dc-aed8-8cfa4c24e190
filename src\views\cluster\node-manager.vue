<template>
    <div class="container">
        <!-- 页面搜索表单 -->
        <el-card v-show="showSearch" class="box-card" shadow="always" style="padding-bottom: 10px">
            <el-form :inline="true" :show-message="false" class="user-search comForm" style="text-align: left">
                <el-form-item label="集群 :">
                    <!-- <el-input size="small" v-model="node.queryParams.clusterId" clearable></el-input> -->
                    <el-select v-model="node.queryParams.clusterId" clearable>
                        <el-option v-for="item in clusters" :label="item.clusterName" :value="item.id" :key="item.id">
                            <span>{{ item.clusterName }}</span>
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="节点名称 :">
                    <el-input size="small" v-model="node.queryParams.nodeName" clearable></el-input>
                </el-form-item>
                <el-form-item label="节点IP :">
                    <el-input size="small" v-model="node.queryParams.nodeIp" clearable></el-input>
                </el-form-item>
                <el-form-item style="background-color: transparent;">
                    <el-button class="comBtn com_send_btn" size="mini" type="primary" icon="el-icon-search"
                        @click="refreshList">搜索</el-button>
                    <el-button class="comBtn com_reset_btn" size="mini" type="primary" icon="el-icon-refresh"
                        @click="reset">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <div v-show="showSearch" style="padding: 10px"></div>
        <el-card class="box-card" shadow="always" style="padding-bottom: 10px">
            <!-- 操作方法 -->
            <el-form label-width="100px">
                <el-row>
                    <el-col :span="14" style="text-align: left">
                        <el-button class="comBtn com_add_btn" size="mini" type="success"
                            @click="openAddWindow()">新增</el-button>
                        <el-button class="comBtn" size="mini" type="success"
                            @click="updateLoadBalancing()">更新负载配置</el-button>
                        <Strategy :policyType="2"></Strategy>
                        <div style="font-size: 14px;color: #606266;display: inline-block;margin-left: 10px;">
                            策略批次号：{{ batchNumber }}
                        </div>
                    </el-col>
                    <el-col :span="10">
                        <div style="text-align: right">
                            <el-button-group>
                                <el-button size="mini" icon="el-icon-search" @click="showAndSearch"></el-button>
                                <el-button size="mini" icon="el-icon-refresh-left" @click="refreshList"></el-button>
                            </el-button-group>
                        </div>
                    </el-col>
                </el-row>
            </el-form>
            <!-- 业务节点列表 -->
            <div style="padding-top: 10px">
                <createTable :tableData="tableData" :tableHeader="tableHeader" :isPage="isPage" :pageAttributes="pageAttr"
                    :size-change="sizeChange" :current-change="currentChange" :prev-click="prevClick"
                    :next-click="nextClick" />
            </div>

            <!-- Dialog ---------------------------------------------- -->
            <!-- 添加业务节点 -->
            <el-dialog :title="editNode ? '编辑业务节点' : '添加业务节点'" :visible.sync="addNodeOpen" width="500px" append-to-body
                :close-on-click-modal="false" @close="closeWindow">
                <el-form ref="addform" :model="form" :rules="rules" label-width="100px" size="medium">
                    <el-form-item label="所属集群：" prop="clusterId">
                        <el-select v-model="form.clusterId" :disabled="form.ipNotEdit" :popper-append-to-body="false"
                            class="option_style">
                            <el-option v-for="item in clusters" :label="item.clusterName" :value="item.id" :key="item.id"
                                :title="item.clusterName">
                                <span>{{ item.clusterName }}</span>
                            </el-option>
                        </el-select>
                        <!--<el-input v-model="form.clusterId" placeholder="2~10个字符，可使用字母、数字" clearable/>-->
                    </el-form-item>
                    <el-form-item label="节点名称：" prop="nodeName">
                        <el-input v-model="form.nodeName" placeholder="2~30个字符，可使用汉字、字母、数字、下划线" clearable />
                    </el-form-item>
                    <el-form-item label="IP地址：" prop="nodeIp">
                        <el-input v-model="form.nodeIp" clearable :disabled="form.ipNotEdit" placeholder="请输入IP地址" />
                    </el-form-item>
                    <el-form-item label="通讯端口：" prop="proxyPort">
                        <el-input v-model="form.proxyPort" placeholder="通讯端口（通常为：6090）" clearable
                            :disabled="form.ipNotEdit" />
                    </el-form-item>
                    <el-form-item label="描述：">
                        <el-input v-model="form.nodeDes" maxlength="255" show-word-limit placeholder="请输入描述" />
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button class="comBtn com_reset_btn" @click="closeWindow">取 消</el-button>
                    <el-button class="comBtn com_send_btn" type="primary" @click="submitForm">确 定</el-button>
                </div>
            </el-dialog>

            <!-- 修改业务节点日志配置 -->
            <el-dialog title="日志配置" :visible.sync="logConfigOpen" width="80%" append-to-body @close="closeLogConfig">
                <NodeLogConfigure ref="logConfig"></NodeLogConfigure>
            </el-dialog>
        </el-card>
    </div>
</template>

<script>
// import cluster from "@/api/cluster"
import NodeLogConfigure from "@/views/cluster/node-log-config";
import Strategy from "@/components/strategy/index";
import { dateFormat } from "@/utils/util";

export default {
    name: "node-manage",
    components: { NodeLogConfigure, Strategy },
    data() {
        let _this = this;
        return {
            showSearch: true,
            addNodeOpen: false,
            editNode: false,
            logConfigOpen: false,
            batchNumber: "-",
            node: {
                queryParams: {
                    pageNo: 1,
                    pageSize: 10,
                    clusterId: "",
                    nodeName: "",
                    nodeIp: ""
                }
            },
            clusters: [],
            tableData: [],
            tableHeader: [
                { label: "序号", type: "select", width: "38" },
                { label: "节点名称", prop: "nodeName", type: "normal" },
                { label: "IP地址", prop: "nodeIp", type: "normal" },
                { label: "通讯端口", prop: "proxyPort", type: "normal" },
                {
                    label: "设备状态", prop: "nodeStatus", type: "col_componet",
                    componet: function (h, props) {
                        switch (props.row.nodeStatus) {
                            case 1:
                                // return "待连接";
                                return [h("el-button", {
                                    attrs: { type: "info", circle: true, size: "mini", style: "vertical-align: middle;" }
                                })];
                            case 2:
                                // return "接管请求状态";
                                return [h("el-button", {
                                    attrs: { type: "info", circle: true, size: "mini", style: "vertical-align: middle;" }
                                })];
                            case 3:
                                // return "接管拒绝";
                                return [h("el-button", {
                                    attrs: { type: "warning", circle: true, size: "mini", style: "vertical-align: middle;" }
                                })];
                            case 4:
                                return [h("el-button", {
                                    attrs: { type: "success", circle: true, size: "mini", style: "vertical-align: middle;" }
                                })];
                            case 5:
                                return [h("el-button", {
                                    attrs: { type: "danger", circle: true, size: "mini", style: "vertical-align: middle;" }
                                })];
                        }
                    }
                },
                {
                    label: "服务状态", prop: "nodeGoStatus", type: "switch",
                    attrs: { "disabled": true },
                    callback: function (row) {
                        // if (row.nodeStatus === 4) {
                        //     _this.changeGoStatus(row);
                        // } else if (row.nodeStatus === 5) {
                        //     _this.$message.warning('设备失联!');
                        // } else {
                        //     _this.$message.warning('请先接管');
                        // }
                    }
                },
                {
                    label: "所属集群", prop: "clusterId", type: "text_formatter",
                    formatter(value) {
                        let label;
                        _this.clusters.forEach(function (item) {
                            if (item.id == value) {
                                label = item.clusterName;
                            }
                        });
                        return label;
                    }
                },
                {
                    label: "最后一次心跳", prop: "lastHeartBeat", type: "text_formatter",
                    formatter(value) {
                        if (value && value != 0) {
                            return dateFormat("YYYY-mm-dd HH:MM:SS", new Date(parseInt(value)));
                        } else {
                            return "-";
                        }
                    }
                },
                {
                    label: "策略批次号", prop: "policyBatch", type: "text_formatter",
                    formatter(value) {
                        if (value) {
                            return value;
                        } else {
                            return "-";
                        }
                    }
                },
                {
                    label: "负载状态", prop: "nodeLbsStatus", type: "col_componet", showOverflowTooltip: false, width: "130",
                    componet: function (h, props) {
                        let status = [];
                        let content = "未纳管";
                        switch (props.row.nodeLbsStatus) {
                            case 0:
                                status.push("未纳管");
                                content = "未纳管";
                                if (props.row.nodeStatus == 4 && props.row.nodeGoStatus == 1) {
                                    status.push("(");
                                    status.push(h("i", { attrs: { class: "el-icon-warning", style: "color: rgb(230, 162, 60);" }, }));
                                    status.push("待纳管)");
                                    content = "请“更新负载配置”，使此节点加入负载，提供服务。";
                                }
                                break;
                            case 1:
                                status.push("已纳管");
                                content = "已纳管";
                                if (props.row.nodeStatus != 4 || props.row.nodeGoStatus != 1) {
                                    status.push("(");
                                    status.push(h("i", { attrs: { class: "el-icon-warning", style: "color: rgb(230, 162, 60);" }, }));
                                    status.push("待移除)");
                                    content = "服务异常，请“更新负载配置”，否则业务访问此节点，可能会造成业务失败，或者响应慢。";
                                }
                                break;
                            default:
                                status.push("未纳管");
                                break;
                        }
                        return h("span", [
                            h("el-tooltip", {
                                attrs: {
                                    class: "item",
                                    effect: "dark",
                                    content: content,
                                    placement: "top",
                                },
                            }, [h("span", status)]),
                        ]);
                    }
                },
                {
                    label: "操作",
                    prop: "1",
                    type: "operation",
                    width: "450",
                    tag: [
                        {
                            name: "编辑",
                            operType: "put",
                            tagType: "el-button",
                            attributes: { size: "mini", type: "text", icon: "el-icon-edit" },
                            callback: function (row) {
                                _this.openEditWindow(row);
                            }
                        },
                        {
                            name: "删除",
                            operType: "put",
                            tagType: "el-button",
                            attributes: { size: "mini", type: "text", icon: "el-icon-delete" },
                            callback: function (row) {
                                if (row.nodeStatus == 4) {
                                    _this.$message.warning('请先断开链接!');
                                    return;
                                }
                                if (row.nodeLbsStatus == 1) {
                                    _this.$message.warning('节点已经负载，请先更新负载配置!');
                                    return;
                                }
                                _this.delNode(row.id);
                            }
                        },
                        {
                            name: "接管",
                            operType: "put",
                            tagType: "el-button",
                            attributes: { size: "mini", type: "text", icon: "el-icon-link" },
                            isShow: function (row) {
                                return row.nodeStatus == 1;
                            },
                            callback: function (row) {
                                _this.linkNode(row);
                            }
                        },
                        {
                            name: "接管(待应答)", operType: "put", tagType: "el-button",
                            attributes: { size: "mini", type: "text", icon: "el-icon-link", disabled: true },
                            isShow: function (row) {
                                return row.nodeStatus == 2;
                            }
                        },
                        {
                            name: "接管(被拒绝)", operType: "put", tagType: "el-button",
                            attributes: { size: "mini", type: "text", icon: "el-icon-link", disabled: true },
                            isShow: function (row) {
                                return row.nodeStatus == 3;
                            }
                        },
                        {
                            name: "日志配置",
                            operType: "put",
                            tagType: "el-button",
                            attributes: { size: "mini", type: "text", icon: "el-icon-setting" },
                            isShow: function (row) {
                                return row.nodeStatus == 4;
                            },
                            callback: function (row) {
                                _this.openLogConfig(row);
                            }
                        },
                        {
                            name: "证书下载",
                            operType: "put",
                            tagType: "el-button",
                            attributes: { size: "mini", type: "text", icon: "el-icon-download" },
                            isShow: function (row) {
                                return row.nodeStatus > 3;
                            },
                            callback: function (row) {
                                _this.downloadNodeCert(row);
                            }
                        },
                        {
                            name: "断开链接",
                            operType: "put",
                            tagType: "el-button",
                            attributes: { size: "mini", type: "text", icon: "el-icon-remove-outline" },
                            isShow: function (row) {
                                return row.nodeStatus > 3;
                            },
                            callback: function (row) {
                                _this.unLinkNode(row);
                            }
                        }
                    ]
                }
            ],
            fullLoading: null,
            isPage: true,
            pageAttr: {
                total: 0,
                pageSize: 10,
                currentPage: 1
            },
            form: {
                clusterId: "",
                nodeName: "",
                nodeIp: "",
                proxyPort: "",
                nodeDes: "",
                ipNotEdit: false
            },
            rules: {
                clusterId: [
                    { required: true, message: '请选择所属集群！', trigger: 'change' },
                ],
                "nodeName": [
                    { required: true, message: '节点名称不能为空！', trigger: 'blur' },
                    {
                        validator: function (rule, value, callback) {
                            if (!value) {
                                return callback(new Error('节点名称不能为空！'));
                            }
                            let length = value.length;
                            if (length < 2 || length > 30) {
                                return callback(new Error('长度在 2 ~ 30 个字符！'));
                            }
                            let patt = /^[a-zA-Z0-9_\u4e00-\u9fa5]+$/;
                            if (!patt.test(value)) {
                                return callback(new Error('可使用汉字、字母、数字、下划线！'));
                            }
                            let exitsNodeName = async function () {
                                //TODO Peter 判断名称是否存在
                                // await appMG.checkAppName(value, _this.form.app.id).then(res => {
                                //   if (!res.data) {
                                //     callback(new Error('应用名称已存在！'));
                                //   } else {
                                callback();
                                //   }
                                // });
                            };
                            exitsNodeName();
                        },
                        trigger: 'blur'
                    }],
                "nodeIp": [
                    { required: true, message: '请输入IP地址！', trigger: 'blur' },
                    {
                        validator: function (rule, value, callback) {
                            if (value && value != "") {
                                let reg = /^(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}$/;
                                if (!reg.test(value)) {
                                    return callback(new Error('请输入正确的IP地址！'));
                                }
                            }
                            callback();
                        },
                        trigger: 'blur'
                    }],
                "proxyPort": [
                    { required: true, message: '请输入端口号！', trigger: 'blur' },
                    {
                        validator: function (rule, value, callback) {
                            if (value && value != "") {
                                let reg = /^([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/;
                                if (!reg.test(value)) {
                                    return callback(new Error('请输入正确的端口号！'));
                                }
                            }
                            callback();
                        },
                        trigger: 'blur'
                    }],
                nodeDes: [{ max: 255, message: '最大255个字符', trigger: 'blur' },]
            }
        }
    },
    methods: {
        showAndSearch() {
            this.showSearch = !this.showSearch;
        },
        reset() {
            Object.assign(this.node.queryParams, this.$options.data().node.queryParams);
        },
        refreshList: function () {
            let _this = this;
            _this.node.queryParams.pageNo = _this.pageAttr.currentPage;
            _this.node.queryParams.pageSize = _this.pageAttr.pageSize;
            let queryParam = _this.node.queryParams;
            _this.tableData = [];
            this.$http.cluster.nodeList(queryParam).then(res => {
                _this.tableData = res.data;
                _this.pageAttr.total = res.row;
                if (res.row == 0) _this.isPage = false;
            });
            this.$http.cluster.getBatchNumber().then(res => {
                _this.batchNumber = res.data;
            });
            this.$http.cluster.listManage().then(res => {
                _this.clusters = res.data;
            });
        },
        updateLoadBalancing: function () {
            let _this = this;
            this.$http.cluster.updateLoadBalancing().then(res => {
                _this.$message.success(res.msg);
                setTimeout(() => {
                    _this.refreshList()
                }, 500);
            });
        },
        openAddWindow: function () {
            this.addNodeOpen = true;
        },
        openEditWindow: function (value) {
            this.addNodeOpen = true;
            this.editNode = true;
            this.form = {
                id: value.id,
                clusterId: value.clusterId,
                nodeName: value.nodeName,
                nodeIp: value.nodeIp,
                proxyPort: value.proxyPort,
                nodeDes: value.nodeDes,
                ipNotEdit: value.nodeStatus != 1
            }
        },
        closeWindow: function () {
            this.addNodeOpen = false;
            this.editNode = false;
            this.form = {};
            this.$refs['addform'].resetFields();
        },
        submitForm() {
            let _this = this;
            _this.$refs['addform'].validate(valid => {
                if (valid) {
                    if (_this.editNode) {
                        this.$http.cluster.editNode(_this.form).then(res => {
                            let code = res.code;
                            if (code === 0) {
                                _this.closeWindow();
                                _this.refreshList();
                            }
                        });
                    } else {
                        this.$http.cluster.addNode(_this.form).then(res => {
                            let code = res.code;
                            if (code === 0) {
                                _this.closeWindow();
                                _this.refreshList();
                            }
                        });
                    }
                }
            });
        },
        delNode(id) {
            let _this = this;
            _this.$confirm('确定要删除吗?', '删除确认', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$http.cluster.delNode({ id: id }).then(res => {
                    _this.refreshList();
                });
            });
        },
        changeGoStatus(row) {
            let _this = this;
            let status = row.nodeGoStatus === 1 ? 0 : 1;
            this.$http.cluster.changeGoStatus({ "nodeId": row.id, "status": status }).then(res => {
                if (res.code == 0) {
                    row.nodeGoStatus = status;
                }
                _this.refreshList();
            });
        },
        openLogConfig(row) {
            this.logConfigOpen = true;
            this.$nextTick(() => {
                this.$refs.logConfig.queryLogConfigure(row.id);
            });
        },
        closeLogConfig() {
            this.logConfigOpen = false;
            this.$refs.logConfig.cleanPageData();
        },
        downloadNodeCert(row) {
            this.$http.cluster.downloadNodeCert(row.id).then(res => {
                let blob = new Blob([res], {
                    type: 'application/force-download'
                });
                let fileName = row.nodeIp + '.cer';
                if (window.navigator.msSaveOrOpenBlob) {
                    navigator.msSaveBlob(blob, fileName)
                } else {
                    var link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    link.download = fileName;
                    link.click();
                    //释放内存
                    window.URL.revokeObjectURL(link.href)
                }
            })
        },
        linkNode(row) {
            let _this = this;
            this.openFullLoading();
            this.$http.cluster.linkNode({ id: row.id }).then(res => {
                if (res.code === 400) return _this.$message.warning(res.msg);
                _this.refreshList();
                this.fullLoading.close();
            }).catch(() => {
                this.fullLoading.close();
            });
        },
        unLinkNode(row) {
            let _this = this;
            this.$http.cluster.unLinkNode({ id: row.id }).then(res => {
                if (res.code === 400) return _this.$message.warning(res.msg);
                _this.refreshList();
            });
        },
        // loading openFullScreen2
        openFullLoading() {
            this.fullLoading = this.$loading({
                lock: true,
                text: '业务节点接管中...',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)',
                customClass: 'my_full_loading'
            });
        },
        // 每页显示条数改变
        sizeChange(res) {
            this.pageAttr.pageSize = res;
            this.refreshList()
        },
        // 前往页
        currentChange(res) {
            this.pageAttr.currentPage = res;
            this.refreshList()
        },
        // 上一页
        prevClick(res) {
            this.pageAttr.currentPage = res;
            this.refreshList()
        },
        // 下一页
        nextClick(res) {
            this.pageAttr.currentPage = res;
            this.refreshList()
        },
    },
    created() {
        this.refreshList();
    }
}
</script>
<style lang="less">
.my_full_loading {
    .el-loading-spinner i {
        font-size: 35px;
    }
}
</style>
<style lang="less" scoped>
.option_style /deep/ .el-select-dropdown__item {
    width: 300px;
    /*display: inline-block;*/
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/deep/.el-input .el-input__count .el-input__count-inner {
    background: #F0F0F0;
    padding: 10px 4px;
}


/* 弹出框滚动条 */
/* 设置滚动条的样式 */
/**解决了滚动条之间发生错位的现象 */
::-webkit-scrollbar {
    width: 10px !important;
    height: 10px !important;
    border-radius: 5px;
}

::-webkit-scrollbar-thumb {
    border-radius: 5px;
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.2);
    /* 滚动条的颜色 */
    background-color: #e4e4e4;
}

.user-search .el-form-item {
    margin-bottom: 0;
    margin-top: 10px;
}
</style>

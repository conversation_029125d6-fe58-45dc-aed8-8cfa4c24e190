<template>
  <div style="height: calc(100% - 20px)">
    <div style="height: 100%;min-height: 400px">
      <div class="about_con">
        <div class="about_tit">
          <img src="@/assets/img/login3.png" alt="">
          <div class="sys_name">数盾科技</div>
        </div>
        <el-form :model="aboutForm" ref="ruleForm" label-width="100px" class="about_form">
          <el-form-item label="产品名称：">
            <el-input size="small" :readonly="true" v-model="aboutForm.softwareName"></el-input>
          </el-form-item>
          <el-form-item label="产品版本：">
            <el-input size="small" :readonly="true" v-model="aboutForm.softwareVersions"></el-input>
          </el-form-item>
          <el-form-item label="密钥上限：">
            <el-input size="small" :readonly="true" v-model="aboutForm.asymmetricNum"></el-input>
          </el-form-item>
          <el-form-item label="应用上限：">
            <el-input size="small" :readonly="true" v-model="aboutForm.appNum"></el-input>
          </el-form-item>
          <el-form-item label="到期时间：">
            <el-input size="small" :readonly="true" v-model="aboutForm.expirationDate"></el-input>
          </el-form-item>
          <el-form-item label=" " v-show="roleId == 1 || roleId.includes(1)">
            <el-button class="comBtn com_send_btn" size="small" type="primary" icon="el-icon-setting"
              @click="setLicense">导入License</el-button>
          </el-form-item>
        </el-form>
        <!--        <div class="about_footer">-->
        <!--          <span>运维电话：{{ telNumber }}</span>-->
        <!--          <span>版权信息：{{ copyright }}</span>-->
        <!--        </div>-->
      </div>
    </div>
    <el-dialog :title="paramsTit" :visible.sync="licenseShow" width="600px" @click="clearDialog">
      <el-form label-width="120px" :model="setForm" :rules="rules" ref="setForm">
        <el-form-item label="License:" prop="fileList">
          <el-upload class="upload-demo" drag action="" accept=".license" :auto-upload="false" :on-remove="handleRemove"
            :on-change="handleChange" :limit="1" :file-list="setForm.fileList">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__text">只能上传license格式</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="clearDialog" class="comBtn com_reset_btn">取消</el-button>
        <el-button size="small" type="primary" :loading="loading" class="comBtn com_send_btn"
          @click="saveLicense()">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import {getLicenseApi, getParams, getversion, setLicenseApi, uploadLicenseApi} from '../../api/about'
import { clearStore, getStore } from "@/utils/util";
// import symmetryMG from "../../api/symmetryMG";
// import systemMG from "../../api/systemMG";

export default {
  name: 'about',
  data() {
    return {
      loading: false, //是显示加载
      roleId: getStore("roleId"),
      aboutForm: {
        softwareName: '签名验签服务器',
        softwareVersions: 'v1.*******',
        appNum: null,
        asymmetricNum: null,
        expirationDate: '未授权',
      },
      systemName: "",
      telNumber: '',
      copyright: '',
      licenseShow: false,
      paramsTit: '导入 License',
      setForm: {
        fileList: []
      },
      fileForm: new FormData(),
      rules: {
        fileList: [{ required: true, message: '请选择文件', trigger: 'change' }]
      }
    }
  },
  /**
   * 创建完毕
   */
  created() {
    this.getLicenseFun();
    this.getLicenseParam();
    this.getSysVersion();
  },
  methods: {
    getSysVersion() {
      this.$http.about.getversion().then(res => {
        if (res.code == "0") {
          this.aboutForm.softwareVersions = res.data.sysVersion;
          this.aboutForm.softwareName = res.data.sysName;
          this.telNumber = res.data.sysTel;
          this.copyright = res.data.sysVerInfo
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    getLicenseFun() {
      this.$http.systemMG.getProjectInfo().then((res) => {
        const code = res.code;
        if (res.code == "0") {
          this.systemName = res.data.systemName;
        }
        this.$http.about.getLicenseApi({}).then(({ code, data, msg }) => {
          let _data = data.data;
          if ((typeof data.data == "string") && data.data.constructor == String) {
            _data = eval('(' + data.data + ')');
          }
          this.aboutForm.softwareName = this.systemName;//_data.name;
          if (code != 30005) {
            this.aboutForm.expirationDate = _data.endTime === -1 || !_data.endTime ? '长期' : this.dateChange(_data.endTime < 10000000000 ? _data.endTime * 1000 : _data.endTime);
          }
        }, (err) => {
          if (err.data.code != 30005) {
            this.$message.error(err.data.msg);
          }
          if (err.data.code === 30008) {
            let _data = err.data.data.data;
            if ((typeof err.data.data.data == "string") && err.data.data.data.constructor == String) {
              _data = eval('(' + err.data.data.data + ')');
            }
            this.aboutForm.softwareName = _data.name;
            this.aboutForm.expirationDate = _data.endTime === -1 || !_data.endTime ? '长期' : this.dateChange(_data.endTime < 10000000000 ? _data.endTime * 1000 : _data.endTime);
          }
        })

      })


    },
    // 时间
    dateChange(val) {
      if (val === null) return '';
      var time = new Date(val), y = time.getFullYear(), m = time.getMonth() + 1, d = time.getDate(),
        h = time.getHours();
      return y + '年' + (m < 10 ? '0' + m : m) + '月' + (d < 10 ? '0' + d : d) + '日 '// + (h < 10 ? '0' + h : h) + '时';
    },
    setLicense() {
      this.licenseShow = true;
      this.$nextTick(() => {
        this.$refs.setForm.clearValidate();
      });
    },
    saveLicense() {
      this.$refs['setForm'].validate(valid => {
        if (valid) {
          this.$confirm('确定要更新License？更新后当前在线用户将需要重新登录，请确认！', '更新 License', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            // 调用接口
            // let params = new FormData();
            // params.append('license', this.setForm.license);
            this.$http.about.uploadLicenseApi(this.fileForm).then(({ code, data, msg }) => {
              if (code == 0) {
                this.$http.symmetryMG.checkSymmetryKeyUpdate().then(res => {
                  console.log(res);
                  this.clearDialog();
                  this.$message.success(msg);
                  clearStore();
                  window.location.href = '/'
                }).catch((err) => {
                  console.log(err)
                });
              }
            })
          })
        }
      })
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
      this.fileForm.delete('file');
      this.setForm.fileList = [];
    },
    handleChange(file, fileList, num) {
      if (file.size == "0") {
        this.$message.error('选择文件大小不能为0！');
        this.setForm.fileList = [];
        return false;
      } else if (file.name.split(".")[1] != "license") {
        this.$message.error('请上传license格式！');
        this.setForm.fileList = [];
        return false;
      } else {
        this.setForm.fileList.push(file);
        this.$refs.setForm.validateField('fileList');
        this.fileForm.delete('file');
        this.fileForm.append('file', file.raw);
      }
    },
    clearDialog() {
      this.licenseShow = false;
      this.setForm.fileList = [];
      this.fileForm.delete('file');
    },
    getLicenseParam() {
      this.$http.about.getParams().then(({ code, data, msg }) => {
        let _data = data.data;
        if ((typeof data.data == "string") && data.data.constructor == String) {
          _data = eval('(' + data.data + ')');
        }
        if (code != 30005) {
          this.aboutForm.appNum = _data.appNum == -1 ? '无限制' : _data.appNum;
          this.aboutForm.asymmetricNum = _data.asymmetricNum == -1 ? '无限制' : _data.asymmetricNum;
        }
      }, (err) => {
        if (err.data.code === 30008) {
          let _data = err.data.data.data;
          if ((typeof err.data.data.data == "string") && err.data.data.data.constructor == String) {
            _data = eval('(' + err.data.data.data + ')');
          }
          this.aboutForm.appNum = _data.appNum == -1 ? '无限制' : _data.appNum;
          this.aboutForm.asymmetricNum = _data.asymmetricNum == -1 ? '无限制' : _data.asymmetricNum;
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
.about_con {
  width: 500px;
  padding-bottom: 15px;
  position: relative;
  top: 45%;
  left: 50%;
  transform: translate(-50%, -50%);
  /*border-radius: 5px;*/
  background-color: #fff;

  .about_tit {
    padding: 4px 15px;
    background-image: linear-gradient(to left, #022547, #26447a);
    /* 自右向左 */
    display: flex;
    //justify-content: center;
    align-items: center;

    .sys_name {
      margin-left: 20px;
      font-size: 18px;
      color: #fff;
      font-weight: bolder;
      white-space: nowrap
    }
  }

  .about_form {
    /*margin-top: 10px*/
    padding: 10px 50px;

    .el-form-item {
      margin-bottom: 5px;
    }

    /deep/ .el-input__inner:focus {
      border-color: #DCDFE6;
    }

    /deep/ .el-input__inner:hover {
      border-color: #DCDFE6;
    }
  }

  .about_footer {
    padding: 6px 0;
    font-size: 14px;
    color: #606266;
    text-align: center;

    span:first-child {
      margin-right: 20px;
    }
  }
}
</style>

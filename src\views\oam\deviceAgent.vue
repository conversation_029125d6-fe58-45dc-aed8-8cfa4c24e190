<template>
  <div class="container">
    <el-card style="padding-bottom: 10px">
      <el-form label-width="100px">
        <el-row>
          <el-col :span="14" style="text-align: left">
            <el-button class="comBtn com_add_btn" size="mini" :disabled="cryptoCardStatus != 3" type="success"
              @click="openCertApply">生成设备证书申请</el-button>
            <el-button :disabled="cryptoCardStatus != 3" class="comBtn com_batch_btn"
              @click="openUploadCert">一键导入</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-card>
      <el-descriptions class="margin-top" title="设备信息" :column="1" :size="size" border>
        <el-descriptions-item label="设备ID">{{ deviceInfo.DEVID }}</el-descriptions-item>
        <el-descriptions-item label="上级管理平台ID">{{ deviceInfo.CENTERID }}</el-descriptions-item>
        <el-descriptions-item label="上级管理平台IP">{{ deviceInfo.CENTERIP }}</el-descriptions-item>
        <el-descriptions-item label="上级管理平台端口">{{ deviceInfo.CENTERPORT }}</el-descriptions-item>
      </el-descriptions>
      <!-- 密码卡登录 -->
      <el-dialog title="生成设备证书申请" :visible.sync="certApplyVisible" width="35%" :before-close="closeCertApply"
        :close-on-click-modal="false">
        <el-form label-width="160px" ref="certForm" :model="certForm" :rules="rules">
          <el-form-item label="证书算法：">
            <el-input value="国密SM2算法" readOnly="true" />
          </el-form-item>
          <el-form-item label="国密证书类型：" prop="certType">
            <el-select v-model="certForm.certType" placeholder="请选择" style="width: 90%" size="small">
              <el-option v-for="item in certTypeOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="CA：">
            <el-input value="国密签名CA" readOnly="true" />
          </el-form-item>
          <el-form-item label="国家/C：" prop="countryCode">
            <el-input value="CN" readOnly="true" />
          </el-form-item>
          <el-form-item label="省份/ST：" prop="province">
            <el-input size="small" v-model="certForm.province" />
          </el-form-item>
          <el-form-item label="城市/L：" prop="city">
            <el-input size="small" v-model="certForm.city" />
          </el-form-item>
          <el-form-item label="组织/O：" prop="organization">
            <el-input size="small" v-model="certForm.organization" />
          </el-form-item>
          <el-form-item label="部门/OU：" prop="organizationalUnit">
            <el-input size="small" v-model="certForm.organizationalUnit" />
          </el-form-item>
          <el-form-item label="主题名称/CN：" prop="commonName">
            <el-input size="small" v-model="certForm.commonName" />
          </el-form-item>
          <el-form-item label="用户邮箱：" prop="sendMail">
            <el-input size="small" v-model="certForm.sendMail" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button class="comBtn com_reset_btn" size="small" @click="closeCertApply">取消</el-button>
          <el-button class="comBtn com_send_btn" size="small" :loading="loginLoading" type="primary"
            @click="certApply">申请证书</el-button>
        </div>
      </el-dialog>


      <!-- 密码卡登录 -->
      <el-dialog title="上传证书压缩文件" :visible.sync="uploadVisible" width="35%" :before-close="closeUploadCert"
        :close-on-click-modal="false">
        <el-form label-width="160px" ref="uploadForm" :model="uploadForm">
          <el-upload class="upload-demo" drag action="#" multiple :on-remove="fileRemove" :on-change="fileChange"
            :limit="1" :auto-upload="false" :file-list="uploadForm.fileList">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">只能上传zip文件!</div>
          </el-upload>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button class="comBtn com_reset_btn" size="small" @click="closeUploadCert">取消</el-button>
          <el-button class="comBtn com_send_btn" size="small" :loading="loginLoading" type="primary"
            @click="importDeviceCert">上传</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
// import oamMG from "@/api/oamMG";
export default {
  name: "cryptoCard",
  data() {

    return {
      certApplyVisible: false,
      uploadVisible: false,
      loginLoading: false,
      cryptoCardStatus: 0,
      rules: {
        commonName: [{ required: true, message: "请输入加密卡密码", trigger: 'blur' }]
      },
      certTypeOptions: [
        { label: '签名证书', value: 0 },
        { label: '加密证书', value: 1 }
      ],
      deviceInfo: {
        DEVID: '',
        CENTERID: '',
        CENTERIP: '',
        CENTERPORT: ''
      },
      certForm: {
        certType: 0,
        countryCode: 'CN',
        province: '',
        city: '',
        organization: '',
        organizationalUnit: '',
        commonName: '',
        sendMail: ''
      },
      uploadForm: {
        fileList: [],
        backUpFileSize: 0,
        zipForm: new FormData()
      },
      importResult: null
    }
  },
  methods: {
    openCertApply() {
      this.certApplyVisible = true;
    },
    closeCertApply() {
      this.certApplyVisible = false;
    },
    openUploadCert() {
      this.uploadVisible = true;
    },
    closeUploadCert() {
      this.uploadVisible = false;
      this.fileRemove();
    },
    fileRemove() {
      this.uploadForm.fileList = [];
      this.uploadForm.backUpFileSize = 0;
      this.uploadForm.zipForm.delete("file");
    },
    fileChange(file) {
      this.uploadForm.fileList = [];
      let certExtArray = file.name.split(".");
      let ext = certExtArray[certExtArray.length - 1];
      if (ext.size == "0") {
        this.uploadForm.backUpFileSize = 0;
        this.$message.error('选择文件大小不能为0！');
        this.uploadForm.fileList = []
        this.uploadForm.zipForm.delete("file");
        return false
      } else if (!(ext == "zip")) {
        this.$message.error('请上传zip格式文件！');
        this.uploadForm.fileList = []
        this.uploadForm.backUpFileSize = 0;
        this.uploadForm.zipForm.delete("file");
        return false
      } else {
        this.uploadForm.backUpFileSize = 1;
        this.uploadForm.fileList.push(file);
        this.uploadForm.zipForm.append("file", file.raw)
      }
    },
    getDeviceInfo() {
      this.tableData = [];
      this.logging = true;
      this.$http.oamMG.getDeviceInfo().then(res => {
        let code = res.code;
        if (code == 0) {
          this.deviceInfo.DEVID = res.data.DEVID;
          this.deviceInfo.CENTERID = res.data.CENTERID;
          this.deviceInfo.CENTERIP = res.data.CENTERIP;
          this.deviceInfo.CENTERPORT = res.data.CENTERPORT;
        }
      })
    },
    certApply() {
      this.$refs["certForm"].validate(valid => {
        if (valid) {
          this.loginLoading = true;
          this.$http.oamMG.generateDeviceCertApply(this.certForm).then(res => {
            let blob = new Blob([res], {
              type: 'application/force-download'
            });
            let fileName = this.certForm.commonName + '.csr';
            if (window.navigator.msSaveOrOpenBlob) {
              // console.log(2)
              navigator.msSaveBlob(blob, fileName)
            } else {
              // console.log(3)
              var link = document.createElement('a');
              link.href = window.URL.createObjectURL(blob);
              link.download = fileName;
              link.click();
              //释放内存
              window.URL.revokeObjectURL(link.href)
            }
            this.clearCertForm();
            this.certApplyVisible = false;
            this.loginLoading = false;
          })
        }
      })
    },
    importDeviceCert() {
      this.$http.oamMG.importDeviceCert(this.uploadForm.zipForm, function () {
      }).then(res => {
        let code = res.code;
        if (code == 0) {
          this.resultForm = res.data;
          this.closeUploadCert();
          this.$message.success("导入成功!");
        }
      })
    },
    getCryptoCardStatus() {
      this.$http.oamMG.getCryptoCardStatus().then(res => {
        let code = res.code;
        if (code == 0) {
          this.cryptoCardStatus = res.data;
        }
      })
    },
    clearCertForm() {
      this.certForm.certType = 0;
      this.certForm.countryCode = 'CN';
      this.certForm.province = null;
      this.certForm.city = null;
      this.certForm.organization = null;
      this.certForm.organizationalUnit = null;
      this.certForm.commonName = null;
      this.certForm.sendMai = null;
    }

  },
  created() {
    this.getCryptoCardStatus();
    this.getDeviceInfo();
  }
}
</script>

<style>
.init_but {
  width: 200px;
  height: 40px;
  margin-top: 20px;
  margin-bottom: 20px;
  margin-left: 200px;
}
</style>

import axios from 'axios'
import qs from 'qs';
import {getStore, removeStore, clearStore} from '../utils/util'
import {Notification, MessageBox, Message} from 'element-ui'
// import router from '../router'


import domMessage from './messageOnce'  // 引入方法
const messageOnce = new domMessage();


axios.defaults.withCredentials = true;

//axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8'
// 创建axios实例
const service = axios.create({
    // axios中请求配置有baseURL选项，表示请求URL公共部分
    baseURL: 'svs',
    // 超时
    timeout: 1800000,

    withCredentials: true
});


// request拦截器
service.interceptors.request.use(config => {
    //是否需要设置 token
    let token = getStore('token');
    let isHashToken = config["isHashToken"] || false;
    if (token && isHashToken) {
        config.headers['Authorization'] = 'Bearer ' + token // 让每个请求携带自定义token 请根据实际情况自行修改
    }
    // get请求映射params参数
    if (config.method === 'get' && config.params) {
        let url = config.url + '?';
        for (const propName of Object.keys(config.params)) {
            const value = config.params[propName];
            var part = encodeURIComponent(propName) + "=";
            if (value !== null && typeof (value) !== "undefined") {
                if (typeof value === 'object') {
                    for (const key of Object.keys(value)) {
                        let params = propName + '[' + key + ']';
                        var subPart = encodeURIComponent(params) + "=";
                        url += subPart + encodeURIComponent(value[key]) + "&";
                    }
                } else {
                    url += part + encodeURIComponent(value) + "&";
                }
            }
        }
        url = url.slice(0, -1);
        config.params = {};
        config.url = url;
    }
    if (config.headers["Content-Type"] === "application/x-www-form-urlencoded;charset=utf-8") {
        if (typeof config.data == "object") {
            config.data = qs.stringify(config.data);
        }
    }
    return config
}, error => {
    console.log(error);
    // Promise.reject(error)
    return Promise.reject(error)
});


// 响应拦截器
service.interceptors.response.use(res => {
        const config = res.config;
        const errorAlert = config.errorAlert == undefined ? true : config.errorAlert;
        const responseType = config.responseType; //如果是blob
        if (config.complete && typeof config.complete == "function") {
            config.complete(res.data);
        }
        // 未设置状态码则默认成功状态
        let code = res.data.code || 200;
        // 获取错误信息
        const msg = res.data.msg || res.data.message;
        res.data.msg = msg;
        if (responseType == "blob") { //下载文件时，如果session失效时
            let size = res.data.size;
            if (size == 51) {
                // MessageBox.alert('登录状态已过期，您可以继续留在该页面，或者重新登录', '系统提示', {
                MessageBox.alert('登录状态已过期，请重新登录', '系统提示', {
                        confirmButtonText: '重新登录',
                        // cancelButtonText: '取消',
                        showClose: false,
                        type: 'warning'
                    }
                ).then(() => {
                    location.href = '/';
                    clearStore();
                });
                return Promise.reject('error');
            }
        }

        if (code === 200 || code === 0) return res.data;
        if (code === 401) {
            // debugger;
            // console.log(123456)
            // messageOnce.alert({
            //     message: '登录状态已过期，您可以继续留在该页面，或者重新登录！',
            //     title:'系统提示',
            //     confirmButtonText: '重新登录',
            //     customClass: 'my_el-message-box__wrapper',
            //     dangerouslyUseHTMLString: true,
            //     closeOnClickModal: false,
            //     closeOnPressEscape: false,
            //     // showClose:false,
            //     type: 'warning',
            //     callback: (action, instance) => {
            //         console.log(action, instance);
            //         if (action === 'cancel' || action === 'close') {
            //             // window.location.reload();
            //             // let element = document.getElementsByClassName('my_el-message-box__wrapper'); // 根据标签名获取要删除的元素
            //             // console.log(element);
            //             // element[0].style.display =
            //             // element[0].innerHTML = ''; // 通过父节点的innerHTML属性将元素的HTML代码从父节点的HTML代码中移除
            //             // element[0].outerHTML = '';
            //             return;
            //         }
            //         router.push({path: '/'});
            //         window.sessionStorage.clear();
            //         window.localStorage.clear();
            //         // console.log(getStore("interName"));
            //         // if (getStore("interName")) clearInterval(getStore("interName"))
            //         // let end = setInterval(function () { }, 10000);
            //         for (let i = 1; i <= 100000; i++) {
            //             clearInterval(i);
            //         }
            //     }
            // });


            // MessageBox.alert('登录状态已过期，您可以继续留在该页面，或者重新登录', '系统提示', {
            MessageBox.alert('登录状态已过期，请重新登录', '系统提示', {
                    confirmButtonText: '重新登录',
                    // cancelButtonText: '取消',
                    showClose: false,
                    type: 'warning'
                }
            ).then(() => {
                location.href = '/';
                let end = setInterval(function () { }, 10000);
                for (let i = 1; i <= end; i++) {
                    clearInterval(i);
                }
                clearStore();
            })
        } else if (code === 500) {
            if (errorAlert) {
                Message({
                    message: msg,
                    showClose: true,
                    type: 'error',
                    customClass: 'messageIndex',
                    dangerouslyUseHTMLString: true
                })
            }
            // return Promise.reject(new Error(msg));
            return res.data;
        } else if (code === 400) {
            Message({
                message: msg,
                showClose: true,
                type: 'warning',
                customClass: 'messageIndex',
                dangerouslyUseHTMLString: true
            });
            return res.data
        } else {
            if (errorAlert) {
                Notification.error({
                    title: msg
                });
            }
            return Promise.reject(res);
        }
    },
    error => {
        console.log('err' + error);
        const config = error.config;
        const errorAlert = !config.errorAlert ? true : config.errorAlert;
        let {message} = error;
        if (message == "Network Error") {
            message = "后端接口连接异常";
        } else if (message.includes("timeout")) {
            message = "系统接口请求超时";
        } else if (message.includes("Request failed with status code")) {
            message = "系统接口" + message.substr(message.length - 3) + "异常";
        }
        if (errorAlert) {
            Message({
                message: message,
                type: 'error',
                customClass: 'messageIndex',
                duration: 5 * 1000
            })
        }

        return Promise.reject(error);
    }
);

export default service

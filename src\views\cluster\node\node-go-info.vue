<template>
  <el-card>
    <el-form class="demo-ruleForm" label-width="120px" size="mini">
      <div>
        <el-row>
          <span style="display: block;font-weight: bold;margin-bottom: 1.33em">服务详情</span>
        </el-row>
        <el-row :gutter="14">
          <el-col :span="14">
            <el-form-item label="服务端口:" :inline="true">
              {{ goInfo.goPort }}
            </el-form-item>
            <el-form-item label="服务状态:">
              <el-switch @change="changeGoStatus" active-color="#13ce66" inactive-color="#ff4949"
                         v-model="goInfo.goStatus" :disabled="true"></el-switch>
            </el-form-item>
            <el-form-item label="服务策略批次:" :inline="true">
              <span v-if="batchDiff" style="color: red;font-weight:bold">{{ goInfo.goBatch }}</span>
              <span v-else>{{ goInfo.goBatch }}</span>
            </el-form-item>
            <el-form-item label="集群策略批次:" :inline="true">
              {{ nodeBatch }}
            </el-form-item>
            <el-form-item :inline="true">
              <el-button class="comBtn com_send_btn" size="mini" type="primary" style="margin-bottom: 10px"
                         @click="syncBatch" :disabled="!goInfo.goStatus">同步
              </el-button>
              <span style="margin-left: 10px"><span
                style="color: red;font-weight:bold">*</span>服务策略批次与集群批次不一样时，可手动触发策略同步</span>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
  </el-card>
</template>

<script>
// import nodeInfoMg from "@/api/nodeInfoMg";

export default {
  name: "node-go-info.vue",
  data() {
    return {
      goInfo: {
        goPort: 6091,
        goStatus: false,
        goBatch: "-"
      },
      nodeBatch: "-",
      batchDiff: false,
      loading: null
    }
  },
  methods: {
    loading1() {
      let _this = this;
      _this.loading = _this.$loading({
        lock: true,
        text: '加载中，请稍等...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    },
    refreshInfo() {
      let _this = this;
      this.$http.nodeInfoMg.nodeInfo().then(({code, data}) => {
        if (code == 0) {
          _this.goInfo.goPort = data.goPort;
          _this.goInfo.goStatus = data.goStatus === 1;
          _this.goInfo.goBatch = (data.goBatch == null || data.goBatch === '') ? "-" : data.goBatch;
          _this.nodeBatch = (data.nodeBatch == null || data.nodeBatch === '') ? "-" : data.nodeBatch;
        }
        if (_this.nodeBatch != _this.goInfo.goBatch) {
          _this.batchDiff = true;
        }
        _this.loading.close();
      }, err => {
        _this.loading.close();
      });
    },
    changeGoStatus() {
      let _this = this;
      _this.loading1();
      this.$http.nodeInfoMg.changeGoStatus({"status": _this.goInfo.goStatus}).then(res => {
        if (res.code != 0) {
          _this.goInfo.goStatus = !_this.goInfo.goStatus;
        }
        _this.loading.close();
        _this.refreshInfo();
      }, err => {
        _this.loading.close();
      });
    },
    syncBatch() {
      let _this = this;
      _this.loading1();
      this.$http.nodeInfoMg.syncBatch().then(res => {
        if (res.code == 0) {
          _this.$message.success("操作成功")
        }
        _this.loading.close();
        _this.refreshInfo();
      }, err => {
        _this.loading.close();
      });
    }
  },
  created() {
    this.loading1();
    this.refreshInfo();
  }
}
</script>

<style scoped>

</style>

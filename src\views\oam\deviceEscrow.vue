<template>
    <div style="height: 100%">
        <el-button class="comBtn com_send_btn" size="small" @click="creatDevCert" type="primary" style="margin-bottom: 10px">生成设备证书申请</el-button>
        <el-button class="comBtn" size="small" @click="restartDevMg" type="warning" style="margin-bottom: 10px">重启服务</el-button>
        <el-tabs class="comTabs" v-model="activeName" @tab-click="handleClick" type="border-card">
            <el-tab-pane label="自动注册" name="first" style="text-align: center">
                <!--<el-form-item label="一键导入" prop="fileList1">-->
               <div>
                   <!--一键导入-->
                   <el-upload
                           class="upload-demo my_upload"
                           drag
                           action=""
                           accept=".zip"
                           :on-remove="handleRemove"
                           :on-change="handleChange"
                           :limit="1"
                           :auto-upload="false"
                           :file-list="uploadForm.fileList"
                           multiple>
                       <i class="el-icon-upload"></i>
                       <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em> <br/> <span class="el-upload__tip" slot="tip">提示：只能上传zip文件</span></div>
                   </el-upload>
               </div>
                <el-button class="comBtn com_send_btn" size="small" type="primary" @click="importDeviceCert">确 定</el-button>
                <!--</el-form-item>-->
            </el-tab-pane>
            <el-tab-pane label="手动注册" name="second">
                <register-manually-dia ref="registerManuallyDia"></register-manually-dia>
            </el-tab-pane>
        </el-tabs>

        <el-card v-show="activeName=='first'">
          <el-form :model="devForm" ref="devForm" label-width="150px">
            <h4>上级平台信息</h4>
            <el-form-item label="上级管理平台ID：" prop="centerId">
              <el-input size="small" v-model="devForm.centerId" disabled="disabled"/>
            </el-form-item>
            <el-form-item label="上级管理平台IP：" prop="centerIp">
              <el-input size="small" v-model="devForm.centerIp" disabled="disabled"/>
            </el-form-item>
            <el-form-item label="上级管理平台端口：" prop="centerPort">
              <el-input size="small" v-model.number="devForm.centerPort" disabled="disabled"/>
            </el-form-item>
          </el-form>
        </el-card>

        <creat-dev-cert-dia ref="creatDevCertDia"></creat-dev-cert-dia>
    </div>
</template>

<script>
    import creatDevCertDia from './components/creatDevCertDia'
    import registerManuallyDia from './components/registerManuallyDia'
    // import oamMG from "@/api/oamMG";

    export default {
        components: {creatDevCertDia, registerManuallyDia},
        data() {
            return {
                activeName: 'first',
                registerType: '自动注册',
                editForm: {},
                uploadForm: {
                    fileList: [],
                    backUpFileSize: 0,
                    zipForm: new FormData()
                },
                devForm: {
                  centerId: null,
                  centerIp: null,
                  centerPort: null
                }
            }
        },
        methods: {
            creatDevCert() {
                this.$refs['creatDevCertDia'].initDialog()
            },
            handleClick(val) {
                this.uploadForm.fileList = [];
                this.uploadForm.zipForm.delete("file");
                if (val.name === 'second') this.$refs.registerManuallyDia.initDialog();
            },
            handleRemove(file, fileList) {
                this.uploadForm.fileList = [];
                this.uploadForm.backUpFileSize = 0;
                this.uploadForm.zipForm.delete("file");
            },
            handleChange(file, fileList) {
                this.uploadForm.fileList = [];
                let certExtArray = file.name.split(".");
                let ext = certExtArray[certExtArray.length - 1];
                if (ext.size == "0") {
                    this.uploadForm.backUpFileSize = 0;
                    this.$message.error('选择文件大小不能为0！');
                    this.uploadForm.fileList = [];
                    this.uploadForm.zipForm.delete("file");
                    return false
                } else if (!(ext === "zip")) {
                    this.$message.error('请上传zip格式文件！');
                    this.uploadForm.fileList = [];
                    this.uploadForm.backUpFileSize = 0;
                    this.uploadForm.zipForm.delete("file");
                    return false
                } else {
                    this.uploadForm.backUpFileSize = 1;
                    this.uploadForm.fileList.push(file);
                    this.uploadForm.zipForm.append("file", file.raw)
                }
            },
            importDeviceCert() {
                if (!this.uploadForm.zipForm.get("file")) {
                    this.$message.info('请上传文件!');
                    return
                }
                this.$http.oamMG.importDeviceCert(this.uploadForm.zipForm, function () {}).then(({code, data, msg})=> {
                    if (!code) {
                        let arr = [];
                        for(let key in data) {
                            if(!data[key] && data.hasOwnProperty(key)) {
                                arr.push(msg)
                            }
                        }
                        if (arr.length !== 0) {
                            this.$message.warning(arr.join(', ') || '导入失败!');
                        } else {
                            this.$message.success(msg ||"导入成功!");
                        }
                    } else {
                        this.$message.warning(msg || '导入失败!')
                    }
                    this.uploadForm.fileList = [];
                    this.uploadForm.zipForm.delete("file");
                    this.getPlatformMsgFun();
                })
            },
            // 重启签名验签服务
            restartDevMg() {
              this.$confirm('确定要重启服务器吗?', '重启确认', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                this.$http.oamMG.restartDevMg().then(res => {
                  let code = res.code;
                  if (code === 0) {
                    this.$message.success(res.msg);
                  } else {
                    this.$message.warning(res.msg);
                  }
                })
              })
            },
            getPlatformMsgFun() {
                this.$http.oamMG.getDeviceInfo().then(({code, data, msg}) => {
                    // this.deviceInfo = res.data;
                    this.devForm.devId = data.DEVID;
                    this.devForm.centerId = data.CENTERID;
                    this.devForm.centerIp = data.CENTERIP;
                    this.devForm.centerPort = data.CENTERPORT
                })
            },
            // 手动注册
            registerManually() {
                this.$refs['registerManuallyDia'].initDialog()
            }
        },
        created() {
            this.getPlatformMsgFun();
        }
    }
</script>

<style lang="less" scoped>
    /*/deep/ .el-tabs--border-card {*/
        /*!*height: 100%;*!*/
        /*!*min-height: 360px;*!*/
        /*!*height: calc(100% - 40px);*!*/
        /*.el-tabs__content {*/
            /*height: calc(100% - 100px);*/
            /*!*height: 100%;*!*/
            /*!*min-height: 360px !important;*!*/
            /*!*overflow: auto;*!*/
        /*}*/
    /*}*/
    .manually_register {
        text-align: right;

        .tip {
            color: #1887ee;
            margin-right: 10px;
        }
    }

    .el-upload__tip {
        color: #ccc;
    }
    .my_upload {
        display: inline-block;
        vertical-align: top;
        /deep/.el-upload-dragger {
            width: 500px;
            height: 240px;
            .el-icon-upload {
                font-size: 110px;
                margin: 70px 0 45px;
            }
        }
    }
</style>

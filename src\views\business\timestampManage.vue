<template>
    <div class="container">
        <el-card v-show="showSearch" class="box-card" shadow="always" style="margin-bottom: 10px">
            <el-form :inline="true" :show-message="false" label-width="100px" class="user-search comForm" style="text-align: left">
                <el-form-item label="时间戳序列号：">
                    <el-input size="small" v-model="queryParams.sn" clearable style="width: 260px"></el-input>
                </el-form-item>
                <el-form-item style="background-color: transparent;">
                    <el-button class="comBtn com_send_btn" size="small" type="primary" icon="el-icon-search" @click="searchHandle">搜索</el-button>
                    <el-button class="comBtn com_reset_btn" size="small" type="primary" icon="el-icon-refresh" @click="resetHandle">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card class="box-card" shadow="always" style="padding-bottom: 10px">
            <el-button class="comBtn com_send_btn" size="mini" icon="el-icon-plus" @click="backup">备 份</el-button>
            <el-button-group style="float: right; margin-bottom: 10px">
                <el-button size="mini" icon="el-icon-search" @click="showAndSearch"></el-button>
                <el-button size="mini" icon="el-icon-refresh-left" @click="searchHandle"></el-button>
            </el-button-group>
            <div id="appList" style="padding-top: 10px">
                <createTable
                        :tableData="tableData"
                        :tableHeader="tableDataHeader"
                        :isPage="isPage"
                        :pageAttributes="{total: total, currentPage: queryParams.pageNo, pageSize: queryParams.pageSize}"
                        :current-change="currentChange"
                        :sizeChange="sizeChange"
                >
                </createTable>
            </div>
        </el-card>
        <el-dialog title="详情" :visible.sync="detailShow" width="650px" append-to-body :close-on-click-modal="false">
            <el-descriptions class="margin-top" :column="1" border :labelStyle="{'width': '130px'}" >
                <el-descriptions-item label="时间戳序列号">{{detailForm.timestampSn}}</el-descriptions-item>
                <el-descriptions-item label="时间戳签名值">{{detailForm.timestampSignature}}</el-descriptions-item>
                <el-descriptions-item label="时间戳入库时间">{{ detailForm.saveTime }}</el-descriptions-item>
            </el-descriptions>
            <div slot="footer" class="dialog-footer">
                <el-button class="comBtn com_reset_btn" size="small" @click="detailShow = false">关 闭</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    // import bussMg from '../../api/bussMG'
    export default {
        data() {
            let _this = this;
            return {
                loading: null,
                queryParams: {
                    sn: '', // 时间戳序列号
                    pageNo: 1,
                    pageSize: 10,
                },
                total: 0,
                isPage: true,
                showSearch: true,
                detailShow: false, // 详情
                detailForm: {
                    timestampSn: '',
                    timestampSignature: '',
                    saveTime: ''
                }, // 详情
                tableData: [],
                tableDataHeader: [
                    {label: "序号", type: "index", width: "50"},
                    {label: "时间戳序列号", prop: "timestampSn", type: "normal"},
                    {label: "时间戳签名值", prop: "timestampSignature", type: "normal",
                        componet: function (h, props) {
                            return h("span", [
                                h("el-tooltip", {
                                    attrs: {
                                        class: "sfsfdfdfsdfdfdsfdfsd",
                                        effect: "dark",
                                        content: props.row.timestampSignature,
                                        placement: "top",
                                    },
                                    style: {
                                        width: "600px",
                                    }
                                })
                            ]);
                        }
                    },
                    {label: "时间戳入库时间", prop: "saveTime", type: "normal"},
                    {
                        label: "操作",
                        width: "200",
                        type: "operation",
                        tag: [{
                            name: "详情",
                            operType: "edit",
                            tagType: "el-button",
                            attributes: {
                                size: "mini",
                                type: "text",
                                icon: "el-icon-document"
                            },
                            callback: function (row) {
                                _this.viewDetailsHandle(row);
                            }
                        }, {
                            name: "删除",
                            operType: "del",
                            tagType: "el-button",
                            attributes: {
                                size: "mini",
                                type: "text",
                                icon: "el-icon-delete"
                            },
                            callback: function (row) {
                                _this.deleteHandle(row.timestampSn);
                            }
                        }, {
                            name: "销毁",
                            operType: "del",
                            tagType: "el-button",
                            attributes: {
                                size: "mini",
                                type: "text",
                                icon: "el-icon-document-delete"
                            },
                            callback: function (row) {
                                _this.destroyHandle(row.timestampSn);
                            }
                        }]
                    }
                ],
            }
        },
        methods: {
            openLoading() {
                let _this = this;
                _this.loading = _this.$loading({
                    lock: true,
                    text: '备份中，请稍等...',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });
            },
            // 查询
            searchHandle() {
                this.getTimestampListFun()
            },
            // 重置
            resetHandle() {
                this.queryParams.sn = '';
                this.queryParams.pageNo = 1;
                this.queryParams.pageSize = 10;
                this.getTimestampListFun()
            },
            showAndSearch() {
                this.showSearch = !this.showSearch;
            },
            backup() {
                this.openLoading();
                this.$http.bussMg.backupApi(null).then(({code, data, msg}) => {
                    this.loading.close();
                    if (code == 0) {
                        this.$message.success(msg);
                    } else if (code !== 500) {
                      return this.$message.warning(msg)
                    }
                    this.getTimestampListFun()
                }, (err) => {
                  this.loading.close();
                })
            },
            // 获取列表 bussMg
            getTimestampListFun() {
                this.$http.bussMg.timestampList(this.queryParams).then(({code, data, msg, row}) => {
                    if (code !== 0 && code !== 500) return this.$message.warning(msg);
                    this.tableData = data;
                    this.total = row
                })
            },
            // 查看详情
            viewDetailsHandle(row) {
                this.detailForm = row;
                this.detailShow = true;
            },
            // 删除
            deleteHandle(timestampSn) {
                this.$confirm('确定要删除吗?', '删除确认', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.bussMg.deleteApi({sn: timestampSn}).then(({code, data, msg, row}) => {
                        console.log(code, data, msg, row);
                        if (code == 0) {
                            this.$message.success("删除成功!");
                        } else if (code !== 500) {
                          return this.$message.warning(msg)
                        }
                        this.getTimestampListFun()
                    })
                }).catch(() => {});
            },
            // 销毁
            destroyHandle(timestampSn) {
                this.$confirm('确定要销毁吗?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.bussMg.destroyApi({sn: timestampSn}).then(({code, data, msg, row}) => {
                        if (code == 0) {
                            this.$message.success("销毁成功!");
                        } else if (code !== 500) {
                          return this.$message.warning(msg)
                        }
                        this.getTimestampListFun()
                    })
                }).catch(() => {});
            },
            // 分页操作
            sizeChange: function (param) {
                this.queryParams.pageSize = param;
                this.searchHandle();
            },
            currentChange: function (param) {
                this.queryParams.pageNo = param;
                this.searchHandle();
            },
        },
        mounted() {
            this.getTimestampListFun()
        }
    }
</script>
<style lang="less">
    .el-tooltip__popper {
        max-width: 600px;
    }
</style>

<template>
    <div>

        <create-table
                :tableData="tableData"
                :tableHeader="tableDataHeader"
                :isPage="true"
                :pageAttributes="{total: total, currentPage: pageParam.pageNo}"
                :size-change="sizeChange"
                :current-change="currentChange"
                :prev-click="prevClick"
                :next-click="nextClick"
        ></create-table>


        <el-dialog :title="title + '通知模板'" :visible.sync="templateShow" width="500px" @closed="closeDialog" :close-on-click-modal="false">
            <el-form label-width="100px" :model="noticeModelFrom" :rules="rules" ref="noticeModelFrom">
                <el-form-item label="模板名称" prop="modelName">
                    <el-input size="small" v-model="noticeModelFrom.modelName" maxlength="20" readonly placeholder="请输入模板名称"></el-input>
                </el-form-item>
                <el-form-item label="邮件主题" prop="emailSubject">
                    <el-input size="small" v-model="noticeModelFrom.emailSubject" maxlength="50" placeholder="请输入邮件主题"></el-input>
                </el-form-item>
                <el-form-item label="邮件正文" prop="emailContent">
                    <el-input type="textarea" v-model="noticeModelFrom.emailContent" :rows="6" maxlength="200" placeholder="请输入邮件正文"></el-input>
                </el-form-item>
                <!--<el-form-item label="通知人" prop="">-->
                    <!--<el-input size="small" v-model="noticeModelFrom.noticeUser" style="width: 76%" clearable placeholder="请输入通知人"></el-input>-->
                <!--</el-form-item>-->
                <el-form-item label="通知人" v-for="(noticeUserArr, index) in noticeModelFrom.noticeUserArr" :key="noticeUserArr.key"
                              :prop="'noticeUserArr.' + index + '.value'"
                              :rules="[{required: true, message: '通知人不能为空', trigger: 'blur'},
                                        { validator: Email, trigger: 'blur'}
                                        ]"
                              style="margin-bottom: 15px">
                    <el-input size="small" v-model="noticeUserArr.value" style="width: 70%" clearable placeholder="请输入通知人"></el-input>
                    <el-button class="plus_btn" icon="el-icon-plus" size="small" type="primary" @click="addNotifier"></el-button>
                    <el-button class="minus_btn" icon="el-icon-minus" size="small" @click="delNotifier(noticeUserArr)" v-if="index !== 0"></el-button>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button size="small" @click="closeDialog">取消</el-button>
                <el-button size="small" type="primary" :loading="loading" @click="updateNoticeModel">保存</el-button>
            </div>
        </el-dialog>

    </div>
</template>

<script>
    import alarmMG from "@/api/alarmMG";

    export default {
        name: "alarm-template",
        data() {
            let _this = this;
            return {
                title: '编辑',
                tableDataHeader: [
                    {label: '序号', type: "index", width: '100'},
                    {label: '模板名称', prop: 'modelName', type: "normal", width: '200'},
                    {label: '模板主题', prop: 'emailSubject', type: "normal", width: '200'},
                    {label: '通知内容', prop: 'emailContent', type: "normal"},
                    {label: '通知人', prop: 'noticeUser', type: "normal"},
                    {
                        label: "操作",
                        prop: "1",
                        type: "operation",
                        width: "100",
                        tag: [
                            {
                                name: "编辑",
                                operType: "put",
                                tagType: "el-button",
                                attributes: {
                                    size: "mini",
                                    type: "text",
                                    icon: "el-icon-edit"
                                },
                                callback: function (row, opts, event) {
                                    _this.editNoticeModel(row);
                                }
                            }
                        ]
                    }
                ],
                templateShow: false,
                noticeModelFrom: {
                    id: null,
                    modelName: '',
                    emailSubject: '',
                    emailContent: '',
                    noticeUserArr: [
                        {value: ''}
                    ],
                    noticeUser: '',
                    alarmType: '',
                    alarmLevel: ''
                },
                total: 0,
                loading: false,
                pageParam: {
                    pageNo: 1,
                    pageSize: 10
                },
                tableData: [],
                rules: {
                    modelName: [
                        {required: true, message: '请输入模板名称', trigger: 'blur'}
                    ],
                    emailSubject: [
                        {required: true, message: '请输入邮件主题', trigger: 'blur'}
                    ],
                    emailContent: [
                        {required: true, message: '请输入邮件正文', trigger: 'blur'}
                    ],
                    // noticeUser: [
                    //     {required: true, message: '请输入通知人邮箱', trigger: 'blur'}
                    // ],
                }
            }
        },
        computed: {
            Email() {
                return (rule, value, callback) => {
                    // let email = /^(\w+\.?)*\w+@(?:\w+\.)\w+$/;
                    let email = /^[A-Za-z\d]+([-_.][A-Za-z\d]+)*@([A-Za-z\d]+[-.])+[A-Za-z\d]{2,4}$/;
                    if (value == null) {
                        callback()
                    }
                    if (value != '') {
                        if (!email.test(value)) {
                            callback(new Error('请输入正确的邮箱!'))
                        } else {
                            callback()
                        }
                    } else {
                        callback()
                    }
                }
            }
        },
        methods: {
            // 每页显示条数改变
            sizeChange(res) {
                this.pageParam.pageSize = res;
                this.pageNoticeModel()
            },
            // 前往页
            currentChange(res) {
                this.pageParam.pageNo = res;
                this.pageNoticeModel()
            },
            // 上一页
            prevClick(res) {
                this.pageParam.pageNo = res;
                this.pageNoticeModel()
            },
            // 下一页
            nextClick(res) {
                this.pageParam.pageNo = res;
                this.pageNoticeModel()
            },
            closeDialog() {
                this.templateShow = false;
                this.noticeModelFrom.id = null;
                this.noticeModelFrom.modelName = null;
                this.noticeModelFrom.emailSubject = null;
                this.noticeModelFrom.emailContent = null;
                this.noticeModelFrom.noticeUser = null;
                // this.noticeModelFrom.noticeUser = null;
                this.noticeModelFrom.alarmType = null;
                this.noticeModelFrom.alarmLevel = null;
                this.$refs["noticeModelFrom"].clearValidate();
                this.loading = false;
            },
            editNoticeModel(row) {
                this.templateShow = true;
                this.noticeModelFrom.id = row.id;
                this.noticeModelFrom.modelName = row.modelName;
                this.noticeModelFrom.emailSubject = row.emailSubject;
                this.noticeModelFrom.emailContent = row.emailContent;
                // this.noticeModelFrom.noticeUser = row.noticeUser;
                this.noticeModelFrom.alarmType = row.alarmType;
                this.noticeModelFrom.alarmLevel = row.alarmLevel;

                let arr = row.noticeUser.split(",");
                // let str = '1111111111, 4444444';
                // let arr = str.split(", ");
                console.log(arr);
                let tempArr = [];
                arr.map(item => {
                    console.log(item);
                    tempArr.push({
                        value: item
                    })
                });
                console.log(tempArr);
                this.noticeModelFrom.noticeUserArr = tempArr
            },
            addNotifier() {
              if (this.noticeModelFrom.noticeUserArr.length === 5) return this.$message.warning('通知人数量不能超过5人!');
                this.noticeModelFrom.noticeUserArr.push({
                    value: '',
                    key: Date.now()
                })
            },
            delNotifier(item) {
                let index = this.noticeModelFrom.noticeUserArr.indexOf(item);
                if (index !== -1) {
                    this.noticeModelFrom.noticeUserArr.splice(index, 1)
                }
            },

            updateNoticeModel() {
                this.$refs["noticeModelFrom"].validate((valid) => {
                    if (valid) {
                        this.loading = true;
                        let arr = [];
                        this.noticeModelFrom.noticeUserArr.map(item => {
                            arr.push(item.value);
                        });

                        this.noticeModelFrom.noticeUser = arr.join(",");
                        alarmMG.editNoticeModel(this.noticeModelFrom).then(res => {
                            let code = res.code;
                            if (code === 400) {
                                this.loading = false;
                                this.$message.warning(res.msg)
                            };
                            if (code == 0) {
                                this.$message.success("编辑成功");
                                this.closeDialog();
                                this.pageNoticeModel();
                            }
                        }).catch(() => {
                            this.loading = false;
                        })
                    }
                })
            },
            pageNoticeModel() {
                alarmMG.pageNoticeModel(this.pageParam).then(res => {
                    let code = res.code;
                    if (code == 0) {
                        this.tableData = res.data;
                        this.total = res.row;
                    } else {
                        this.$message.warning(res.msg || "获取模板异常")
                    }
                })
            }
        }
    }
</script>

<style scoped>

</style>

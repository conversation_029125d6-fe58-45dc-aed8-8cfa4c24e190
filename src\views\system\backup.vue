<template>
    <div class="container">
        <el-card class="box-card" shadow="always" style="padding-bottom: 10px">
            <el-form label-width="100px">
                <el-row>
                    <el-col :span="14" style="text-align: left">
                        <el-button class="comBtn com_del_btn" size="mini" type="danger" @click="batchDel">删除</el-button>
                        <el-button class="comBtn com_send_btn" size="mini" type="success"
                            @click="backUpNowWindow = true">立即备份</el-button>
                        <el-button class="comBtn com_send_btn" size="mini" type="success"
                            @click="uploadBackUpWindow = true">上传备份</el-button>
                        <el-button class="comBtn com_reset_btn" size="mini" type="info"
                            @click="logInfoOpen = true">查看任务详情</el-button>
                    </el-col>
                    <el-col :span="10">
                        <div style="text-align: right">
                            <el-button-group>
                                <el-button size="mini" icon="el-icon-refresh-left" @click="getData"></el-button>
                            </el-button-group>
                        </div>
                    </el-col>
                </el-row>
            </el-form>


            <div id="backup" style="padding-top: 10px">
                <createTable :tableData="tableData" :tableHeader="tableDataHeader" :isPage="isPage"
                    :pageAttributes="pageAttr" :current-change="currentChange" :sizeChange="sizeChange"
                    :selectionChange="handleSelectionChange">
                </createTable>
            </div>

            <!-- 上传备份 -->
            <el-dialog title="上传备份" :visible.sync="uploadBackUpWindow" width="400px" ref="uploadForm"
                @close="closeBackUpDia" centerappend-to-body :close-on-click-modal="false" :append-to-body="true">
                <el-form label-width="120px" ref="uploadForm" :model="uploadForm" :rules="urule" size="small">
                    <el-form-item label="备份上传：" prop="backUpFileSize">
                        <el-upload class="upload-demo" action="#" :on-remove="fileRemove" :on-change="fileChange"
                            :limit="1" :auto-upload="false" :headers="authHeader" :file-list="uploadForm.fileList">
                            <el-input v-show="false" v-model="uploadForm.backUpFileSize"></el-input>
                            <el-button class="comBtnDef com_send_btn" size="small" type="primary">点击上传</el-button>
                        </el-upload>

                    </el-form-item>
                    <el-form-item label="备份密码：" prop="password">
                        <el-input show-password v-model="uploadForm.password" />
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button class="comBtn com_reset_btn" size="small" @click="closeBackUpDia">取 消</el-button>
                    <el-button class="comBtn com_send_btn" size="small" type="primary" :loading="uploadBackUpLoading" @click="uploadBackUp">确 定</el-button>
                </div>
            </el-dialog>

            <!-- 立即备份 -->
            <el-dialog title="立即备份" :visible.sync="backUpNowWindow" width="400px" @close="closeBackUpNowDia"
                :append-to-body="true">
                <el-form label-width="130px" ref="backUpForm" :model="backUpForm" :rules="brule" size="small">
                    <el-form-item label="请选择备份内容：" v-show="false">
                        <el-select v-model="backUpForm.backupContentType">
                            <el-option label="全部" :value="1"></el-option>
                            <!--
                              <el-option label="网络备份" :value="2"></el-option>
                            -->
                            <el-option label="数据备份" :value="3"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="设置备份密码：" prop="password">
                        <el-input show-password v-model="backUpForm.password" />
                    </el-form-item>
                    <el-form-item label="确认密码：" prop="confirmPassword">
                        <el-input show-password v-model="backUpForm.confirmPassword" />
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button class="comBtn com_reset_btn" size="small" @click="closeBackUpNowDia">取 消</el-button>
                    <el-button class="comBtn com_send_btn" size="small" @click="backUpNow"
                        :loading="backUpLoading">确定</el-button>
                </div>
            </el-dialog>


            <!-- 还原 -->
            <el-dialog title="还原" :visible.sync="reduceOpen" width="400px"
                @close="Object.assign(form, $options.data().form); lastIndex = -1" :append-to-body="true">
                <el-form label-width="130px" ref="form" :model="form" :rules="rrule">
                    <el-form-item label="还原密码：" prop="password">
                        <el-input show-password v-model="form.password" />
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button class="comBtn com_send_btn" @click="reduceById" :loading="reduceLoading">确 定</el-button>
                    <el-button class="comBtn com_reset_btn" @click="reduceOpen = false">取 消</el-button>
                </div>
            </el-dialog>


            <!-- 任务详情 -->
            <el-dialog title="任务详情" :visible.sync="logInfoOpen" width="800px" @close="websock.close();"
                @opened="messageInfo" :append-to-body="true">
                <div style="height: 500px;">
                    <el-scrollbar style="height:100%">
                        <ul id="logInfo">
                        </ul>
                    </el-scrollbar>
                </div>
                <div slot="footer" class="dialog-footer">
                    <el-button class="comBtn com_reset_btn" @click="logInfoOpen = false">取 消</el-button>
                </div>
            </el-dialog>
        </el-card>
    </div>
</template>

<script>
// import backUpMG from "@/api/backUpMG";
import { getStore, getWebSocketPre, url_pre } from "@/utils/util";
// import bussMG from "@/api/bussMG";
import $ from "jquery";
import { Message, MessageBox } from "element-ui";
// import systemMG from "@/api/systemMG";
import shellMG from "@/api/shellMG";
// import configMG from "@/api/configMG";

export default {
    name: "backup",
    data() {
        const checkPass2 = (rule, value, callback) => {
            if (value !== this.backUpForm.password) {
                callback(new Error('两次输入密码不一致!'));
            } else {
                callback();
            }
        };
        return {
            urule: {
                "backUpFileSize": [
                    { required: true, message: "请上传备份文件", trigger: "change" },
                ],
                "password": [
                    { required: true, message: "请输入上传文件密码", trigger: "blur" },
                ]
            },
            brule: {
                password: [
                    { required: true, message: "请输入备份文件密码", trigger: "blur" },
                    { min: 8, max: 16, message: "请输入备份文件密码8-16字符", trigger: "blur" },
                    // { validator: checkPass2, trigger: 'blur' }
                ],
                confirmPassword: [
                    { required: true, message: "请输入确认密码", trigger: "blur" },
                    { validator: checkPass2, trigger: 'blur' }
                ]
            },
            rrule: {
                "password": [
                    { required: true, message: "请输入密码", trigger: "blur" },
                ]
            },
            wsIP: "",
            reduceOpen: false,
            backUpLoading: false,
            reduceLoading: false,
            logInfoOpen: false,
            authHeader: { token: "Bearer " + getStore('token') },
            uploadBackUpWindow: false,
            uploadBackUpLoading: false,
            backUpNowWindow: false,
            wsServer: "",
            interval: {},
            tableData: [],
            tableHeader: [],
            isPage: true,
            lastIndex: -1,
            pageAttr: {},
            queryParams: {
                pageNo: 1,
                pageSize: 10
            },
            form: {
                delIds: "",
                id: "",
                password: "",
            },
            uploadForm: {
                fileList: [],
                password: "",
                backUpFileSize: "",
                file: "",
                isBackUp: false,
                uploadForm: new FormData()
            },
            backUpForm: {
                backupContentType: 3,
                password: "",
                confirmPassword: "",
            }
        }
    },
    computed: {
        uploadUrl() {
            return url_pre + "/backup/upload";
        }
    },
    methods: {
        messageInfo() {
            //
            // systemMG.getWebSocketPathgetWsIpNoSid().then((res) => {
            //   const code = res.code;
            //   if (code == 0) {
            //     this.wsIP = res.data + '/100';
            //     this.initWebSocket();
            //   } else {
            //     this.$message.error(res.msg);
            //   }
            // });
            this.initWebSocket();
        },
        getBackUpInfo() {
            this.$http.backUpMG.getBackUpInfo(-1, function (data) {
            }).then(res => {
                let data = res.data;
                let datas = data.backUpInfos;
                let tempStr = "";
                for (let c = 0; c < datas.length; c++) {
                    console.log(datas[c].type);
                    // tempStr += "<li style='text-align: left'><span>" + datas[c].dataStr + "   " + datas[c].message + "<span></li>";
                    if (datas[c].type === 1) tempStr += "<li style='text-align: left'><span>" + datas[c].dataStr + "   " + datas[c].message + "<span></li>";
                    if (datas[c].type === 2) tempStr += "<li style='text-align: left'><span>" + datas[c].dataStr + "   " + datas[c].message + "<span></li>";
                    if (datas[c].type === 3) tempStr += "<li style='text-align: left;color: red'><span>" + datas[c].dataStr + "   " + datas[c].message + "<span></li>";
                }
                $("#logInfo").append(tempStr);
            });
        },
        getData() {
            let _this = this;
            _this.tableData = [];
            this.$http.backUpMG.list(this.queryParams).then(res => {
                _this.tableData = res.data;
                _this.isPage = res.row > 0 ? true : false;
                _this.pageAttr.total = res.row;
            });
        },
        batchDel() {
            let _this = this;
            let delArray = _this.form.delIds;
            if (delArray === "") {
                this.$alert("请先选择删除项!", "提示", { type: 'warning' });
            } else {
                this.$confirm('确定删除数据吗?', '删除确认', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.backUpMG.batchDel(_this.form.delIds).then(() => _this.getData());
                });
            }
        },
        del(id) {
            let _this = this;
            this.$confirm('确定删除数据吗?', '删除确认', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$http.backUpMG.del(id).then(res => {
                    _this.getData();
                });
            });
        },
        reduceById() {
            this.$refs["form"].validate(val => {
                if (val) {
                    this.reduceLoading = true;
                    // this.logInfoOpen = true;
                    let _this = this;
                    this.$http.backUpMG.reduce(this.form, function () {
                        _this.reduceLoading = false;
                        // 
                        // _this.initWebSocket();
                    }).then(res => {
                        let code = res.code;
                        if (code == 0) {
                            _this.logInfoOpen = true;
                            this.$alert("备份还原成功!", "提示", { type: 'warning' });
                        }
                        _this.reduceOpen = false;
                    });
                }
            })
        },
        backUpNow() {
            let _this = this;
            this.$refs["backUpForm"].validate((val) => {
                if (val) {
                    this.backUpLoading = true;
                    this.$http.backUpMG.backup(this.backUpForm, function () {
                        _this.backUpLoading = false;
                        _this.backUpNowWindow = false;

                    }).then(({ code, msg }) => {
                        // if(code === 400) return this.$message.warning(msg);
                        if (code !== 0) return;
                        _this.logInfoOpen = true;
                        // this.initWebSocket();
                        _this.getData();
                    });
                }
            })

        },
        uploadBackUp() {
            this.$refs["uploadForm"].validate((val) => {
                if (val) {
                    this.uploadBackUpLoading = true;
                    this.uploadForm.uploadForm.delete("pwd");
                    this.uploadForm.uploadForm.append("pwd", this.uploadForm.password);
                    let _this = this;
                    this.$http.backUpMG.upload(this.uploadForm.uploadForm, function () {
                        _this.uploadBackUpWindow = false;
                    }).then(res => {
                        _this.uploadBackUpLoading = false;
                        _this.uploadBackUpWindow = false;
                        _this.getData();
                    }).catch(() => {
                        _this.uploadBackUpLoading = false;
                    });
                }
            })
        },
        fileRemove() {
            this.uploadForm.fileList = [];
            this.uploadForm.backUpFileSize = 0;
            this.uploadForm.uploadForm.delete("file");
        },
        fileChange(file) {
            this.uploadForm.fileList = [];
            let certExtArray = file.name.split(".");
            let ext = certExtArray[certExtArray.length - 1];
            if (ext.size == "0") {
                this.uploadForm.backUpFileSize = 0;
                this.$message.error('选择文件大小不能为0！')
                this.uploadForm.fileList = []
                this.uploadForm.uploadForm.delete("file");
                return false
            } else if (!(ext == "img" || ext == "IMG")) {
                this.$message.error('请上传img格式文件！')
                this.uploadForm.fileList = []
                this.uploadForm.backUpFileSize = 0;
                this.uploadForm.uploadForm.delete("file");
                return false
            } else {
                this.uploadForm.backUpFileSize = 1
                this.uploadForm.fileList.push(file)
                this.uploadForm.uploadForm.append("file", file.raw)
            }
        },
        handleSelectionChange(val) {
            let delA = "";
            let length = val.length;
            val.forEach((item, index) => {
                delA += item.id + ",";
            });
            if (length > 0) {
                delA = delA.substr(0, delA.length - 1);
            }
            this.form.delIds = delA;
        },
        currentChange(val) {
            this.queryParams.pageNo = val;
            this.getData();
        },
        sizeChange(val) {
            this.queryParams.pageSize = val;
            this.getData();
        },
        downLoad(id) {
            var link = document.createElement('a')
            link.href = url_pre + "/backup/download?id=" + id + "&token=" + getStore('token');
            link.click();
            link.remove();
        },
        initWebSocket() { //初始化websocket
            let _this = this;
            this.$nextTick(() => {
                if (!this.websock) {
                    this.websock = new WebSocket(_this.wsServer);
                    this.websock.onmessage = this.websocketonmessage;
                    this.websock.onopen = this.websocketonopen;
                    this.websock.onerror = this.websocketonerror;
                    this.websock.onclose = this.websocketclose;
                    $("#logInfo").html("")
                } else {
                    if (this.websock.readyState != this.websock.CONNECTING) {
                        this.websock = new WebSocket(_this.wsServer);
                        this.websock.onmessage = this.websocketonmessage;
                        this.websock.onopen = this.websocketonopen;
                        this.websock.onerror = this.websocketonerror;
                        this.websock.onclose = this.websocketclose;
                        $("#logInfo").html("")
                    }
                }
            });
        },
        websocketonopen(e, c, d) { //连接建立之后执行send方法发送数据
            this.getBackUpInfo();
            console.log("open session")
        },
        websocketonerror() {//连接建立失败重连
            this.initWebSocket();

        },
        websocketonmessage(e, l, d) { //数据接收
            try {
                let data = JSON.parse(e.data);
                let tempStr = "";
                console.log("get data!" + e.data);
                if (data.type == -1) {
                    tempStr = "";
                    $("#logInfo").html("");
                } else {
                    tempStr += "<li style='text-align: left'><span>" + data.dataStr + "   " + data.message + "<span></li>";
                }
                $("#logInfo").append(tempStr);
            } catch (e) {
                alert(e);
            }
        },
        websocketsend(Data) {//数据发送
            this.websock.send(Data);
        },
        websocketclose(e) {  //关闭
            console.log('断开连接', e);
        },

        closeBackUpDia() {
            this.uploadBackUpWindow = false;
            this.uploadBackUpLoading = false;
            Object.assign(this.uploadForm, this.$options.data().uploadForm);
            this.$nextTick(() => {
                this.$refs["uploadForm"].clearValidate();
            })
        },
        closeBackUpNowDia() {
            this.backUpNowWindow = false;
            Object.assign(this.backUpForm, this.$options.data().backUpForm);
            this.$nextTick(() => {
                this.$refs["backUpForm"].clearValidate();
            })
        }
    },
    created() {
        let _this = this;
        this.getData();
        this.wsServer = getWebSocketPre() + "/backWebsocket/100";
        this.tableDataHeader = [
            {
                type: "select",
                label: "全选",
                width: "100"
            },
            {
                type: "index",
                label: "序号",
                width: "100"
            },
            {
                type: "normal",
                label: "备份信息",
                prop: "fileName"
            },
            {
                type: "text_formatter",
                label: "备份状态",
                prop: "backupStatus",
                width: "100",
                formatter: function (value, row) {
                    if (value == "0") {
                        return "失败";
                    } else {
                        return "成功";
                    }
                }
            },
            // {
            //   type: "text_formatter",
            //   label: "备份类型",
            //   width: "100",
            //   prop: "backupContentType",
            //   formatter: function (value, row) {
            //     if (value == "1") {
            //       return "全部";
            //     } else if (value == "2") {
            //       return "备份网络";
            //     } else {
            //       return "备份数据";
            //     }
            //   }
            // },
            {
                type: "operation",
                label: "操作",
                tag: [
                    {
                        name: "还原",
                        operType: "update",
                        tagType: "el-button",
                        attributes: {
                            size: "mini",
                            type: "text",
                            icon: "el-icon-refresh-right"
                        },
                        callback: function (row) {
                            _this.reduceOpen = true;
                            _this.form.id = row.id;
                        }
                    },
                    {
                        name: "下载",
                        operType: "update",
                        tagType: "el-button",
                        attributes: {
                            size: "mini",
                            type: "text",
                            icon: "el-dropdown-item"
                        },
                        callback: function (row) {
                            _this.downLoad(row.id);
                        }
                    },
                    {
                        name: "删除",
                        operType: "update",
                        tagType: "el-button",
                        attributes: {
                            size: "mini",
                            type: "text",
                            icon: "el-icon-delete"
                        },
                        callback: function (row) {
                            _this.del(row.id);
                        }
                    }
                ]
            }
        ]
    }
}
</script>
<!--<style lang="less" scoped>-->
<!--@import "../../assets/css/commons.less";-->
<!--</style>-->
<style lang="less" scoped>
/deep/.el-message-box {
    z-index: 2099;
    /* 这个值需要高于遮罩层的z-index值 */
}

.el-checkbox {
    text-align: left;
}

/deep/ .el-pagination__total {
    float: left;
}

.upload-demo {
    text-align: left;
}

.checkleft {
    text-align: left;
}

body .el-scrollbar__wrap {
    overflow-x: hidden;
}
</style>

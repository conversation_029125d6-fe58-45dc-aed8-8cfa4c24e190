import {reqheaders, req, reqParams, noAuthreq, reqParamNo<PERSON>son, uploadReq, reqCommon} from './axiosFun';
import API from "@/api/apiuri"
let configApi = API.configApi;
export default {
  list(param) {
    return reqParamNo<PERSON><PERSON>("POST", configApi.list, param);
  },
  del(id) {
    return reqParamNo<PERSON><PERSON>("GET", configApi.deleteById + "/" + id, {});
  },
  queryById(id) {
    return reqParamNo<PERSON>son("GET", configApi.queryById + "/" + id, {});
  },
  edit(param) {
    return reqParamNo<PERSON>son("POST", configApi.edit, param);
  },
  add(param) {
    return reqParamNo<PERSON>son("POST", configApi.add, param);
  },
  getWebSocketAddress() {
    return reqParamNo<PERSON><PERSON>("GET", configApi.getWebSocketAddress, {});
  },
}





import {CryptoJS} from "@/sm3/core";
import {SM3Digest} from "@/sm3/sm3";
const url_pre = "/svs";
/**
 * 时间戳
 * @param {*} timestamp  时间戳
 */
const timestampToTime = (timestamp) => {
  let date = new Date(timestamp); //时间戳为10位需*1000，时间戳为13位的话不需乘1000
  let Y = date.getFullYear() + '-';
  let M =
    (date.getMonth() + 1 < 10 ?
      '0' + (date.getMonth() + 1) :
      date.getMonth() + 1) + '-';
  let D =
    (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' ';
  let h =
    (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
  let m =
    (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) +
    ':';
  let s =
    date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
  return Y + M + D + h + m + s
};
/**
 * 存储localStorage
 */
const setStore = (name, content) => {
  if (!name) return;
  if (typeof content !== 'string') {
    content = JSON.stringify(content);
  }
  window.localStorage.setItem(name, content);
};

/**
 * 获取localStorage
 */
const getStore = name => {
  if (!name) return;
  return window.localStorage.getItem(name);
};

/**
 * 删除localStorage
 */
const removeStore = name => {
  if (!name) return;
  window.localStorage.removeItem(name);
};

/**
 * 清空localStorage
 */
const clearStore = () => {
  window.localStorage.clear();
};

/**
 * 设置cookie
 **/
function setCookie(name, value, day) {
  let date = new Date();
  date.setDate(date.getDate() + day);
  document.cookie = name + '=' + value + ';expires=' + date;
}

/**
 * 获取cookie
 **/
function getCookie(name) {
  let reg = RegExp(name + '=([^;]+)');
  let arr = document.cookie.match(reg);
  if (arr) {
    return arr[1];
  } else {
    return '';
  }
}

/**
 * 删除cookie
 **/
function delCookie(name) {
  setCookie(name, null, -1);
}

const authHeader =   {token: "Bearer "+getStore('token')};


function doSM3(msg) {
  var msgData = CryptoJS.enc.Utf8.parse(msg);
  var sm3keycur = new SM3Digest();
  msgData = sm3keycur.GetWords(msgData.toString());
  sm3keycur.BlockUpdate(msgData, 0, msgData.length);
  var c3 = new Array(32);
  sm3keycur.DoFinal(c3, 0);
  var hashHex = sm3keycur.GetHex(c3).toString();
  return hashHex.toUpperCase();
}


function dateFormat(fmt, date) {
  let ret;
  const opt = {
    "Y+": date.getFullYear().toString(),        // 年
    "m+": (date.getMonth() + 1).toString(),     // 月
    "d+": date.getDate().toString(),            // 日
    "H+": date.getHours().toString(),           // 时
    "M+": date.getMinutes().toString(),         // 分
    "S+": date.getSeconds().toString()          // 秒
    // 有其他格式化字符需求可以继续添加，必须转化成字符串
  };
  for (let k in opt) {
    ret = new RegExp("(" + k + ")").exec(fmt);
    if (ret) {
      fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, "0")))
    }
  }
  return fmt;
}


function getWebSocketPre(){
  let dic = {
    "http:":"ws://",
    "https:":"wss://",
    "HTTP:":"ws://",
    "HTTPS:":"wss://"
  };
  let protocol = window.location.protocol; /* 获取协议 */
  // let host = window.location.host;
  let host = process.env.NODE_ENV === 'production' ? window.location.host : '**********:8080';
  return dic[protocol]+host+"/wbs";
}

function getExtendName(fileName){
  let lastIndex = fileName.lastIndexOf(".");
  let certExt = fileName.substring(lastIndex+1);
  return certExt;
}

/**
 * 导出
 **/
export {
  timestampToTime,
  setStore,
  getStore,
  removeStore,
  setCookie,
  getCookie,
  delCookie,
  doSM3,
  dateFormat,
  url_pre,
  getWebSocketPre,
  getExtendName,
  clearStore
}

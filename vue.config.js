'use strict'
const path = require('path')
const FileManagerPlugin = require('filemanager-webpack-plugin')
const port = 2024

function resolve(dir) {
  return path.join(__dirname, dir)
}

// All configuration item explanations can be find in https://cli.vuejs.org/config/
module.exports = {
  lintOnSave: 'warning',
  /**
   * 判断是开发环境还是生产环境
   * 配置打包后静态资源访问路径 /cms/
   * */
  productionSourceMap: false,
  publicPath: process.env.NODE_ENV === 'production' ? '/' : '/',
  // assetsDir: 'assets',
  devServer: {
    compress: true,
    port: port,
    historyApiFallback: false,
    // overlay: {
    //   warnings: true,
    //   errors: true
    // },
    proxy: {
      [process.env.VUE_APP_BASE_API]: {
        target: `http://localhost:${port}/mock`,
        pathRewrite: {
          ['^' + process.env.VUE_APP_BASE_API]: ''
        }
      },
      [process.env.VUE_APP_LOCALSERVER]: {
        target: 'http://localhost:8082',
        pathRewrite: {
          ['^' + process.env.VUE_APP_LOCALSERVER]: ''
        },
        ws: true
      },
      // '/tests': {
      //   target: `http://**************:8089`,
      //   pathRewrite: {
      //     ['^' + '/tests']: ''
      //   }
      // },
      // '/zhangyanbin': {
      //   target: `http://***************:8082`,
      //   pathRewrite: {
      //     ['^' + '/zhangyanbin']: ''
      //   }
      // },
      // '/caopanpan': {
      //   target: `http://***************:8081`,
      //   pathRewrite: {
      //     ['^' + '/caopanpan']: ''
      //   }
      // },
      // '/licenseServer': {
      //   target: `http://**********:10245`,
      //   pathRewrite: {
      //     ['^' + '/licenseServer']: ''
      //   }
      // },http://***********/#/administrator-manage
      '/svs': {
        target: `http://***********:8080`,
        // target: `http://***********:8080`,
        // pathRewrite: {
        //   ['^' + '/svs']: ''
        // }
      },
    },
    // after: require('./mock/mock-server.js')
  },
  configureWebpack: {
    // if (process.env.NODE_ENV === 'production') {
    //   // config.devtool = 'none'
    //   // config.devtool = 'nosources-source-map'
    // }
    performance: {
      hints: 'warning',
      // 入口起点的最大体积
      maxEntrypointSize: **********,
      // 生成文件的最大体积
      maxAssetSize: **********,
      // 只给出 js 文件的性能提示
      assetFilter: function (assetFilename) {
        return assetFilename.endsWith('.js') || assetFilename.endsWith('.css');
      }
    }
  },
  chainWebpack: config => {
    config.plugins.delete('preload') // TODO: need test
    config.plugins.delete('prefetch') // TODO: need test
    config.module.rules
      .delete('eslint')
      .end()
    config.resolve.alias
      .set('@', resolve('src'))
      .set('@less', resolve('src/styles'))
      .set('@image', resolve('src/assets/image'))
      .set('@components', resolve('src/components'))
      .set('@views', resolve('src/views'))
      .set('@assets', resolve('src/assets'))
      .end()
    config.resolve.modules
      .prepend(path.resolve('node_modules'))
      .end()
    config.module
      .rule('svg')
      .exclude.add(resolve('src/icons'))
      .end()
    config.module
      .rule('svgs')
      .test(/\.svg$/)
      .include.add(resolve('src/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()
    // copy build assets to static
    // config
    //   .plugin('copy-to-assets')
    //   .use(FileManagerPlugin, [{
    //     onEnd: {
    //       copy: [
    //         { source: './dist', destination: resolve('../resources/static') },
    //         { source: './dist/index.html', destination: resolve('../resources/templates') },
    //         { source: './dist/favicon.ico', destination: resolve('../resources/templates') },
    //         { source: './dist/static/*', destination: resolve('../resources/static') }
    //       ]
    //     }
    //   }])
    //   .end()
  }
}

import {reqheaders, req, reqParams, noAuthreq, reqParamNoJson, uploadReq, reqCommon, reqFormData} from './axiosFun';
import API from "@/api/apiuri"
import {getStore} from "@/utils/util";
// import {url} from "../../../../ucmp_vue/ucmpvue/vueproject/src/api/commonApi";

let bussApi = API.bussApi;
const addApi = (params) => { return req("post", `/business/ca/add`, params) };
const updateApi = (params) => { return req("post", `/business/ca/update`, params) };
const configCrlApi = (params) => { return reqFormData("post", `/business/ca/configCrl`, params) };
// const certListApi = (params) => { return req("post", url + `/svs/business/ca/analysusCerts`, params) };

export default {
  addApi,
  updateApi,
  configCrlApi,
  //------------------------------ca相关操作-----------------------------------------
  caList(param) {
    // return reqParamNoJson("post", bussApi.caList, param);
    return reqParamNoJson("post", bussApi.caList, param);
  },
  add(param) {
    return req("post", bussApi.caAdd, param);
  },
  del(id) {
    return reqParamNoJson("get", bussApi.caDel + id);
  },
  delBatch(ids) {
    return reqParamNoJson("POST", bussApi.cabatchDel, {ids: ids});
  },
  caUpdate(param) {
    return reqParamNoJson("POST", bussApi.caUpdate, param);
  },
  queryCaById(id) {
    return reqParamNoJson("GET", bussApi.baseCa + "/" + id, {});
  },
  caNameCheck(name, id) {
    return reqParamNoJson("POST", bussApi.caNameCheck, {name: name, id: id});
  },
  //-------------------证书链相关操作-------------------------------------------------
  chainList(param) {
    return reqParamNoJson("POST", bussApi.chainList, param);
  },
  queryCertChain(id) {
    return reqParamNoJson("GET", bussApi.queryCertChainById + id, {});
  },
  chainEdit(param) {
    return reqParamNoJson("POST", bussApi.chainEdit, param);
  },
  chainDownLoad(id) {
    // return reqheaders("post", "/business/app/del", param, {"Content-Type": "application/x-www-form-urlencoded;charset=utf-8"});
    let option = {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
        "token": "Bearer " + getStore('token')
      },
      responseType: 'blob'
    }
    return reqCommon("post", bussApi.chainDownLoad + "?id=" + id, {id: id}, option)
  },
  chainDelete(id) {
    // return reqheaders("post", "/business/app/del", param, {"Content-Type": "application/x-www-form-urlencoded;charset=utf-8"});
    return reqParamNoJson("GET", bussApi.chainDelete + id, {});
  },
  configCertChina(param) {
    return uploadReq("POST", bussApi.configCertChain, param);
  },

  //CRL配置相关方法
  queryCrlConfigByCaId(caId) {
    return reqParamNoJson("POST", bussApi.queryCrlConfigByCaId, {caId: caId});
  },
  configCrl(param) {
    return reqParamNoJson("POST", bussApi.crlConfig, param);
  },
  deleteFile(caId, type) {
    return reqParamNoJson("POST", bussApi.fileDelete, {caId: caId, type: type});
  },
  testConnetcion(address, callBack) {
    return reqParamNoJson("POST", bussApi.testConnetcion, {address: address}, callBack);
  },
  testConnetcionNoAlert(address, callBack) {
    return reqParamNoJson("POST", bussApi.testConnetcion, {address: address}, callBack,false);
  },
  //CRL缓存相关任务
  queryCertSn(caId, certSn) {
    return reqParamNoJson("GET", bussApi.queryCertSn + caId + "/" + certSn);
  }

}





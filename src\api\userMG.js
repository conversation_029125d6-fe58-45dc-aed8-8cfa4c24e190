import API from './apiuri';
import axios from "axios";
import {req, reqParams, noAuthreq, fileReq, reqfrom, reqParamNoJson, reqFrom, reqCommonForm} from './axiosFun';
import {getStore} from "@/utils/util";

const userApi = API.userApi;
const strategyApi = API.strategyApi;
const roleApi = API.roleApi;
const initApi = API.initApi;

export default {

  /**
   * 初始化
   */
  //初始化管理员
  initUser(params) {
    return reqfrom("post", initApi.initAccount, params)
  },//初始化管理员
  downloadApi(params) {
    let option = {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
        "token": "Bearer "+getStore('token')
      },
      responseType: 'blob'
    };
    return reqCommonForm("post", '/account/download/cert', params, option)
  },
  //获取系统初始化状态
  statusInit() {
    return noAuthreq("get", initApi.initStatus)
  },
  //查询初始化信息
  queryInitInfo() {
    return noAuthreq("get", initApi.queryInit)
  },
  //设置初始化状态
  setStatus(info) {
    return noAuthreq("put", initApi.setStatus, info)
  },
  //初始化网卡
  initEth(eth) {
    return noAuthreq("post", initApi.initEth, eth)
  },
  //删除管理员
  deleteAccount(id) {
    return noAuthreq("delete", initApi.deleteAccount, id)
  },

  /**
   * 管理员管理
   */
  //获取随机数
  randomNumber() {
    return req("post", userApi.random)
  },
  //证书及双因子登录
  loginCert(params) {
    return noAuthreq("post", userApi.loginCert, params)
  },
  //证书及双因子登录
  login(params) {
    return noAuthreq("post", userApi.login, params)
  },
  //退出登录
  loginOut(user) {
    return req("post", userApi.loginOut, user)
  },
  analysisCert(params) {
    return req("post", userApi.analysisCert, params)
  },
  //添加用户
  addUser(user) {
    return reqfrom("post", userApi.addUser, user)
  },
  //编辑用户
  updateUser(user) {
    return req("put", userApi.updateUser, user)
  },
  //删除用户
  deleteUser(userId) {
    return req("delete", userApi.deleteUser + '/' + userId, "")
  },
  //查询管理员列表
  listUser() {
    return req("get", userApi.listUser, "")
  },
  //分页显示管理员
  pageUser(page) {
    return reqParams("get", userApi.pageUser, page)
  },
  //上传证书
  uploadCert(params) {
    return reqfrom("post", userApi.uploadCert, params)
  },
  //查看用户下的证书
  searchCert(id) {
    return req("get", userApi.searchCert + '/' + id)
  },
  //修改密码
  rePassword(id, params) {
    return reqParams("put", userApi.rePassword + '/' + id, params)
  },
  //重置密码
  resetPassword(id) {
    return req("post", userApi.resetPassword + '/' + id)
  },
  //验证DN
  verifyAccountDN(dn, callBack) {
    return req("post", userApi.verifyDN, dn, callBack)
  },

  /**
   * 管理员策略
   */
  //添加管理员策略
  insertStrategy(strategy){
    return req("post", strategyApi.insertStrategy, strategy);
  },
  //编辑管理员策略
  updateStrategy(strategy){
    return req("put", strategyApi.updateStrategy, strategy);
  },
  //删除管理员策略
  deleteStrategy(id){
    return req("delete", strategyApi.deleteStrategy + '/' + id, "")
  },
  //获取管理员策略列表
  listStrategy(){
    return req("get", strategyApi.listStrategy, "")
  },
  //管理员策略分页列表
  pageStrategy(page){
    return reqParams("get", strategyApi.pageStrategy, page)
  },
  //开启管理员策略
  enableStrategy(id){
    return req("put", strategyApi.enableStrategy + '/' + id)
  },
  //关闭管理员策略
  unEnableStrategy(id) {
    return req("put", strategyApi.unEnableStrategy + '/' + id)
  },
  queryEnableStrategy() {
    return req("get", strategyApi.queryEnableStrategy);
  },

  /**
   *  角色管理
   */
  //获取所有管理员角色
  listRoles() {
    return req("get", roleApi.listRole, null)
  },
  //新增管理员角色
  insertRole(role) {
    return req("post", roleApi.insertRole, role)
  },
  //编辑管理员角色
  updateRole(role) {
    return req("put", roleApi.updateRole, role)
  },
  //删除管理员角色
  deleteRole(id) {
    return req("delete", roleApi.deleteRole + '/' + id)
  },
  //分页显示管理员角色
  pageRoles(page) {
    return reqParams("get", roleApi.pageRole, page)
  },
  //角色绑定的菜单
  listMenus(roleId) {
    return req("get", roleApi.listMenus + '/' + roleId, "")
  },
  //角色绑定的菜单
  setRoleMenu(params) {
    return req("post", roleApi.setRoleMenu, params)
  },
  addRule(params) {
    return req("post", `/role/add`, params)
  },
  getV2Config(params) {
    return req("get", `/system/snmp/getV2Config`, params)
  },
  startSnmpV2(params) {
    return req("get", `/system/snmp/startSnmpV2`, params)
  },
  stopSnmpV2(params) {
    return req("get", `/system/snmp/stopSnmpV2`, params)
  },
  queryV3User(params) {
    return req("post", `/system/snmp/queryV3User`, params)
  },
};

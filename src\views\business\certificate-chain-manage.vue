<template>
    <div class="container">
        <el-card v-show="showSearch" class="box-card" shadow="always" style="margin-bottom: 10px">
            <el-form :inline="true" :show-message="false" label-width="80px" class="user-search comForm">
                <el-form-item label="CA名称：">
                    <el-input size="small" v-model="ca.queryParams.name" clearable></el-input>
                </el-form-item>
                <el-form-item label="验证方式：">
                    <!--<el-input size="small" v-model="ca.queryParams.name" clearable></el-input>-->
                    <el-select v-model="ca.queryParams.updateStatue" placeholder="请选择">
                        <el-option label="全部" value=""></el-option>
                        <el-option label="不验证" :value="1"></el-option>
                        <el-option label="CRL验证" :value="2"></el-option>
                        <el-option label="OCSP验证" :value="3"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item style="background-color: transparent;">
                    <el-button class="comBtn com_send_btn" size="small" icon="el-icon-search" @click="refresh">搜索</el-button>
                    <el-button class="comBtn com_reset_btn" size="small" icon="el-icon-refresh" @click="resetHandle">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <!--<div v-show="showSearch" style="padding: 10px"></div>-->
        <el-card class="box-card" shadow="always" style="padding-bottom: 10px">
            <!-- 操作方法 -->
            <div style="margin-bottom: 10px">
                <el-button class="comBtn com_add_btn" size="mini" type="success" @click="addCertChain">新增</el-button>
                <el-button class="comBtn com_del_btn" size="mini" type="danger" @click="bathDeleteCert">删除</el-button>
                <div style="float: right">
                    <el-button-group>
                        <el-button size="mini" icon="el-icon-search" @click="showAndSearch"></el-button>
                        <el-button size="mini" icon="el-icon-refresh-left" @click="refresh"></el-button>
                    </el-button-group>
                </div>
            </div>

            <createTable
                    :tableData="ca.tableData"
                    :tableHeader="ca.tableDataHeader"
                    :isPage="ca.isPage"
                    :pageAttributes="ca.pageAttr"
                    :selectionChange="handleSelectionChange"
                    :current-change="ca_currentChange"
                    :sizeChange="ca_sizeChange"
            >
            </createTable>
            <!--添加CA-->
            <add-ca-dia ref="addCaDia" @parentDataFun="parentDataFun"></add-ca-dia>
            <!--CRL配置-->
            <el-dialog title="证书验证" :visible.sync="crlConfig.openConfig" width="700px" append-to-body :close-on-click-modal="false"
                       @close="Object.assign(crlConfig,$options.data().crlConfig);  $refs['crlConfigForm'].clearValidate();">
                <el-form label-width="150px" size="medium" style="padding-right: 20px" ref="crlConfigForm" :model="crlConfig" :rules="crlRules">
                    <el-form-item label="验证类型：">
                        <el-radio-group v-model="crlConfig.verificationmethod" @change="changeValidateType">
                            <el-radio :label="1">不验证</el-radio>
                            <!--<el-radio :label="3">CRL验证</el-radio>-->
                            <el-radio :label="5">CRL验证</el-radio>
                            <el-radio :label="4">OCSP验证</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="验证方式：" key="crlValidate" v-if="crlConfig.verificationmethod === 5">
                        <el-radio-group v-model="crlConfig.crlValidate">
                            <el-radio :label="2">文件上传</el-radio>
                            <el-radio :label="3">LDAP下载</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item label="CRL文件上传：" prop="crlFile" key="crlFile" v-if="crlConfig.verificationmethod === 5 && crlConfig.crlValidate === 2">
                        <el-input v-show="false" v-model="crlConfig.crlFile"/>
                        <!-- :data="{caId:crlConfig.form.caId,type:crlConfig.form.type}" -->
                        <el-upload
                                class="upload-demo"
                                accept='.crl'
                                action="/svs/business/ca/uploadFile"
                                :on-remove="crlFileRemove"
                                :on-change="crlFileChange"
                                :on-success="uploadSuccess"
                                :before-remove="beforeRemove"
                                :limit="1"
                                :headers="authHeader"
                                :auto-upload="true"
                                :data="{caId:crlConfig.caId,type:crlConfig.fileType}"
                                :file-list="crlConfig.fileList">
                            <el-button size="small" v-if="crlConfig.isCrlUploadDisable" type="primary" slot="tip" :disabled="crlConfig.isCrlUploadDisable">文件上传
                            </el-button>
                            <el-button size="small" v-if="!crlConfig.isCrlUploadDisable" type="primary" @click="crlConfig.type = 'crl'">上传CRL文件</el-button>
                        </el-upload>
                    </el-form-item>

                    <div v-if="crlConfig.verificationmethod === 5 && crlConfig.crlValidate === 3">
                        <el-form-item label="LDAP用户名：" prop="ldapName" key="ldapName">
                            <el-input size="small" v-model="crlConfig.ldapName" clearable auto-complete="off" placeholder="请输入LDAP用户名"></el-input>
                        </el-form-item>
                        <el-form-item label="LDAP口令：" prop="ldapPassword" key="ldapPassword">
                            <el-input size="small" type="password" v-model="crlConfig.ldapPassword" clearable auto-complete="off" placeholder="请输入LDAP口令"></el-input>
                        </el-form-item>
                        <div style="display: flex">
                            <el-form-item label="LDAP更新周期：" prop="times" key="times">
                                <el-input size="mini" v-model="crlConfig.times" style="width: 130px" maxlength="6" clearable auto-complete="off"></el-input>
                            </el-form-item>
                            <el-form-item label="" label-width="10px" prop="unitStr" key="unitStr">
                                <el-radio-group v-model="crlConfig.unitStr" @change="updateCycleChange">
                                    <el-radio :label="0">天</el-radio>
                                    <el-radio :label="1">时</el-radio>
                                    <el-radio :label="2">分</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </div>

                        <el-form-item label="CRL类型：" prop="crlType" key="crlType">
                            <el-radio-group v-model="crlConfig.type" @change="changeCrlType">
                                <el-radio :label="0">总CRL</el-radio>
                                <el-radio :label="1">分CRL</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="LDAP上总CRL：" prop="url" v-if="crlConfig.type === 0" key="urls">
                            <el-input size="small" v-model="crlConfig.url" style="width: 82%" clearable placeholder="请输入地址"></el-input>
                            <el-button size="small" type="primary" @click="testConnection(crlConfig.url, 'crl')">下载测试</el-button>
                        </el-form-item>
                        <span v-if="crlConfig.type === 1">
                            <el-form-item :label="'LDAP上分CRL'+index+'：'" v-for="(ldapPartCrl, index) in crlConfig.ldapPartCrl" :key="ldapPartCrl.key"
                                          :prop="'ldapPartCrl.' + index + '.value'"
                                          :rules="crlRules.caZUrl"
                                          style="margin-bottom: 15px">
                                <el-input size="small" v-model="ldapPartCrl.value" style="width: 65%" clearable placeholder="请输入地址"></el-input>
                                <el-button class="plus_btn" icon="el-icon-plus" size="small" type="primary" @click="addLdapPartCrl"></el-button>
                                <el-button class="minus_btn" icon="el-icon-minus" size="small" @click="delLdapPartCrl(ldapPartCrl)" v-if="index !== 0"></el-button>
                                <el-button class="minus_btn" type="text" style="padding: 16px; border: 0 none; visibility: hidden;" size="small" v-if="index === 0"></el-button>
                                <el-button size="small" type="primary" @click="testConnection(ldapPartCrl.value, 'crl')">下载测试</el-button>
                            </el-form-item>
                        </span>
                    </div>
                    <el-form-item label="OCSP地址：" key="oscpUrl" prop="oscpUrl" v-if="crlConfig.verificationmethod === 4">
                        <div style="display: flex">
                            <el-input size="small" v-model="crlConfig.oscpUrl" placeholder="请输入OCSP地址" clearable></el-input>
                            <el-button size="small" type="primary" style="margin-left: 10px" :loading="crlConfig.test.ocsp" @click="testConnection('ocsp')">测试连接</el-button>
                        </div>
                    </el-form-item>
                </el-form>

                <div slot="footer" class="dialog-footer">
                    <el-button class="comBtn com_reset_btn" @click="addAndUpdateConfigCancel">取 消</el-button>
                    <el-button class="comBtn com_send_btn" type="primary" @click="addAndUpdateConfig">确 定</el-button>
                </div>
            </el-dialog>
            <!--CRL缓存-->
            <el-dialog title="CRL管理" :visible.sync="crlCach.open" width="700px" append-to-body :close-on-click-modal="false" :before-close="closeCRL">
                <el-form :inline="true" label-width="80px" style="margin-top: -20px">
                    <el-form-item label="SN搜索：">
                        <el-input size="small" v-model="crlCach.form.cerSn" clearable></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button class="comBtn com_send_btn" icon="el-icon-search" size="small" @click="queryCertSn">搜索</el-button>
                    </el-form-item>
                </el-form>
                <createTable :tableData="crlCach.tableData" :tableHeader="crlCach.tableDataHeader"></createTable>
                <div slot="footer" class="dialog-footer">
                    <el-button class="comBtn com_send_btn" size="small" type="primary" @click="closeCRL">关闭</el-button>
                </div>
            </el-dialog>
        </el-card>
    </div>
</template>

<script>
    // import caMg from "@/api/caMG"
    import {dateFormat, getStore} from "@/utils/util";
    // import caMG from "@/api/caMG";
    // import bussMG from "@/api/bussMG";
    import {Message} from "element-ui";
    // import userMG from "@/api/userMG";
    // import appMG from "@/api/appMG";
    import Template from "@/components/template";
    import addCaDia from './components/addCaDia'
    import {isIpLdap, checkNumber} from '@/utils/myValidate'

    export default {
        // 模块名字
        name: "certificate-chain-manage",
        components: {Template, addCaDia},
        // 模块数据
        data() {
            let _this = this;
            let valCrlUrlF = (rule, value, callback) => {
                if (!value) return callback(new Error('请输入CRL发布点!'));
                _this.$http.caMG.testConnetcionNoAlert(value, function () {
                }).then(res => {
                    if (res.code == 0) {
                        callback();
                    } else {
                        return callback(new Error(res.msg));
                    }
                });
            };

            let valOcspUrlF = (rule, value, callback) => {
                if (!value) return callback(new Error('请输入OCSP地址!'));
                _this.$http.caMG.testConnetcionNoAlert(value, function () {
                }).then(res => {
                    if (res.code == 0) {
                        callback();
                    } else {
                        return callback(new Error(res.msg));
                    }
                });
            };
            //数据
            return {
                showSearch: true,
                authHeader: {token: "Bearer " + getStore('token')},
                crlRules: {
                    crlUrl: [
                        {required: true, trigger: 'blur', validator: valCrlUrlF}
                    ],
                    crlFile: [
                        {required: true, message: '请上传CRL文件', trigger: 'change'}
                    ],
                    oscpUrl: [
                        {required: true, trigger: 'blur', validator: valOcspUrlF}
                    ],
                    certFile: [
                        {required: true, message: '上传OCSP服务器证书', trigger: 'change'}
                    ],
                    times: [
                        {required: true, message: '请输入LDAP更新周期', trigger: 'blur'},
                        {validator: checkNumber, trigger: 'blur'}
                    ],
                    url: [
                        {required: true, message: '地址不能为空', trigger: 'blur'},
                        {required: true, validator: isIpLdap, trigger: 'blur'}
                    ],
                    caZUrl: [
                        {required: true, message: '地址不能为空', trigger: 'blur'},
                        {required: true, validator: isIpLdap, trigger: 'blur'}
                    ],
                },
                ca: {
                    tableData: [],
                    queryParams: {
                        pageNo: 1,
                        pageSize: 10,
                        name: "",
                        updateStatue: ''
                    },
                    delIds: '',
                    isPage: false,
                    pageAttr: {},
                    tableDataHeader: []
                },
                // chain: {
                //     addAppOpen: false,
                //     listOpen: false,
                //     queryCertOpen: false,
                //     queryParams: {
                //         pageNo: 1,
                //         pageSize: 10,
                //         caId: ""
                //     },
                //     // certEntityInfo: {},
                //     isPage: false,
                //     pageAttr: {},
                //     tableData: [],
                //     tableDataHeader: [],
                //     form: {
                //         name: "",
                //         caId: "",
                //         fileSize: "",
                //         fileList: []
                //     },
                //     uploadForm: new FormData(),
                // },
                crlConfig: {
                    test: {
                        ocsp: false,
                        crl: false
                    },
                    openConfig: false,
                    isCrlUploadDisable: false,
                    isCertUploadDisable: false,

                    // form: {
                    caId: "",
                    fileType: "crl",
                    verificationmethod: 1,
                    crlValidate: 2, // crl 验证方式
                    ocsp: "",
                    crlUrl: "",
                    crlFile: "",
                    certFile: "",
                    oscpUrl: "",
                    ip: "",
                    id: "",
                    port: "",
                    crlDn: "",
                    fileList: [],
                    fileSize: "",
                    checkExtraFromOcspFile: [],

                    ldapName: '', // LDAP用户名 ldapUsername
                    ldapPassword: '', // LDAP口令
                    times: '', // CRL更新策略
                    unitStr: 0, // 0 天   1 时   2 分
                    type: 0, // CRL类型 0 => 总CRL  1 => 分CRL
                    url: '', // LDAP上总CRL 下载地址
                    // caUrl: '', // LDAP上总CRL 下载地址
                    ldapPartCrl: [
                        {value: ''}
                    ],
                    // },
                    uploadForm: new FormData()
                },
                crlCach: {
                    open: false,
                    tableData: [],
                    tableDataHeader: [],
                    form: {
                        caId: "",
                        cerSn: ""
                    }
                }
            };
        },
        // 里面的函数只有调用才会执行（实时计算）里面是定义的方法
        methods: {
            showAndSearch() {
                this.showSearch = !this.showSearch;
            },
            // CA相关方法 查询列表
            refresh() {
                this.ca.tableData = [];
                this.$http.caMG.caList(this.ca.queryParams).then(res => {
                    this.ca.tableData = res.data;
                    this.ca.isPage = res.row > 0;
                    this.ca.pageAttr.total = res.row;
                });
            },
            // 重置
            resetHandle() {
                Object.assign(this.ca.queryParams, this.$options.data().ca.queryParams);
                this.refresh()
            },
            bathDeleteCert: function () {
                let _this = this;
                var delArray = _this.delArray;
                if (this.ca.delIds == "") {
                    this.$alert("请选择删除项!", "信息提示", {
                        cancelButtonText: '取消',
                        type: 'warning'
                    });
                    return;
                }

                this.$confirm('删除证书链可能会影响业务使用，确定是否删除！', '提示', {
                    confirmButtonText: '确定',
                    // cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.caMG.delBatch(this.ca.delIds).then(res => {
                        _this.refresh();
                    })
                });
            },
            parentDataFun() {
                this.refresh();
                // this.addWindowsCancel();
            },
            // 添加CA
            addCertChain() {
                this.$refs.addCaDia.addCaInitFun()
            },

            handleSelectionChange(val) {
                let delA = "";
                let length = val.length;
                val.forEach((item, index) => {
                    delA += item.id + ",";
                });
                if (length > 0) {
                    delA = delA.substr(0, delA.length - 1);
                }
                this.ca.delIds = delA;
            },
            ca_currentChange(val) {
                this.ca.queryParams.pageNo = val;
                this.refresh();
            },
            ca_sizeChange(val) {
                this.ca.queryParams.pageSize = val;
                this.refresh();
            },
            //CRL=========================================================
            // CRL更新策略 updateCycleChange
            updateCycleChange() {
                // this.crlConfig.times = '';
                this.$nextTick(() => {
                    this.$refs['crlConfigForm'].clearValidate(['times']);
                })
            },
            // 选择CRL类型 changeCrlType
            changeCrlType(val) {
                console.log(val);
                if (val === 1) {
                    this.crlConfig.url = ''
                } else {
                    this.crlConfig.ldapPartCrl = [{
                        value: ''
                    }]
                }
            },
            addLdapPartCrl() {
                this.crlConfig.ldapPartCrl.push({
                    value: '',
                    key: Date.now()
                })
            },
            delLdapPartCrl(item) {
                let index = this.crlConfig.ldapPartCrl.indexOf(item);
                if (index !== -1) {
                    this.crlConfig.ldapPartCrl.splice(index, 1)
                }
            },
            // LDAP 下载测试
            testConnection(url, type) {
                let address = "";
                let _this = this;
                if (type === "crl") {
                    this.crlConfig.test.crl = true;
                    // address = this.crlConfig.form.crlUrl;
                    address = url;
                } else {
                    this.crlConfig.test.ocsp = true;
                    address = this.crlConfig.oscpUrl;
                }
                this.$http.caMG.testConnetcion(address, function () {
                    if (type === "crl") {
                        _this.crlConfig.test.crl = false;
                    } else {
                        _this.crlConfig.test.ocsp = false;
                    }
                }).then(res => {
                    if (res.code == 0) {
                        Message({
                            message: "测试成功",
                            showClose: true,
                            type: 'success'
                        })
                    }
                });
            },
            beforeRemove(file) {
                return this.$confirm(`确定移除 ${file.name}？`);
            },
            // 上传CRL文件
            crlFileChange(file) {
                console.log(file);
                this.crlConfig.fileList = [];
                let certExt = file.name.split(".")[1];
                if (file.size === 0) {
                    this.$message.error('选择文件大小不能为0！');
                    this.crlConfig.fileList = [];
                    return false
                } else if (!(certExt === "crl" || certExt === "CRL")) {
                    this.$message.error('请上传crl格式文件！');
                    this.crlConfig.fileList = [];
                    return false
                } else {
                    // this.crlConfig.crlFile = file.raw;
                    this.crlConfig.fileList.push(file)
                }
            },
            uploadSuccess(res) {
                if (res.code == 0) {
                    // if (this.crlConfig.type == "crl") {
                        this.crlConfig.crlFile = res.data;
                        this.crlConfig.isCrlUploadDisable = true;
                    // } else {
                    //     this.crlConfig.form.certFile = res.data;
                    //     this.crlConfig.isCertUploadDisable = true;
                    // }
                }
            },
            crlFileRemove() {
                // this.crlConfig.crlFile = null;
                // this.crlConfig.fileList = []
                this.deleteFile("crl");
            },
            deleteFile(type) {
                this.$http.caMG.deleteFile(this.crlConfig.caId, type).then(res => {
                    this.crlConfig.fileList = [];
                    this.crlConfig.isCrlUploadDisable = false;
                    this.crlConfig.crlFile = "";
                });
            },
            changeValidateType (val) {
                console.log(val)
            },
            // 配置 crl配置 提交 crlConfigForm
            addAndUpdateConfig() {
                console.log(this.crlConfig);
                this.$refs["crlConfigForm"].validate((val) => {
                    if (val) {
                        if (this.crlConfig.times < 10 && this.crlConfig.unitStr === 2 && this.crlConfig.crlValidate === 3) {
                            this.$message.info('LDAP更新周期分钟数不能小于10分钟');
                            return
                        }
                        if (this.crlConfig.times <=0 && this.crlConfig.unitStr !== 2 && this.crlConfig.crlValidate === 3 && this.crlConfig.verificationmethod === 5) {
                            this.$message.info('LDAP更新周期要大于0');
                            return
                        }
                        let param = this.processDataFun();
                        this.$http.caMG.configCrlApi(param).then(res => {
                            this.addAndUpdateConfigCancel();
                            this.refresh()
                        });
                    }
                })
            },
            // 处理数据
            processDataFun() {
                let params = new FormData();
                params.append('caId', this.crlConfig.caId);
                if (this.crlConfig.verificationmethod !== 5) params.append('verificationmethod', this.crlConfig.verificationmethod);
                if (this.crlConfig.verificationmethod === 5) {
                    params.append('verificationmethod', this.crlConfig.crlValidate);
                    if (this.crlConfig.crlValidate === 2) {
                        params.append('crlFile', this.crlConfig.crlFile);
                    } else {
                        let url = '';
                        if (this.crlConfig.type === 1) {
                            let urlArr = [];
                            this.crlConfig.ldapPartCrl.map(item => {
                                urlArr.push(item.value)
                            });
                            url = urlArr.join("||")
                        }
                        params.append('ldapName', this.crlConfig.ldapName);
                        params.append('ldapPassword', this.crlConfig.ldapPassword);
                        params.append('times', this.crlConfig.times);
                        params.append('unitStr', this.crlConfig.unitStr);
                        params.append('type', this.crlConfig.type);
                        params.append('urls', this.crlConfig.type === 1 ? url : this.crlConfig.url);
                    }
                }
                // ocsp
                if (this.crlConfig.verificationmethod === 4) {
                    params.append('oscpUrl', this.crlConfig.oscpUrl)
                }
                return params
            },
            addAndUpdateConfigCancel() {
                this.crlConfig.openConfig = false;
            },
            queryCertSn() {
                let _this = this;
                if (!this.crlCach.form.caId == "" && !this.crlCach.form.cerSn == "") {
                    this.$http.caMG.queryCertSn(this.crlCach.form.caId, this.crlCach.form.cerSn).then(res => {
                        _this.crlCach.tableData = res.data;
                    });
                }
            },
            closeCRL() {
                this.crlCach.open = false;
                this.crlCach.form.cerSn = "";
                this.crlCach.tableData = [];
            }
        }
        ,
        created() {
            let _this = this;
            this.ca.tableDataHeader = [
                {
                    label: "全选",
                    width: "50",
                    type: "select",
                    tableColumnAttributes: {
                        align: "center",
                        "header-align": "center"
                    }
                }, {
                    label: "序号",
                    width: "50",
                    type: "index",
                    tableColumnAttributes: {
                        align: "center",
                        "header-align": "center"
                    }
                }, {
                    label: "CA名称",
                    type: "normal",
                    prop: "name",
                    tableColumnAttributes: {
                        align: "center",
                        "header-align": "center"
                    }
                }, {
                    label: "验证方式",
                    // width: 80,
                    type: "text_formatter",
                    prop: "updateStatue",
                    tableColumnAttributes: {
                        align: "center",
                        "header-align": "center"
                    },
                    formatter: function (value) {
                        console.log(value);
                        return value === 1 ? '不验证' : value === 2 || value ===3 ? 'CRL验证' : 'OCSP验证';
                    }
                }, {
                    label: "操作",
                    type: "operation",
                    width: 180,
                    tag: [
                        {
                            name: "修改",
                            operType: "edit",
                            tagType: "el-button",
                            attributes: {
                                size: "mini",
                                type: "text",
                                icon: "el-icon-edit"
                            },
                            callback: function (row) {
                                _this.$refs.addCaDia.editCaFun(row);
                            }
                        },
                        {
                            name: "删除",
                            operType: "delete",
                            tagType: "el-button",
                            attributes: {
                                size: "mini",
                                type: "text",
                                icon: "el-icon-delete"
                            },
                            callback: function (row) {
                                _this.$confirm('删除证书链可能会影响业务使用，确定是否删除！', '提示', {
                                    confirmButtonText: '确定',
                                    cancelButtonText: '取消',
                                    type: 'warning'
                                }).then(() => {
                                    _this.$http.caMG.del(row.id).then(res => {
                                        _this.refresh();
                                    });
                                });
                            }
                        }, {
                            operType: "config",
                            tagType: "el-dropdown",
                            on: {
                                "command": (command, row) => {
                                    let _this = this;
                                    if (command === "crl_config") {
                                        _this.crlConfig.openConfig = true;
                                        _this.crlConfig.caId = row.id;
                                        _this.crlConfig.crlUploadShow = true;
                                        _this.crlConfig.certUploadShow = true;
                                        _this.$http.caMG.queryCrlConfigByCaId(row.id).then(res => {
                                            let data = res.data;
                                            if (data.verificationmethod === 1 || data.verificationmethod === 4) _this.crlConfig.verificationmethod = data.verificationmethod;
                                            _this.crlConfig.oscpUrl = data.oscpUrl;
                                            if (data.verificationmethod === 2 || data.verificationmethod === 3) {
                                                _this.crlConfig.verificationmethod = 5;
                                                _this.crlConfig.crlValidate = data.verificationmethod;
                                                _this.crlConfig.certFile = data.certFile;
                                                _this.crlConfig.ldapName = !data.ldapName || data.ldapName === 'null' ? '' : data.ldapName;
                                                _this.crlConfig.ldapPassword = !data.ldapPassword || data.ldapPassword === 'null' ? '' : data.ldapPassword;
                                                _this.crlConfig.type = data.type;
                                                _this.crlConfig.times = data.times;
                                                _this.crlConfig.unitStr = data.unitStr;
                                                if (data.type === 0) {
                                                    this.crlConfig.url = data.urls
                                                } else {
                                                    let ldapPartCrl = data.urls.split('||');
                                                    this.crlConfig.ldapPartCrl = ldapPartCrl.map((val, index) => ({
                                                        value: ldapPartCrl[index],
                                                        key: index
                                                    }));
                                                    console.log(this.crlConfig.ldapPartCrl)
                                                }

                                            }
                                            if (data.crlFile && data.crlFile != "" && data.crlFile != null && data.crlFile != "null") {
                                                console.log(data.crlFile);
                                                _this.crlConfig.fileList = [{name: "third.crl"}];
                                                _this.crlConfig.crlFile = data.crlFile;
                                                _this.crlConfig.isCrlUploadDisable = true;
                                            }
                                            // if (data.certFile && data.certFile != "" && data.certFile != null && data.certFile != "null") {
                                            //     _this.crlConfig.form.checkExtraFromOcspFile = [{name: "ocsp.cer"}];
                                            //     ; //jinglk
                                            //     _this.crlConfig.isCertUploadDisable = true;
                                            // }
                                        });
                                    } else if (command === "crl_cach") {
                                        _this.crlCach.open = true;
                                        _this.crlCach.form.caId = row.id;
                                    }
                                }
                            },
                            children: [
                                {
                                    name: "配置",
                                    tagType: "el-button",
                                    attributes: {
                                        size: "mini",
                                        type: "text",
                                        icon: "el-icon-setting"
                                    }
                                }, {
                                    tagType: "el-dropdown-menu",
                                    attributes: {
                                        slot: "dropdown"
                                    },
                                    children: [
                                        {
                                            name: "证书验证",
                                            tagType: "el-dropdown-item",
                                            attributes: {
                                                command: "crl_config"
                                            }
                                        },
                                        {
                                            name: "CRL管理",
                                            tagType: "el-dropdown-item",
                                            attributes: {
                                                command: "crl_cach"
                                            }
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ];
            this.crlCach.tableDataHeader = [
                {
                    label: "序号",
                    width: "50",
                    type: "index",
                    tableColumnAttributes: {
                        align: "center",
                        "header-align": "center"
                    }
                },
                {
                    label: "SN",
                    type: "normal",
                    prop: "certSn",
                    tableColumnAttributes: {
                        align: "center",
                        "header-align": "center"
                    }
                }
            ];
            this.refresh();
        }
    }
</script>

<style scoped>
    .minus_btn {
        margin-left: 4px;
    }

    .plus_btn, .minus_btn {
        padding: 9px;
    }

    .user-search .el-form-item {
        margin-bottom: 0;
    }
</style>

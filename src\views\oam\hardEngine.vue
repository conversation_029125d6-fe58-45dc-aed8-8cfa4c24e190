<template>
  <div>
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span style="font-size: 18; font-weight: 600;">当前算力</span>
      </div>
      <div v-if="Object.keys(infos).length > 0">
        <div v-for="(item,index) in infos" :key="index">
          <label>{{item.key}}: {{item.value}}</label>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: "manage-log",
  data() {
    return {
      infos: {},
    }
  },
  methods: {
    searchInfo() {
      this.$http.oamMG.getHardEngine().then(res => {
        if (res.code === 0) {
          this.infos = res.data;
        }
      })
    },
  },
  mounted() {
    this.searchInfo();
  }
}
</script>
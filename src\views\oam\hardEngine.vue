<template>
  <div>
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span style="font-size: 18; font-weight: 600;">当前算力</span>
      </div>
      <div v-if="Object.keys(infos).length > 0">
        <div v-for="(value, key) in infos" :key="key">
          <label style="display: inline-block; width: 50px; text-align: right; padding-top: 5px;">{{key}}：</label><span>{{value}}</span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: "manage-log",
  data() {
    return {
      infos: {},
    }
  },
  methods: {
    searchInfo() {
      this.$http.oamMG.getHardEngine().then(res => {
        if (res.code === 0) {
          this.infos = res.data;
          console.log(this.infos);
        }
      })
    },
  },
  mounted() {
    this.searchInfo();
  }
}
</script>

<style scoped>
.info-label {
  display: inline-block;
  width: 150px;
  text-align: right;
  margin-right: 10px;
}
</style>
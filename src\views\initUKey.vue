<template>
  <div>
    <el-card class="box-card" shadow="always" style="margin-bottom: 10px">
      <!-- 搜索筛选 -->
      <el-form :model="initUkeyForm" :rules="rules" ref="initUkeyForm" class="demo-ruleForm" label-width="150px">
        <div>
          <el-row>
            <h3 class="title_back">Ukey清空并初始化</h3>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="10">
              <el-form-item label="应用:" :inline="true" prop="application">
                <el-input placeholder="例:application" v-model="initUkeyForm.application"></el-input>
              </el-form-item>
              <el-form-item label="SM2容器:" :inline="true" prop="sm2_container">
                <el-input placeholder="例:container" v-model="initUkeyForm.sm2_container"></el-input>
              </el-form-item>
              <el-form-item label="RSA容器:" :inline="true" prop="rsa_container">
                <el-input placeholder="例:rsa_container" v-model="initUkeyForm.rsa_container"></el-input>
              </el-form-item>
              <el-form-item label="管理员PIN:" :inline="true" prop="adminPin">
                <el-input placeholder="例:********" v-model="initUkeyForm.adminPin"></el-input>
              </el-form-item>
              <el-form-item label="用户PIN:" :inline="true">
                <el-input placeholder="例:********" v-model="pin"></el-input>
              </el-form-item>
              <el-form-item>
                <el-button class="comBtn com_send_btn" size="small" type="primary" @click="initUkey">初始化</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </el-card>
    <el-card class="box-card" shadow="always" style="margin-bottom: 10px">
      <!-- 搜索筛选 -->
      <el-form :model="createCertForm" :rules="rules" ref="createCertForm" class="demo-ruleForm" label-width="150px">
        <div>
          <el-row>
            <h3 class="title_back">证书生成</h3>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="10">
              <el-form-item label="证书DN:" :inline="true" prop="dn">
                <el-input placeholder="例:C=CN,CN=test" v-model="createCertForm.dn"></el-input>
              </el-form-item>
              <el-form-item label="用户PIN:" :inline="true">
                <el-input placeholder="例:********" v-model="pin"></el-input>
              </el-form-item>
              <el-form-item label="申请类型：" prop="doubleCert" key="doubleCert">
                <el-radio :label="1" v-model="createCertForm.doubleCert" @change="changeUsage">双证</el-radio>
                <el-radio :label="0" v-model="createCertForm.doubleCert" @change="changeUsage">单证</el-radio>
              </el-form-item>
              <el-form-item label="证书用途：" prop="Usage" key="Usage">
                <el-checkbox-group v-model="createCertForm.Usage">
                  <el-checkbox :disabled="createCertForm.doubleCert === 1" :label="0">签名</el-checkbox>
                  <el-checkbox :disabled="createCertForm.doubleCert === 1" :label="3">加密</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
<!--              <el-form-item label="扩展密钥用途：" prop="" key="expandedKey">-->
<!--                <el-checkbox-group v-model="createCertForm.UsageExt">-->
<!--                  <el-checkbox :label="101">服务器认证</el-checkbox>-->
<!--                  <el-checkbox :label="102">客户端认证</el-checkbox>-->
<!--                </el-checkbox-group>-->
<!--              </el-form-item>-->
              <el-form-item>
                <el-button class="comBtn com_send_btn" size="small" type="primary" @click="createCert">写入证书</el-button>
                <el-button class="comBtn com_send_btn" size="small" type="primary" @click="initAndCreate">清空、初始化并写入证书
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </el-card>

    <el-card class="box-card" shadow="always" style="margin-bottom: 100px">
    </el-card>
  </div>
</template>

<script>
import {INIT_UKEY_FUN_ALL, testGetCSRByApp, testImportCert, testImportDoubleCertByApp, testWS} from "../utils/Ukey";
import bussMG from "@/api/bussMG";

export default {
  name: "initUKey",
  data() {
    const validatorAccountDN = (rule, value, callback) => {
      if (value !== "") {
        if (value.indexOf("，") != -1) {
          callback(new Error("dn不能包含中文逗号"));
        } else if (value.indexOf(", ") != -1) {
          callback(new Error("逗号后不能有空格"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    return {
      loading: null,
      pin: "********",
      initUkeyForm: {
        application: "application",
        sm2_container: "container",
        rsa_container: "rsa_container",
        adminPin: "********"
      },
      createCertForm: {
        dn: "C=CN,CN=test",
        doubleCert: 0,
        Usage: [0],
        UsageExt: []
      },
      rules: {
        dn: [
          {required: true, message: '请输入申请证书DN', trigger: 'blur'},
          {validator: validatorAccountDN, trigger: 'blur'}
        ],
        pin: [{required: true, message: '请输入PIN码', trigger: 'blur'}],
        adminPin: [{required: true, message: '请输入管理员PIN码', trigger: 'blur'}],
        application: [{required: true, message: '请输入应用名字', trigger: 'blur'}],
        rsa_container: [{required: true, message: '请输入RSA容器名字', trigger: 'blur'}],
        sm2_container: [{required: true, message: '请输入SM2容器名字', trigger: 'blur'}],
        Usage: [
          {required: true, message: "请选择证书用途", trigger: "change"},
          {
            validator: function (rule, value, callback) {
              if (value.indexOf(0) === -1 && value.indexOf(3) === -1) callback(new Error("请选择证书用途！"));
              callback();
            }, trigger: 'blur'
          }
        ],
      }
    }
  },
  methods: {
    // 选择证书类型
    changeUsage(val) {
      this.createCertForm.Usage = val === 1 ? [0, 3] : this.createCertForm.Usage
    },
    openLoading() {
      let _this = this;
      _this.loading = _this.$loading({
        lock: true,
        text: '执行中，请稍等...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    },
    initUkey() {
      this.$refs["initUkeyForm"].validate((valid) => {
        if (valid) {
          if (!this.pin) {
            this.$message.error("pin不能为空！");
            return;
          }
          this.$confirm('确定要清空UKey并初始化吗?', '初始化确认', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.openLoading();
            INIT_UKEY_FUN_ALL(
              this.initUkeyForm.application,
              this.initUkeyForm.sm2_container,
              this.initUkeyForm.rsa_container,
              this.initUkeyForm.adminPin,
              this.pin, ({data}) => {
                this.loading.close();
                this.$message.success("清空成功！");
              }
            );
          })
        }
      })
    },
    createCert() {
      this.$refs["createCertForm"].validate((valid) => {
        if (valid) {
          if (!this.pin) {
            this.$message.error("pin不能为空！");
            return;
          }
          this.$confirm('确定要生成证书并写入UKey吗?', '写入证书', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.openLoading();
            const pin = this.pin;
            const addDn = this.createCertForm.dn;
            const dn = '/' + addDn.replaceAll(',', '/');
            testGetCSRByApp(
              this.initUkeyForm.application,
              this.initUkeyForm.sm2_container, pin, dn, (res) => {
                if (res.errCode === 0) {
                  let p = new FormData();
                  p.append('doubleCert', this.createCertForm.doubleCert);
                  p.append('usage', this.createCertForm.Usage);
                  p.append('usageExt', this.createCertForm.UsageExt);
                  p.append('csr', res.b64CertCSR);
                  bussMG.createCertByP10(p).then((res) => {
                    let code = res.code;
                    if (code == 0) {
                      const double = res.data.doubleCert;
                      const certByte = res.data.certByte;
                      if (double == 1) {
                        const enCertByte = res.data.enCertByte;
                        const enKeyByte = res.data.enKeyByte;
                        testImportDoubleCertByApp(
                          this.initUkeyForm.application,
                          this.initUkeyForm.sm2_container, this.pin, certByte, enCertByte, enKeyByte, (ress) => {
                            const errCode = ress.errCode;
                            this.loading.close();
                            if (errCode == 0) {
                              this.$message.success("写入成功！");
                            } else {
                              this.$message.error("写入证书失败！");
                            }
                          });
                      } else {
                        testImportCert(this.pin, certByte, (ress) => {
                          const errCode = ress.errCode;
                          this.loading.close();
                          if (errCode == 0) {
                            this.$message.success("写入成功！");
                          } else {
                            this.$message.error("写入证书失败！");
                          }
                        });
                      }
                    } else {
                      this.$message.error(res.msg);
                      this.loading.close();
                    }
                  }, (err) => {
                    this.loading.close();
                  })
                } else {
                  this.$message.error("CSR生成异常！");
                  this.loading.close();
                }
              })
          })
        }
      })
    },
    initAndCreate() {
      this.$refs["initUkeyForm"].validate((valid) => {
        if (valid) {
          this.$refs["initUkeyForm"].validate((valid) => {
            if (valid) {
              if (!this.pin) {
                this.$message.error("pin不能为空！");
                return;
              }
              this.$confirm('确定要清空UKey并重新写入证书吗?', '写入证书', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                this.openLoading();
                INIT_UKEY_FUN_ALL(
                  this.initUkeyForm.application,
                  this.initUkeyForm.sm2_container,
                  this.initUkeyForm.rsa_container,
                  this.initUkeyForm.adminPin,
                  this.pin, ({data}) => {
                    const pin = this.pin;
                    const addDn = this.createCertForm.dn;
                    const dn = '/' + addDn.replaceAll(',', '/');
                    testGetCSRByApp(
                      this.initUkeyForm.application,
                      this.initUkeyForm.sm2_container, pin, dn, (res) => {
                        if (res.errCode === 0) {
                          let p = new FormData();
                          p.append('doubleCert', this.createCertForm.doubleCert);
                          p.append('usage', this.createCertForm.Usage);
                          p.append('usageExt', this.createCertForm.UsageExt);
                          p.append('csr', res.b64CertCSR);
                          bussMG.createCertByP10(p).then((res) => {
                            let code = res.code;
                            if (code == 0) {
                              const double = res.data.doubleCert;
                              const certByte = res.data.certByte;
                              if (double == 1) {
                                const enCertByte = res.data.enCertByte;
                                const enKeyByte = res.data.enKeyByte;
                                testImportDoubleCertByApp(
                                  this.initUkeyForm.application,
                                  this.initUkeyForm.sm2_container, this.pin, certByte, enCertByte, enKeyByte, (ress) => {
                                    const errCode = ress.errCode;
                                    this.loading.close();
                                    if (errCode == 0) {
                                      this.$message.success("写入成功！");
                                    } else {
                                      this.$message.error("写入证书失败！");
                                    }
                                  });
                              } else {
                                testImportCert(this.pin, certByte, (ress) => {
                                  const errCode = ress.errCode;
                                  this.loading.close();
                                  if (errCode == 0) {
                                    this.$message.success("写入成功！");
                                  } else {
                                    this.$message.error("写入证书失败！");
                                  }
                                });
                              }
                            } else {
                              this.$message.error(res.msg);
                              this.loading.close();
                            }
                          }, (err) => {
                            this.loading.close();
                          })
                        } else {
                          this.$message.error("CSR生成异常！");
                          this.loading.close();
                        }
                      })
                  }
                );
              })
            }
          })
        }
      })
    }
  },
  created() {
    testWS(() => {
      this.$message.success("UKey服务连接成功");
    }, (evt) => {
      if (this.loading) {
        this.loading.close();
      }
      this.$message.error("UKey服务连接断开/异常");
    });
  }
}
</script>

<style scoped>

</style>

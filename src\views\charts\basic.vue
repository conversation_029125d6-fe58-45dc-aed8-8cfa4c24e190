<template>
    <div :style="{ height: '165px'}">
        <p>默认网关：{{gateway}}</p>
        <p>首选DNS：{{dns}}</p>
        <p>双机热备：{{doubleServer}}</p>
        <p>系统审计：{{statistics}}</p>
    </div>
</template>

<script>
    import systemMG from "@/api/systemMG";

    export default {
        name: "basic",
        data() {
            return {
                dns: '',
                doubleServer: '',
                gateway: '',
                statistics: '',
            }
        },
        created() {

            systemMG.getSystemInfo().then((res) => {
                const code = res.code;
                if (code == "0") {
                    this.dns = res.data.dns;
                    this.doubleServer = res.data.doubleServer;
                    this.gateway = res.data.gateway;
                    this.statistics = res.data.statistics;
                }
            })
        },
    }
</script>

<style scoped>

</style>

import axios from 'axios';
import {getStore} from "./util";
import {Message} from 'element-ui'

export function exportzip(request, url, options = {}) {
    return new Promise((resolve, reject) => {
        axios.defaults.headers['content-type'] = 'application/json;charset=UTF-8';
        axios.defaults.headers['Authorization'] = "Barer " + getStore("token");
        axios({
            method: request,
            url: url, // 请求地址
            data: options, // 参数
            responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then(
            response => {
                resolve(response.data);
                let blob = new Blob([response.data], {
                    type: "application/zip"
                });
                let fileName = "系统日志";
                if (window.navigator.msSaveOrOpenBlob) {
                    // console.log(2)
                    navigator.msSaveBlob(blob, fileName)
                } else {
                    // console.log(3)
                    let link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    link.download = fileName;
                    link.click();
                    //释放内存
                    window.URL.revokeObjectURL(link.href)
                }
            },
            err => {
                reject(err)
            }
        )
    })
}


export function exportExcel1(url, options = {}, name) {
    return new Promise((resolve, reject) => {
        axios.defaults.headers['content-type'] = 'application/json;charset=UTF-8';
        axios.defaults.headers['Authorization'] = "Barer " + getStore("token");
        axios({
            method: 'post',
            url: url, // 请求地址
            data: options, // 参数
            responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then(
            response => {
                resolve(response.data);
                let blob = new Blob([response.data], {
                    type: 'application/vnd.ms-excel'
                });
                let fileName = name;
                if (window.navigator.msSaveOrOpenBlob) {
                    navigator.msSaveBlob(blob, fileName)
                } else {
                    let link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    link.download = fileName;
                    link.click();
                    //释放内存
                    window.URL.revokeObjectURL(link.href)
                }
            },
            err => {
                reject(err)
            }
        )
    })
}

export function exporttxt(request, url, options = {}) {
    return new Promise((resolve, reject) => {
        axios.defaults.headers['Authorization'] = "Barer " + getStore("token");
        axios({
            method: request,
            url: url, // 请求地址
            data: options, // 参数
            responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then(
            response => {
                resolve(response.data);
                let blob = new Blob([response.data], {
                    type: "application/txt"
                });
                let fileName = decodeURI(response.headers['content-disposition'].split('=')[1]);
                if (window.navigator.msSaveOrOpenBlob) {
                    // console.log(2)
                    navigator.msSaveBlob(blob, fileName)
                } else {
                    // console.log(3)
                    let link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    link.download = fileName;
                    link.click();
                    //释放内存
                    window.URL.revokeObjectURL(link.href)
                }
            },
            err => {
                reject(err)
            }
        )
    })
}

/**
 * @property result
 * */
export function exportDevCert(request, url, options = {}) {
    return new Promise((resolve, reject) => {
        axios.defaults.headers['Authorization'] = "Barer " + getStore("token");
        axios.defaults.headers['Token'] = "Barer " + getStore("token");
        axios({
            method: request,
            url: url, // 请求地址
            data: options, // 参数
            responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then(
            response => {
                let reader = new FileReader();
                reader.readAsText(response.data, 'utf-8');
                reader.onload = function (e) {
                    try {
                        const {code, msg} = JSON.parse(e.target.result);
                        if (code === 500) {
                            Message({
                                message: msg || 'Error',
                                type: 'error',
                                // duration: 5 * 1000
                            });
                        }
                    } catch (err) { // 正常下载
                        resolve(response.data);
                        let blob = new Blob([response.data], {type: 'application/zip'});
                        let fileName = decodeURI(response.headers['content-disposition'].split('=')[1]);
                        if (window.navigator.msSaveOrOpenBlob) {
                            navigator.msSaveBlob(blob, fileName)
                        } else {
                            let link = document.createElement('a');
                            link.href = window.URL.createObjectURL(blob);
                            link.download = fileName;
                            link.click();
                            // 释放内存
                            window.URL.revokeObjectURL(link.href)
                        }
                    }
                }
            },
            err => {
                reject(err)
            }
        )
    })
}

export function downloadPackage(request, url, options = {}) {
    return new Promise((resolve, reject) => {
        // axios.defaults.headers['Authorization'] = "Barer " + getStore("token");
        axios.defaults.headers['Authorization'] = "Bearer " + getStore("token");
        axios.defaults.headers['Token'] = "Bearer " + getStore("token");
        axios({
            method: request,
            url: url, // 请求地址
            data: options, // 参数
            responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then(
            response => {
                let reader = new FileReader();
                reader.readAsText(response.data, 'utf-8');
                reader.onload = function (e) {
                    try {
                        const {code, msg} = JSON.parse(e.target.result);
                        // if (code === 500) {
                        //     Message({
                        //         message: msg || 'Error',
                        //         type: 'error',
                        //         // duration: 5 * 1000
                        //     });
                        // }
                        if (code !== 0) {
                            Message({
                                message: msg || '下载证书异常',
                                type: 'error'
                            })
                        }
                    } catch (err) { // 正常下载
                        resolve(response.data);
                        let blob = new Blob([response.data], {type: 'application/zip'});
                        let fileName = decodeURI(response.headers['content-disposition'].split('=')[1]);
                        if (window.navigator.msSaveOrOpenBlob) {
                            navigator.msSaveBlob(blob, fileName)
                        } else {
                            let link = document.createElement('a');
                            link.href = window.URL.createObjectURL(blob);
                            link.download = fileName;
                            link.click();
                            // 释放内存
                            window.URL.revokeObjectURL(link.href)
                        }
                    }
                }
            },
            err => {
                reject(err)
            }
        )
    })
}

export function exportDevLog(request, url, options = {}) {
    return new Promise((resolve, reject) => {
        axios.defaults.headers['Authorization'] = "Barer " + getStore("token");
        axios({
            method: request,
            url: url, // 请求地址
            data: options, // 参数
            responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then(
            response => {
                let reader = new FileReader();
                reader.readAsText(response.data, 'utf-8');
                reader.onload = function (e) {
                    try {
                        const {code, msg} = JSON.parse(e.target.result);
                        if (code === 500) {
                            Message({
                                message: msg || 'Error',
                                type: 'error',
                                // duration: 5 * 1000
                            });
                        }
                    } catch (err) { // 正常下载
                        resolve(response.data);
                        let blob = new Blob([response.data]);
                        console.log(response.headers);
                        // let fileName = decodeURI(response.headers['content-disposition'].split('=')[1]);
                        let fileName = '升级日志';
                        if (window.navigator.msSaveOrOpenBlob) {
                            navigator.msSaveBlob(blob, fileName)
                        } else {
                            let link = document.createElement('a');
                            link.href = window.URL.createObjectURL(blob);
                            link.download = fileName;
                            link.click();
                            // 释放内存
                            window.URL.revokeObjectURL(link.href)
                        }
                    }
                }
            },
            err => {
                reject(err)
            }
        )
    })
}

export function exportCertFile(request, url, options) {
    return new Promise((resolve, reject) => {
        axios.defaults.headers['Authorization'] = "Bearer " + getStore("token");
        axios.defaults.headers['Token'] = "Bearer " + getStore("token");
        axios.defaults.headers['Content-Type'] = "application/x-www-form-urlencoded;charset=utf-8";
        // "Content-Type": "application/x-www-form-urlencoded;charset=utf-8"
        axios({
            method: request,
            url: url, // 请求地址
            data: options, // 参数
            responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then(
            response => {
                let reader = new FileReader();
                reader.readAsText(response.data, 'utf-8');
                reader.onload = function (e) {
                    try {
                        const {code, msg} = JSON.parse(e.target.result);
                        if (code === 500) {
                            Message({
                                message: msg || 'Error',
                                type: 'error',
                                // duration: 5 * 1000
                            });
                        }
                    } catch (err) { // 正常下载
                        resolve(response.data);
                        let blob = new Blob([response.data]);
                        let fileName = decodeURI(response.headers['content-disposition'].split('=')[1]);
                        if (window.navigator.msSaveOrOpenBlob) {
                            navigator.msSaveBlob(blob, fileName)
                        } else {
                            let link = document.createElement('a');
                            link.href = window.URL.createObjectURL(blob);
                            link.download = fileName;
                            link.click();
                            // 释放内存
                            window.URL.revokeObjectURL(link.href)
                        }
                    }
                }
            },
            err => {
                reject(err)
            }
        )
    })
}

export function exportXml(request, url, options = {}) {
    return new Promise((resolve, reject) => {
        axios.defaults.headers['content-type'] = 'application/json;charset=UTF-8';
        axios.defaults.headers['Authorization'] = "Barer " + getStore("token");
        axios({
            method: request,
            url: url, // 请求地址
            data: options, // 参数
            responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then(
            response => {
                resolve(response.data);
                console.log(response);
                let blob = new Blob([response.data], {
                    // type: "application/xml"
                    type: "text/xml"
                });
                // let fileName = decodeURI(response.headers['content-disposition'].split('=')[1])
                let fileName = '证书';
                if (window.navigator.msSaveOrOpenBlob) {
                    // console.log(2)
                    navigator.msSaveBlob(blob, fileName)
                } else {
                    // console.log(3)
                    let link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    link.download = fileName;
                    link.click();
                    //释放内存
                    window.URL.revokeObjectURL(link.href)
                }
            },
            err => {
                reject(err)
            }
        )
    })
}

export function exportExcel(url, options = {}) {
    return new Promise((resolve, reject) => {
        axios.defaults.headers['content-type'] = 'application/json;charset=UTF-8';
        axios.defaults.headers['Authorization'] = "Barer " + getStore("token");
        axios({
            method: 'get',
            url: url, // 请求地址
            data: options, // 参数
            responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then(
            response => {
                resolve(response.data);
                let blob = new Blob([response.data], {
                    type: 'application/vnd.ms-excel'
                });
                let fileName = decodeURI(response.headers['content-disposition'].split('=')[1]);
                if (window.navigator.msSaveOrOpenBlob) {
                    // console.log(2)
                    navigator.msSaveBlob(blob, fileName)
                } else {
                    // console.log(3)
                    let link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    link.download = fileName;
                    link.click();
                    //释放内存
                    window.URL.revokeObjectURL(link.href)
                }
            },
            err => {
                reject(err)
            }
        )
    })
}

//证书导出
export function exportcret(request, url, options = {}) {
    return new Promise((resolve, reject) => {
        axios.defaults.headers['content-type'] = 'application/json;charset=UTF-8';
        axios.defaults.headers['Authorization'] = "Barer " + getStore("token");
        axios({
            method: request,
            url: url, // 请求地址
            data: options, // 参数
            responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then(
            response => {
                resolve(response.data);
                let blob = new Blob([response.data], {
                    type: 'application/vnd.ms-excel'
                });
                let fileName = decodeURI(response.headers['content-disposition'].split('=')[1]);
                // let fileName = 'cert';
                if (window.navigator.msSaveOrOpenBlob) {
                    // console.log(2)
                    navigator.msSaveBlob(blob, fileName)
                } else {
                    // console.log(3)
                    let link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    link.download = fileName;
                    link.click();
                    //释放内存
                    window.URL.revokeObjectURL(link.href)
                }
            },
            err => {
                reject(err)
            }
        )
    })
}

// CA证书导出
export function exportCaCert(request, url, options = {}) {
    return new Promise((resolve, reject) => {
        axios.defaults.headers['content-type'] = 'application/json;charset=UTF-8';
        axios.defaults.headers['Authorization'] = "Barer " + getStore("token");
        axios({
            method: request,
            url: url, // 请求地址
            params: options, // 参数
            responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then(
            response => {
                resolve(response.data);
                let blob = new Blob([response.data], {
                    // type: 'application/vnd.ms-excel'
                    // type: "application/xml"
                    type: "text/xml"
                });
                let fileName = decodeURI(response.headers['content-disposition'].split('=')[1]);
                //   let fileName = '证书';
                if (window.navigator.msSaveOrOpenBlob) {
                    // console.log(2)
                    navigator.msSaveBlob(blob, fileName)
                } else {
                    // console.log(3)
                    let link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    link.download = fileName;
                    link.click();
                    //释放内存
                    window.URL.revokeObjectURL(link.href)
                }
            },
            err => {
                reject(err)
            }
        )
    })
}

export function batchExportCaCert(request, url, options = {}) {
    return new Promise((resolve, reject) => {
        axios.defaults.headers['Authorization'] = "Barer " + getStore("token");
        axios({
            method: request,
            url: url, // 请求地址
            params: options, // 参数
            responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then(
            response => {
                let reader = new FileReader();
                reader.readAsText(response.data, 'utf-8');
                reader.onload = function (e) {
                    try {
                        const {code, msg} = JSON.parse(e.target.result);
                        if (code === 500) {
                            Message({
                                message: msg || 'Error',
                                type: 'error',
                                // duration: 5 * 1000
                            });
                        }
                    } catch (err) { // 正常下载
                        resolve(response.data);
                        let blob = new Blob([response.data], {type: 'application/zip'});
                        let fileName = decodeURI(response.headers['content-disposition'].split('=')[1]);
                        // let fileName = '证书';
                        if (window.navigator.msSaveOrOpenBlob) {
                            navigator.msSaveBlob(blob, fileName)
                        } else {
                            let link = document.createElement('a');
                            link.href = window.URL.createObjectURL(blob);
                            link.download = fileName;
                            link.click();
                            // 释放内存
                            window.URL.revokeObjectURL(link.href)
                        }
                    }
                }
            },
            err => {
                reject(err)
            }
        )
    })
}

export function exportcretform(request, url, options = {}) {
    return new Promise((resolve, reject) => {
        // axios.defaults.headers['content-type'] = 'multipart/form-data'
        axios.defaults.headers['Authorization'] = "Barer " + getStore("token");
        console.log(options);
        axios({
            method: request,
            url: url, // 请求地址
            data: options, // 参数
            responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then(
            response => {
                resolve(response.data);
                let blob = new Blob([response.data], {
                    type: 'application/vnd.ms-excel'
                });
                let fileName = decodeURI(response.headers['content-disposition'].split('=')[1]);
                if (window.navigator.msSaveOrOpenBlob) {
                    // console.log(2)
                    navigator.msSaveBlob(blob, fileName)
                } else {
                    // console.log(3)
                    let link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    link.download = fileName;
                    link.click();
                    //释放内存
                    window.URL.revokeObjectURL(link.href)
                }
            },
            err => {
                reject(err)
            }
        )
    })
}

export function exporttxt1(request, url, options = {}) {
    return new Promise((resolve, reject) => {
        axios.defaults.headers['Authorization'] = "Barer " + getStore("token");
        axios.defaults.headers['content-type'] = 'application/json;charset=UTF-8';
        axios({
            method: request,
            url: url, // 请求地址
            data: options, // 参数
            responseType: 'blob' // 表明返回服务器返回的数据类型
        }).then(
            response => {
                resolve(response.data);
                let blob = new Blob([response.data], {
                    type: 'application/vnd.ms-excel'
                });
                let name = decodeURI(response.headers['content-disposition'].split('=')[1]);
                let fileName = name.split("''")[1];
                if (window.navigator.msSaveOrOpenBlob) {
                    // console.log(2)
                    navigator.msSaveBlob(blob, fileName)
                } else {
                    // console.log(3)
                    let link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    link.download = fileName;
                    link.click();
                    //释放内存
                    window.URL.revokeObjectURL(link.href)
                }
            },
            err => {
                reject(err)
            }
        )
    })
}

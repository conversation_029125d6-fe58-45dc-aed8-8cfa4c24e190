// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import App from './App'
import router from './router'
import VueClipboard from 'vue-clipboard2'

Vue.use(VueClipboard);

// 过滤
import installFilter from './utils/filters'
installFilter(Vue);


Vue.config.productionTip = false;

import "@/assets/css/shudun.css"

// 注册 createTable
import createTable from "@/utils/createTable";
Vue.component('createTable', createTable);

//element-ui组件
import ElementUI from 'element-ui' //element-ui的全部组件
import 'element-ui/lib/theme-chalk/index.css'//element-ui的css

// global css
import '@/assets/css/commons.less' // global css
ElementUI.Dialog.props.closeOnClickModal.default = false;
Vue.use(ElementUI);

// 引入echarts
import echarts from 'echarts'
Vue.prototype.$echarts = echarts;

//下拉框懒加载
import _lazyLoading from './utils/lazyLoading'
const lazyLoading = Vue.directive('lazyLoading', _lazyLoading)
export { lazyLoading }

// 引入状态管理
import store from './vuex/store';

// 引入Axios
import axios from 'axios';
Vue.prototype.axios = axios;

import '@/icons'
import { getStore } from "@/utils/util";


// 实现自动化导入子组件
// const COM = [];
// const modules = require.context('./', true, /\w+.vue$/);
// console.log(modules);
// // const res = [];
// modules.keys().forEach(filePath => {
//     const key = filePath.replace(/^\.\/(.*)\.\w+$/, '$1');
//     const com_item = modules(filePath).default;
//     console.log(filePath === './views/index.vue');
//     COM.push(com_item)
// //     res[key] = component
// });
// // console.log(COM);
// import com_system from './views/system'
// console.log(com_system);
// const COM_ARR = [];
// const modules = require.context('./views', true, /\w+.vue$/);
// const res = {};
// // console.log(modules.keys());
// modules.keys().forEach(filePath => {
//     // console.log(filePath);
//     const key = filePath.replace(/^\.\/(.*)\.\w+$/, '$1');
//     // console.log();
//     const component = modules(filePath).default;
//     COM_ARR.push(component)
//     let key_item = '';
//     if (key.lastIndexOf('/') !== -1) {
//         const index = key.lastIndexOf('/');
//         key_item = key.substr(index + 1)
//     } else {
//         key_item = key
//     }
//     console.log(key_item);
//     res[key_item] = component
// });
// console.log(res);

// const install = function (Vue, opts = {}) {
//     COM_ARR.forEach(component => {
//         console.log(component.name);
//         Vue.component(component.name, component);
//     });
// }

// 引入 API
import API from './api'
console.log(API);
Vue.$http = API;
Vue.prototype.$http = API;

// store.dispatch("sendPolicyFun").then(res => {
//     console.log(res);
//     // next();
// });

router.beforeEach((to, from, next) => {
    if (to.path === '/' || to.path === '/root/login' || to.path === '/init' || to.path === '/initukey') {
        next()
    } else {
        if (getStore('roleId') === '2' || getStore('roleId').includes('2')) {
            store.dispatch("sendPolicyFun").then(res => {
                console.log(res);
                next();
            });
        } else {
            next()
        }
    }
});

if (process.env.NODE_ENV === 'production') {
    if (window) {
        window.console.log = function () { };
    }
}



/* eslint-disable no-new */
// new Vue({
//     el: '#app',
//     router,
//     store,
//     components: {App},
//     template: '<App/>',
//     data: {
//         // 空的实例放到根组件下，所有的子组件都能调用
//         Bus: new Vue()
//     },
//     mounted() {
//         this.$store.dispatch("getCurrConfigFun").then(data => {
//             document.title = data.productName
//         })
//     }
// });

new Vue({
    router,
    store,
    render: h => h(App),
    data: {
        // 空的实例放到根组件下，所有的子组件都能调用
        Bus: new Vue()
    },
    mounted() {
        this.$store.dispatch("getCurrConfigFun").then(data => {
            document.title = data.productName
        })
    }
}).$mount('#app')

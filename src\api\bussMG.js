import API from './apiuri';
// import axios from "axios";
import {req, reqParams, noAuthreq, fileReq, reqPost, reqCommon, reqheaders, uploadReq, reqParamNoJson, reqFormData, reqGet} from './axiosFun';
import {getStore} from "@/utils/util";
// import systemMG from "@/api/systemMG";

const bussApi = API.bussApi;

const certImportApi = (params) => { return reqFormData("post", `/business/cert/uploadCert`, params) };

/**
 * 时间戳管理 API
 * */
// 查询列表
const timestampList = (params) => { return reqGet("get", `/timestamp/page`, params) };
// 删除
const deleteApi = (params) => { return reqheaders("post", `/timestamp/del`, params, {"Content-Type": "application/x-www-form-urlencoded;charset=utf-8"}) };
// 销毁
const destroyApi = (params) => { return reqheaders("post", `/timestamp/destroy`, params, {"Content-Type": "application/x-www-form-urlencoded;charset=utf-8"}) };
// 销毁
const backupApi = (params) => { return reqheaders("post", `/timestamp/backup`, params, {"Content-Type": "application/x-www-form-urlencoded;charset=utf-8"}) };
// 查询备份列表
const timestampCheckList = (params) => { return reqGet("get", `/timestamp/backup/page`, params) };
// 查询备份列表
const timestampImportApi = (params) => { return reqFormData("post", `/timestamp/backup/update`, params) };

export default {
  certImportApi,

  // 时间戳管理
  timestampList,
  deleteApi,
  destroyApi,
  backupApi,
  timestampCheckList,
  timestampImportApi,

  certList(params) {
    return reqParamNoJson("post", bussApi.certList, params);
  },
  certListByParam(params) {
    return reqParams("post", bussApi.listByParam, params);
  },
  queryById(id) {
    return req("get", bussApi.queryById + "?id=" + id)
  },
  queryHistoryById(params) {
    return reqParamNoJson("post", bussApi.queryHistoryById, params)
  },
  downloadCert(id) {
    // return reqheaders("post", "/business/app/del", param, {"Content-Type": "application/x-www-form-urlencoded;charset=utf-8"});
    let option = {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
        "token": "Bearer "+getStore('token')
      },
      responseType: 'blob'
    };
    return reqCommon("post", bussApi.dowloadCert + "?id=" + id, {id: id}, option)
  },
  downloadHistoryCert(id) {
    let option = {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
        "token": "Bearer "+getStore('token')
      },
      responseType: 'blob'
    };
    return reqCommon("post", bussApi.downloadHistoryCert + "?id=" + id, {id: id}, option)
  },
  bindApp(params) {
    return req("post", bussApi.bindApp, params);
  },
  bathDeleteCert(id) {
    let param = {ids: id};
    return reqheaders("post", bussApi.batchDel, param, {"Content-Type": "application/x-www-form-urlencoded;charset=utf-8"});
  },
  verifyDN(dn) {
    return reqheaders("post", bussApi.verify, {dn:dn},{"Content-Type": "application/x-www-form-urlencoded;charset=utf-8"});
  },
  editCert(param) {
    return reqFormData("post", bussApi.edit, param);
  },
  regCert(param) {
    return uploadReq("post", '/business/cert/addOrUpdateCert', param);
  },
  queryCertByApp(param) {
    return reqParamNoJson("post", bussApi.listAppReal, param);
  },
  unbindCert(certId){
    return reqParamNoJson("post", bussApi.unbindApp, {certId:certId});
  },
  createCertByP10(param){
    return reqFormData("post", "/business/cert/createCertByP10", param);
  }
};

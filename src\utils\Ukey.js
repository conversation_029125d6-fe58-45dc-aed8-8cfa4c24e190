import {testOpen, send, showRespResult} from "@/utils/ukeyWS/Ukey-WS";
import {Message} from "element-ui";
import store from "@/vuex/store";

const Base64 = {
    _keyStr: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",
    encode: function (e) {
        let t = "";
        let n, r, i, s, o, u, a;
        let f = 0;
        e = Base64._utf8_encode(e);
        while (f < e.length) {
            n = e.charCodeAt(f++);
            r = e.charCodeAt(f++);
            i = e.charCodeAt(f++);
            s = n >> 2;
            o = (n & 3) << 4 | r >> 4;
            u = (r & 15) << 2 | i >> 6;
            a = i & 63;
            if (isNaN(r)) {
                u = a = 64
            } else if (isNaN(i)) {
                a = 64
            }
            t = t + this._keyStr.charAt(s) + this._keyStr.charAt(o) + this._keyStr.charAt(u) + this._keyStr.charAt(a)
        }
        return t
    },
    decode: function (e) {
        let t = "";
        let n, r, i;
        let s, o, u, a;
        let f = 0;
        e = e.replace(/[^A-Za-z0-9+/=]/g, "");
        while (f < e.length) {
            s = this._keyStr.indexOf(e.charAt(f++));
            o = this._keyStr.indexOf(e.charAt(f++));
            u = this._keyStr.indexOf(e.charAt(f++));
            a = this._keyStr.indexOf(e.charAt(f++));
            n = s << 2 | o >> 4;
            r = (o & 15) << 4 | u >> 2;
            i = (u & 3) << 6 | a;
            t = t + String.fromCharCode(n);
            if (u != 64) {
                t = t + String.fromCharCode(r)
            }
            if (a != 64) {
                t = t + String.fromCharCode(i)
            }
        }
        t = Base64._utf8_decode(t);
        return t
    },
    _utf8_encode: function (e) {
        e = e.replace(/rn/g, "n");
        var t = "";
        for (var n = 0; n < e.length; n++) {
            var r = e.charCodeAt(n);
            if (r < 128) {
                t += String.fromCharCode(r)
            } else if (r > 127 && r < 2048) {
                t += String.fromCharCode(r >> 6 | 192);
                t += String.fromCharCode(r & 63 | 128)
            } else {
                t += String.fromCharCode(r >> 12 | 224);
                t += String.fromCharCode(r >> 6 & 63 | 128);
                t += String.fromCharCode(r & 63 | 128)
            }
        }
        return t
    },
    _utf8_decode: function (e) {
        let t = "";
        let n = 0;
        let r = 0;
        let c1 = 0;
        let c2 = 0;
        while (n < e.length) {
            r = e.charCodeAt(n);
            if (r < 128) {
                t += String.fromCharCode(r);
                n++
            } else if (r > 191 && r < 224) {
                c2 = e.charCodeAt(n + 1);
                t += String.fromCharCode((r & 31) << 6 | c2 & 63);
                n += 2
            } else {
                c2 = e.charCodeAt(n + 1);
                let c3 = e.charCodeAt(n + 2);
                t += String.fromCharCode((r & 15) << 12 | (c2 & 63) << 6 | c3 & 63);
                n += 3
            }
        }
        return t
    }
};


const APP = "application";
const CONTAINER = "container";
const RSA_CONTAINER = "rsa_container";
const FILE_NAME = 'kek';
const ADMIN_PIN = '12345678';

// 获取UKey状态
function CHECK_UKEY_STATE(callback) {
    send("1001", function (data) {
        let parse = JSON.parse(data);
        // if (parse.code !== 0) return Message.error(parse.msg || 'UKey服务异常！');
        // if (parse.data.deviceName.length === 0) return Message.error('UKey未插入！');
        callback(parse);
    });
}
function GET_KEY_STATE(callback) {
    send("1001", function (data) {
        let parse = JSON.parse(data);
        if (parse.code !== 0) return Message.error(parse.msg || 'UKey服务异常！');
        if (parse.data.deviceName.length === 0) return Message.error('UKey未插入！');
        callback(parse);
    });
}

// 登录UKey
const TEST_LOGIN = function (pinNode, deviceName, callback) {
    let sendJson = {
        deviceName: deviceName,
        app: APP,
        PIN: pinNode,
        userType: 1
    };
    let reqbody = JSON.stringify(sendJson);
    // send('2003' + reqbody, callback);
    send('2003' + reqbody, function (res) {
        // console.log(JSON.parse(res));
        const {code, data, msg} = JSON.parse(res);
        if (code === 0) return callback(data);
        if (code !== 0 && code !== 167772196) {
            Message({message: msg, type: 'error' , customClass: 'messageIndex'});
            return
        };
        if (data.retryCount === 0 && code !== 0) {
            Message({message: 'PIN码输入次数已达上限，请联系管理人员！',type: 'error' , customClass: 'messageIndex'});
            return 
        };
        if (code === 167772196) {
            Message({ message: 'UKey PIN码不正确,剩余次数为' + data.retryCount + '！！！' , type: 'error' , customClass: 'messageIndex'});
            return 
        };
        // console.log(data);
        
    });
};

// 登录UKey
const TEST_LOGINByApp = function (app, pinNode, deviceName, callback) {
  let sendJson = {
    deviceName: deviceName,
    app: app,
    PIN: pinNode,
    userType: 1
  };
  let reqbody = JSON.stringify(sendJson);
  // send('2003' + reqbody, callback);
  send('2003' + reqbody, function (res) {
    // console.log(JSON.parse(res));
    const {code, data, msg} = JSON.parse(res);
    if (code !== 0 && code !== 167772196) return Message.error(msg);
    // if (code === 167772196) return Message.error(msg);
    // console.log(code, data, msg);
    if (data.retryCount === 0 && code !== 0) return Message.error('PIN码输入次数已达上限，请联系管理人员！');
    if (code === 167772196) {
        // customClass: 'messageIndex'
        Message({
            message: 'UKey PIN码不正确,剩余次数为' + data.retryCount + '！！！',
            type: 'error',
            // duration: 0,
            customClass: 'messageIndex'
        });
        return
    } 
    // console.log(data);
    callback(data)
  });
};

function testGetKeyState(callback) {
    send("1001", function (data) {
        // console.log(data);
        //{"code":0,"data":{"deviceName":["UKCD2M2208020003"]},"msg":"成功"}
        let parse = JSON.parse(data);
        let stateStr = "";
        if (parse.data.deviceName.length > 0) {
            stateStr = "1";
        } else {
            stateStr = "0";
        }
        let json = {
            "state": stateStr
        };
        callback(json);
    });
}

const testGetKeyInfo = function (callback) {
    send("1001", function (data) {
        console.log(data);
        //{"code":0,"data":{"deviceName":["UKCD2M2208020003"]},"msg":"成功"}
        let parse = JSON.parse(data);
        let serialNumber = "";
        if (parse.data.deviceName.length > 0) {
            serialNumber = parse.data.deviceName[0];
        }
        let json = {
            "serialNumber": serialNumber
        };
        callback(json);
    });
};

// const testGetCSR = function (pinNode, dnNode, callback) {
//     if (pinNode.length == 0 || dnNode.length == 0) {
//         Message.error('pin和DN值不能为空');
//         return;
//     }
//     send("1001", res => {
//         let parse = JSON.parse(res);
//         if (parse.data.deviceName.length > 0) {
//             let deviceName = parse.data.deviceName[0];
//             //登录
//             testLogin(pinNode, deviceName, res => {
//                 let resDate = JSON.parse(res);
//                 if (resDate.code != 0) {
//                     showRespResult(resDate.code, ress => {
//                         console.log(ress);
//                         Message.error(ress)
//                     })
//                 } else {
//                     let sendJson = {
//                         "deviceName": deviceName,
//                         "app": APP,
//                         "container": CONTAINER,
//                         "bSM2Flag": true,
//                         "userPIN": pinNode,
//                         "dn": dnNode
//                     };
//                     send('5003' + JSON.stringify(sendJson), ress => {
//                         console.log(ress);
//                         let data = JSON.parse(ress);
//                         let json = {
//                             "errCode": data.code,
//                             "b64CertCSR": data.data.certCSR
//                         };
//                         console.log(json);
//                         callback(json);
//                     });
//                 }
//             });
//         } else {
//             Message.error('ukey未插入！');
//             return;
//         }
//     })
// };

const testGetCSR = function (pinNode, dnNode, callback) {
    if (pinNode.length === 0 || dnNode.length === 0) return Message.error('pin和DN值不能为空');
    GET_KEY_STATE(({data}) => {
        let deviceName = data.deviceName[0];
        //登录
        TEST_LOGIN(pinNode, deviceName, res => {
            console.log(res);
            let sendJson = {
                "deviceName": deviceName,
                "app": APP,
                "container": CONTAINER,
                "bSM2Flag": true,
                "userPIN": pinNode,
                "dn": dnNode
            };
            send('5003' + JSON.stringify(sendJson), ress => {
                let data = JSON.parse(ress);
                let json = {
                    "errCode": data.code,
                    "b64CertCSR": data.data.certCSR
                };
                callback(json);
            });
        });
    })
};

const testGetCSRByApp = function (app, container, pinNode, dnNode, callback) {
  if (pinNode.length === 0 || dnNode.length === 0) return Message.error('pin和DN值不能为空');
  GET_KEY_STATE(({data}) => {
    let deviceName = data.deviceName[0];
    //登录
    TEST_LOGINByApp(app, pinNode, deviceName, res => {
      console.log(res);
      let sendJson = {
        "deviceName": deviceName,
        "app": app,
        "container": container,
        "bSM2Flag": true,
        "userPIN": pinNode,
        "dn": dnNode
      };
      send('5003' + JSON.stringify(sendJson), ress => {
        let data = JSON.parse(ress);
        let json = {
          "errCode": data.code,
          "b64CertCSR": data.data.certCSR
        };
        callback(json);
      });
    });
  })
};


const testLogin = function (pinNode, deviceName, callback) {
    let sendJson = {
        "deviceName": deviceName,
        "app": APP,
        "PIN": pinNode,
        "userType": 1
    };
    let reqbody = JSON.stringify(sendJson);
    send('2003' + reqbody, callback);
};

const testImportCert = function (pinNode, certNode, callback) {
    if (pinNode.length === 0 || certNode.length === 0) return Message.error('pin和证书数据不能为空');
    GET_KEY_STATE(({data}) => {
        let deviceName = data.deviceName[0];
        //登录
        TEST_LOGIN(pinNode, deviceName, () => {
            let sendJson = {
                "deviceName": deviceName,
                "app": APP,
                "container": CONTAINER,
                "bSM2Flag": true,
                "bSignFlag": true,
                "cert": certNode
            };
            send('5001' + JSON.stringify(sendJson), ress => {
                let data = JSON.parse(ress);
                let json = {
                    "errCode": data.code
                };
                callback(json);
            });
        });
    })
};

const testImportDoubleCertByApp = function (app, container, pinNode, certSign, certEnc, pairKey, callback) {
    if (pinNode.length === 0 || certSign.length === 0 || certEnc.length === 0 || pairKey.length === 0) return Message.error('pin和证书数据不能为空');
    GET_KEY_STATE(({data}) => {
        let deviceName = data.deviceName[0];
        //登录
        TEST_LOGINByApp(app, pinNode, deviceName, () => {
            let sendJson = {
                "deviceName": deviceName,
                "app": app,
                "container": container,
                "bSM2Flag": true,
                "certSign": certSign,
                "certEnc": certEnc,
                "pairKey": pairKey
            };
            send('5101' + JSON.stringify(sendJson), ress => {
                let data = JSON.parse(ress);
                let json = {
                    "errCode": data.code
                };
                callback(json);
            });
        });
    })
};

function testExportCert(callback) {
    // let deviceName;
    GET_KEY_STATE(({data}) => {
        // let parse = JSON.parse(res);
        // if (parse.data.deviceName.length > 0) {
        let deviceName = data.deviceName[0];
        let sendJson = {
            "deviceName": deviceName,
            "app": APP,
            "container": CONTAINER,
            "bSM2Flag": true,
            "bSignFlag": true,
        };
        send('5002' + JSON.stringify(sendJson), res => {
            let data = JSON.parse(res);
            let json = {
                "errCode": data.code,
                "b64CertData": data.data.cert
            };
            callback(json);
        });
        // } else {
        //     Message.error('ukey未插入！');
        //     return;
        // }
    })
}

/**
 * 获取签名数据
 * */
function testSignData(pass, yuan, callback) {
    if (pass.length === 0 || yuan.length === 0) return Message.error('签名原文和pin不能为空');
    GET_KEY_STATE(({data}) => {
        let deviceName = data.deviceName[0];
        //登录
        TEST_LOGIN(pass, deviceName, res => {
            console.log(res);
            let sendJson = {
                "deviceName": deviceName,
                "app": APP,
                "container": CONTAINER,
                "algSign": 4,
                "userID": "",
                "data": yuan
            };
            send('6003' + JSON.stringify(sendJson), ress => {
                const {code, data, msg} = JSON.parse(ress);
                let json = {
                    errCode: code,
                    b64SignData: data.signData,
                    msg: msg
                };
                callback(json);
            });
        });
    })
}

/**
 * UKey 清空
 * */
const EMPTY_UKEY_FUN = (callback) => {
    GET_KEY_STATE(({data}) => {
        let deviceName = data.deviceName[0];
        //登录
        // TEST_LOGIN(pass, deviceName, res => {
            let sendJson = {
                deviceName: deviceName,
            };
            send('2001' + JSON.stringify(sendJson), ress => {
                const {code, data, msg} = JSON.parse(ress);
                let json = {
                    code: code,
                    data: data,
                    msg: msg
                };
                callback(json);
            });
        // });
    })
};

/**
 * UKey 初始化
 * */
const INIT_UKEY_FUN_ALL = (application, sm2_container, rsa_container, adminPin, pin, callback) => {
    EMPTY_UKEY_FUN(({data}) => {
        let deviceName = data.deviceName;
        let sendJson = {
            deviceName: deviceName,
            app: application,
            sm2Container: sm2_container,
            rsaContainer: rsa_container,
            sm2KeyPair: true,
            rsaKeyPair: true,
            // adminPIN: "88888888",
            adminPIN: adminPin,
            // userPIN: "88888888",
            userPIN: pin,
            retryCount: 10,
            accRights: 2,
            force: true
        };
        send('2002' + JSON.stringify(sendJson), ress => {
            const {code, data, msg} = JSON.parse(ress);
            let json = {
                code: code,
                data: data,
                msg: msg
            };
            callback(json);
        });
    });
};

/**
 * UKey 初始化
 * */
const INIT_UKEY_FUN = (PIN, callback) => {
    EMPTY_UKEY_FUN(({data}) => {
        let deviceName = data.deviceName;
        let sendJson = {
            deviceName: deviceName,
            app: APP,
            sm2Container: CONTAINER,
            rsaContainer: RSA_CONTAINER,
            sm2KeyPair: true,
            rsaKeyPair: true,
            // adminPIN: "88888888",
            adminPIN: ADMIN_PIN,
            // userPIN: "88888888",
            userPIN: PIN,
            retryCount: 10,
            accRights: 2,
            force: true
        };
        send('2002' + JSON.stringify(sendJson), ress => {
            const {code, data, msg} = JSON.parse(ress);
            let json = {
                code: code,
                data: data,
                msg: msg
            };
            callback(json);
        });
    });
};

/**
 * 导入密钥分量
 * */
function IMPORT_KEY_COMPONENT(fileData, callback) {
    GET_KEY_STATE(({data}) => {
        let deviceName = data.deviceName[0];
        let sendJson = {
            deviceName: deviceName,
            app: APP,
            container: CONTAINER,
            bSignFlag: true,
            fileName: FILE_NAME,
            data: fileData,
            bForce: true,
            userType: 1
        };
        send('8001' + JSON.stringify(sendJson), res => {
            let data = JSON.parse(res);
            let json = {
                code: data.code,
                msg: data.msg
            };
            callback(json);
        });
    })
}

/**
 * 导出密钥分量
 * */
function EXPORT_KEY_COMPONENT(callback) {
    GET_KEY_STATE(({data}) => {
        let deviceName = data.deviceName[0];
        let sendJson = {
            deviceName: deviceName, // UKey 序列号
            app: APP, // 应用名
            container: CONTAINER, // 容器名
            bSignFlag: true,
            fileName: FILE_NAME // 文件名(分量)
        };
        // 获取分量
        send('8002' + JSON.stringify(sendJson), res => {
            let data = JSON.parse(res);
            let json = {
                code: data.code,
                deviceName: data.data.deviceName,
                content: data.data.content
            };
            callback(json);
        });
    })
}

/**
 * 测试服务是否存在
 * */
function testWS(open,callback) {
  testOpen(open,callback);
}

export {
    testGetKeyState, testSignData, testExportCert, testGetKeyInfo, testGetCSR, testImportCert,
    CHECK_UKEY_STATE,
    GET_KEY_STATE,
    TEST_LOGIN,
    INIT_UKEY_FUN,
    IMPORT_KEY_COMPONENT,
    EXPORT_KEY_COMPONENT,
    INIT_UKEY_FUN_ALL,
    testImportDoubleCertByApp,
    testGetCSRByApp,
    testWS
}



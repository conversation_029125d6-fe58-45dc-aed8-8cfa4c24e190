import { url, get, req } from './axiosFun'

/**
 * 权限管理 API
 * */
// 初始化系统
// 清空UKEY /crypto/cleanUKey
const clearKey = () => {return req("post", url + "/crypto/cleanUKey")};
// 创建设备密钥
const creatDevKey = (params) => {return req("post", url + "/crypto/genDeviceKey", params)};
// 创建管理员
const creatManage = (params) => {return req("post", url + "/crypto/generate/manager", params)};
// "/crypto/manager/status",  //查看生成密码卡主管状态
const manageStatus = () => {return get("get", url + "/crypto/manager/status")};



// 获取管理员和操作员  0: 管理员  1: 操作员
const getDataList = () => {return get("get", url + `/svs/auth/queryPwdUser`)};
const supervisorList = () => {return get("get", url + `/svs/auth/querySupervisor`)};
// 获取操作员索引
const OperatorIndex = () => {return get("get", url + `/csm/getIDS`)};
// 添加密码用户
const addOperator = (params) => {return req("post", url + `/auth/addPwdUser`, params)};
// 删除操作员
const delOperator = (index) => {return req("delete", url + `/csm/admin/${index}`)};
// 编辑操作员
const editOperator = (params) => {return req("put", url + `/csm/adminInfo`, params)};


// 根据卡状态获取菜单
const statusMenus = (id) => {return get("get", url + `/role/menus/${id} `)};



// 网卡配置 /system/network/select
const networkListApi = () => {return req("get", url + "/group/tree")};
const setNetwork = (params) => {return req("post", url + "/group/tree", params)};
const setBond = (params) => {return req("post", url + "/group/tree", params)};
const getBondStatus = () => {return req("get", url + "/group/tree")};
const restartDevApi = () => {return req("post", url + "/group/tree")};

// 重启
const rebootDevice = () => {return req("post", url + "/csm/system/reboot")};
// 关机
const shutdownDevice = () => {return req("post", url + "/csm/system/shutdown")};


// 查询卡状态 1 正常    0 异常
const queryCardStatus = () => {return get("get", url + "/init/csmInit")};
// 重置
const resetApi = () => {return req("put", url + "/init/reset")};


export default {
    rebootDevice,
    shutdownDevice,

    // 初始化
    clearKey,
    creatDevKey,
    creatManage,
    manageStatus,
    // 获取管理员和操作员
    getDataList,
    supervisorList,
    OperatorIndex,
    addOperator,
    delOperator,
    editOperator,

    statusMenus,
    queryCardStatus,
    resetApi,
}
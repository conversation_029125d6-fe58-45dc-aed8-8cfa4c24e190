<template>
    <div id="momory" :style="{ height: '305px'}"></div>
</template>

<script>
    export default {
        name: 'memory',
        data() {
            return {
                cache: [],
                memory: [],
            }
        },
        props: ['cacheUsed',],
        methods: {
            drawLine() {
                let result = new Array();
                for (let i = 0; i < this.cacheUsed.length; i++) {
                    result.push(this.cacheUsed[i]);
                }
                // 基于准备好的dom，初始化echarts实例
                let momory = this.$echarts.init(document.getElementById('momory'));
                let colorList = ['#3aa1ff', '#36CBcb'];
                // 绘制图表
                momory.setOption({
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>{b} : {c} ({d}%)'
                    },
                    legend: {
                        orient: 'vertical',
                        left: 'left',
                        data: ['使用率', '总量']
                    },
                    series: [
                        {
                            name: '内存使用率',
                            itemStyle: {
                                normal: {
                                    color: function (params) {
                                        return colorList[params.dataIndex]
                                    }
                                }
                            },
                            type: 'pie',
                            radius: '60%',
                            center: ['50%', '50%'],

                            data: result,
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            }
                        }
                    ]
                    /* tooltip: {
                       trigger: 'axis',
                       axisPointer: {            // 坐标轴指示器，坐标轴触发有效
                         type: 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
                       }
                     },

                     grid: {
                       left: '3%',
                       right: '4%',
                       bottom: '3%',
                       containLabel: true
                     },
                     xAxis: [
                       {
                         type: 'category',
                         data: ['内存使用率', ]
                       }
                     ],
                     yAxis: [
                       {
                         type: 'value',
                         axisLabel: {
                           show: true,
                           interval: 'auto',
                           formatter: '{value} M'
                         },
                         show: true
                       }
                     ],
                     series: [
                       {
                         name: '缓存',
                         type: 'bar',
                         data: this.cache
                       },
                       {
                         name: '已使用',
                         type: 'bar',
                         data: this.memory
                       },
                     ]*/
                });
                momory.resize();
                window.addEventListener("resize", () => {
                    momory.resize()
                }, false);
            }
        },
        watch: {
            cacheUsed: function (newVal, oldVal) { //不能用箭头函数
                this.cache = [];
                this.cache.push(newVal);
                if (newVal != undefined) {
                    this.drawLine();
                }
            }
        },
    }
</script>

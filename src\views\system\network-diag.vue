<template>
    <div>
        <el-card class="box-card" shadow="always" style="padding-bottom: 10px">
            <h3>网络诊断工具</h3>
            <el-form :model="diagForm" :rules="rules" ref="diagForm" label-width="120px">
                <el-form-item label="诊断方式" prop="diagType ">
                    <el-col :span="3">
                        <el-select v-model="diagForm.diagType" placeholder="请选择" style="width: 300px" size="small">
                            <el-option v-for="item in diagOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                        </el-select>
                    </el-col>
                </el-form-item>

                <el-form-item label="目的主机" prop="prot">
                    <el-col :span="3">
                        <el-input size="small" v-model="diagForm.prot" placeholder="请输入正确的IP地址" style="width: 300px"></el-input>
                    </el-col>
                </el-form-item>

                <el-form-item label="检测结果" prop="result">
                    <el-col :span="3">
                        <el-input type="textarea" rows="8" v-model="diagForm.result" style="width: 600px"></el-input>
                    </el-col>
                </el-form-item>

                <el-form-item>
                    <el-col :span="1">
                        <el-button class="comBtn com_send_btn" size="small" @click="diagNetWork" :loading="loading">测试</el-button>
                    </el-col>
                </el-form-item>
            </el-form>
        </el-card>
    </div>
</template>

<script>
    // import systemMG from "@/api/systemMG";

    export default {
        name: "network-diag",
        data() {
            const validatorIp = (rule, value, callback) => {
                if (value !== "") {
                    let ip = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
                    let ipv6 = /(^(?:(?:(?:[0-9A-Fa-f]{1,4}:){7}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}:[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){5}:([0-9A-Fa-f]{1,4}:)?[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:){0,2}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,3}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,4}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){5}:((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:)?((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,2}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,3}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:):([0-9A-Fa-f]{1,4}:){0,4}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(::([0-9A-Fa-f]{1,4}:){0,5}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|([0-9A-Fa-f]{1,4}::([0-9A-Fa-f]{1,4}:){0,5}[0-9A-Fa-f]{1,4})|(::([0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){1,7}:))$)/;
                    if (!ip.test(value) && !ipv6.test(value)) {
                        callback(new Error('请输入正确的IP地址'))
                    } else {
                        callback()
                    }
                } else {
                    callback();
                }
            };

            return {
                loading: false,
                diagForm: {
                    diagType: 'ping',
                    prot: '',
                    result: ''
                },
                diagOptions: [
                    {value: 'ping', label: 'ping检测'},
                    {value: 'traceroute', label: 'traceroute检测'}
                ],
                rules: {
                    diagType: [{required: true, message: "请选择诊断方式", trigger: "blur"}],
                    prot: [
                        {required: true, message: "请输入正确的IP地址", trigger: "blur"},
                        {validator: validatorIp, trigger: 'blur'}
                    ]
                }
            };
        },
        methods: {
            diagNetWork() {
                this.$refs["diagForm"].validate((valid) => {
                    if (valid) {
                        this.loading = true;
                        this.$http.systemMG.diagNetWork(this.diagForm).then((res) => {
                                const code = res.code;
                                if (code == 0) {
                                    this.diagForm.result = res.data.result;
                                    this.loading = false;
                                } else {
                                    this.loading = false;
                                    // this.$message.error(res.msg);
                                }
                            },
                            error => {
                                this.loading = false;
                            })
                    }
                })
            }
        }
    }
</script>

<style scoped>

</style>

<template>
  <div class="container">
    <el-card v-show="showSearch" class="box-card" shadow="always" style="margin-bottom: 10px">
      <el-form :inline="true" :show-message="false" label-width="100px" class="user-search comForm"
               style="text-align: left">
        <el-form-item label="时间戳序列号：">
          <el-input size="small" v-model="queryParams.sn" clearable style="width: 260px"></el-input>
        </el-form-item>
        <el-form-item style="background-color: transparent;">
          <el-button class="comBtn com_send_btn" size="small" type="primary" icon="el-icon-search"
                     @click="searchHandle">搜索
          </el-button>
          <el-button class="comBtn com_reset_btn" size="small" type="primary" icon="el-icon-refresh"
                     @click="resetHandle">重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <el-card class="box-card" shadow="always" style="padding-bottom: 10px">
      <el-button class="comBtn com_send_btn" size="mini" icon="el-icon-plus" @click="backupImport">上传查看</el-button>
      <el-button-group style="float: right; margin-bottom: 10px">
        <el-button size="mini" icon="el-icon-search" @click="showAndSearch"></el-button>
        <el-button size="mini" icon="el-icon-refresh-left" @click="searchHandle"></el-button>
      </el-button-group>
      <div id="appList" style="padding-top: 10px">
        <createTable
          :tableData="tableData"
          :tableHeader="tableDataHeader"
          :isPage="isPage"
          :pageAttributes="{total: total, currentPage: queryParams.pageNo, pageSize: queryParams.pageSize}"
          :current-change="currentChange"
          :sizeChange="sizeChange"
        >
        </createTable>
      </div>
    </el-card>
    <el-dialog title="详情" :visible.sync="detailShow" width="650px" append-to-body :close-on-click-modal="false">
      <el-descriptions class="margin-top" :column="1" border :labelStyle="{'width': '130px'}">
        <el-descriptions-item label="时间戳序列号">{{ detailForm.timestampSn }}</el-descriptions-item>
        <el-descriptions-item label="时间戳签名值">{{ detailForm.timestampSignature }}</el-descriptions-item>
        <el-descriptions-item label="时间戳入库时间">{{ detailForm.saveTime }}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button class="comBtn com_reset_btn" size="small" @click="detailShow = false">关 闭</el-button>
      </div>
    </el-dialog>
    <el-dialog title="上传备份文件" :visible.sync="show" width="600px" append-to-body :close-on-click-modal="false"
               @close="closeCackupFileDia">
      <el-form ref="backForm" :model="backForm" :rules="rules" label-width="130px">
        <el-form-item prop="certFile">
          <el-upload
            class="upload-demo myUpload"
            action="#"
            accept=''
            :on-remove="backupFileRemove"
            :on-change="backupFileChange"
            :multiple='false'
            :auto-upload="false"
            :file-list="backForm.backupFileList">
            <el-button class="comBtn com_send_btn" size="small" type="primary">导入备份文件</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button class="comBtn com_reset_btn" @click="closeCackupFileDia">取 消</el-button>
        <el-button class="comBtn com_send_btn" type="primary" @click="submitFormFun">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import bussMg from '../../api/bussMG'
// import bussMG from "@/api/bussMG";

export default {
  data() {
    let _this = this;
    return {
      queryParams: {
        sn: '', // 时间戳序列号
        pageNo: 1,
        pageSize: 10,
      },
      rules: {
        backupFile: [
          {required: true, message: "请上传证书", trigger: "chang"}
        ]
      },
      total: 0,
      isPage: true,
      loading: null,
      showSearch: true,
      detailShow: false, // 详情
      show: false,
      detailForm: {
        timestampSn: '',
        timestampSignature: '',
        saveTime: ''
      }, // 详情
      backForm: {
        backupFile: null,
        backupFileList: []
      },
      tableData: [],
      tableDataHeader: [
        {label: "序号", type: "index", width: "50"},
        {label: "时间戳序列号", prop: "timestampSn", type: "normal"},
        {
          label: "时间戳签名值", prop: "timestampSignature", type: "normal",
          componet: function (h, props) {
            return h("span", [
              h("el-tooltip", {
                attrs: {
                  class: "sfsfdfdfsdfdfdsfdfsd",
                  effect: "dark",
                  content: props.row.timestampSignature,
                  placement: "top",
                },
                style: {
                  width: "600px",
                }
              })
            ]);
          }
        },
        {label: "时间戳入库时间", prop: "saveTime", type: "normal"},
        {
          label: "时间戳是否删除", prop: "del", type: "text_formatter", width: "90",
          formatter: function (value) {
            return value === 1 ? '是' : '否'
          }
        }
      ]
    }
  },
  methods: {
    openLoading() {
      let _this = this;
      _this.loading = _this.$loading({
        lock: true,
        text: '上传中，请稍等...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    },
    // 查询
    searchHandle() {
      this.getTimestampListFun()
    },
    // 重置
    resetHandle() {
      this.queryParams.sn = '';
      this.queryParams.pageNo = 1;
      this.queryParams.pageSize = 10;
      this.getTimestampListFun()
    },
    showAndSearch() {
      this.showSearch = !this.showSearch;
    },
    // 获取列表 bussMg
    getTimestampListFun() {
      this.$http.bussMg.timestampCheckList(this.queryParams).then(({code, data, msg, row}) => {
        console.log(code, data, msg, row);
        if (code !== 0) return this.$message.warning(msg);
        this.tableData = data;
        this.total = row
      })
    },
    // 查看详情
    viewDetailsHandle(row) {
      this.detailForm = row;
      this.detailShow = true;
    },
    // 分页操作
    sizeChange: function (param) {
      this.queryParams.pageSize = param;
      this.searchHandle();
    },
    currentChange: function (param) {
      this.queryParams.pageNo = param;
      this.searchHandle();
    },
    // 上传签名证书
    backupFileChange(file) {
      this.backForm.backupFileList = [];
      this.backForm.backupFile = file.raw;
      this.backForm.backupFileList.push(file);
      this.$refs["backForm"].validateField('certFile');
    },
    backupFileRemove() {
      this.backForm.backupFile = null;
      this.backForm.backupFileList = [];
    },
    backupImport() {
      this.show = true;
    },
    // 提交上传证书
    submitFormFun() {
      this.$refs["backForm"].validate((valid) => {
        if (valid) {
          this.openLoading();
          let params = new FormData();
          params.append("file", this.backForm.backupFile);
          this.$http.bussMG.timestampImportApi(params).then(({code, data, msg}) => {
            this.loading.close();
            if (code === 0) {
              this.getTimestampListFun()
              this.closeCackupFileDia();
              this.$message.success('上传成功!');
              this.$emit('parentData')
            } else if (code !== 500) {
              return this.$message.warning(msg)
            }
          }, (err) => {
            this.loading.close();
          })
        }
      })
    },
    closeCackupFileDia() {
      this.show = false;
      this.backupFileRemove();
      Object.assign(this.backForm, this.$options.data().backForm);
      this.$refs['backForm'].clearValidate();
    }
  },
  mounted() {
    this.getTimestampListFun()
  }
}
</script>
<style lang="less">
.el-tooltip__popper {
  max-width: 600px;
}
</style>

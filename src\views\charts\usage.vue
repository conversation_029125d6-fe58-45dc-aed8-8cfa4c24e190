<template>
    <div :style="{ height: '165px'}">
        <p><span class="msg_label">在线用户数：</span>{{onLineNum}}</p>
        <p><span class="msg_label">注册用户数：</span>{{register}}</p>
        <!--<p>组织机构数：{{orgNum}}</p>-->
        <p><span class="msg_label">注册应用数：</span>{{clientNum}}</p>
        <p><span class="msg_label">角色数：</span>{{roleNum}}</p>
    </div>
</template>

<script>
    // import systemMG from "@/api/systemMG";

    export default {
        name: "usage",
        data() {
            return {
                clientNum: '',
                onLineNum: '',
                orgNum: '',
                register: "",
                roleNum: '',
            }
        },
        created() {
            this.$http.systemMG.getOnlineNum().then((res) => {
                const code = res.code;
                if (code == "0") {
                    this.clientNum = res.data.clientNum;
                    this.onLineNum = res.data.onLineNum;
                    this.orgNum = res.data.orgNum;
                    this.register = res.data.register;
                    this.roleNum = res.data.roleNum;
                }
            })

        },
    }
</script>

<style lang="less" scoped>
    .msg_label {
        display: inline-block;
        /*width: 110px;*/
        text-align: right;
    }
    .w2{
        letter-spacing:3em; /*如果需要y个字两端对齐，则为(x-y)/(y-1),这里是（5-2）/(2-1)=3em */
        margin-right:-3em; /*同上*/
    }
    .w3{
        letter-spacing:1em;  /*如果需要y个字两端对齐，则为(x-y)/(y-1),这里是（5-3）/(3-1)=1em */
        margin-right:-1em;/*同上   2/2 */
    }
    .w4{
        letter-spacing:0.33em;  /*如果需要y个字两端对齐，则为(x-y)/(y-1),这里是（5-4）/(4-1)=0.33em */
        margin-right:-0.33em;/*同上   1/3 */
    }
</style>

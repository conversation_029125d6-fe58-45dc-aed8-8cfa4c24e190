import {req, reqCommon, reqheaders, reqParam<PERSON>o<PERSON><PERSON>, reqParams} from '@/api/axiosFun';
import Api from "@/api/apiuri";
import {getStore} from "@/utils/util";

const clusterApi = Api.clusterApi;

export default {
  getBatchNumber() {
    return reqParamNo<PERSON>son("get", clusterApi.getBatchNumber, "");
  },
  updateLoadBalancing() {
    return req("get", clusterApi.updateLoadBalancing, "");
  },
  nodeList(param) {
    return reqParamNo<PERSON>son("post", clusterApi.nodeList, param);
  },
  addNode(param) {
    return req("post", clusterApi.addNode, param);
  },
  editNode(param) {
    return req("post", clusterApi.editNode, param);
  },
  delNode(param) {
    return reqheaders("post", clusterApi.delNode, param, {"Content-Type": "application/x-www-form-urlencoded;charset=utf-8"});
  },
  downloadNodeCert(id) {
    let option = {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
        "token": "Bearer " + getStore('token')
      },
      responseType: 'blob'
    }
    return reqCommon("post", clusterApi.downloadNodeCert + "?id=" + id, {id: id}, option)
  },
  linkNode(param) {
    return reqheaders("post", clusterApi.linkNode, param, {"Content-Type": "application/x-www-form-urlencoded;charset=utf-8"});
  },
  unLinkNode(param) {
    return reqheaders("post", clusterApi.unLinkNode, param, {"Content-Type": "application/x-www-form-urlencoded;charset=utf-8"});
  },
  getLogConfig(param) {
    return reqParamNoJson("post", clusterApi.getLogConfig, param);
  },
  setLogConfig(param) {
    return req("post", clusterApi.setLogConfig, param);
  },
  changeGoStatus(param) {
    return reqParamNoJson("post", clusterApi.changeGoStatus, param);
  },
  //查询全部集群
  listManage() {
    return req("get", clusterApi.manageList, "");
  },
  //分页查询集群
  pageManage(param) {
    return reqParams("get", clusterApi.managePage, param);
  },
  //新增集群
  insertManage(param) {
    return req("post", clusterApi.manageInsert, param);
  },
  //编辑集群
  editManage(param) {
    return req("put", clusterApi.manageEdit, param);
  },
  //删除集群
  deleteManage(id) {
    return req("delete", clusterApi.manageDelete + "/" + id);
  },
  //校验端口是否存在
  verifyPort(port) {
    return req("get", clusterApi.managePortVerify + "/" + port);
  }
}

import {getErrMsg} from "@/utils/ukeyWS/err";
import {datas} from "@/utils/ukeyWS/Ukey-WS";

var SAR_REQPARAMERR = 0x0B000090 //"请就报文错误";//json格式错误，缺少参数项，参数项的数据格式错误，不可为空参数为空
var SAR_REQUNSUPPORTED = 0x0B000091 //"不支持的请求";

const proRespMsg = function proRespMsg(respMsg) {
    if (respMsg.length < 4) {
        return -1;
    }
    debugger
    var reqCode = respMsg.substring(0, 4);
    var msg = respMsg.substring(4, respMsg.length);

    var json = JSON.parse(msg)
    if (isValidResp(json.code) == false) {
        // alert("请求报文异常 resp code:" + json.code);
        showRespResult(json.code);
        return -1;
    }
    // showRespResult(json.code);
    datas[reqCode](JSON.parse(msg));
    /*switch (parseInt(reqCode)) {
        case 1001: //获取UKey列表
            parseRespUKeyList(json);
            break;
        case 1002: // UKey插拔事件
            parseRespUkeyEvent(json);
            break;
        case 1003: //获取UKey状态
            break;
        case 1004: //获取UKey信息
            parseRespUKeyInfo(json);
            break;
        case 2001: //清空
            parseRespClear(json);
            break;
        case 2002: //初始化
            parseRespInitUKey(json);
            break;
        case 2003: //登录
            parseRespLogin(json);
            break;
        case 2004: //修改PIN码
            parseRespChgPIN(json);
            break;
        case 2005: //获取PIN信息
            parseRespPinInfo(json);
            break;
        case 4001: //创建文件
            parseRespCreateFile(json);
            break;
        case 4002: //写入文件
            parseRespWriteFile(json);
            break;
        case 4003: //读取文件
            parseRespReadFile(json);
            break;
        case 4004: //删除文件
            parseRespDelFile(json);
            break;
        case 4005: //枚举文件
            parseRespEnumFile(json);
        case 4101: //保存文件
            parseRespSaveFile(json);
            break;
        case 4102: //读取整个文件
            parseRespReadAllFile(json);
            break;
        case 5001: //导入证书
            parseRespImportCert(json);
            break;
        case 5002: //导出证书
            parseRespExportCert(json);
            break;
        case 5003: //生成CSR
            parseRespCertCSR(json);
            break;
        case 6001: //生成公私钥对
            parseRespGenKeyPair(json);
            break;
        case 6002: //获取随机数
            parseRespGenRandom(json);
            break;
        case 6003: //数据签名
            parseRespSign(json);
            break;
        case 6004: //验签
            parseRespVerify(json);
            break;
        case 9001: //心跳
            break;
        default:
            break;
    }*/
    Reflect.deleteProperty(datas, reqCode)

}

function isValidResp(code) {
    if (code == SAR_REQPARAMERR) { //报文格式错误
        alert("报文格式错误");
        return false;

    } else if (code == SAR_REQUNSUPPORTED) { //不支持的请求
        alert("不支持的请求")
        return false;
    }

    return true;
}


function isValidString(str) {
    if (str == "" || str == undefined) {
        return false;
    }

    return true;
}

function splitString(data) {
    var res = data.split("|");
    // alert(res);
    return res;
}

//获取ukey列表 1001
function parseRespUKeyList(json) {
    //解析ukey、列表
    // var count = json.data.deviceName.length;
    for (let index = 0; index < json.data.deviceName.length; index++) {
        const element = json.data.deviceName[index];
        var array = splitString(element);

        if (isSelectOptionExist(array[0]) == false) {
            if (isValidString(array[1]) == true) {
                selectAddOption("ukeyList", array[0], array[1]);
            } else {
                selectAddOption("ukeyList", array[0], array[0]);
            }
        }
    }
}

//ukey 事件监测   1002
function parseRespUkeyEvent(json) {

    if (isValidString(json.data.deviceName) == false) {
        return;
    }

    var array = splitString(json.data.deviceName);
    // var deviceName = json.data.deviceName;
    var event = json.data.event;
    var deviceName = array[0];
    var lable = array[1];
    if (isValidString(lable) == false) {
        lable = deviceName;
    }

    switch (parseInt(event)) {
        case 1: //ukey插入事件
            selectDelOption("ukeyList", deviceName);
            selectAddOption("ukeyList", deviceName, lable);
            break;
        case 2: //ukey 拔出事件
            selectDelOption("ukeyList", deviceName);
            break;
        default:
            break;
    }

}
//获取ukey状态 1003

//获取UKey信息 1004
function parseRespUKeyInfo(json) {

    var algAsymCap = "0x0" + json.data.algAsymCap.toString(16).toUpperCase();
    var algHashCap = "0x0" + json.data.algHashCap.toString(16).toUpperCase();
    var algSymCap = "0x0" + json.data.algSymCap.toString(16).toUpperCase();

    setTextData("lable", json.data.label);
    setTextData("serialNumber", json.data.serialNumber);
    setTextData("manufacturer", json.data.manufacturer);
    setTextData("issuer", json.data.vissuer);
    setTextData("freeSpace", json.data.freeSpace);
    setTextData("totalSpace", json.data.totalSpace);
    setTextData("version", json.data.version.major + "." + json.data.version.minor);
    setTextData("firmwareVersion", json.data.firmwareVersion.major + "." + json.data.firmwareVersion.minor);
    setTextData("hwVersion", json.data.hwVersion.major + "." + json.data.hwVersion.minor);
    setTextData("algAsymCap", algAsymCap);
    setTextData("algSymCap", algSymCap);
    setTextData("algHashCap", algHashCap);
}

//清空 2001
function parseRespClear(json) {

}

//初始化 2002
function parseRespInitUKey(json) {
    setTextData("initUkey_sm2pubkey", json.data.sm2PubKey);
    setTextData("initUkey_rsapubkey", json.data.rsaPubKey);
}

//登录 2003
function parseRespLogin(json) {
    setTextData("login_retryCount", json.data.retryCount);

}

//修改PIN 2004
function parseRespChgPIN(json) {
    setTextData("chgPIN_retryCount", json.data.retryCount);
}

//获取PIN信息  2005
function parseRespPinInfo(json) {
    setTextData("maxRetryCount", json.data.maxRetryCount);
    setTextData("remainRetryCount", json.data.remainRetryCount);

    var info = "否";
    if (json.data.isDefaultPin == true) {
        info = "是";
    }
    setTextData("isDefaultPin", info);
}

//创建文件  4001
function parseRespCreateFile(json) {

}

//写文件 4002
function parseRespWriteFile(json) {


}
//读取文件 4003
function parseRespReadFile(json) {
    setTextData("readfile_data", json.data.content);
}

//删除文件 4004
function parseRespDelFile(json) {

}

//枚举文件  4005
function parseRespEnumFile(json) {
    var fileList = "";
    for (let index = 0; index < json.data.files.length; index++) {
        const file = json.data.files[index];

        fileList += file + " ";
    }
    setTextData("enumFile_data", fileList);
}

//保存到文件 4101
function parseRespSaveFile(json) {

}

//读取整个文件内容 4102
function parseRespReadAllFile(json) {
    var str = codeHandler.decode(json.data.content, 'base64');
    setTextData("readAllfile_data", str);
}

//导入证书 5001
function parseRespImportCert(json) {

}

//导出证书 5002
function parseRespExportCert(json) {

    setTextData("exportCert_data", json.data.cert);
}

//生成CSR 5003
function parseRespCertCSR(json) {
    setTextData("certCSR_data", json.data.certCSR);
}

//生成密钥对 6001
function parseRespGenKeyPair(json) {
    setTextData("pairKey_data", json.data.pubKey);
}

//生成随机数 6002
function parseRespGenRandom(json) {
    setTextData("random_data", json.data.random);
}

//签名 6003
function parseRespSign(json) {
    setTextData("sign_rst", json.data.signData);
}

//验签 6004
function parseRespVerify(json) {

}


//显示操作结构
function showRespResult(code) {
    var rst_code = "0x0" + code.toString(16).toUpperCase();
    var rst_msg = getErrMsg(code);
    console.log("code:{},msg{}", rst_code, rst_msg)
}

export {
    proRespMsg
}

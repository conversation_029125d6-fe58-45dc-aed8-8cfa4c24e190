// 授权API
const userApi = {
  random: '/login/random',  //获取随机数
  login: '/login/auth',    //用户名密码登录
  loginCert: '/login/login-cert',  //证书登录及双因子登录
  loginOut: '/login/login-out',  //退出登录
  analysisCert: '/login/analysis',  //解析证书
  addUser: '/account/insert',     //添加管理员
  deleteUser: '/account/delete',  //删除管理员
  updateUser: '/account/update',  //编辑管理员
  listUser: '/account/list',      //获取管理员列表
  pageUser: '/account/page',      //分页查询管理员列表
  uploadCert: '/account/upload/cert',   //上传管理员证书
  searchCert: '/account/search/cert',  //查询管理员绑定的证书
  rePassword: '/account/edit/password',  //修改密码
  resetPassword: '/account/reset/password',  //重置密码
  verifyDN: '/account/verify',  //验证dn
};


// 初始化API
const initApi = {
  initEth: '/init/eth',  // 初始化设置网卡
  queryInit: '/init/queryInit',  // 查询初始化状态
  setStatus: '/init/setStatus',  // 设置初始化状态
  initAccount: '/init/account',  // 初始化管理员
  initStatus: '/init/status',   // 获取初始化状态
  sslCert: '/init/initSSL',  //初始化设备证书
  generateCsr: '/init/generateCsr',  //生成证书请求
  deleteAccount: '/init/delete',  //删除管理员用户
};


// 角色API
const roleApi = {
  insertRole: '/role/insert',  //添加角色
  updateRole: '/role/update',  //编辑角色
  deleteRole: '/role/delete',  //删除角色
  pageRole: '/role/page',      //分页显示角色
  listRole: '/role/list',      //获取所有的角色
  listMenus: '/role/menus',    //获取角色下所有的url
  setRoleMenu: '/role/menus',    //设置角色菜单
};

// 管理员策略API
const strategyApi = {
  insertStrategy: '/strategy/insert',  //添加管理员策略
  updateStrategy: '/strategy/update',  //编辑管理员策略
  deleteStrategy: '/strategy/delete',  //删除管理员策略
  listStrategy: '/strategy/list',      //获取管理员策略列表
  pageStrategy: '/strategy/page',      //管理员策略分页列表
  enableStrategy: '/strategy/enable',  //开启管理员策略
  unEnableStrategy: '/strategy/unenable',  //关闭管理员策略
  queryEnableStrategy: '/strategy/query/enable',   //查询启用的策略
};

// 公共模块API
const systemApi = {
  networkList: '/system/network/select', // 获取网口列表
  setNetwork: '/system/setNetwork', // 设置网络
  setDns: '/system/network/setDns', // 设置DNS
  getDns: '/system/network/getDns', // 获取DNS
  setBond: '/system/network/bond', // 设置 bond
  getBondStatus: '/system/network/bond/status', // 设置 bond
  restartDev: '/system/network/reboot', // 重启服务器
  unBond: '/system/network/unbond', // 设置 bond
  bindableList: '/system/network/bindableList', // 查看可绑定网口
  setDefaultGw: '/system/network/setDefaultGw',  // 设置默认网关所在网口(管理口)

  queryEth: '/net/queryNetCardInfo',   // 查看全部网卡信息
  setNetCardStatus: '/net/setNetCardStatus',  //设置网卡状态
  setDefGW: '/net/setDefGW',    // 设置默认网关
  delEth: '/net/delIPAddr',     // 删除设置ip
  queryDefGW: '/net/queryDefGW',   //查询默认网关
  queryNetwork: '/net/queryNetwork',  //查询空白网卡
  // setDateTime: '/sys/setDateTime',   //设置系统时间
  queryDateTime: '/sys/queryDateTime',   //查询系统时间
  // setNTP: '/sys/NTPsetDateTime',   //设置时间服务器
  queryNtp: '/system/queryNtp',   //查询时间服务器
  quertCurrentTime: '/system/queryTime',   //获取当前时间
  diagNetWork: '/system/diag',   //网络诊断
  allEth: '/system/allEth',  //查询所有网口
  setEth: '/system/setEth',    //设置IP地址
  onlineNum: '/system/onlineNum',  //系统使用情况
  systemInfo: '/system/systemInfo',  //系统基本配置
  projectInfo: '/system/projectInfo',  //系统基本信息
  setTime: '/system/setTime',  //设置系统时间
  getWsIp: '/system/getWsIp',  //获取webSocket地址
  queryPageAudit: '/audit/pageAudit', //分页查询日志
  exportAuditExcel: '/audit/exportAudit', //分页查询日志
  rebootNginx: '/system/rebootNginx', //重启nginx服务
  openFirewall: '/system/setFirewall', //设置防火墙
  shutdown: '/system/shutdown',  //关机
  reboot: '/system/reboot',  //重启
  rebootService: '/system/rebootService',   //重启服务
  addDns: '/system/addDns',  //添加DNS
  updateDns: '/system/updateDns',  //编辑DNS
  deleteDns: '/system/deleteDns',  //删除DNS
  allDns: '/system/allDns',  //DNS列表
  selfCheck: '/system/selfCheck',  //DNS列表
  verifyNtp: '/system/verifyNtp',  //校验Ntp地址
  verifyDN: '/system/verifyDN',  //校验DN地址


  sslCertList: '/ssl/cert/list',  // 设备证书列表
  uploadSslCert: '/ssl/cert/ssl',  // 更新设备证书
  downloadCert: '/ssl/cert/download',  // 更新设备证书
  enableSslCert: '/ssl/cert/setSsl',  // 启用设备证书
  clearSslCert: '/ssl/cert/clear',  // 启用设备证书

  queryGroup: '/Availability/queryGroup',  //查询热备组
  addGroup: '/Availability/addGroup',   //添加热备组
  startGroup: '/Availability/startGroup',   //开启热备组
  manual: '/Availability/manual',   //手动同步
  queryStatus: '/Availability/queryStatus',   //监控状态

  appKeyInfo: '/system/keySync/appKeyInfo',
  turnOnReceiveAppKey: '/system/keySync/receiveAppKey',
  turnOffReceiveAppKey: '/system/keySync/unReceiveAppKey',
  syncAppKey: '/system/keySync/syncAppKeyTo',
  testConnection: '/system/keySync/testConnection',
  listKeyExportInfo: '/system/keySync/exportInfo',

  getversion: '/sys/getversion'
};

const bussApi = {
  certList: '/business/cert/list',//业务证书列表
  listByParam: '/business/cert/listByParam',//业务证书列表
  listAppReal: '/business/cert/listAppReal',//应用页面证书管理
  addCert: '/business/cert/addOrUpdateCert',//添加证书
  queryById: '/business/cert/queryById',//查看详情
  queryHistoryById: '/business/cert/queryHistoryById',//查看历史详情
  edit: '/business/cert/edit',//编辑信息
  batchDel: '/business/cert/del',//批量删除
  dowloadCert: '/business/cert/dowloadCert',//下载证书
  downloadHistoryCert: '/business/cert/downloadHistoryCert',//下载证书
  bindApp: '/business/cert/bindApp',//绑定业务证书
  unbindApp: '/business/cert/unbindCert',//绑定业务证书
  verify:'/business/cert/verify',//验证dn有效性

  baseCa: '/business/ca',
  caList: '/business/ca/list',//ca
  caAdd: '/business/ca/add',//ca 添加
  caDel: '/business/ca/del/',//批量删除
  caUpdate: '/business/ca/update',
  cabatchDel: '/business/ca/batchDel',
  caNameCheck:'/business/ca/checkName',

  chainList: '/business/ca/chainList',
  chainEdit: '/business/ca/chain/edit',
  queryCertChainById: '/business/ca/chain/',
  chainDelete: '/business/ca/deleteChain/',
  chainDownLoad: '/business/ca/chain/downLoad',

  crlConfig: '/business/ca/configCrl',
  queryCrlConfigByCaId: '/business/ca/queryCrlConfigByCaId',
  configCertChain: '/business/ca/configCertChain',
  queryCertSn: '/business/ca/crl/',
  fileDelete: '/business/ca/deleteFile',
  testConnetcion: '/business/ca/testConnetcion'
};


const whiteListApi = {
  list: "/business/white/list",
  del: "/business/white/del",
  batchDel: "/business/white/batchDel",
  queryById: "/business/white/queryById",
  edit: "/business/white/edit",
  add: "/business/white/add"
};


const svsServerApi = {
  list: "/business/svs/list",
  queryById: "/business/svs/queryById",
  edit: "/business/svs/update",
  changeStatus: "/business/svs/changeStatus"
};


const keyApi = {
  list: "/business/key/list",
  del: "/business/key/del",
  init:"/business/key/init",
  clearKey:"/business/key/clearKey",
  generateSignKey:"/business/key/generateSignKey",
  generateMulKey:"/business/key/generateMulKey",
  keyUnUseInfo:"/business/key/keyUnUseInfo",
  synCard:"/business/key/synCard",
  getKeyIndex:"/business/key/getKeyIndex",
  getKeyManageType:"/business/key/getKayManageType",
};

const extKeyApi = {
  list: "/business/ext/key/list",
  del: "/business/ext/key/del",
  clearKey:"/business/ext/key/clearKey",
  generateSignKey:"/business/ext/key/generateSignKey",
  generateMulKey:"/business/ext/key/generateMulKey",
  getMaxCount:"/business/ext/key/getMaxCount",
  getCurrentCount: "/business/ext/key/currentCount"
};

const backupApi = {
  list: "/backup/list",
  del: "/backup/del",
  batchDel: "/backup/batchDel",
  download: "/backup/download",
  backup: "/backup/backup",
  reduce: "/backup/reduce",
  upload: "/backup/upload",
  getBackUpInfo: "/backup/getBackUpInfo"
};


const configApi = {
  list: "/config/list",
  add: "/config/add",
  queryById: "/config/queryById",
  edit: "/config/edit",
  deleteById: "/config/deleteById",
  getWebSocketAddress:"/config/getWebSocketAddress",
};

const strategyConfigApi = {
  distribution: "/strategy/distribution"
};

const shellApi = {
  restartServer: "/shell/restartServer"
};

const checkApi = {
  random: "/service/check/random",    //获取随机数
  sm2Encryption: "/service/check/sm2Encryption",    //sm2加密
  sm2Decryption: "/service/check/sm2Decryption",    //sm2解密
  sm2SignaturePre: "/service/check/sm2SignaturePre",    //sm2签名预处理
  sm2Signature: "/service/check/sm2Signature",    //sm2签名
  sm2VerifyPre: "/service/check/sm2VerifyPre",    //sm2验签预处理
  sm2Verify: "/service/check/sm2Verify",    //sm2验签
  sm3Hash1: "/service/check/sm3Hash1",    //sm3摘要
  sm3Hash2: "/service/check/sm3Hash2",    //sm3摘要
  sm4ECBEncryption: "/service/check/sm4ECBEncryption",    //SM4（ECB）加密
  sm4ECBDecryption: "/service/check/sm4ECBDecryption",    //SM4（ECB）解密
  sm4CBCEncryption: "/service/check/sm4CBCEncryption",    //SM4（CBC）加密
  sm4CBCDecryption: "/service/check/sm4CBCDecryption",    //SM4（CBC）解密
  sm4OFBEncryption: "/service/check/sm4OFBEncryption",    //SM4（OFB）加密
  sm4OFBDecryption: "/service/check/sm4OFBDecryption",    //SM4（OFB）解密
  sm4MAC: "/service/check/sm4MAC",    //CBC-MAC(SM4)
  checkPage: "/service/check/page",    //分页查询自检记录
  generateSm2: "/service/check/generateSm2Cert",    //生成SM2证书
  generateSm4: "/service/check/generateSm4Cert",    //生成SM4证书
};

const oamApi = {
  rootLevel: '/logback/root/level',   //获取root日志级别
  loggerList: '/logback/logger/list',   //获取logger列表
  changeLevel: "/logback/changeLevel",    //设置root级别
  loggerLevel: "/logback/logger/level",    //单独设置logger级别
  loggerDelete:"/logback/logger/del",
  downloadLog: "/logback/download/log",    //下载日志
  downloadGoLog: "/logback/download/go/log",    //下载日志


  cryptoCardSelect: "/crypto/select",   //密码卡状态查询
  cryptoCardLogin: "/crypto/login",    //密码卡登录
  cryptoCardLoginOut: "/crypto/loginOut",    //密码卡登出
  cryptoCardStatus: "/crypto/status",    //查看密码卡状态
  cryptoManagerStatus: "/crypto/manager/status",  //查看生成密码卡主管状态
  generateManager: "/crypto/generate/manager",  //生成密码卡主管用户
  generateUser: "/crypto/generate/user",  //生成密码卡操作员用户
  cleanUKey: "/crypto/cleanUKey",  //清空Ukey
  genDeviceKey: "/crypto/genDeviceKey",  //清空Ukey


  upgradeUrl: "/common/upgrade/getUpgradeInfo",  // 系统升级 列表
  // updateUrl: "/common/upgrade/saveUpgradeInfo",  // 系统升级 升级
  updateUrl: "/common/upgrade/upload",//系统升级 上传升级包
  applyCert: "/csm/sys/applyCert",  //生成设备证书申请
  importSysCertApply: "/csm/sys/import/sysCertApply",  //一键导入设备证书
  deviceInfo: "/csm/sys/deviceInfo",   //获取设备信息
  restartDevMg: "/csm/sys/restartDevMg",   //重启服务
  submitApi: "/csm/sys/set/devInfo", //设置本地设备ID
  goLevel: "/logback/go/level",   //获取go日志级别
  changeGoLevel: "/logback/go/changeLevel",   //获取go日志级别
};

const logConfApi = {
  logConfigure: "/logConf/sys/category",   //日志配置
  setLogConfigure: "/logServerManage/saveConfiguration",   //日志配置
  cleanupConfigure: "/logServerManage/cleanup/config",   //日志配置
};

const licenseApi = {
  getAbility: "/sys/license/ability",
  getLicense: "/sys/license/appInfo",
  setLicense: "/licenseManage/importLicense",
  getLicenseParam: "/sys/license/params"
};

const clusterApi = {
  getBatchNumber: "/cluster/getBatchNumber",
  updateLoadBalancing: "/cluster/updateLoadBalancing",
  nodeList: "/cluster/node/list",
  addNode: "/cluster/node/add",
  editNode: "/cluster/node/edit",
  delNode: "/cluster/node/del",
  downloadNodeCert: "/cluster/node/downloadCert",
  linkNode: "/cluster/node/link",
  unLinkNode: "/cluster/node/unLink",
  getLogConfig: "/cluster/node/getLogConfig",
  setLogConfig: "/cluster/node/setLogConfig",
  changeGoStatus: "/cluster/node/changeGoStatus",
  manageList: "/cluster/manage/all",  //全部集群
  managePage: "/cluster/manage/page",  //分页查询集群
  manageInsert: "/cluster/manage/insert",  //新增集群
  manageEdit: "/cluster/manage/edit",  //编辑集群
  manageDelete: "/cluster/manage/delete",  //删除集群
  managePortVerify: "/cluster/manage/verify",  //集群端口校验
};

const nodeInfoMGApi = {
  nodeInfo: "/nodeManager/self/getNodeInfo",
  changeGoStatus: "/nodeManager/self/changeGoStatus",
  syncBatch: "/nodeManager/self/changeDistribution",
  linkReply: "/nodeManager/self/linkReply",
  unLinkManager: "/nodeManager/self/unLinkManager"
};

const alarmInfoApi = {
  setAlarmInfo: "/alarm/info/setAlarmInfo",    //设置告警配置
  getAlarmInfo: "/alarm/info/getAlarmInfo",    //获取告警配置
  bussinessAlarm: "/alarm/info/bussinessAlarm",    //设置业务告警配置
  getbussinessAlarm: "/alarm/info/bussinessAlarm",    //获取业务告警配置
  setAlarmStatus: "/alarm/info/setAlarmStatus",    //设置告警状态
  setNoticeType: "/alarm/info/setNoticeType",    //设置通知管理
  configure: "/alarm/info/configure",    //获取告警配置
  changeAlarmStatus: '/alarm/info/status/change',   //修改告警启用状态
  alarmStatus: '/alarm/info/status',   //获取告警状态
  setEmailAccount: '/alarm/info/email/account',    //设置邮箱用户
  setEmailServer: '/alarm/info/email/server',   //设置告警服务器
  getEmailInfo: '/alarm/info/email/info',    //获取邮箱配置
  editNoticeModel: '/alarm/info/model/edit',    //修改告警模板
  pageNoticeModel: '/alarm/info/model/page',    //分页查询告警模板
  pageAlarmRecord: '/alarm/info/record',    //分页查询告警记录
  getAlarmType: '/alarm/info/alarmType',   //获取告警类型
  confirmAlarmRecord: '/alarm/info/confirmAlarm',  //确认告警记录
};

const symmetryApi = {
  symmetryKeysMaxCount: "/symmetry/key/maxCount",     //对称密钥最大条数
  pageSymmetryKey: "/symmetry/key/page",     //分页查询对称密钥
  addSymmetryKey: "/symmetry/key/add",      //新增对称密钥
  batchAddSymmetryKey: "/symmetry/key/add/batch",   //批量新增对称密钥
  clearSymmetryKey: "/symmetry/key/clear",     //清空对称密钥
  unUsedIndex: "/symmetry/key/unUsed",    //获取未使用的对称密钥索引列表
  checkSymmetryKeyUpdate: "/symmetry/key/check/update",    //检查license更新
}


const timeStampApi = {
  list:"/timestamp/list",
  cerList:"/timestamp/cer/list",
  bindCert:"/timestamp/bind/cert",
  unBindCert:"/timestamp/unbind/cert"
}

const menuApi = {
  pageMenu: "/menu/page",   //分页查询菜单列表
  pageParentList: "/menu/page/parent",   //页面父级列表
  menuParent: "/menu/menuList",   //获取根菜单列表
  updateMenu: "/menu/update",   //编辑菜单
  insertMenu: "/menu/insert",   //新增菜单
  deleteMenu: "/menu/delete",   //删除菜单
  treeMenu: "/menu/tree",   //获取菜单树
  roleMenuIds: "/menu/menuIds",   //角色菜单ids

}

const firewalldApi = {
  getList: "/firewalld/list",   //查询列表
  add: "/firewalld/add",
  addOneTime: "/firewalld/addOneTime",
  del: "/firewalld/del",
}

export default {
    userApi,
    roleApi,
    strategyApi,
    systemApi,
    initApi,
    bussApi,
    keyApi,
    extKeyApi,
    backupApi,
    whiteListApi,
    svsServerApi,
    strategyConfigApi,
    configApi,
    shellApi,
    checkApi,
    oamApi,
    logConfApi,
    licenseApi,
    clusterApi,
    nodeInfoMGApi,
    alarmInfoApi,
    symmetryApi,
    timeStampApi,
    menuApi,
    firewalldApi,
}

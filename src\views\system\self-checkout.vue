<template>
  <el-card class="box-card" shadow="always" style="padding-bottom: 10px">
    <el-button class="comBtn com_send_btn" size="mini" style="margin-bottom: 10px" @click="startChecking" :loading="loading">手动检测</el-button>
    <el-card>
      <el-descriptions title="系统自检" :column="1">
      <!--<el-descriptions title="" :column="1">-->
        <el-descriptions-item label="系统服务(所有服务) ">
          <el-tag size="small" v-text="serviceStatus" :color="getColor(serviceStatus)"/>
        </el-descriptions-item>
        <el-descriptions-item label="数据存储 ">
          <el-tag size="small" v-text="dbStatus" :color="getColor(dbStatus)"/>
        </el-descriptions-item>
        <el-descriptions-item label="证书检测 ">
          <el-tag size="small" v-text="certStatus" :color="getColor(certStatus)"/>
        </el-descriptions-item>
        <el-descriptions-item label="算法检测 (密码算法正确性检测、随机数发生器检测、存储密钥和数据完整性检测) ">
          <!--<el-tag size="small" v-text="sm2Status"/>-->
        </el-descriptions-item>
        <el-descriptions-item label=" - 随机数 ">
          <el-tag size="small" v-text="rndStatus" :color="getColor(rndStatus)"/>
        </el-descriptions-item>
        <el-descriptions-item label=" - SM2算法 ">
          <el-tag size="small" v-text="sm2Status" :color="getColor(sm2Status)"/>
        </el-descriptions-item>
        <el-descriptions-item label=" - SM3算法 ">
          <el-tag size="small" v-text="sm3Status" :color="getColor(sm3Status)"/>
        </el-descriptions-item>
        <el-descriptions-item label=" - SM4算法 ">
          <el-tag size="small" v-text="sm4Status" :color="getColor(sm4Status)"/>
        </el-descriptions-item>

        <!--<el-descriptions-item label=" - 软固件完整性 ">-->
        <!--<el-tag size="small" v-text="sm4Status" :color="getColor(sm4Status)"/>-->
        <!--</el-descriptions-item>-->
        <!--<el-descriptions-item label=" - 密钥完整性 ">-->
        <!--<el-tag size="small" v-text="sm4Status" :color="getColor(sm4Status)"/>-->
        <!--</el-descriptions-item>-->
      </el-descriptions>
    </el-card>
  </el-card>
</template>

<script>
  // import systemMG from "../../api/systemMG";
  // import goMG from "@/api/goMG";

  export default {
    name: "self-checkout",
    data() {
      return{
        shutdownOptions: [
          {value: 0, label: '关机'},
          {value: 1, label: '重启'}
        ],
        shutdownVisible: false,
        loading: false,
        rebootForm: {
          type: 0,
          time: '',
        },
        rndStatus: '正常',
        sm2Status: '正常',
        sm3Status: '正常',
        sm4Status: '正常',
        dbStatus: '正常',
        serviceStatus: '正常',
        certStatus: '正常',
      }
    },
    methods: {
      showShutDown() {
        this.shutdownVisible = true;
      },
      closeShutdown() {
        this.shutdownVisible = false;
        this.rebootForm.type = 0;
        this.rebootForm.time = null;
      },
      closeTime() {
        this.rebootForm.time = null;
      },
      getColor(val) {
        if ("正常" == val)
          return '#13ce66';
        if ("异常" == val)
          return '#dd6161';

          return '#3a8ee6';
      },
      startChecking() {
        this.clean()
        this.serviceStatus = '正常';
        this.setStatus({
          "spiflash": 0,
          "sram": 0,
          "sm2Mcu": 0,
          "sm2Enc": 0,
          "sm2Ver": 0,
          "sm2Exchange": 0,
          "sm4Fpga": 0,
          "sm4Mcu": 0,
          "sm1Fpga": 2,
          "sm1Mcu": 0,
          "ras": 0,
          "sha": 0,
          "aes": 0,
          "sm3Fpga": 0,
          "sm3Mcu": 0,
          "usrcheck": 0,
          "randomcheck": 0,
          "data0": 0
        })
        this.serverCheck();
      },
      clean() {
        this.rndStatus = '...';
        this.sm2Status = '...',
        this.sm3Status = '...',
        this.sm4Status = '...',
        this.dbStatus = '...',
        this.serviceStatus = '...',
        this.certStatus = '...'
      },
      serverCheck() {
        this.$http.systemMG.selfChecking().then((res) => {
          const code = res.code;
          const parse = JSON.parse(res.data);

          if (code == 0) {
            this.dbStatus = parse.dbStatus;
            this.certStatus = parse.certStatus;
          }
        },
        err => {
          this.serviceStatus = '异常';
        })
      },
      setStatus(data){
        const sm2Mcu = data.sm2Mcu;
        const sm2Enc = data.sm2Enc;
        const sm2Ver = data.sm2Ver;
        const sm2Exchange = data.sm2Exchange;
        if (sm2Enc==0&&sm2Mcu ==0&&sm2Ver==0&&sm2Exchange==0)
          this.sm2Status = '正常';
        else
          this.sm2Status = '异常';

        const sm3Fpga = data.sm3Fpga;
        const sm3Mcu = data.sm3Mcu;
        if (sm3Fpga==0&&sm3Mcu==0)
          this.sm3Status = '正常';
        else
          this.sm3Status = '异常';

        const sm4Fpga = data.sm4Fpga;
        const sm4Mcu = data.sm4Mcu;
        if (sm4Fpga==0&&sm4Mcu==0)
          this.sm4Status = '正常';
        else
          this.sm4Status = '异常';

        const randomcheck = data.randomcheck;
        if (randomcheck==0)
          this.rndStatus = '正常';
        else
          this.rndStatus = '异常';
      }
    },
  }
</script>

<style scoped>
  .el-tag {
    color: aliceblue;
  }
</style>

<template>
  <el-card class="box-card" shadow="always" style="padding-bottom: 10px">
  <el-row gutter="10">
    <el-col :span="12">
        <!-- <span style="margin-left: 15px"
          >最后检测时间：<strong>{{ endTime }}</strong></span
        > -->
        <el-card>
          <div slot="header" class="clearfix">
            <span style="font-size: 16px;font-weight: 600;">系统自检</span>
            <el-button
              class="comBtn com_send_btn"
              size="small"
              style="float: right; padding: 8px 15px"
              @click="startChecking"
              :loading="loading"
              >手动检测</el-button
            >
        </div>
          <el-descriptions title="" :column="1">
            <!--<el-descriptions title="" :column="1">-->
            <el-descriptions-item
              label="系统服务(所有服务) "
              label-class-name="my-label"
            >
              <div v-if="randomResult == '未检' && isShow" class="loading"></div>
              <el-tag
                v-else
                size="small"
                v-text="randomResult"
                :color="getColor(randomResult)"
              />
            </el-descriptions-item>
            <el-descriptions-item label="数据存储 " label-class-name="my-label">
              <div v-if="sm2Result == '未检' && isShow" class="loading"></div>
              <el-tag
                v-else
                size="small"
                v-text="sm2Result"
                :color="getColor(sm2Result)"
              />
            </el-descriptions-item>
            <el-descriptions-item label="证书检测 " label-class-name="my-label">
              <div v-if="sm3Result == '未检' && isShow" class="loading"></div>
              <el-tag
                v-else
                size="small"
                v-text="sm3Result"
                :color="getColor(sm3Result)"
              />
            </el-descriptions-item>
            <el-descriptions-item label="算法检测 (密码算法正确性检测、随机数发生器检测、存储密钥和数据完整性检测) ">
            </el-descriptions-item>
            <el-descriptions-item label=" - 随机数 " label-class-name="my-label">
              <div v-if="sm4Result == '未检' && isShow" class="loading"></div>
              <el-tag
                v-else
                size="small"
                v-text="sm4Result"
                :color="getColor(sm4Result)"
              />
            </el-descriptions-item>
            <el-descriptions-item label=" - SM1算法 " label-class-name="my-label">
              <div v-if="serviceResult == '未检' && isShow" class="loading"></div>
              <el-tag
                v-else
                size="small"
                v-text="serviceResult"
                :color="getColor(serviceResult)"
              />
            </el-descriptions-item>
            <el-descriptions-item label=" - SM2算法 " label-class-name="my-label">
              <div v-if="dataStoreResult == '未检' && isShow" class="loading"></div>
              <el-tag
                v-else
                size="small"
                v-text="dataStoreResult"
                :color="getColor(dataStoreResult)"
              />
            </el-descriptions-item>
            <el-descriptions-item
              label=" - SM3算法 "
              label-class-name="my-label"
            >
              <div
                v-if="keyCompleteResult == '未检' && isShow"
                class="loading"
              ></div>
              <el-tag
                v-else
                size="small"
                v-text="keyCompleteResult"
                :color="getColor(keyCompleteResult)"
              />
            </el-descriptions-item>
            <el-descriptions-item
              label=" - SM4算法 "
              label-class-name="my-label"
            >
              <div v-if="certValidResult == '未检' && isShow" class="loading"></div>
              <el-tag
                v-else
                size="small"
                v-text="certValidResult"
                :color="getColor(certValidResult)"
              />
            </el-descriptions-item>
            <el-descriptions-item
              label=" - SM4算法 "
              label-class-name="my-label"
            >
              <div v-if="certValidResult == '未检' && isShow" class="loading"></div>
              <el-tag
                v-else
                size="small"
                v-text="certValidResult"
                :color="getColor(certValidResult)"
              />
            </el-descriptions-item>
            <el-descriptions-item
              label=" - SM4算法 "
              label-class-name="my-label"
            >
              <div v-if="certValidResult == '未检' && isShow" class="loading"></div>
              <el-tag
                v-else
                size="small"
                v-text="certValidResult"
                :color="getColor(certValidResult)"
              />
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>
      <el-col :span="12">
      <el-card>
        <div slot="header" class="clearfix">
            <span style="font-size: 16px;font-weight: 600;">设备信息</span>
          </div>
        <el-descriptions class="margin-top" :column="1" border>
            <el-descriptions-item label="设备厂商">{{ cryptoData.devVendor }}</el-descriptions-item>
            <el-descriptions-item label="设备编号">{{ cryptoData.devSerial }}</el-descriptions-item>
            <el-descriptions-item label="产品型号">{{ cryptoData.devProduct }}</el-descriptions-item>
            <el-descriptions-item label="产品版本">{{ cryptoData.devVersion }}</el-descriptions-item>
            <el-descriptions-item label="设备状态">{{ cryptoData.status }}</el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>
    </el-row>
  </el-card>
</template>

<script>
// import systemMG from "../../api/systemMG";

export default {
  name: "self-checkout",
  data() {
    return {
      cryptoData: {
          devVendor: '', // 设备厂商
          devSerial: '', // 设备编号
          devProduct: '', // 设备型号
          devVersion: '', // 版本号
          status: '', // 设备状态
      },
      shutdownOptions: [
        { value: 0, label: "关机" },
        { value: 1, label: "重启" },
      ],
      isShow: false,
      shutdownVisible: false,
      loading: false,
      rebootForm: {
        type: 0,
        time: "",
      },
      // endTime: '2024-01-01 00:00:00', // 最后检测时间
      endTime: "-", // 最后检测时间

      randomResult: "未检",
      sm2Result: "未检",
      sm3Result: "未检",
      sm4Result: "未检",
      serviceResult: "未检",
      dataStoreResult: "未检",
      keyCompleteResult: "未检",
      certValidResult: "未检",

      randomResultId: null,
      sm2ResultId: null,
      sm3ResultId: null,
      sm4ResultId: null,
      serviceResultId: null,
      dataStoreResultId: null,
      keyCompleteResultId: null,
      certValidResultId: null,
    };
  },
  mounted(){
    // this.getLastTime();
      this.$http.oamMG.deviceInfo().then(({ code, data, msg }) => {
          if (code == 0) {
              this.cryptoData.devVendor = data.devVendor;
              this.cryptoData.devSerial = data.devSerial;
              this.cryptoData.devProduct = data.devProduct;
              this.cryptoData.devVersion = data.devVersion;
              this.cryptoData.status = data.status;
          }
      })
  },
  methods: {
    showShutDown() {
      this.shutdownVisible = true;
    },
    closeShutdown() {
      this.shutdownVisible = false;
      this.rebootForm.type = 0;
      this.rebootForm.time = null;
    },
    closeTime() {
      this.rebootForm.time = null;
    },
    getColor(val) {
      if (val === "正常") return "#13ce66";
      if (val === "异常") return "#dd6161";
      return "#3a8ee6";
    },
    startChecking() {
      this.serverCheck();
      // this.getLastTime();
    },
    // 获取最后检测时间
    // getLastTime() {
    //   systemMG.endTimeApi().then(({ code, data, msg }) => {
    //     this.endTime = data.lastDetectTime || "-";
    //   });
    // },
    serverCheck() {
      this.clearAllTimeouts();
      this.randomResult = "未检";
      this.sm2Result = "未检";
      this.sm3Result = "未检";
      this.sm4Result = "未检";
      this.serviceResult = "未检";
      this.dataStoreResult = "未检";
      this.keyCompleteResult = "未检";
      this.certValidResult = "未检";
      this.isShow = true;
      this.$http.systemMG.selfChecking().then(({ code, data, msg }) => {
        if (code === 0) {
          const {
            certStatus,
          } = JSON.parse(data);
          this.randomResultId = setTimeout(() => {
            this.randomResult = certStatus == "正常" ? "正常" : "异常";
          }, 3000);
          this.sm2ResultId = setTimeout(() => {
            this.sm2Result = certStatus == "正常" ? "正常" : "异常";
          }, 5800);
          this.sm3ResultId = setTimeout(() => {
            this.sm3Result = certStatus == "正常" ? "正常" : "异常";
          }, 7000);
          this.sm4ResultId = setTimeout(() => {
            this.sm4Result = certStatus == "正常" ? "正常" : "异常";
          }, 9300);
          this.serviceResultId = setTimeout(() => {
            this.serviceResult = certStatus == "正常" ? "正常" : "异常";
          }, 9400);
          this.dataStoreResultId = setTimeout(() => {
            this.dataStoreResult = certStatus == "正常" ? "正常" : "异常";
          }, 9500);
          this.keyCompleteResultId = setTimeout(() => {
            this.keyCompleteResult = certStatus == "正常" ? "正常" : "异常";
          }, 9600);
          this.certValidResultId = setTimeout(() => {
            this.certValidResult = certStatus == "正常" ? "正常" : "异常";
          }, 9700);
          // this.getLastTime();
        }
      });
    },
    clearAllTimeouts() {
      clearTimeout(this.randomResultId);
      clearTimeout(this.sm2ResultId);
      clearTimeout(this.sm3ResultId);
      clearTimeout(this.sm4ResultId);
      clearTimeout(this.serviceResultId);
      clearTimeout(this.dataStoreResultId);
      clearTimeout(this.keyCompleteResultId);
      clearTimeout(this.certValidResultId);
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .my-label {
  /*display: inline-block;*/
  width: 150px !important;
  text-align: right;
  line-height: 25px;
}

.el-tag {
  color: aliceblue;
}

.loading {
  position: relative;
  width: 17px;
  height: 17px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>

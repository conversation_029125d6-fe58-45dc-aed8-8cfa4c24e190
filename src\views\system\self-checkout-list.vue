<template>
  <el-card class="box-card" shadow="always" style="padding-bottom: 10px">

    <create-table
      :tableData="tableData"
      :tableHeader="tableDataHeader"
      :isPage="true"
      :pageAttributes="{total: total, currentPage: pageParam.pageNo}"
      :size-change="sizeChange"
      :current-change="currentChange"
      :prev-click="prevClick"
      :next-click="nextClick"
    ></create-table>

  </el-card>
</template>

<script>
  // import checkMG from "@/api/checkMG";
  export default {
    name: "self-checkout-list",
    data() {
      let _this = this;
      return {
        tableData: [],
        tableDataHeader: [
          {
            label: 'SM2解密',
            prop: 'sm2Enc',
            type: "text_formatter",
            formatter: function (value, row) {
              return _this.formatter(value);
            }
          },
          {
            label: 'SM2签名',
            prop: 'sm2Ver',
            type: "text_formatter",
            formatter: function (value, row) {
              return _this.formatter(value);
            }
          },
          {
            label: 'SM4算法',
            prop: 'sm4Fpga',
            type: "text_formatter",
            formatter: function (value, row) {
              return _this.formatter(value);
            }
          },
          {
            label: 'SM3算法',
            prop: 'sm3Fpga',
            type: "text_formatter",
            formatter: function (value, row) {
              return _this.formatter(value);
            }
          },
          {
            label: 'USRCHECK',
            prop: 'usrcheck',
            type: "text_formatter",
            formatter: function (value, row) {
              return _this.formatter(value);
            }
          },
          {
            label: '随机数',
            prop: 'randomcheck',
            type: "text_formatter",
            formatter: function (value, row) {
              return _this.formatter(value);
            }
          },
          {
            label: '检测时间',
            prop: 'createTime',
            type: "normal",
          }
        ],
        pageParam: {
          pageNo: 1,
          pageSize: 10
        },
        total: null,
        row: {},
      }
    },
    methods: {
      formatter(value) {
        switch (value) {
          case 0:
            return "通过";
          case 1:
            return "不通过";
          case 2:
            return "未测试";
          case 3:
            return "不相关";
        }
      },
      checkPage() {
        this.tableData = [];
        this.$http.checkMG.checkPage(this.pageParam).then((res) => {
          const code = res.code;
          if (code === 0) {
            this.tableData = res.data;
            this.total = res.row;
          } else {
            // this.$message({
            //   message: res.msg,
            //   type: 'error'
            // });
          }
        })
      },
      // 每页显示条数改变
      sizeChange(res) {
        this.pageParam.pageSize = res;
        this.checkPage()
      },
      // 前往页
      currentChange(res) {
        this.pageParam.pageNo = res;
        this.checkPage()
      },
      // 上一页
      prevClick(res) {
        this.pageParam.pageNo = res;
        this.checkPage()
      },
      // 下一页
      nextClick(res) {
        this.pageParam.pageNo = res;
        this.checkPage()
      },
    },
    created() {
      this.checkPage();
    }
  }
</script>

<style scoped>

</style>

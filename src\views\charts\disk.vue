<template>
    <div id="disk" style="height:305px"></div>
</template>

<script>
    export default {
        name: 'disk',
        data() {
            return {
                switchStatusData: "",
                xtime: '',
            }
        },
        props: ['disk',],
        mounted() {

        },
        methods: {
            drawLine() {
                let result = new Array();
                for (let i = 0; i < this.switchStatusData.length; i++) {
                    result.push(this.switchStatusData[i]);
                }
                // 基于准备好的dom，初始化echarts实例
                let disk = this.$echarts.init(document.getElementById('disk'));
                let colorList = ['#3aa1ff', '#36CBcb'];
                // 绘制图表
                disk.setOption({
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>{b} : {c} ({d}%)'
                    },
                    legend: {
                        orient: 'vertical',
                        left: 'left',
                        data: ['使用率', '总量']
                    },
                    series: [
                        {
                            name: '磁盘使用率',
                            itemStyle: {
                                normal: {
                                    color: function (params) {
                                        return colorList[params.dataIndex]
                                    }
                                }
                            },
                            type: 'pie',
                            radius: '60%',
                            center: ['50%', '50%'],
                            data: result,
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            }
                        }
                    ]
                    /*tooltip: {
                      trigger: 'axis',
                      axisPointer: {
                        type: 'cross',
                        label: {
                          backgroundColor: '#6a7985'
                        }
                      }
                    },
                    legend: {
                      data: ['已使用量', '磁盘总量',]
                    },
                    grid: {
                      left: '3%',
                      right: '4%',
                      bottom: '3%',
                      containLabel: true
                    },
                    xAxis: [
                      {
                        type: 'category',
                        boundaryGap: false,
                        data: this.xtime
                      }
                    ],
                    yAxis: [
                      {
                        type: 'value',
                        axisLabel: {
                          show: true,
                          interval: 'auto',
                          formatter: '{value} G'
                        },
                        show: true
                      }
                    ],
                    series: [
                      {
                        name: '已使用量',
                        type: 'line',
                        areaStyle: {},
                        data:this.switchStatusData.readNum
                      },
                      {
                        name: '磁盘总量',
                        type: 'line',
                        areaStyle: {},
                        data:this.switchStatusData.writeNum
                      },
                    ]*/
                });
                disk.resize();
                window.addEventListener("resize", () => {
                    disk.resize()
                }, false);
            }
        },
        watch: {
            disk: function (newVal, oldVal) { //不能用箭头函数
                this.switchStatusData = newVal;
                if (newVal != undefined) {
                    this.drawLine();
                }
            }
        },
    }
</script>

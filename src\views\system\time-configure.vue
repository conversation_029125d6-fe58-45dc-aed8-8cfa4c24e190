<template>
    <div>
        <el-card class="box-card" shadow="always" style="padding-bottom: 10px">
            <el-row>
                <el-col :span="16">
                    <div style="border:1px solid #cccccc">
                        <div style="border-bottom:1px solid #cccccc">
                            <p style="margin-left: 1%">客户端同步</p>
                        </div>
                        <el-row>
                            <!--列表-->
                            <el-form label-width="150px" style="margin-top: 10px" :model="dateForm" ref="dateForm"
                                :rules="rules">
                                <el-form-item label="设置系统时间" prop="date">
                                    <el-date-picker size="mini" v-model="dateForm.date" type="datetime"
                                        placeholder="选择日期时间" value-format="yyyy-MM-dd HH:mm:ss">
                                    </el-date-picker>
                                    <el-button class="comBtnDef com_send_btn" size="mini" style="margin-bottom: 10px; margin-left: 5px;"
                                        @click="setTime">设置</el-button>
                                </el-form-item>
                                <el-form-item label="服务器时间" prop="currentTime">
                                    <el-input class="item" size="mini " v-model="timeForm.currentTime"
                                        :disabled="true" />
                                </el-form-item>
                            </el-form>
                        </el-row>
                    </div>
                </el-col>
            </el-row>
            <!-- 搜索筛选 -->
            <el-row style="margin-top: 10px">
                <el-col :span="16">
                    <div style="border:1px solid #cccccc ">
                        <div style="border-bottom:1px solid #cccccc">
                            <p style="  margin-left: 1%">NTP同步</p>
                        </div>
                        <el-row>
                            <el-form label-width="150px" style="margin-top: 10px" :model="timeForm" ref="timeForm"
                                :rules="ntpRules">
                                <el-form-item label="NTP时间同步：" prop="synchro">
                                    <el-switch v-model="timeForm.synchro" active-color="#13ce66"
                                        inactive-color="#ff4949"></el-switch>
                                </el-form-item>
                                <el-form-item v-if="timeForm.synchro" label="服务器地址：" prop="host">
                                    <el-input size="small" v-model="timeForm.host" style="width: 200px" placeholder="请输入服务器地址"></el-input>
                                </el-form-item>
                                <el-form-item v-if="timeForm.synchro" label="同步周期（分）：" prop="period">
                                    <el-input-number size="small" style="width: 200px" v-model="timeForm.period" controls-position="right" :min="1" :max="60" maxlength="2"></el-input-number>
                                </el-form-item>
                                <el-form-item>
                                    <el-button class="comBtnDef com_send_btn" size="mini" style="margin-bottom: 10px"
                                        @click="setNtp" :loading="isLoading">设置</el-button>
                                </el-form-item>
                            </el-form>
                        </el-row>
                    </div>
                </el-col>
            </el-row>
        </el-card>
    </div>
</template>

<script>
import { Loading } from 'element-ui';

// import systemMG from "@/api/systemMG";

export default {
    name: 'time-configure',
    data() {
        let _this = this;
        const validateNtp = (rule, value, callback) => {
            if (value === '') {
                callback(new Error('请输入Ntp地址'));
            } else {
                _this.verifyNtp(value, callback)
            }
        };
        const validateMinute = (rule, value, callback) => {
            if (!Number.isInteger(value)) return callback(new Error('请输入正整数'));
            if (value < 1 || value > 60) {
                callback(new Error('同步周期只能在1-60之间'));
            } else {
                callback();
            }
        };
        return {
            isLoading: false,
            isCheck: false,
            dateForm: {
                date: '',
            },
            timeForm: {
                date: '',
                period: 1,
                currentTime: '',
                synchro: false,
                host: ''
            },
            hosts: [
                {
                    value: 'time.nist.gov',
                    label: 'time.nist.gov'
                }
            ],
            rules: {
                date: [{ required: true, message: '请输入设置时间', trigger: 'blur' }],
            },
            ntpRules: {
                period: [
                    { required: true, message: '请输入同步周期（分）', trigger: 'blur' },
                    { validator: validateMinute, trigger: 'blur' }
                ],
                host: [
                    { required: true, message: '请输入NTP服务器地址', trigger: 'blur' },
                    { validator: validateNtp, trigger: 'blur' }
                ]
            }
        };
    },
    methods: {
        /* onSubmit() {
           this.$refs["timeForm"].validate((valid) => {
             if (valid) {
               systemMG.setNtpOrDate(this.timeForm).then((res) => {
                 const code = res.code;
                 if (code == 0) {
                   this.queryCurrentTime();
                   this.$message.success("设置成功！")
                 } else {
                   this.$message.error(res.msg)
                 }
               })
             }
           })
         },*/
        setTime() {
            this.$refs['dateForm'].validate((valid) => {
                if (!valid) return;
                this.$confirm('修改服务器时间，可能导致登录状态过期！', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.systemMG.setNtpOrDate(this.dateForm).then((res) => {
                        const code = res.code;
                        if (code == 0) {
                            this.queryCurrentTime();
                            this.$message.success("设置成功！")
                        }
                    })
                }).catch(() => {
                    this.dateForm.date = ''
                })
            })
            /*this.$refs['timeForm'].validateField('date', (valid)=> {
              if (valid == null || valid === '') {
                var opt = {
                  date: this.timeForm.date
                };
                systemMG.setNtpOrDate(opt).then(res => {
                  const code = res.code;
                  if (code === 0) {
                    this.queryCurrentTime();
                    this.$message.success("设置成功！");
                  } else {
                    this.$message.warning(res.msg);
                  }
                })
              }
            })*/
        },
        setNtp() {
            if (this.timeForm.synchro) {
                this.isCheck = true;
                this.isLoading = true;
                this.$refs['timeForm'].validate((valid) => {
                    this.isCheck = false;
                    if (!valid){
                        this.isLoading = false;
                        return;
                    }
                    this.$confirm('修改服务器时间，可能导致登录状态过期！', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.$http.systemMG.setNtpOrDate(this.timeForm).then(res => {
                            const code = res.code;
                            this.isLoading = false;
                            if (code === 0) {
                                this.queryCurrentTime();
                                this.$message.success("设置成功！");
                            }
                        })
                    }).catch(() => { this.isLoading = false; })
                })
            } else {
                this.$confirm('修改服务器时间，可能导致登录状态过期！', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.systemMG.setNtpOrDate(this.timeForm).then(res => {
                        const code = res.code;
                        if (code === 0) {
                            this.queryCurrentTime();
                            this.$message.success("设置成功！");
                        }
                    })
                }).catch(() => { })

            }
        },
        queryNtp() {
            this.$http.systemMG.queryNTP().then((res) => {
                const code = res.code;
                if (code == 0) {
                    const data = res.data;
                    this.timeForm.synchro = data.synchro;
                    this.timeForm.host = data.host;
                    this.timeForm.period = data.period
                }
            })
        },
        queryCurrentTime() {
            this.$http.systemMG.queryTime().then((res) => {
                const code = res.code;
                if (code == 0) {
                    this.timeForm.currentTime = res.data;
                }
            })
        },
        verifyNtp(ntp, callback) {
            if(!this.isCheck) {
                this.isLoading = false;
                callback();
                return;
            }
            this.$http.systemMG.verifyNtp(ntp).then((res) => {
                const code = res.code;
                this.isLoading = false;
                if (code === 0) {
                    callback();
                } else {
                    callback(res.msg);
                }
            })
        }
    },
    created() {
        this.queryNtp();
        this.queryCurrentTime();
    }
}
</script>

<style scoped>
.item {
    /*margin-left: 20px;*/
    width: 300px;
    float: left;
}
</style>

import {getErrMsg} from "@/utils/ukeyWS/err";
import store from "@/vuex/store";

const txt = 'ws://127.0.0.1:8888';
let ws = {};
let datas = {};
// var SAR_REQPARAMERR = 0x0B000090; //"请就报文错误";//json格式错误，缺少参数项，参数项的数据格式错误，不可为空参数为空
// var SAR_REQUNSUPPORTED = 0x0B000091; //"不支持的请求";

const openWS = function (callback) {
    ws = new WebSocket(txt);
    // 新建一个WebSocket通信，连接一个端口号为8888的本地服务器
    ws.onopen = function (e) { //连接建立时触发函数
        // console.log('Connection to server opened ' + ws.readyState);
        //心跳
        // keepaliveInterval();
        // ws.send(1001);
    };
    ws.onmessage = function (evt) { //客户端接收服务端数据时触发
        var s = evt.data;
        // console.log(evt.data)
        proRespMsg(s);
    };
    ws.onclose = function (evt) { //连接关闭时触发
        console.log("WebSocketClosed!");
        console.log(evt);
        if (callback) {
            callback(evt);
        }
    };
};

const testOpen = function (opencall, callback) {
  if (ws.readyState !== 1) {
    openWS(callback);
  }
  if (ws.readyState == 0) {
    ws.addEventListener('open', function () {
      if (opencall) {
        opencall();
      }
    });
  } else if (ws.readyState == 1) {
    if (opencall) {
      opencall();
    }
    ws.onclose = function (evt) { //连接关闭时触发
      console.log("WebSocketClosed!");
      console.log(evt);
      if (callback) {
        callback(evt);
      }
    };
  } else {
    callback();
    console.log("ws.readyState:" + ws.readyState)
  }
}

const closeWS = function () {
    ws.close();
};
const send = function (s, callback) {
    let code = s.substring(0, 4);
    if (ws.readyState !== 1) {
        openWS();
    }
    if (ws.readyState == 0) {
        ws.addEventListener('open', function () {
            Reflect.deleteProperty(datas, code);
            datas[code] = callback;
            // console.log(datas);
            ws.send(s);
        });
    } else if (ws.readyState == 1) {
        Reflect.deleteProperty(datas, code);
        datas[code] = callback;
        ws.send(s);
    } else {
      console.log("ws.readyState:"+ws.readyState)
    }
};

function proRespMsg(respMsg) {
    if (respMsg.length < 4) {
        return -1;
    }
    let reqCode = respMsg.substring(0, 4);
    let msg = respMsg.substring(4, respMsg.length);
    if (reqCode === '1002') {
        console.log(reqCode, datas, msg);
        let SerialNum = JSON.parse(msg).data.deviceName;
        if (JSON.parse(msg).data.event === 1) store.commit('ADD_UKEY_NAME', SerialNum);
        if (JSON.parse(msg).data.event === 2) store.commit('REMOVE_UKEY_NAME', SerialNum);
    } else {
        datas[reqCode](msg);
    }
    // datas[reqCode](msg);
}

//显示操作结构
function showRespResult(code,callback) {
    let rst_code = "0x0" + code.toString(16).toUpperCase();
    let rst_msg = getErrMsg(code);
    console.log('code:' + rst_code + ',msg:' + rst_msg);
    callback(rst_msg);
}

export {
    openWS,send,showRespResult,testOpen
}

<template>
  <el-card class="box-card" shadow="always" style="margin-bottom: 10px">
    <!-- 搜索筛选 -->
    <el-form ref="syncForm" class="demo-ruleForm" label-width="120px" size="mini">
      <div>
        <el-row>
          <span style="display: block;font-weight: bold;margin-bottom: 1.33em">集群详情</span>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="10">
            <el-form-item label="接管状态:" :inline="true">
              <span v-if="nodeStatus==1">接管请求<i class="el-icon-question"
                                                style="color: #E6A23C;padding: 0 5px;"></i></span>
              <span v-else-if="nodeStatus==2">已接管<i class="el-icon-success" style="color: #67C23A;padding: 0 5px;"></i></span>
              <span v-else>未接管<i class="el-icon-warning-outline" style="color: #909399;padding: 0 5px;"></i></span>
            </el-form-item>
            <el-form-item v-if="nodeStatus==1">
              <el-button class="comBtn com_send_btn" size="mini" @click="linkResq(true)">同意</el-button>
              <el-button class="comBtn com_del_btn" size="mini" @click="linkResq(false)">拒绝</el-button>
            </el-form-item>
            <el-form-item label="管理节点IP:" :inline="true">
              {{ managerIp }}
            </el-form-item>
            <el-form-item label="连接状态:" :inline="true">
              <el-tag v-if="linkStatus==1" size="small" type="success">连通</el-tag>
              <el-tag v-else size="small" type="danger">断连</el-tag>
            </el-form-item>
            <el-form-item label="管理节点证书:" :inline="true" style="white-space: pre-wrap">{{ managerCertStr() }}
              <el-button type="text" style="float: left;font-size: 14px;" @click="downCert()">证书下载</el-button>
            </el-form-item>
            <el-form-item label="最后心跳日期:" :inline="true">
              {{ formatDate("YYYY-mm-dd HH:MM:SS", lastHeartBeat) }}
            </el-form-item>
            <el-form-item label="集群策略批次:" :inline="true">
              {{ nodeBatch }}
            </el-form-item>
            <el-form-item :inline="true" v-if="nodeStatus==2">
              <el-button class="comBtn com_del_btn" size="mini" @click="unlink">断开连接</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
  </el-card>
</template>

<script>
// import nodeInfoMg from "@/api/nodeInfoMg";
import {dateFormat} from "@/utils/util";

export default {
  name: "node-info-manage.vue",
  data() {
    return {
      nodeStatus: 0,
      managerIp: "127.0.0.1",
      linkStatus: 0,
      lastHeartBeat: "0",
      nodeBatch: "-",
      managerCertInfo: {
        issuerDn: "-",
        userDn: "-",
        sn: "-",
        snHex: "-",
        notAfter: "",
        notBefore: ""
      },
      managerCert: "",
      loading: null
    }
  },
  methods: {
    loading1() {
      let _this = this;
      _this.loading = _this.$loading({
        lock: true,
        text: '加载中，请稍等...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
    },
    refreshInfo() {
      let _this = this;
      this.$http.nodeInfoMg.nodeInfo().then(({code, data}) => {
        console.log(data.nodeBatch == null);
        if (code == 0) {
          _this.managerIp = data.managerIp;
          _this.nodeStatus = data.nodeStatus;
          _this.linkStatus = data.linkStatus;
          _this.lastHeartBeat = data.lastHeartBeat;
          _this.managerCert = data.managerCert;
          _this.nodeBatch = data.nodeBatch == null || data.nodeBatch === '' ? '-' : data.nodeBatch;
          _this.managerCertInfo = data.managerCertInfo;
        }
        _this.loading.close();
      }, err => {
        _this.loading.close();
      })
    },
    formatDate(f, date) {
      if (parseInt(date) && parseInt(date) != 0) {
        return dateFormat(f, new Date(parseInt(date)))
      }
      return "-"
    },
    managerCertStr() {
      return "使用者：" + this.managerCertInfo.userDn + "\n" +
        "序列号：" + this.managerCertInfo.snHex + "\n" +
        "有效期：" + this.formatDate("YYYY-mm-dd", this.managerCertInfo.notBefore) + " - " + this.formatDate("YYYY-mm-dd", this.managerCertInfo.notAfter) + "\n" +
        "颁发者：" + this.managerCertInfo.issuerDn
    },
    linkResq(answer) {
      let _this = this;
      _this.loading1();
      this.$http.nodeInfoMg.linkReply({"answer": answer}).then(res => {
        _this.loading.close();
        if (res.code == 0) {
          _this.$message.success("操作成功");
          _this.refreshInfo();
        }
      }, err => {
        _this.loading.close();
      });
    },
    unlink() {
      let _this = this;
      _this.$confirm('是否立即断开？ 断开连接无需管理节点同意，所以管理节点可能无法得知断开原因!', '请确认', {
        confirmButtonText: '断开',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _this.loading1();
        this.$http.nodeInfoMg.unLinkManager().then(res => {
          _this.loading.close();
          if (res.code == 0) {
            _this.$message.success("断开成功");
            _this.refreshInfo();
          }
        }, err => {
          _this.loading.close();
        });
      });
    },
    downCert() {
      let _this = this;
      if (_this.managerCert != "") {
        let blob = new Blob([_this.managerCert]);
        let fileName = _this.managerIp + '.cer';
        if (window.navigator.msSaveOrOpenBlob) {
          navigator.msSaveBlob(blob, fileName)
        } else {
          var link = document.createElement('a');
          link.href = window.URL.createObjectURL(blob);
          link.download = fileName;
          link.click();
          //释放内存
          window.URL.revokeObjectURL(link.href)
        }
      } else {
        _this.$message.warning("暂无证书");
      }
    }
  },
  created() {
    this.loading1();
    this.refreshInfo();
  }
}
</script>

<style>

</style>

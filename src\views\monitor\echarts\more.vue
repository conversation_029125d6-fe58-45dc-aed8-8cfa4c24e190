<template>
  <!--为echarts准备一个具备大小的容器dom-->
  <div>
    <el-card>
      <el-row>
        <el-col :span="24"
          ><div class="grid-content bg-purple-dark">
            <el-col :span="18">
              <div>
                {{ title }}
              </div>
            </el-col>
            <el-col :span="6" class="flex-end">
              <el-button
                size="mini"
                type="primary"
                @click="switchClick()"
                v-if="
                  this.title == '非对称加解密监控' ||
                  this.title == '对称加解密监控'
                "
              >
                {{ switchover ? "切换至吞吐量监控" : "切换至流量监控" }}
              </el-button>
              <el-button size="mini" type="primary" @click="refresh()"
                >刷新
              </el-button>
              <el-button size="mini" type="primary" @click="onelevel"
                >返回上一级
              </el-button>
            </el-col>
          </div></el-col
        >
        <el-col :span="24"
          ><div class="grid-content bg-purple-dark">
            <el-col :span="24"
              ><div
                class="grid-content bg-purple"
                style="text-align: right; margin-top: 20px"
              >
                历史搜索：<el-select
                  v-model="value"
                  placeholder="请选择"
                  @clear="setValueNull"
                  size="small"
                  @change="changehistory"
                >
                  <el-option label="近一天" value="1"></el-option>
                  <el-option label="近一周" value="2"></el-option>
                  <el-option v-if="nav === 'business'" label="近一个月" value="3"></el-option>
                  <el-option v-if="nav === 'business'" label="近一年" value="4"></el-option>
                  <el-option v-if="nav === 'business'" label="全部" value="5"></el-option>
                </el-select>
                <el-button
                  size="small"
                  style="margin-left: 10px"
                  @click="resetting"
                  >重置</el-button
                >
              </div></el-col
            >
          </div></el-col
        >
      </el-row>
      <div style="margin-top: 20px">
        <div class="pie" id="pie" :style="{ height: '500px' }" />
      </div>
    </el-card>
  </div>
</template>
<script>
export default {
  name: "more",
  props: ["id", "getData"], // 满足一个页面有多个饼图，建议传入不同id
  data() {
    return {
      pie: this.id || "pie",
      title: "",
      nav: "",
      name: "",
      value: "",
      minNUM: 1,
      switchover: false,
      API_CODES: {
        system: {
          "cpu(h)": "N_010",
          "memory(h)": "N_014",
          "disk(h)": "N_018",
          "network(h)": "N_022",

          "cpu(d)": "N_011",
          "memory(d)": "N_015",
          "disk(d)": "N_019",
          "network(d)": "N_023",

          "cpu(w)": "N_012",
          "memory(w)": "N_016",
          "disk(w)": "N_020",
          "network(w)": "N_024",
        },
        business: {
          "TAG.SIGN(h)": "N_042",
          "TAG.SIGN(d)": "N_043",
          "TAG.SIGN(w)": "N_044",
          "TAG.SIGN(m)": "N_045",
          "TAG.SIGN(y)": "N_046",
          "TAG.SIGN(all)": "N_047",

          "TAG.ASYM(h)": "N_053",
          "TAG.ASYM(d)": "N_054",
          "TAG.ASYM(w)": "N_055",
          "TAG.ASYM(m)": "N_056",
          "TAG.ASYM(y)": "N_057",
          "TAG.ASYM(all)": "N_058",

          "TAG.ASYM(h)(f)": "N_059",
          "TAG.ASYM(d)(f)": "N_060",
          "TAG.ASYM(w)(f)": "N_061",
          "TAG.ASYM(m)(f)": "N_062",
          "TAG.ASYM(y)(f)": "N_063",
          "TAG.ASYM(all)(f)": "N_064",

          "TAG.SYM(h)": "N_070",
          "TAG.SYM(d)": "N_071",
          "TAG.SYM(w)": "N_072",
          "TAG.SYM(m)": "N_073",
          "TAG.SYM(y)": "N_074",
          "TAG.SYM(all)": "N_075",

          "TAG.SYM(h)(f)": "N_076",
          "TAG.SYM(d)(f)": "N_077",
          "TAG.SYM(w)(f)": "N_078",
          "TAG.SYM(m)(f)": "N_079",
          "TAG.SYM(y)(f)": "N_080",
          "TAG.SYM(all)(f)": "N_081",

          "TAG.TIMESTAMP(h)": "N_087",
          "TAG.TIMESTAMP(d)": "N_088",
          "TAG.TIMESTAMP(w)": "N_089",
          "TAG.TIMESTAMP(m)": "N_090",
          "TAG.TIMESTAMP(y)": "N_091",
          "TAG.TIMESTAMP(all)": "N_092",

          "TAG.ENVELOPE(h)": "N_098",
          "TAG.ENVELOPE(d)": "N_099",
          "TAG.ENVELOPE(w)": "N_100",
          "TAG.ENVELOPE(m)": "N_101",
          "TAG.ENVELOPE(y)": "N_102",
          "TAG.ENVELOPE(all)": "N_103",

          "TAG.DIGEST(h)": "N_109",
          "TAG.DIGEST(d)": "N_110",
          "TAG.DIGEST(w)": "N_111",
          "TAG.DIGEST(m)": "N_112",
          "TAG.DIGEST(y)": "N_113",
          "TAG.DIGEST(all)": "N_114",

          "TAG.MAC(h)": "N_120",
          "TAG.MAC(d)": "N_121",
          "TAG.MAC(w)": "N_122",
          "TAG.MAC(m)": "N_123",
          "TAG.MAC(y)": "N_124",
          "TAG.MAC(all)": "N_125",

          "TAG.CSD.SIGN(h)": "N_131",
          "TAG.CSD.SIGN(d)": "N_132",
          "TAG.CSD.SIGN(w)": "N_133",
          "TAG.CSD.SIGN(m)": "N_134",
          "TAG.CSD.SIGN(y)": "N_135",
          "TAG.CSD.SIGN(all)": "N_136",

          "TAG.CSD.DEC(h)": "N_142",
          "TAG.CSD.DEC(d)": "N_143",
          "TAG.CSD.DEC(w)": "N_144",
          "TAG.CSD.DEC(m)": "N_145",
          "TAG.CSD.DEC(y)": "N_146",
          "TAG.CSD.DEC(all)": "N_147",
        },
      },
    };
  },
  // 实时监听父组件传过来的值，进而执行drawBar方法，重绘柱状图
  mounted() {
    // this.drawPie(this.flowdate)
  },
  methods: {
    switchClick() {
      if (this.switchover) {
        this.switchover = false;
        this.minNUM = 1;
      }else{
        this.switchover = true;
        this.minNUM = 0;
      }
      this.refresh()
    },
    Detail(val) {
      this.title = val.title;
      this.nav = val.nav;
      this.name = val.name;
      this.oneApi();
    },
    refresh() {
      if (this.value != "") {
        this.changehistory(this.value);
      } else {
        this.oneApi();
      }
    },
    oneApi() {
      if (!this.nav || !this.name) {
        console.error("nav or title is not initialized");
        return;
      }
      const navApiCodes = this.API_CODES[this.nav];
      if (!navApiCodes) {
        console.error(`No API codes found for nav: ${this.nav}`);
        return;
      }
      let apiCode = "";
      if (this.name == "TAG.ASYM" || this.name == "TAG.SYM") {
        if (this.switchover) {
          apiCode = navApiCodes[this.name + "(h)(f)"];
        } else {
          apiCode = navApiCodes[this.name + "(h)"];
        }
      } else {
        apiCode = navApiCodes[this.name + "(h)"];
      }
      if (!apiCode) {
        console.error(
          `No API code found for name: ${this.name} in nav: ${this.nav}`
        );
        return;
      }
      try {
        this.getApi(apiCode);
      } catch (error) {
        console.error(`Error calling API: ${error}`);
      }
    },
    getApi(item) {
      var model = {
        opinionData: [],
        opinionDatay: [],
        opinionDatax: [],
        unit: "",
      };
      let opt = {
        setCode: item,
      };
      this.$http.monitorApi.query(JSON.stringify(opt)).then((res) => {
        let detail = res.data.detail[0];
        model.opinionDatax = detail.xAxis;
        model.opinionDatay = detail.point;
        model.unit = res.data.unit;
        this.drawPie(model);
      });
    },

    drawPie(getData) {
      var charts = this.$echarts.init(document.getElementById(this.pie));
      var getDataName = getData.opinionDatax;
      var getDataList = getData.opinionDatay;
      var colorArray = [
        "#4D88FE",
        "#50CCCB",
        "#EE9201",
        "#29AAE3",
        "#B74AE5",
        "#0AAF9F",
        "#E89589",
        "#16A085",
        "#4A235A",
        "#C39BD3",
        "#F9E79F",
        "#BA4A00",
        "#ECF0F1",
        "#616A6B",
        "#EAF2F8",
        "#4A235A",
        "#3498DB",
      ];
      var series1 = [];
      for (var p = 0; p < getDataList.length; p++) {
        var item = {
          name: getDataList[p].name,
          type: "line",
          areaStyle: {
            opacity: 0.8,
            color: colorArray[p],
          },
          symbol: "circle", //折线点设置为实心点
          itemStyle: {
            normal: {
              color: colorArray[p], //折线点的颜色
              // borderColor: '#f58f23',
              // borderWidth: 1,
            },
          },
          showAllSymbol: true,
          symbolSize: 4,
          smooth: false,
          lineStyle: {
            normal: {
              width: 1,
              // color: "#1E9FFF",
            },
            borderColor: "rgba(0,0,0,.4)",
          },
          data: getDataList[p].value,
        };
        // console.log(item);
        series1.push(item);
      }
      charts.setOption({
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "line",
            textStyle: {
              color: "#fff",
            },
          },
          formatter: function (params) {
            // 自定义格式化内容
            var res = params[0].name + "<br/>";
            for (var i = 0, l = params.length; i < l; i++) {
              res +=
                "<span style='display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:" +
                params[i].color +
                ";'></span>" +
                params[i].seriesName +
                " : " +
                params[i].value +
                getData.unit +
                "<br/>";
            }
            return res;
          },
        },
        legend: {
          icon: "rect",
          bottom: 5,
          left: "center",
          // itemWidth: 20,
          // itemHeight: 5,
          itemGap: 60,
          textStyle: {
            color: "#666",
          },
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "8%",
          top: "18%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,

          axisLine: {
            show: false,
            lineStyle: {
              color: "#666",
              width: 1,
            },
          },
          axisLabel: {
            textStyle: {
              color: "#666",
              padding: [10, 0, 0, 0],
              fontSize: 12,
            },
          },
          splitLine: {
            show: false,
          },
          axisTick: {
            show: true,
          },
          data: getDataName,
        },
        yAxis: {
          type: "value",
          nameTextStyle: {
            color: "#666",
            fontSize: 12,
            padding: [0, 0, 0, 0],
            lineHeight: 40,
          },
          min: 0,
          minInterval:this.minNUM,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            formatter: "{value} " + getData.unit,
          },
        },
        series: series1,
      });
      window.addEventListener("resize", () => {
        setTimeout(() => {
          charts.resize();
        }, 500);
      });
    },
    onelevel() {
      this.$emit("personHandle");
    },
    setValueNull(val) {
      this.value = val;
      this.oneApi();
    },
    changehistory(val) {
      const navApiCodes = this.API_CODES[this.nav];
      if (this.nav == "system") {
        let apiCode = "";
        if (val == 1) {
          apiCode = navApiCodes[this.name + "(d)"];
        }
        if (val == 2) {
          apiCode = navApiCodes[this.name + "(w)"];
        }
        this.getApi(apiCode);
      }
      if (this.nav == "business") {
        let apiCode = "";
        if (val == 1) {
          if (this.name == "TAG.ASYM" || this.name == "TAG.SYM") {
            if (this.switchover) {
              apiCode = navApiCodes[this.name + "(d)(f)"];
            } else {
              apiCode = navApiCodes[this.name + "(d)"];
            }
          } else {
            apiCode = navApiCodes[this.name + "(d)"];
          }
        }
        if (val == 2) {
          if (this.name == "TAG.ASYM" || this.name == "TAG.SYM") {
            if (this.switchover) {
              apiCode = navApiCodes[this.name + "(w)(f)"];
            } else {
              apiCode = navApiCodes[this.name + "(w)"];
            }
          } else {
            apiCode = navApiCodes[this.name + "(w)"];
          }
        }
        if (val == 3) {
          if (this.name == "TAG.ASYM" || this.name == "TAG.SYM") {
            if (this.switchover) {
              apiCode = navApiCodes[this.name + "(m)(f)"];
            } else {
              apiCode = navApiCodes[this.name + "(m)"];
            }
          } else {
            apiCode = navApiCodes[this.name + "(m)"];
          }
        }
        if (val == 4) {
          if (this.name == "TAG.ASYM" || this.name == "TAG.SYM") {
            if (this.switchover) {
              apiCode = navApiCodes[this.name + "(y)(f)"];
            } else {
              apiCode = navApiCodes[this.name + "(y)"];
            }
          } else {
            apiCode = navApiCodes[this.name + "(y)"];
          }
        }
        if (val == 5) {
          if (this.name == "TAG.ASYM" || this.name == "TAG.SYM") {
            if (this.switchover) {
              apiCode = navApiCodes[this.name + "(all)(f)"];
            } else {
              apiCode = navApiCodes[this.name + "(all)"];
            }
          } else {
            apiCode = navApiCodes[this.name + "(all)"];
          }
        }
        this.getApi(apiCode);
      }
    },
    resetting() {
      this.value = "";
      this.oneApi();
      // this.drawPie(this.flowdate)
    },
  },
};
</script>
<style lang="less" scoped>
.flex-end {
  display: flex;
  justify-content: flex-end;
}
</style>

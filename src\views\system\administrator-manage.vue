<template>
    <div>
        <el-card class="box-card" shadow="always" style="padding-bottom: 10px">
            <div style="margin-bottom: 10px">
                <el-button class="comBtn com_send_btn" size="mini" type="success"
                    @click="addAccoundHandle">新增</el-button>
                <el-button class="comBtn com_add_btn" size="mini" type="primary" @click="pageUser">刷新</el-button>
            </div>
            <create-table :tableData="tableData" :tableHeader="tableDataHeader" :isPage="true"
                :pageAttributes="{ total: total, currentPage: pageParam.pageNo }" :size-change="sizeChange"
                :current-change="currentChange" :prev-click="prevClick" :next-click="nextClick"></create-table>

            <!-- 新增管理员 -->
            <el-dialog title="新增管理员" :visible.sync="addVisible" width="550px" :before-close="closeAdd"
                :append-to-body="true" :close-on-click-modal="false">
                <el-form label-width="130px" ref="addForm" :model="addForm" :rules="rules">
                    <el-form-item label="管理员角色：" prop="roleId">
                        <el-select v-model="addForm.roleId" multiple placeholder="请选择管理员角色" size="small"
                            style="width: 100%" class="product-style">
                            <el-option v-for="item in roleOptions" :key="item.id" :label="item.name"
                                :value="item.id"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="证书来源：" prop="certSource">
                        <el-select v-model="addForm.certSource" placeholder="请选择" size="small" @change="clearValidate">
                            <el-option v-for="item in sourceOptions" :key="item.value" :label="item.label"
                                :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="证书上传：" prop="fileList" v-if="addForm.certSource !== 1">
                        <el-upload class="upload-demo" action="#" accept=".cer" :on-remove="handleRemove"
                            :on-change="handleChange" :limit="1" :auto-upload="false" :file-list="addForm.fileList">
                            <el-button class="comBtn com_send_btn" size="small" type="primary">点击上传</el-button>
                        </el-upload>
                    </el-form-item>

                    <el-form-item v-if="addForm.certSource === 1" label="证书DN：" prop="dn">
                        <el-input size="small" v-model="addForm.dn" maxlength="67" show-word-limit
                            placeholder="DN格式：CN=XXXXX,C=CN"></el-input>
                    </el-form-item>
                    <el-form-item label="用户名：" prop="username">
                        <el-input size="small" v-model="addForm.username" placeholder="请输入用户名"></el-input>
                    </el-form-item>
                    <el-form-item label="姓名：" prop="realName">
                        <el-input size="small" v-model="addForm.realName" placeholder="请输入姓名"></el-input>
                    </el-form-item>
                    <el-form-item label="手机号：" prop="phone">
                        <el-input size="small" v-model="addForm.phone" autocomplete="new-password"
                            placeholder="请输入手机号"></el-input>
                    </el-form-item>
                    <el-form-item v-if="addForm.certSource === 1" label="Ukey PIN码：" prop="pin">
                        <el-input size="small" type="password" autocomplete="new-password" v-model="addForm.pin"
                            placeholder="请输入PIN码"></el-input>
                    </el-form-item>
                    <el-form-item label="设置登录密码：" prop="password">
                        <el-input size="small" type="password" autocomplete="new-password" v-model="addForm.password"
                            placeholder="请输入登录密码"></el-input>
                    </el-form-item>
                    <el-form-item label="确认登录密码：" prop="rePassword">
                        <el-input size="small" type="password" autocomplete="new-password" v-model="addForm.rePassword"
                            placeholder="再次输入登录密码"></el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button class="comBtn com_reset_btn" size="small" @click="closeAdd">取消</el-button>
                    <el-button class="comBtn com_send_btn" size="small" type="primary" :loading="addLoading" @click="addAdmin">保存</el-button>
                    <!-- <el-button class="comBtn com_send_btn" size="small" type="primary" @click="addAdmin">保存</el-button> -->
                </div>
            </el-dialog>

            <!-- 编辑管理员 -->
            <edit-account-dia ref="editAccountDia" @parentHandle='parentHandle'></edit-account-dia>

            <!-- 查看证书 -->
            <view-cert-dia ref="viewCertDia"></view-cert-dia>

            <!-- 证书更新 -->
            <el-dialog title="证书更新" :visible.sync="certUploadVisible" width="30%" :before-close="closeRenew"
                :append-to-body="true" :close-on-click-modal="false">
                <el-form label-width="130px" ref="editForm" :model="editForm" :rules="editRules">
                    <el-form-item label="证书来源：" prop="certSource">
                        <el-select v-model="editForm.certSource" placeholder="请选择" style="width: 100%" size="small"
                            @change="cerSourceChange">
                            <el-option v-for="item in sourceOptions" :key="item.value" :label="item.label"
                                :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="证书上传：" prop="fileList" v-if="editForm.certSource !== 1">
                        <el-upload class="upload-demo" action="#" accept=".cer" :on-remove="handleRemove1"
                            :on-change="handleChange1" :limit="1" :auto-upload="false" :file-list="editForm.fileList">
                            <el-button class="comBtn com_send_btn" size="small" type="primary">点击上传</el-button>
                        </el-upload>
                    </el-form-item>
                    <el-form-item v-if="editForm.certSource === 1" label="证书DN：" prop="dn">
                        <el-input size="small" v-model="editForm.dn" maxlength="67" show-word-limit
                            placeholder="请输入证书DN" autocomplete="new-password"></el-input>
                    </el-form-item>
                    <el-form-item v-if="editForm.certSource === 1" label="Ukey PIN码：" prop="pin">
                        <el-input size="small" type="password" v-model="editForm.pin" placeholder="请输入PIN码"
                            autocomplete="new-password"></el-input>
                    </el-form-item>
                </el-form>

                <div slot="footer" class="dialog-footer">
                    <el-button class="comBtn com_reset_btn" size="small" @click="closeRenew">取消</el-button>
                    <el-button class="comBtn com_send_btn" size="small" type="primary"
                        @click="renewAccountCert">保存</el-button>
                </div>
            </el-dialog>

            <!-- 修改密码 -->
            <el-dialog title="修改密码" :visible.sync="rePasswordVisible" width="600px" :before-close="cleanRePassword" :close-on-click-modal="false">
                <el-form label-width="100px" ref="editPassword" :model="editPassword" :rules="resetRules">
                    <el-form-item label="原密码：" prop="oldPwd">
                        <el-input size="small" type="password" placeholder="请输入原密码" v-model="editPassword.oldPwd" />
                    </el-form-item>
                    <el-form-item label="新密码：" prop="password">
                        <el-input size="small" type="password" placeholder="请输入密码" v-model="editPassword.password" />
                    </el-form-item>
                    <el-form-item label="确认密码：" prop="rePassword">
                        <el-input size="small" type="password" placeholder="请再次输入密码, 确保两次输入的一致"
                            v-model="editPassword.rePassword" />
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button class="comBtn com_reset_btn" size="small" @click="cleanRePassword">取消</el-button>
                    <el-button class="comBtn com_send_btn" size="small" type="primary"
                        @click="rePassword">保存</el-button>
                </div>
            </el-dialog>
            <!-- 重置密码 -->
            <el-dialog title="重置密码" :visible.sync="resetVisible" width="500px" :before-close="closeReset"
                :close-on-click-modal="false">
                <el-form label-width="100px" ref="resetPassword" :model="editPassword">
                    <el-form-item label="新密码：" prop="password">
                        <el-input v-model="editPassword.password" readonly />
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button size="small" class="comBtnDef com_add_btn" v-clipboard:copy="editPassword.password"
                        v-clipboard:success="clipboardSuccessHandler" v-clipboard:error="clipboardErrorHandler">复制
                    </el-button>
                    <el-button size="small" type="primary" class="comBtnDef com_send_btn"
                        @click="closeReset">确认</el-button>
                </div>
            </el-dialog>
        </el-card>
    </div>
</template>

<script>
import createTable from "@/utils/createTable";
// import userMG from "@/api/userMG";
import { doSM3, setStore } from "@/utils/util";
// import {exportCertFile, exportcret} from '../../utils/exportExcel'
import { testGetCSR, testImportCert, CHECK_UKEY_STATE } from "../../utils/Ukey";
import viewCertDia from './components/viewCertDia'
import editAccountDia from './components/editAccountDia'

export default {
    name: "administrator-manage",
    components: { createTable, viewCertDia, editAccountDia },
    data() {
        let _this = this;
        const validatePass = (rule, value, callback) => {
            let pass = /^(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])(?=.*[!"#$%&'()*+,\-@/\.])[0-9a-zA-Z!"#$%&'()*+,\-@/\\.]{8,16}$/;
            let middlePass = /^(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])[0-9a-zA-Z]{8,16}$/;
            if (value === '') {
                callback(new Error('请输入密码'));
            } else if (value != '') {
                const b = this.passwordStrength == 1;
                if (b && !pass.test(value)) {
                    callback(new Error('密码格式为8-16位，包含大小写、数字和特殊字符!'));
                } else if (!b && !middlePass.test(value)) {
                    callback(new Error('密码格式为8-16位，包含大小写和数字!'));
                } else {
                    if (this.addForm.rePassword == null || this.addForm.rePassword == '') {
                        callback();
                    } else {
                        if (value != this.addForm.rePassword) {
                            callback(new Error('两次输入密码不一致!'));
                        } else {
                            callback();
                        }
                    }
                }
            } else {
                callback();
            }
        };
        const validatePass2 = (rule, value, callback) => {
            if (value === '') {
                callback(new Error('请再次输入密码'));
            } else {
                if (this.addVisible && value !== this.addForm.password) {
                    callback(new Error('两次输入密码不一致!'));
                } else if (this.addForm.password && value !== this.addForm.password) {
                    callback(new Error('两次输入密码不一致!'));
                } else {
                    callback();
                }
            }
        };
        const validateResetPass = (rule, value, callback) => {
            let pass = /^(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])(?=.*[!"#$%&'()*+,\-@/\.])[0-9a-zA-Z!"#$%&'()*+,\-@/\\.]{8,16}$/;
            let middlePass = /^(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])[0-9a-zA-Z]{8,16}$/;
            if (value === '') {
                callback(new Error('请输入密码'));
            } else if (value != '') {
                const b = this.passwordStrength == 1;
                if (b && !pass.test(value)) {
                    callback(new Error('密码格式为8-16位，包含大小写、数字和特殊字符!'));
                } else if (!b && !middlePass.test(value)) {
                    callback(new Error('密码格式为8-16位，包含大小写和数字!'));
                } else {
                    if (this.editPassword.rePassword == null || this.editPassword.rePassword == '') {
                        callback();
                    } else {
                        if (value != this.editPassword.rePassword) {
                            callback(new Error('两次输入密码不一致!'));
                        } else {
                            callback();
                        }
                    }
                }
            } else {
                callback();
            }
        };
        const validateResetPass2 = (rule, value, callback) => {
            if (value === '') {
                callback(new Error('请再次输入密码'));
            } else {
                if (value !== this.editPassword.password) {
                    callback(new Error('两次输入密码不一致!'));
                } else {
                    callback();
                }
            }
        };
        const iphone1 = (rule, value, callback) => {
            const iphone1 = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
            if (value == null) {
                value = ''
            }
            if (value != '') {
                if (!iphone1.test(value)) {
                    callback("请输入正确的手机号");
                    return false
                } else {
                    callback();
                }
            } else {
                callback();
            }
        };
        const validatorAccountDN = (rule, value, callback) => {
            if (value !== "") {
                if (value.indexOf("，") != -1) {
                    callback(new Error("dn不能包含中文逗号"));
                } else if (value.indexOf(", ") != -1) {
                    callback(new Error("逗号后不能有空格"));
                } else {
                    this.verifyAccountDN(value, callback);
                }
            } else {
                callback();
            }
        };
        const validatorName = (rule, value, callback) => {
            if (value !== "") {
                let mask = RegExp(/[(\ )(\~)(\!)(\@)(\#)(\$)(\%)(\^)(\&)(\*)(\()(\))(\-)(\+)(\=)(\[)(\])(\{)(\})(\|)(\\)(\;)(\:)(\')(\")(\，)(\。)(\/)(\<)(\>)(\?)(\)]+/);
                if (mask.test(value)) {
                    callback(new Error("不能含有特殊字符！"));
                } else {
                    callback();
                }
            } else {
                callback();
            }
        };
        return {
            tableDataHeader: [
                { label: '序号', type: "index", width: '60' },
                { label: '证书主题', prop: 'dn', type: "normal" },
                { label: '用户名', prop: 'username', type: "normal", width: '200' },
                { label: '姓名', prop: 'realName', type: "normal", width: '200' },
                { label: '手机号', prop: 'phone', type: "normal", width: '150' },
                {
                    label: '关联角色',
                    prop: 'roleName',
                    type: "normal",
                    // width: '240',
                },
                {
                    label: "操作",
                    prop: "1",
                    type: "operation",
                    width: "300",
                    tag: [
                        {
                            name: "编辑",
                            operType: "put",
                            tagType: "el-button",
                            attributes: {
                                size: "mini",
                                type: "text",
                                icon: "el-icon-edit"
                            },
                            callback: function (row) {
                                _this.editAccount(row);
                            }
                        }, {
                            name: "删除",
                            operType: "del",
                            tagType: "el-button",
                            attributes: {
                                size: "mini",
                                type: "text",
                                icon: "el-icon-delete"
                            },
                            isShow: function (row) {
                                return row.system;
                            },
                            callback: function (row) {
                                _this.deleteAccount(row);
                            }
                        },
                        {
                            name: "更多",
                            operType: "config",
                            tagType: "el-dropdown",
                            attributes: {},
                            /*on: {
                                "command": (command, row) => {
                                  let _this = this;
                                  let c = arguments;
                                  alert(command)
                                }
                              },*/
                            children: [
                                {
                                    name: "更多",
                                    tagType: "el-button",
                                    attributes: {
                                        size: "mini",
                                        type: "text",
                                        icon: "el-icon-setting"
                                    }
                                },
                                {
                                    tagType: "el-dropdown-menu", attributes: { slot: "dropdown" },
                                    children: [
                                        {
                                            name: "证书更新",
                                            tagType: "el-dropdown-item",
                                            attributes: {},
                                            callback: function (row) {
                                                _this.updateCert(row);
                                            }
                                        }, {
                                            name: "证书下载",
                                            tagType: "el-dropdown-item",
                                            attributes: {},
                                            callback: function (row) {
                                                _this.certDownloadFun(row);
                                            }
                                        }, {
                                            name: "查看证书",
                                            tagType: "el-dropdown-item",
                                            attributes: {},
                                            callback: function (row) {
                                                _this.searchCert(row);
                                            }
                                        }, {
                                            name: "修改密码",
                                            tagType: "el-dropdown-item",
                                            attributes: {},
                                            callback: function (row) {
                                                _this.rePwd(row)
                                            }
                                        }, {
                                            name: "重置密码",
                                            tagType: "el-dropdown-item",
                                            attributes: {},
                                            callback: function (row) {
                                                _this.resetPwd(row)
                                            }
                                        },
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ],
            // 列表数据
            tableData: [],
            roleOptions: [],
            sourceOptions: [
                { value: 1, label: '证书申请' },
                { value: 2, label: '证书上传' }
            ],
            keyOptions: [
                { value: 'SM2', label: 'SM2' },
                { value: 'RSA2048', label: 'RSA2048' },
                { value: 'RSA4096', label: 'RSA4096' },
            ],
            // 签名算法
            algorithmOptions: [
                { value: 'SM3', label: 'SM3' },
                { value: 'SHA256', label: 'SHA256' }
            ],
            addVisible: false,  //是否显示新增界面
            certUploadVisible: false,  //是否显示证书上传界面
            rePasswordVisible: false,  //是否显示修改密码界面
            resetVisible: false,  //是否显示重置密码界面
            passwordStrength: 2, //当前启用策略密码强度
            uploadForm: new FormData(),
            // 分页参数
            pageParam: {
                pageNo: 1,
                pageSize: 10
            },
            total: null,
            editForm: {
                id: '',
                roleId: '',
                certSource: 1,
                secretKey: 'SM2',
                digestEncAlg: 'SM3',
                dn: '',
                pin: '',
                fileList: [],
                realName: '',
                phone: '',
                password: '',
                certType: '',
                keytLength: '',
                rePassword: '',
                issuerDn: '',
                serialNumber: '',
                validity: '',
                fingerprintSha1: '',
                fingerprintMd5: ''
            },
            addLoading: false,
            addForm: {
                id: '',
                roleId: [],
                certSource: 1,
                secretKey: 'SM2',
                digestEncAlg: 'SM3',
                dn: '',
                fileList: [],
                username: '', // 用户名
                realName: '',
                phone: '',
                password: '',
                certType: '',
                keytLength: '',
                rePassword: '',
                pin: ''
            },
            editPassword: {
                id: '',
                oldPwd: '',
                password: '',
                rePassword: ''
            },
            rules: {
                dn: [
                    { required: true, message: '请输入申请证书DN', trigger: 'blur' },
                    { validator: validatorAccountDN, trigger: 'blur' }
                ],
                username: [
                    { required: true, message: '请输入用户名', trigger: 'blur' },
                    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' },
                    { validator: validatorName, trigger: 'blur' }
                ],
                realName: [
                    // { required: true, message: '请输入姓名', trigger: 'blur' },
                    { message: '请输入姓名', trigger: 'blur' },
                    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' },
                    { validator: validatorName, trigger: 'blur' }
                ],
                password: [
                    { required: true, message: '请输入密码', trigger: 'blur' },
                    { min: 6, max: 16, message: '请输入8-16位字符', trigger: 'blur' },
                    { validator: validatePass, trigger: 'blur' }
                ],
                rePassword: [
                    { required: true, message: '请再次输入密码', trigger: 'blur' },
                    { validator: validatePass2, trigger: 'blur', required: true }
                ],
                roleId: [{ required: true, message: '请选择管理员角色', trigger: 'change' }],
                pin: [{ required: true, message: '请输入PIN码', trigger: 'blur' }],
                name: [{ required: true, message: '请输入名字', trigger: 'blur' }],
                oldPwd: [{ required: true, message: '请输入原密码', trigger: 'blur' }],
                fileList: [{ required: true, message: '请上传证书', trigger: 'blur' }],
                phone: [
                    // { required: true, message: '请输入正确的手机号', trigger: 'blur' },
                    { message: '请输入正确的手机号', trigger: 'blur' },
                    { validator: iphone1, trigger: 'blur', }
                ]
            },
            editRules: {
                dn: [
                    { required: true, message: '请输申请证书DN', trigger: 'blur' },
                    { validator: validatorAccountDN, trigger: 'blur' }
                ],
                pin: [{ required: true, message: '请输入PIN码', trigger: 'blur' }],
                fileList: [
                    { required: true, message: '请上传证书', trigger: 'blur' }
                ],
            },
            resetRules: {
                oldPwd: [{ required: true, message: '请输入之前的密码', trigger: 'blur' }],
                password: [
                    { required: true, message: '请输入密码', trigger: 'blur' },
                    { validator: validateResetPass, trigger: 'blur' }
                ],
                rePassword: [
                    { required: true, message: '请再次输入密码', trigger: 'blur' },
                    { validator: validateResetPass2, trigger: 'blur', required: true }
                ],
            }
        }
    },
    methods: {
        clipboardSuccessHandler() {
            this.$message.success("复制成功");
        },
        clipboardErrorHandler() {
            this.$message.warning("浏览器不支持，请手动复制。");
        },
        verifyAccountDN(dn, callback) {
            this.$http.userMG.verifyAccountDN({ dn: dn }).then((res) => {
                // const code = res.code;
                if (res.data) {
                    callback();
                } else {
                    callback(res.msg);
                }
            });
        },
        certDownloadFun(row) {
            let _this = this;
            // exportCertFile('post', '/svs/account/download/cert', {id: row.id})
            // exportcret('post', '/svs/account/download/cert', {id: row.id})
            this.$http.userMG.downloadApi({ id: row.id }).then(res => {
                let reader = new FileReader();
                reader.readAsText(res, 'utf-8');
                reader.onload = function (e) {
                    try {
                        const { code, msg } = JSON.parse(e.target.result);
                        let msgStr = msg.split(':')[1]
                        _this.$message({
                            message: msgStr || 'Error',
                            type: 'warning',
                            // duration: 5 * 1000
                        });
                    } catch (err) { // 正常下载
                        let blob = new Blob([res], {
                            type: 'application/force-download'
                        });
                        let fileName = Date.parse(new Date()) + '.cer';
                        if (window.navigator.msSaveOrOpenBlob) {
                            navigator.msSaveBlob(blob, fileName)
                        } else {
                            let link = document.createElement('a');
                            link.href = window.URL.createObjectURL(blob);
                            link.download = fileName;
                            link.click();
                            //释放内存
                            window.URL.revokeObjectURL(link.href)
                        }
                    }
                }
            })
        },
        handleChange(file) {
            let index = file.name.lastIndexOf(".");
            let ext = file.name.substr(index + 1);
            this.addForm.fileList = [];
            if (file.size === 0) {
                this.$message.error('选择文件大小不能为0！');
                this.addForm.fileList = [];
                return false
            } else if (ext !== "cer") {
                this.$message.error('请上传cer格式！');
                this.addForm.fileList = [];
                return false
            } else {
                this.addForm.fileList.push(file);
                this.$refs.addForm.validateField('fileList');
                this.uploadForm.append('adminFile', file.raw);
            }
        },
        handleRemove1() {
            this.uploadForm.delete("adminFile");
            this.editForm.fileList = [];
        },
        handleChange1(file) {
            let index = file.name.lastIndexOf(".");
            let ext = file.name.substr(index + 1);
            this.editForm.fileList = [];
            if (file.size === 0) {
                this.$message.error('选择文件大小不能为0！');
                this.editForm.fileList = [];
                return false
            } else if (ext !== "cer") {
                this.$message.error('请上传cer格式！');
                this.editForm.fileList = [];
                return false
            } else {
                this.editForm.fileList.push(file);
                this.$refs.editForm.validateField('fileList');
                this.uploadForm.append('adminFile', file.raw);
            }
        },
        handleRemove() {
            this.editForm.fileList = [];
            this.uploadForm.delete("adminFile")
        },
        addAccoundHandle() {
            this.addVisible = true;
            this.$nextTick(() => {
                this.$refs["addForm"].clearValidate();
            })
        },
        // 关闭添加
        closeAdd() {
            this.addVisible = false;
            this.firstAddForm();
            this.$refs["addForm"].clearValidate();
        },
        // 证书更新 选择证书来源
        cerSourceChange() {
            this.editForm.dn = "";
            this.editForm.pin = "";
            this.editForm.fileList = [];
            // this.$refs["editForm"].resetFields();
            this.$refs["editForm"].clearValidate();
        },
        // 查看证书
        searchCert(row) {
            this.$refs["viewCertDia"].initViewCertFun(row);
        },
        rePwd(row) {
            this.editPassword.id = row.id;
            this.rePasswordVisible = true;
            this.editPassword.oldPwd = '';
            this.editPassword.password = '';
            this.editPassword.rePassword = '';
        },
        // 更新证书
        updateCert(row) {
            this.uploadForm.append('adminId', row.id);
            this.editForm.id = row.id;
            this.editForm.roleId = row.roleId;
            this.editForm.realName = row.realName;
            this.editForm.phone = row.phone;
            this.editForm.password = row.password;
            this.certUploadVisible = true;
        },
        // 关闭更新证书
        closeRenew() {
            this.certUploadVisible = false;
            this.uploadForm.delete('adminId');
            this.$refs["editForm"].clearValidate();
            this.handleRemove1();
            this.firstEditForm();
        },
        clearValidate() {
            this.$refs["addForm"].clearValidate();
        },
        // 获取当前启用策略
        currentStrategy() {
            this.$http.userMG.queryEnableStrategy().then((res) => {
                const code = res.code;
                if (code === 0) {
                    this.passwordStrength = res.data.passwordStrength;
                }
            })
        },
        deleteAccount(row) {
            this.$confirm('确定要删除吗?', '删除确认', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                let userId = row.id;
                this.$http.userMG.deleteUser(userId).then((res) => {
                    let code = res.code;
                    if (code == '0') {
                        this.$message.success('删除成功');
                        this.pageUser();
                    } else {
                        // this.$message.error(res.msg);
                    }
                })
            })
        },
        // 编辑管理员
        editAccount(row) {
            this.$refs["editAccountDia"].initEditAccountFun(row);
        },
        // 更新证书
        renewAccountCert() {
            this.uploadForm.delete("account");
            this.$refs["editForm"].validate((valid) => {
                if (valid) {
                    // 1 证书申请    2 证书上传
                    this.editForm.certSource == 1 ? this.updateBySystemRegister() : this.updateAccountCert();
                }
            })
        },
        // 更新证书 -> 证书申请
        updateBySystemRegister() {
            const pin = this.editForm.pin;
            const addDn = this.editForm.dn;
            const dn = '/' + addDn.replaceAll(',', '/');
            testGetCSR(pin, dn, (res) => {
                if (res.errCode === 0) {
                    this.uploadForm.append('account', JSON.stringify(this.editForm));
                    this.uploadForm.append('csr', res.b64CertCSR);
                    this.uploadForm.append('pin', this.editForm.pin);
                    this.$http.userMG.uploadCert(this.uploadForm).then((res) => {
                        let code = res.code;
                        if (code == 0) {
                            const certByte = res.data.certByte;
                            testImportCert(this.editForm.pin, certByte, (res) => {
                                res.errCode === 0 ? this.$message.success("更新证书成功！") : this.$message.error("导入证书失败！")
                            });
                        } else {
                            // this.$message.error(res.msg);
                        }
                        this.uploadForm.delete('csr');
                        this.uploadForm.delete('pin');
                        this.uploadForm.delete('account');
                        this.firstEditForm();
                        this.certUploadVisible = false;
                        this.pageUser();
                    })
                } else {
                    this.$message.error(res.msg);
                    this.uploadForm.delete('account');
                    this.cleanAddFrom();
                }
            })
        },
        // 更新证书 -> 证书上传
        updateAccountCert() {
            this.uploadForm.append('account', JSON.stringify(this.editForm));
            this.$http.userMG.uploadCert(this.uploadForm).then(res => {
                if (res.code == "0") {
                    this.$message.success("证书上传成功！");
                    this.certUploadVisible = false;
                } else {
                    // this.$message.error(res.msg);
                    this.certUploadVisible = false;
                }
                this.firstEditForm();
                this.certUploadVisible = false;
                this.uploadForm.delete('adminId');
                this.uploadForm.delete('account');
                this.uploadForm.delete("adminFile");
                this.pageUser();
            });
        },
        //修改密码
        rePassword() {
            this.$refs["editPassword"].validate((valid) => {
                if (valid) {
                    let opt = {
                        'oldPassword': doSM3(this.editPassword.oldPwd),
                        'password': doSM3(this.editPassword.password)
                    };
                    this.$http.userMG.rePassword(this.editPassword.id, opt).then((res) => {
                        const code = res.code;
                        if (code == 0) this.$message.success("密码修改成功！");
                    });
                    this.cleanRePassword();
                }
            })
        },
        resetPwd(row) {
            this.$confirm('确定要重置管理员密码吗?', '编辑确认', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$http.userMG.resetPassword(row.id).then((res) => {
                    const code = res.code;
                    if (code == 0) {
                        this.editPassword.password = res.data;
                        this.resetVisible = true;
                    }
                })
            })
        },
        parentHandle() {
            this.pageUser()
        },
        pageUser() {
            this.tableData = [];
            this.$http.userMG.pageUser(this.pageParam).then((res) => {
                let code = res.code;
                if (code == 0) {
                    this.tableData = res.data;
                    this.total = res.row;
                } 
            })
        },
        // 获取所有的角色
        listRoles() {
            this.$http.userMG.listRoles().then((res) => {
                let code = res.code;
                if (code == '0') {
                    this.roleOptions = res.data;
                    setStore('roleOptions', res.data)
                }
            })
        },
        // 新建管理员
        addAdmin() {
            this.$refs["addForm"].validate((valid) => {
                if (!valid) return;
                let tempAddFrom = JSON.stringify(this.addForm);
                let obj = JSON.parse(tempAddFrom);
                obj.password = doSM3(this.addForm.password);
                obj.rePassword = doSM3(this.addForm.rePassword);
                obj.roleId = this.addForm.roleId.join(',');
                // this.uploadForm.append('account', JSON.stringify(obj));
                if (this.addForm.certSource == 2) {    // 证书上传注册
                    this.uploadForm.append('account', JSON.stringify(obj));
                    this.addAdminCertUpload(this.uploadForm)
                } else {  // 系统注册
                    CHECK_UKEY_STATE(res => {
                        if (res.code !== 0) return this.$message.error(res.msg || 'UKey服务异常！');
                        if (res.data.deviceName.length === 0) return this.$message.error('UKey未插入！');
                        this.uploadForm.append('account', JSON.stringify(obj));
                        const pin = this.addForm.pin;
                        const addDn = this.addForm.dn;
                        const dn = '/' + addDn.replaceAll(',', '/');
                        testGetCSR(pin, dn, (res) => {
                            this.addLoading = true;
                            if (res.errCode === 0) {
                                this.uploadForm.append('csr', res.b64CertCSR);
                                this.uploadForm.append('pin', this.addForm.pin);
                                this.$http.userMG.addUser(this.uploadForm).then((res) => {
                                    let code = res.code;
                                    if (code == 0) {
                                        const certByte = res.data.certByte;
                                        testImportCert(this.addForm.pin, certByte, (ress) => {
                                            const errCode = ress.errCode;
                                            if (errCode == 0) {
                                                this.$message.success("添加管理员成功！");
                                                // this.cleanAddFrom();
                                            } else {
                                                this.deleteAddAccount(res.data.adminId);
                                                this.$message.error("导入证书失败！");
                                            }
                                            this.addLoading = false;
                                            this.cleanAddFrom();
                                        });
                                    } else {
                                        // this.$message.error(res.msg);
                                        this.addLoading = false;
                                        this.cleanAddFrom();
                                    }
                                }).catch(err => {
                                    console.log(err);
                                    this.addLoading = false;
                                    this.cleanAddFrom();
                                })
                            } else {
                                this.cleanAddFrom();
                                this.addLoading = false;
                                this.$message.error("CSR生成异常！");
                                return
                            }
                        })
                    })
                }
            })
        },
        // 新增管理员 证书上传
        addAdminCertUpload(uploadForm) {
            this.addLoading = true;
            this.$http.userMG.addUser(uploadForm).then((res) => {
                this.addLoading = false;
                let code = res.code;
                if (code == '0') this.$message.success("添加管理员成功！");
                this.cleanAddFrom();
                this.addForm.password = null;
                this.addForm.rePassword = null;
            }).catch(err => {
                console.log(err);
                this.addLoading = false;
                this.cleanAddFrom();
            })
        },
        deleteAddAccount(id) {
            this.$http.userMG.deleteUser(id).then((res) => {
                const code = res.code;
                if (code != 0) {
                    // this.$message.error(res.msg);
                }
            })
        },
        cleanAddFrom() {
            this.uploadForm.delete('account');
            this.uploadForm.delete('adminId');
            this.uploadForm.delete("adminFile");
            this.uploadForm.delete("pin");
            this.uploadForm.delete("csr");
            this.addVisible = false;
            // this.addLoading = false;
            this.pageUser();
            this.firstAddForm();
        },
        firstAddForm() {
            Object.assign(this.$data.addForm, this.$options.data().addForm);
        },
        // 清除表单
        firstEditForm() {
            Object.assign(this.$data.editForm, this.$options.data().editForm);
            this.editForm.roleId = 1;
        },
        cleanRePassword() {
            this.$refs["editPassword"].clearValidate();
            this.editPassword.id = null;
            this.editPassword.oldPwd = null;
            this.editPassword.password = null;
            this.editPassword.rePassword = null;
            this.rePasswordVisible = false;
        },
        closeReset() {
            this.$refs["resetPassword"].clearValidate();
            this.editPassword.password = null;
            this.resetVisible = false;
        },
        // 每页显示条数改变
        sizeChange(res) {
            this.pageParam.pageSize = res;
            this.pageUser()
        },
        // 前往页
        currentChange(res) {
            this.pageParam.pageNo = res;
            this.pageUser()
        },
        // 上一页
        prevClick(res) {
            this.pageParam.pageNo = res;
            this.pageUser()
        },
        // 下一页
        nextClick(res) {
            this.pageParam.pageNo = res;
            this.pageUser()
        },
    },
    created() {
        this.listRoles();
        this.pageUser();
        this.currentStrategy();
    }
}
</script>
<style>
.messageIndex {
    z-index: 3000 !important;
}

.el-tooltip__popper {
    max-width: 350px !important;
}

.el-select-dropdown {
    max-width: 400px !important;
}
</style>
<style lang="less" scoped>
/deep/.el-select-dropdown__item {
    width: 300px !important;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/deep/ .el-pagination__total {
    float: left;
}

.el-form-item {
    margin-bottom: 15px;
}

/deep/ .el-input .el-input__count .el-input__count-inner {
    padding: 5px;
    background-color: #f0f0f0;
}

.upload-demo {
    float: left;
    /*margin-left: 40px;*/
}

.el-select {
    width: 200px;
}
</style>

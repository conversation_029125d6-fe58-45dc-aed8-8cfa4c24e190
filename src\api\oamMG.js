import Api from "@/api/apiuri";
import {req, reqCommon, get, reqGet, reqPost, uploadReq, reqParamNoJson,reqCommonForm} from "./axiosFun";
import {getStore} from "@/utils/util";

let oamApi = Api.oamApi;


export default {
    //获取root级别
    deviceInfo() {
        return req("get", '/business/svs/deviceInfo')
    },
    // 服务端-获取日志级别
    getLogLevel() {
        return req("get", '/logback/srv/level')
    },
    factoryReset() {
        return req("get", '/cds/server/factory ')
    },
    getRootLevel() {
        return req("get", oamApi.rootLevel)
    },
    //获取logger列表
    getLoggerList() {
        return req("get", oamApi.loggerList)
    },
    //设置logback root日志级别
    chanageRootLevel(param) {
        return req("get", oamApi.changeLevel + "?allLevel=" + param)
    },
    // 服务端-设置日志级别
    setLogLevel(param) {
        return req("get", "/logback/srv/changeLevel?allLevel=" + param)
    },
    loggerLevel(param) {
        return req("post", oamApi.loggerLevel, param)
    },
    loggerDelete(param) {
        return reqParamNoJson("post", oamApi.loggerDelete, param);
    },
    //下载日志
    downloadLog(param) {
        return reqCommon("post", oamApi.downloadLog, param, {responseType: 'blob'})
    },
    //下载日志
    downloadSvsGoLog(param) {
        return reqCommon("post", oamApi.downloadGoLog, param, {responseType: 'blob'})
    },
    //密码卡信息查询
    selectCryptoCard() {
        return req("post", oamApi.cryptoCardSelect)
    },
    //密码卡登录
    cryptoCardLogin(params) {
        return req("post", oamApi.cryptoCardLogin, params)
    },
    //退出加密卡
    cryptoCardLoginOut() {
        return req("post", oamApi.cryptoCardLoginOut)
    },
    //查看加密卡状态
    getCryptoCardStatus() {
        return req("get", oamApi.cryptoCardStatus)
    },
    //获取生成密码卡主管用户状态
    getGenerateManagerStatus() {
        return req("get", oamApi.cryptoManagerStatus)
    },
    //生成密码卡主管用户
    addCardManager(params) {
        return req("post", oamApi.generateManager, params)
    },
    //生成密码卡操作用户
    addCardUser(params) {
        return req("post", oamApi.generateUser, params)
    },
    //清空UKey
    cleanUKey() {
        return req("post", oamApi.cleanUKey)
    },
    //生成设备密钥
    genDeviceKey(params) {
        return req("post", oamApi.genDeviceKey, params)
    },
    // 系统升级 列表
    upgradeApi(url, params) {
        return get("get", url + oamApi.upgradeUrl, params)
    },
    // 系统升级
    updateApi(url, params) {
        return reqPost("post", url + oamApi.updateUrl, params)
    },
    // 生成设备证书申请
    generateDeviceCertApply(params) {
        return reqCommon("post", oamApi.applyCert, params, {responseType: 'blob'})
    },
    // 一键导入设备证书
    importDeviceCert(params, callBack) {
        return uploadReq("post", oamApi.importSysCertApply, params, callBack)
    },
    //获取设备信息
    getDeviceInfo() {
        return req("get", oamApi.deviceInfo)
    },
    //重启devMg服务
    restartDevMg() {
        return req("post", oamApi.restartDevMg);
    },
    //设置本地设备ID
    submitDeviceInfo(params) {
        return req("put", "/csm/sys/set/devInfo", params);
    },
    //获取go日志级别
    getGoLevel() {
        return req("get", oamApi.goLevel);
    },
    //设置go日志级别
    changeGoLevel(params) {
        return req("post", oamApi.changeGoLevel, params);
    },
    //获取抓包列表
    tcpdumpPage(params) {
        return get("get", "/svs/tcpdump/page", params);
    },
    //算力信息
    getHardEngine() {
        return req("get", "/hardEngine/info");
    },
    //算力配置修改
    configEdit(params) {
        return req("post", "cds/hsm/config", params);
    },
    //算力配置查看
    configGet() {
        return req("get", "cds/hsm/config");
    },
    //启动抓包
    tcpdumpStart(params) {
        return req("post", "/tcpdump/start", params);
    },
    //停止抓包
    tcpdumpStop(params) {
        return req("post", "/tcpdump/stop", params);
    },
    //下载抓包
    tcpdumpDownload(id) {
        let option = {
            headers: {
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
                "token": "Bearer "+getStore('token')
            },
            responseType: 'blob'
        };
        return reqCommonForm("post", '/tcpdump/download/'+id, {}, option)
    },
    //删除抓包
    tcpdumpDel(id) {
        return req("delete", "/tcpdump/del/"+id);
    },
}

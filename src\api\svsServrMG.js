import {req, reqParamNoJson, reqGet} from './axiosFun';
import API from "@/api/apiuri"

let svsServerApi = API.svsServerApi;

// 获取端口列表
const portListApi = (params) => {return reqGet("get", `/multi/port/page`, params)};
// 新增端口
const addPortApi = (params) => {return req("post", `/multi/port/insert`, params)};
// 修改端口
const editPortApi = (params) => {return req("put", `/multi/port/update`, params)};
// 删除端口
const delPortApi = (id) => {return req("delete", `/multi/port/delete/${id}`)};
// 下发策略
const sendApi = () => {return req("post", `/multi/port/sendStrategy`)};

export default {
  portListApi,
  addPortApi,
  editPortApi,
  delPortApi,
  sendApi,

  list(param) {
    return reqParamNoJson("POST", svsServerApi.list, {});
  },
  queryById(id) {
    return reqParamNoJson("GET", svsServerApi.queryById + "/" + id, {});
  },
  edit(param){
    return reqParamNoJson("POST", svsServerApi.edit , param);
  },
  changeStatus(status, id){
    return reqParamNoJson("POST", svsServerApi.changeStatus , {status:status,id:id});
  }
}





<template>
    <!-- <el-container class="index-con">
      <el-header class="index-header">
        <navcon></navcon>
      </el-header>
      <el-container class="index-con">
        <el-aside :class="showclass">
          <leftnav></leftnav>
        </el-aside>
        <el-container class="main-con">
          <el-main clss="index-main">
            <router-view></router-view>
          </el-main>
        </el-container>
      </el-container>
    </el-container> -->
    <div style="height: 100%">
        <div class="license_tip yellow" v-if=" license && expireTime > 0 && expireTime < 30">{{ `您的产品 License 还有${expireTime}天到期, 请及时更新!` }}</div>
        <div class="license_tip red" v-else-if=" license && expireTime <= 0 ">{{ `您的产品 License已到期, 请及时更新!` }}</div>
        <div class="license_tip yellow" v-else-if=" !license ">{{ `您未设置产品 License, 请及时设置!` }}</div>
        <el-container class="index-con" :style="allheight">
            <el-aside :class="showclass">
                <div style="height: 100%">
                    <leftnav :allmenu="allmenu" style="height: 100%;" :collapsed="collapsed"></leftnav>
                </div>
            </el-aside>
            <el-container class="main-con">
                <el-header class="index-header" style="background: rgb(51, 65, 87);padding: 0px">
                    <navcon></navcon>
                </el-header>
                <el-main class="index-main">
                    <router-view></router-view>
                </el-main>
                <div class="footer">
                    <div class="pull-right">{{ this.$store.state.customConfig.copyrightInfo || copyright }}</div>
                </div>
            </el-container>

        </el-container>
    </div>
</template>
<script>
    // 导入组件
    import navcon from "@/components/navcon";
    import leftnav from "@/components/leftnav";
    // import userMG from "@/api/userMG";
    import {getStore, removeStore, setStore} from "@/utils/util";
    import {testGetKeyState} from "../utils/Ukey";
    // import {getLicenseApi} from "@/api/about"
    import key from 'keymaster'
    import {mapState} from 'vuex';
    // import router from '../router'
    import {MessageBox} from 'element-ui'

    // document.addEventListener("visibilitychange", function () {
    //     if (!document.hidden && router.currentRoute.path !== '/' && router.currentRoute.path !== '/init' && router.currentRoute.path !== '/initukey') {
    //         if (!getStore('token')) {
    //             setInterval(() => {
    //                 // router.push({path: '/'});
    //                 location.href = '/';
    //             }, 2000);
    //             // ElementUI.MessageBox.confirm('当前登录信息变更，将在2秒后跳转登录页!', '提示', {
    //             MessageBox.confirm('当前token已失效，将在2秒后跳转登录页!', '提示', {
    //                 confirmButtonText: '确 定',
    //                 closeOnClickModal: false,
    //                 closeOnPressEscape: false,
    //                 showClose: false,
    //                 showCancelButton: false,
    //                 showConfirmButton: false,
    //                 type: 'warning',
    //             }).then(() => {
    //                 // window.location.reload();
    //                 // location.href = '/';
    //             });
    //             return
    //         }
    //         // let name = document.querySelector('#USER_NAME').innerHTML;
    //         let USER_ID = document.querySelector('#USER_ID').innerHTML;
    //         console.log(USER_ID);
    //         if (USER_ID !== getStore('userId') && getStore('token')) {
    //             setInterval(() => {
    //                 // location.href = '/home';
    //                 router.push({path: '/home'});
    //                 window.location.reload();
    //             }, 2000);
    //             MessageBox.confirm('当前登录信息变更，将在2秒后刷新页面!', '提示', {
    //                 confirmButtonText: '确 定',
    //                 closeOnClickModal: false,
    //                 closeOnPressEscape: false,
    //                 showClose: false,
    //                 showCancelButton: false,
    //                 showConfirmButton: false,
    //                 type: 'warning',
    //             }).then(() => {
    //                 // window.location.reload();
    //             })
    //         }
    //     }
    // });

    export default {
        name: 'index',
        data() {
            return {
                copyright: '© 2021 svs Copyright',
                expireTime: 9999,
                license: true,//系统是否配置了license false未配置，true已配置
                showclass: 'asideshow',
                // showtype: false, // 无效变量
                collapsed: false,
                allmenu: [],
                interName: '',
                allheight: ''
            }
        },
        // 注册组件
        components: {
            navcon,
            leftnav
        },
        computed: {
            ...mapState({
                WATCH_TOKEN: state => state.WATCH_TOKEN
            }),
        },
        watch: {
            /**
             * 1. 监听 UKeyList 数据变化
             *  a. 当数据为空时 当前所选KEY置空 证书密钥来源切换为"系统";
             *  b. 当前所选KEY拔出时, 当前所选KEY置空;
             * */
            WATCH_TOKEN(newData, oldData) {
                console.log(newData, oldData);
                // alert(newData, oldData)
            },
        },
        methods: {
            getLicenseFun() {
                let _this = this;
                this.$http.about.getLicenseApi({}).then(({code, data, msg}) => {
                    if (code === 30005) {
                        _this.license = false;
                        _this.allheight = 'height:calc(100% - 22px)';
                        return;
                    }
                    let _data = data.data;
                    if ((typeof data.data == "string") && data.data.constructor == String) {
                        _data = eval('(' + data.data + ')');
                    }
                    var now = new Date();
                    if (_data.endTime) {
                        var until = new Date(_data.endTime < 10000000000 ? _data.endTime * 1000 : _data.endTime);
                        var days = ~~(until / 1000 / 3600 / 24) - ~~(now / 1000 / 3600 / 24);
                        var day = Math.floor(days);
                        _this.expireTime = day + 1;
                    } else {
                        _this.expireTime = 99999;
                    }
                    if (!_this.license || _this.expireTime < 30) {
                        _this.allheight = 'height:calc(100% - 22px)';
                    }
                }, (err) => {
                    if (err.data.code === 30005) {
                        _this.license = false;
                        _this.allheight = 'height:calc(100% - 22px)';
                    }
                    if (err.data.code === 30008) {
                        let _data = err.data.data.data;
                        if ((typeof err.data.data.data == "string") && err.data.data.data.constructor == String) {
                            _data = eval('(' + err.data.data.data + ')');
                        }
                        var now = new Date();
                        if (_data.endTime) {
                          var until = new Date(_data.endTime < 10000000000 ? _data.endTime * 1000 : _data.endTime);
                          var days = ~~(until / 1000 / 3600 / 24) - ~~(now / 1000 / 3600 / 24);
                          var day = Math.floor(days);
                          _this.expireTime = day + 1;
                        } else {
                          _this.expireTime = 99999;
                        }
                        _this.allheight = 'height:calc(100% - 22px)';
                    }
                })
            },
            listMenus() {
                let menuStr = getStore("menus");
                this.allmenu = JSON.parse(menuStr);
            },
            //检测UKey状态
            ukeyStatusPoll() {
                let drawKeyPoll = getStore("drawKeyPoll");
                if (drawKeyPoll == "1" && getStore('token')) {
                    this.interName = setInterval(() => {
                        testGetKeyState((res) => {
                            if (res.state != "1" && this.$route.path != '/') {
                                // 退出登录
                                this.$http.userMG.loginOut().then((res) => {
                                    const code = res.code;
                                    if (code === 0) {
                                        this.$store.commit('SET_IS_SHOW_MENU', false);
                                        window.localStorage.clear();
                                        this.$router.push("/");
                                        clearInterval(this.interName)
                                    }
                                });
                                return false
                            }
                        })
                    }, 5000);
                    setStore("interName", this.interName);
                }
            },
            // 组合键
            compositeKeyEvent () {
                console.log(**********);
                // this.$store.commit('SET_IS_SHOW_MENU', true)
                this.$store.commit('SET_IS_SHOW_MENU', !this.$store.state.isShowMenu)
            }
        },
        created() {
            // 监听
            this.getLicenseFun();
            this.listMenus();
            this.ukeyStatusPoll();
            let _this = this;
            this.$root.Bus.$on('toggle', value => {
                _this.collapsed = !value;
                if (value) {
                    setTimeout(() => {
                        this.showclass = 'asideshow'
                    }, 300)
                } else {
                    setTimeout(() => {
                        this.showclass = 'aside'
                    }, 300)
                }
            })
        },
        mounted() {
            key('alt+q', this.compositeKeyEvent);
            this.$store.dispatch("getStatusFun");
            this.$store.dispatch("getCardStatusFun");
        }
        // 挂载前状态(里面是操作)
        // beforeMount() {
        //   // 弹出登录成功
        //   this.$message({
        //     message: '登录成功',
        //     type: 'success'
        //   })
        // }
    }
</script>
<style lang="less">
    .license_tip {
        width: 100%;
        padding: 3px 0;
        color: #f7f7f7;
        font-size: 12px;
        text-align: center;
        z-index: 99;
    }

    .yellow {
        background-color: #E6A23C;
    }

    .red {
        background-color: #F56C6C;
    }

    .index-con {
        height: 100%;
        width: 100%;
        box-sizing: border-box;
        background-color: #0c4161;

        .main-con {
            height: 100%;
            background-color: #EEEEEE;
        }
    }

    /* 收起 */
    .aside {
        width: 64px !important;
        height: 100%;
        background-color: #334157;
        margin: 0;
        box-shadow: 0 0 8px 2px #07294e;
    }

    /* 展开 */
    .asideshow {
        z-index: 10;
        width: 240px !important;
        height: 100%;
        background: #24447a;
        margin: 0;
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;
        box-shadow: 0 0 8px 2px #07294e;
    }

    .index-header,
    .index-main {
        padding: 10px;
        /*border-left: 2px solid #333;*/
    }

    .el-scrollbar__wrap {
        overflow-x: hidden;
    }

    .leftmenu .el-scrollbar__wrap .el-scrollbar__view {
        height: 100%;
        background: #f1f1f1
    }

    aside {
        overflow-x: hidden !important;
    }
</style>
<style lang="less" scoped>
    .index-main {
        padding: 10px;
        height: calc(100% - 110px);
        background: #f1f1f1;
        //滚动条凹槽的颜色，还可以设bai置du边框属性 
        &::-webkit-scrollbar-track-piece {
            background-color: #bfbfbf;
        }

        //滚动条的宽度 
        &::-webkit-scrollbar {
            width: 9px;
            height: 9px;
        }

        //滚动条的设置 
        &::-webkit-scrollbar-thumb {
            border-radius: 5px;
            background-color: #dddddd;
            background-clip: padding-box;
            min-height: 28px;
        }
    }
</style>

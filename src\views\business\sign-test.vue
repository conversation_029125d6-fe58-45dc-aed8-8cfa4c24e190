<template>
  <div class="container">
    <el-card class="box-card" shadow="always" style="padding-bottom: 10px;width: 250px">
      <el-form>
        <el-form-item label="CRL验证：">
          <el-upload
            class="upload-demo"
            action="#"
            :on-remove="certRemove"
            :on-change="certChange"
            :limit="1"
            :auto-upload="false"
            :headers="authHeader"
            :file-list="crl.fileList">
            <el-button class="comBtn com_send_btn" size="small" type="primary">点击上传</el-button>
          </el-upload>
        </el-form-item>
        <el-button type="primary" @click="submitForm();">验证</el-button>
      </el-form>
    </el-card>

    <div style="padding: 10px;"></div>

    <el-card class="box-card" shadow="always" style="padding-bottom: 10px;width: 250px">
      <el-form>
        <el-form-item label="登录证书验证：">
          <el-upload
            class="upload-demo"
            action="#"
            :on-remove="certRemove"
            :on-change="certChange"
            :limit="1"
            :auto-upload="false"
            :headers="authHeader"
            :file-list="crl.fileList">
            <el-button class="comBtn com_send_btn" size="small" type="primary">点击上传</el-button>
          </el-upload>
        </el-form-item>
        <el-button type="primary" @click="certValitate();">验证</el-button>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import {getStore} from "@/utils/util";
// import {uploadReq} from "@/api/axiosFun";

export default {
  name: "sign-test",
  data() {
    return {
      authHeader: {token: getStore('token')},
      crl: {
        fileList: [],
        uploadForm: new FormData()
      }
    }
  },
  methods: {
    certRemove(file, fileList) {
      this.crl.fileList = [];
    },
    certChange(file, fileList) {
      let certExt = file.name.split(".")[1];
      if (file.size == "0") {
        this.$message.error('选择文件大小不能为0！')
        this.crl.fileList = []
        this.crl.uploadForm.delete('certFile');
        return false
      } else if (certExt != "cer" || certExt != "cer") {
        this.$message.error('请上传公钥证书格式！')
        this.crl.fileList = [];
        this.crl.uploadForm.delete('certFile');
        return false
      } else {
        this.crl.fileList.push(file);
        this.crl.uploadForm.append('certFile', file.raw);
      }
    }, submitForm() {
      this.$http.axiosFun.uploadReq("POST", "test/checkCertForm", this.crl.uploadForm).then(res => {
        alert(res.data);
        this.crl.fileList = [];
        this.crl.uploadForm.delete('certFile');
      }, reason => {
        this.crl.fileList = [];
        this.crl.uploadForm.delete('certFile');
      });
    }, certValitate() {
      this.$http.axiosFun.uploadReq("POST", "test/checkAllCert", this.crl.uploadForm).then(res => {
        alert(res.msg);
        this.crl.fileList = [];
        this.crl.uploadForm.delete('certFile');
      }, reason => {
        this.crl.fileList = [];
        this.crl.uploadForm.delete('certFile');
      });
    }
  },
  created() {
  }
}
</script>

<style scoped>

</style>

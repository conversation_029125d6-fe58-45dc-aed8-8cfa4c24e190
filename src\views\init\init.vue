<template>
    <div class="top_step">
        <el-steps :active="active" align-center>
            <el-step title="配置网络"></el-step>
            <el-step title="配置站点证书"></el-step>
            <el-step title="创建密码主管"></el-step>
            <el-step title="创建系统管理员"></el-step>
            <el-step title="创建业务管理员"></el-step>
            <el-step title="创建审计管理员"></el-step>
            <el-step title="完成"></el-step>
        </el-steps>

        <p style="color: #e6a23c" v-if="showName !== 'success'">欢迎使用签名验签管理系统。初次使用需要对系统进行初始化。</p>

        <!-- 配置网络 -->
        <div v-if="showName === 'network'" class="step_item_con" style="text-align: center">
            <!--<el-form label-width="200px" ref="netForm" class="init_from" :model="netForm" :rules="rules">-->
            <!--<el-form-item label="IP地址：" prop="ip">-->
            <!--<el-input size="small" v-model="netForm.ip" placeholder="请输入IP地址" style="width: 280px"></el-input>-->
            <!--</el-form-item>-->
            <!--<el-form-item label="掩码：" prop="netmask">-->
            <!--<el-input size="small" v-model="netForm.netmask" placeholder="请输入子网掩码" style="width: 280px"></el-input>-->
            <!--</el-form-item>-->
            <!--<el-form-item label="网关：" prop="gw">-->
            <!--<el-input size="small" v-model="netForm.gw" placeholder="请输入网关地址" style="width: 280px"></el-input>-->
            <!--</el-form-item>-->
            <!--<el-form-item>-->
            <!--<el-button size="small" type="primary" class="title comBtn com_send_btn" @click="saveNetwork()">下一步</el-button>-->
            <!--<a style="cursor: pointer; text-decoration: underline; color: #1e50a2;" @click="downloadPackage">下载证书应用环境安装程序</a>-->
            <!--</el-form-item>-->
            <!--</el-form>-->

            <el-form label-width="100px" ref="netForm" :model="netForm" :rules="rules" style="display: inline-block;padding-right: 30px;width: 400px">
                <el-form-item label="协议类型：" prop="purpose">
                    <!--<el-input size="small" v-model="netForm.ip" placeholder="请输入IP地址"></el-input>-->
                    <el-select v-model="netForm.ipType" size="small" style="width: 100%" @change="changeIpType">
                        <el-option label="IPv4" value="ipv4"></el-option>
                        <el-option label="IPv6" value="ipv6"></el-option>
                        <el-option label="IPv4 & IPv6" value="all"></el-option>
                    </el-select>
                </el-form-item>
                <div v-if="netForm.ipType === 'ipv4' || netForm.ipType === 'all'">
                    <el-form-item label="IPV4地址：" prop="ipaddr" key="ipaddr">
                        <el-input size="small" v-model="netForm.ipaddr" placeholder="请输入IPV4地址"></el-input>
                    </el-form-item>
                    <el-form-item label="子网掩码：" prop="netmask" key="netmask">
                        <el-input size="small" v-model="netForm.netmask" placeholder="请输入子网掩码"></el-input>
                    </el-form-item>
                    <el-form-item label="网关地址：" prop="gateway" key="gateway">
                        <el-input size="small" v-model="netForm.gateway" placeholder="请输入网关地址"></el-input>
                    </el-form-item>
                </div>

                <div v-if="netForm.ipType === 'ipv6' || netForm.ipType === 'all'">
                    <el-form-item label="IPV6地址：" prop="ipv6addr" key="ipv6addr">
                        <el-input size="small" v-model="netForm.ipv6addr" placeholder="请输入IPV6地址"></el-input>
                    </el-form-item>
                    <el-form-item label="前缀长度：" prop="ipv6mask" key="ipv6mask">
                        <el-input size="small" v-model="netForm.ipv6mask" placeholder="请输入前缀长度"></el-input>
                    </el-form-item>
                    <el-form-item label="网关地址：" prop="ipv6Defaultgw" key="ipv6Defaultgw">
                        <el-input size="small" v-model="netForm.ipv6Defaultgw" placeholder="请输入网关地址"></el-input>
                    </el-form-item>
                </div>

                <el-form-item>
                    <el-button size="small" type="primary" class="title comBtn com_send_btn" @click="saveNetwork()">下一步</el-button>
                    <el-button size="small" type="primary" class="title comBtn com_send_btn" @click="active = 1;showName = 'certificate'">跳 过</el-button>
                </el-form-item>
            </el-form>
        </div>

        <!-- 配置设备证书 -->
        <div v-if="showName === 'certificate'" class="step_item_con">
            <el-form label-width="180px" ref="certificateFrom" class="init_from" :model="certificateFrom" :rules="certRules">
                <el-form-item label="设备证书设置类型：" prop="cerSource">
                    <el-select size="small" v-model="certificateFrom.cerSource" placeholder="请选择" @change="cerSourceChange">
                        <el-option label="证书申请" value="1"></el-option>
                        <el-option label="证书上传" value="2"></el-option>
                        <el-option label="证书请求" value="3"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-if="certificateFrom.cerSource != '2'" label="设备证书使用者DN：" prop="dn" key="dn">
                    <el-input size="small" v-model="certificateFrom.dn" maxlength="67" show-word-limit placeholder="请输入证书DN"></el-input>
                    <span class="passwordText">*DN格式：CN=XXXXX,C=CN</span>
                </el-form-item>
                <el-form-item v-if="certificateFrom.cerSource != '2'" label="哈希算法：" prop="algName" key="algName">
                    <el-select size="small" v-model="certificateFrom.algName" placeholder="请选择">
                        <el-option label="SHA256" value="SHA256"></el-option>
                        <el-option label="SHA512" value="SHA512"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-if="certificateFrom.cerSource != '2'" label="证书类型：" prop="certAlgo" key="certAlgo">
                    <el-select size="small" v-model="certificateFrom.certAlgo" placeholder="请选择">
                        <el-option label="RSA2048" value="RSA2048"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item style="text-align: left">
                    <el-button v-if="certificateFrom.cerSource == '3'" class="comBtn com_send_btn" size="small" type="primary" @click="createCsr()">生成证书请求</el-button>
                </el-form-item>
                <el-divider v-if="certificateFrom.cerSource == '3'"></el-divider>
                <el-form-item v-if="certificateFrom.cerSource == '3'" label="请上传站点证书：" style="margin-top: 40px" prop="base64Text" key="base64Text">
                    <el-input v-show="false" v-model="certificateFrom.base64Text"></el-input>
                    <el-upload class="upload-demo form-item-left" :on-change="certUploadChange"
                               action="https://jsonplaceholder.typicode.com/posts/"
                               :file-list="certificateFrom.fileList">
                        <el-button size="small" class="comBtn com_send_btn" type="primary" :loading="uploadButtionLoading">{{uploadButtionText}}</el-button>
                    </el-upload>
                </el-form-item>

                <el-form-item v-if="certificateFrom.cerSource == '2'" label="PIN码：" prop="pin" key="pin">
                    <el-input type="password" size="small" v-model="certificateFrom.pin" placeholder="请输入PIN码"></el-input>
                </el-form-item>
                <el-form-item label="请上传站点证书：" v-if="certificateFrom.cerSource == '2'" prop="base64Text" key="base64Text">
                    <el-input v-show="false" v-model="certificateFrom.base64Text"></el-input>
                    <el-upload class="upload-demo" style="text-align: left" :on-change="onUploadChange"
                               action="https://jsonplaceholder.typicode.com/posts/"
                               :file-list="certificateFrom.fileList">
                        <el-button class="comBtn com_send_btn" size="small" type="primary">点击上传</el-button>
                    </el-upload>
                </el-form-item>
                <el-form-item>
                    <el-button size="small" type="primary" class="title comBtn com_send_btn" @click="saveCertificate" :loading="buttionLoading">下一步</el-button>
                    <a style="cursor: pointer; text-decoration: underline; color: #1e50a2;" @click="downloadPackage">下载证书应用环境安装程序</a>
                </el-form-item>
            </el-form>
        </div>

        <!-- 创建密码主管 -->
        <div v-if="showName === 'cryptographer'" class="step_item_con">
            <ul class="add_con">
                <li class="add_item" v-for="item in addArr" :key="item.id">
                    <h4>{{ item.name }}{{ item.id }}</h4>
                    <div class="el-icon-user-solid"></div>
                </li>
                <li style="text-align: center" v-if="activeNum < 3">
                    <el-button class="add_btn" @click="addItemFun">＋</el-button>
                    <p style="font-size: 14px">请创建密码主管{{ addArr.length + 1 }}</p>
                </li>
            </ul>
            <div style="text-align: center">
                <el-button size="small" type="primary" @click="saveDevManage">下一步</el-button>
            </div>
        </div>

        <!-- 创建系统管理员 -->
        <div v-if="showName === 'system'" class="step_item_con">
            <p style="color: #e6a23c">当前创建系统管理员，请插入管理员Ukey</p>
            <el-form label-width="250px" ref="systemFrom" class="init_from" :model="systemFrom" :rules="rules">
                <el-form-item label="设备证书来源：" prop="certSource" key="certSource">
                    <el-select v-model="systemFrom.certSource" filterable placeholder="请选择" size="small" @change="readUKey">
                        <el-option v-for="item in sourceOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-if="systemFrom.certSource == 1" label="证书DN：" prop="dn" key="dn" style="position: relative">
                    <el-input size="small" v-model="systemFrom.dn" maxlength="67" show-word-limit placeholder="请输入证书DN"></el-input>
                    <span class="passwordText" style="white-space: nowrap;position: absolute">*DN格式：CN=XXXXX,C=CN</span>
                </el-form-item>

                <el-row v-if="systemFrom.certSource != 1">
                    <el-form-item v-if="!readStatus" label="证书上传：" prop="fileList" key="userMobile">
                        <el-upload
                                class="upload-demo myUpload"
                                action="#"
                                :on-remove="comRemoveFile"
                                :on-change="comUploadChange"
                                :multiple='false'
                                :auto-upload="false"
                                :file-list="systemFrom.fileList">
                            <el-button class="comBtn com_send_btn" size="small" type="primary">点击上传</el-button>
                        </el-upload>
                    </el-form-item>
                    <el-form-item label="证书上传：" v-if="readStatus" prop="switchFileList" key="switchFileList">
                        <el-switch
                                v-model="readStatus"
                                active-color="#13ce66"
                                :disabled="true"
                                active-text="已读取UKey证书">
                        </el-switch>
                    </el-form-item>
                </el-row>

                <el-form-item label="用户名：" prop="username" key="username">
                    <el-input size="small" v-model="systemFrom.username" placeholder="请输入用户名"></el-input>
                </el-form-item>
                <el-form-item label="姓名：" prop="realName" key="realName">
                    <el-input size="small" v-model="systemFrom.realName" placeholder="请输入姓名"></el-input>
                </el-form-item>
                <el-form-item label="手机号：" prop="phone" key="phone">
                    <el-input size="small" v-model="systemFrom.phone" placeholder="请输入手机号"></el-input>
                </el-form-item>
                <el-form-item v-if="systemFrom.certSource == '1'" label="Ukey PIN码：" prop="pin" key="pin">
                    <el-input type="password" size="small" v-model="systemFrom.pin" placeholder="请输入PIN码"></el-input>
                </el-form-item>
                <el-form-item label="设置登录密码：" prop="password" key="password" style="position: relative">
                    <el-input size="small" type="password" v-model="systemFrom.password" placeholder="请输入登录密码"></el-input>
                    <span class="passwordText" style="white-space: nowrap;position: absolute">{{passwordText}}</span>
                </el-form-item>
                <el-form-item label="确认登录密码：" prop="rePassword" key="rePassword">
                    <el-input size="small" type="password" v-model="systemFrom.rePassword" placeholder="请再次输入密码"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button size="small" type="primary" class="title comBtn com_send_btn" @click="saveSystem" style="margin-top: 15px">下一步</el-button>
                    <a style="cursor: pointer; text-decoration: underline; color: #1e50a2;" @click="downloadPackage">下载证书应用环境安装程序</a>
                </el-form-item>
            </el-form>
        </div>

        <!-- 创建业务管理员 -->
        <div v-if="showName === 'business'" class="step_item_con">
            <p style="color: #e6a23c">当前创建业务管理员，请插入管理员Ukey</p>
            <el-form label-width="250px" ref="businessFrom" class="init_from" :model="businessFrom" :rules="rules">
                <el-form-item label="设备证书来源：" prop="certSource" key="certSource">
                    <el-select v-model="businessFrom.certSource" filterable placeholder="请选择" size="small" @change="readUKey">
                        <el-option v-for="item in sourceOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-if="businessFrom.certSource == 1" label="证书DN：" prop="dn" key="dn" style="position: relative">
                    <el-input size="small" v-model="businessFrom.dn" maxlength="67" show-word-limit placeholder="请输入证书DN"></el-input>
                    <span class="passwordText" style="white-space: nowrap;position: absolute">*DN格式：CN=XXXXX,C=CN</span>
                </el-form-item>

                <el-row v-if="businessFrom.certSource !== 1">
                    <el-form-item v-if="!readStatus" label="证书上传：" prop="fileList" key="userMobile">
                        <el-upload
                                class="upload-demo myUpload"
                                action="#"
                                :on-remove="comRemoveFile"
                                :on-change="comUploadChange"
                                :multiple='false'
                                :auto-upload="false"
                                :file-list="businessFrom.fileList">
                            <el-button class="comBtn com_send_btn" size="small" type="primary">点击上传</el-button>
                        </el-upload>
                    </el-form-item>
                    <el-form-item label="证书上传：" v-if="readStatus" prop="switchFileList" key="switchFileList">
                        <el-switch
                                v-model="readStatus"
                                active-color="#13ce66"
                                :disabled="true"
                                active-text="已读取UKey证书">
                        </el-switch>
                    </el-form-item>
                </el-row>
                <el-form-item label="用户名：" prop="username" key="username">
                    <el-input size="small" v-model="businessFrom.username" placeholder="请输入用户名"></el-input>
                </el-form-item>
                <el-form-item label="姓名：" prop="realName" key="realName">
                    <el-input size="small" v-model="businessFrom.realName" placeholder="请输入姓名"></el-input>
                </el-form-item>
                <el-form-item label="手机号：" prop="phone" key="phone">
                    <el-input size="small" v-model="businessFrom.phone" placeholder="请输入手机号"></el-input>
                </el-form-item>
                <el-form-item v-if="businessFrom.certSource == '1'" label="Ukey PIN码：" prop="pin" key="pin">
                    <el-input type="password" size="small" v-model="businessFrom.pin" placeholder="请输入PIN码"></el-input>
                </el-form-item>
                <el-form-item label="设置登录密码：" prop="password" key="password" style="position: relative">
                    <el-input size="small" type="password" v-model="businessFrom.password" placeholder="请输入登录密码"></el-input>
                    <span class="passwordText" style="white-space: nowrap;position: absolute">{{passwordText}}</span>
                </el-form-item>
                <el-form-item label="确认登录密码：" prop="rePassword" key="rePassword">
                    <el-input size="small" type="password" v-model="businessFrom.rePassword" placeholder="请再次输入密码"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button size="small" type="primary" class="title comBtn com_send_btn" @click="saveBusiness" style="margin-top: 15px">下一步</el-button>
                    <a style="cursor: pointer; text-decoration: underline; color: #1e50a2;" @click="downloadPackage">下载证书应用环境安装程序</a>
                </el-form-item>
            </el-form>
        </div>

        <!-- 创建审计管理员 -->
        <div v-if="showName === 'audit'" class="step_item_con">
            <p style="color: #e6a23c">当前创建审计管理员，请插入管理员Ukey</p>
            <el-form label-width="250px" ref="auditFrom" class="init_from" :model="auditFrom" :rules="rules">
                <el-form-item label="设备证书来源：" prop="certSource" key="certSource">
                    <el-select v-model="auditFrom.certSource" filterable placeholder="请选择" size="small" @change="readUKey">
                        <el-option v-for="item in sourceOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-if="auditFrom.certSource == 1" label="证书DN：" prop="dn" key="dn" style="position: relative">
                    <el-input size="small" v-model="auditFrom.dn" maxlength="67" show-word-limit placeholder="请输入证书DN"></el-input>
                    <span class="passwordText" style="white-space: nowrap;position: absolute">*DN格式：CN=XXXXX,C=CN</span>
                </el-form-item>

                <el-row v-if="auditFrom.certSource !== 1">
                    <el-form-item v-if="!readStatus" label="证书上传：" prop="fileList" key="fileList">
                        <el-upload
                                class="upload-demo myUpload"
                                action="#"
                                :on-remove="comRemoveFile"
                                :on-change="comUploadChange"
                                :multiple='false'
                                :auto-upload="false"
                                :file-list="auditFrom.fileList">
                            <el-button class="comBtn com_send_btn" size="small" type="primary">点击上传</el-button>
                        </el-upload>

                    </el-form-item>
                    <el-form-item label="证书上传：" v-if="readStatus" prop="switchFileList" key="switchFileList">
                        <el-switch
                                v-model="readStatus"
                                active-color="#13ce66"
                                :disabled="true"
                                active-text="已读取UKey证书">
                        </el-switch>
                    </el-form-item>
                </el-row>
                <el-form-item label="用户名：" prop="username" key="username">
                    <el-input size="small" v-model="auditFrom.username" placeholder="请输入用户名"></el-input>
                </el-form-item>
                <el-form-item label="姓名：" prop="realName" key="realName">
                    <el-input size="small" v-model="auditFrom.realName" placeholder="请输入姓名"></el-input>
                </el-form-item>
                <el-form-item label="手机号：" prop="phone" key="phone">
                    <el-input size="small" v-model="auditFrom.phone" placeholder="请输入手机号"></el-input>
                </el-form-item>
                <el-form-item v-if="auditFrom.certSource == '1'" label="Ukey PIN码：" prop="pin" key="pin">
                    <el-input size="small" type="password" v-model="auditFrom.pin" placeholder="请输入PIN码"></el-input>
                </el-form-item>
                <el-form-item label="设置登录密码：" prop="password" key="password" style="position: relative">
                    <el-input size="small" type="password" v-model="auditFrom.password" placeholder="请输入登录密码"></el-input>
                    <span class="passwordText" style="white-space: nowrap;position: absolute">{{passwordText}}</span>
                </el-form-item>
                <el-form-item label="确认登录密码：" prop="rePassword" key="rePassword">
                    <el-input size="small" type="password" v-model="auditFrom.rePassword" placeholder="请再次输入密码"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button size="small" type="primary" class="title comBtn com_send_btn" @click="saveAudit" style="margin-top: 15px">下一步</el-button>
                    <a style="cursor: pointer; text-decoration: underline; color: #1e50a2;" @click="downloadPackage">下载证书应用环境安装程序</a>
                </el-form-item>
            </el-form>
        </div>
        <!-- 重启系统 -->
        <div v-if="showName === 'success'" class="step_item_con" style="text-align: center">
            <p style="font-size: 18px; color: #e6a23c">恭喜您，初始化签名验签管理系统成功！</p>
            <el-button size="small" type="primary" class="comBtn com_send_btn" @click="restartService">重启服务</el-button>
        </div>

        <!-- 添加密码主管 弹窗 -->
        <el-dialog title="添加密码主管" :visible.sync="addMangeShow" width="500px" @click="closeDialog" :close-on-click-modal="false">
            <el-form :model="devKeyForm" :rules="manageRules" ref="mangeForm" label-width="100px" class="demo-ruleForm">
                <el-form-item label="选择UKey：" prop="serial" key="serial" style="text-align: left">
                    <el-select v-model="devKeyForm.serial" placeholder="请选择" size="small" @change="changeSerialHandle">
                        <span v-for="item in initUKeyList" :key="item.serial">
                            <el-option
                                    :disabled="item.chosen === 1"
                                    :label="item.serial"
                                    :value="item.serial">
                            </el-option>
                        </span>
                    </el-select>
                </el-form-item>
                <el-form-item label="口令：" prop="pin">
                    <el-input v-model="devKeyForm.pin" clearable size="small" type="password" placeholder="**********"></el-input>
                </el-form-item>
                <el-form-item label="确认口令：" prop="confirmPassword">
                    <el-input v-model="devKeyForm.confirmPassword" clearable size="small" type="password" placeholder="**********"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button size="small" @click="closeDialog">取消</el-button>
                <el-button size="small" type="primary" :loading="buttionLoading" class="title" @click="submitAddManageForm('ruleForm')">保存</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import userMG from "@/api/userMG";
    // import {setStore} from "@/utils/util";
    import {doSM3} from "@/utils/util";
    import systemMG from "@/api/systemMG";
    import initGm from "../../api/initMG";
    import {downloadPackage} from '@/utils/exportExcel'
    import {iphone1, validatorIp, validatorGW, validatorMask, validatorName, checkIPV6, checkIPV6Mask} from "./check"
    import {testGetCSR, testImportCert, testExportCert, GET_KEY_STATE, INIT_UKEY_FUN, testGetKeyState} from "../../utils/Ukey";

    export default {
        name: "init",
        data() {
            const validatePass = (rule, value, callback) => {
                const highPass = /^(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])(?=.*[!"#$%&'()*+,\-@/\.])[0-9a-zA-Z!"#$%&'()*+,\-@/\\.]{8,16}$/;
                const middlePass = /^(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])[0-9a-zA-Z]{8,16}$/;
                if (value === '') {
                    callback(new Error('请输入密码'));
                } else if (value != '') {
                    const b = this.passwordStrength === 1;
                    if (b && !highPass.test(value)) {
                        callback(new Error('密码格式为8-16位，包含大小写、数字和特殊字符!'));
                    } else if (!b && !middlePass.test(value)) {
                        callback(new Error('密码格式为8-16位，包含大小写和数字!'));
                    } else {
                        // 创建系统管理员，校验密码
                        if (this.active === 3) {
                            if (this.systemFrom.rePassword == null || this.systemFrom.rePassword === '') {
                                callback();
                            } else {
                                if (value !== this.systemFrom.rePassword) {
                                    callback(new Error('两次输入密码不一致!'));
                                } else {
                                    callback();
                                }
                            }
                        }
                        // 创建业务管理员，校验密码
                        if (this.active === 4) {
                            if (this.businessFrom.rePassword == null || this.businessFrom.rePassword === '') {
                                callback();
                            } else {
                                if (value !== this.businessFrom.rePassword) {
                                    callback(new Error('两次输入密码不一致!'));
                                } else {
                                    callback();
                                }
                            }
                        }
                        // 创建审计管理员，校验密码
                        if (this.active === 5) {
                            if (this.auditFrom.rePassword == null || this.auditFrom.rePassword === '') {
                                callback();
                            } else {
                                if (value !== this.auditFrom.rePassword) {
                                    callback(new Error('两次输入密码不一致!'));
                                } else {
                                    callback();
                                }
                            }
                        }
                    }
                } else {
                    callback();
                }
            };
            const validatePass2 = (rule, value, callback) => {
                if (value === '') return callback(new Error('请再次输入密码'));
                // 创建系统管理员，校验密码
                this.active === 3 && value !== this.systemFrom.password ? callback(new Error('两次输入密码不一致!')) : callback();
                // 创建业务管理员，校验密码
                this.active === 4 && value !== this.businessFrom.password ? callback(new Error('两次输入密码不一致!')) : callback();
                // 创建审计管理员，校验密码
                this.active === 5 && value !== this.auditFrom.password ? callback(new Error('两次输入密码不一致!')) : callback();
            };
            const validatorAccountDN = (rule, value, callback) => {
                if (value !== "") {
                    if (value.indexOf("，") != -1) {
                        callback(new Error("dn不能包含中文逗号"));
                    } else if (value.indexOf(", ") != -1) {
                        callback(new Error("逗号后不能有空格"));
                    } else {
                        this.verifyAccountDN(value, callback);
                    }
                } else {
                    callback();
                }
            };
            const validatorDN = (rule, value, callback) => {
                let chineseExp = RegExp(/^[\u0000-\u007f]+$/);
                if (value !== "") {
                    if (!chineseExp.test(value)) {
                        callback(new Error("DN不能包含中文及中文字符"));
                    }
                    if (value.indexOf("，") != -1) {
                        callback(new Error("dn不能包含中文逗号"));
                    } else if (value.indexOf(", ") != -1) {
                        callback(new Error("逗号后不能有空格"));
                    } else {
                        this.verifyDN(value, callback);
                    }
                } else {
                    callback();
                }
            };
            // const checkPassword = (rule, value, callback) => {
            //     // const highPass = /^(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])(?=.*[!"#$%&'()*+,\-@/\.])[0-9a-zA-Z!"#$%&'()*+,\-@/\\.]{8,15}$/;
            //     const highPass = /[^\u00-\uFF]{8,15}/;
            //     let checkTrim = /^[^\s]*$/;
            //     if (!checkTrim.test(value)) return callback(new Error('不能包含空格!'));
            //     if (value === '') {
            //         callback(new Error('请输入口令'));
            //     } else if (value !== '') {
            //         if (highPass.test(value)) {
            //             callback(new Error('口令不能输入空格、中文!'));
            //         } else {
            //             if (this.devKeyForm.confirmPassword == null || this.devKeyForm.confirmPassword === '') {
            //                 callback();
            //             } else {
            //                 if (value !== this.devKeyForm.confirmPassword) {
            //                     callback(new Error('两次输入口令不一致!'));
            //                 } else {
            //                     callback();
            //                 }
            //             }
            //         }
            //     } else {
            //         callback();
            //     }
            // };
            // const checkPassword1 = (rule, value, callback) => {
            //     if (value === '') {
            //         callback(new Error('请再次输入口令'));
            //     } else {
            //         // 创建系统管理员，校验密码
            //         if (value !== this.devKeyForm.pin) {
            //             callback(new Error('两次输入口令不一致!'));
            //         } else {
            //             callback();
            //         }
            //     }
            // };


            const checkPIN = (rule, value, callback) => {
                // const highPass = /^(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])(?=.*[!"#$%&'()*+,\-@/\.])[0-9a-zA-Z!"#$%&'()*+,\-@/\\.]{8,16}$/;
                const highPass = /[^\u00-\uFF]{8,15}/;
                const spasePass = /\s+/g;
                if (value === '') {
                    callback(new Error('请输入口令'));
                } else if (value !== '') {
                    if (spasePass.test(value)) {
                        callback(new Error('口令不能包含空格'))
                    }
                    if (highPass.test(value)) {
                        // callback(new Error('密码格式为8-16位，包含大小写、数字和特殊字符!'));
                        callback(new Error('口令格式为8-15位，不包含中文!'));
                    } else {
                        if (this.devKeyForm.confirmPassword == null || this.devKeyForm.confirmPassword === '') {
                            callback();
                        } else {
                            if (value !== this.devKeyForm.confirmPassword) {
                                callback(new Error('两次输入口令不一致!'));
                            } else {
                                callback();
                            }
                        }
                    }
                } else {
                    callback();
                }
            };
            const checkPWD = (rule, value, callback) => {
                if (value === '') {
                    callback(new Error('请再次输入口令'));
                } else {
                    if (value !== this.devKeyForm.pin) {
                        callback(new Error('两次输入口令不一致!'));
                    } else {
                        callback();
                    }
                }
            };
            return {
                buttionLoading: false,
                uploadButtionLoading: false,
                uploadButtionText: "上传文件",
                // 创建密码主管
                addMangeShow: false,
                addArr: [],
                initUKeyList: [],
                activeNum: 0,
                devKeyForm: {
                    type: 0,
                    ukeyIndex: 1,
                    serial: '',
                    pin: '',
                    confirmPassword: '',
                },
                sourceOptions: [
                    {value: 1, label: '证书申请'},
                    {value: 2, label: '证书上传'}
                ],
                keyOptions: [
                    {value: 'SM2', label: 'SM2'},
                    {value: 'RSA2048', label: 'RSA2048'},
                    {value: 'RSA4096', label: 'RSA4096'}
                ],
                // 签名算法
                algorithmOptions: [
                    {value: 'SM3', label: 'SM3'},
                    {value: 'SHA256', label: 'SHA256'}
                ],
                ethOptions: [],
                showName: 'network',
                passwordStrength: 1,
                passwordText: '',
                netForm: {  //网络配置
                    ipType: 'ipv4',
                    eth: 'mgr',
                    ipaddr: '',
                    netmask: '',
                    gateway: '',
                    ipv6addr: '',
                    ipv6mask: '',
                    ipv6Defaultgw: '',
                },
                readStatus: false,
                uploadForm: new FormData(),   //系统管理员证书文件]`
                certificateFrom: {
                    cerSource: '1',
                    dn: '',
                    algName: 'SHA256',
                    certAlgo: 'RSA2048',
                    pin: "",
                    base64Text: "",
                    alias: "",
                    fileList: []
                },
                systemFrom: {
                    id: '',
                    roleId: 1,
                    certSource: 1,
                    secretKey: 'SM2',
                    digestEncAlg: 'SM3',
                    dn: '',
                    fileList: [],
                    username: '',
                    realName: '',
                    phone: '',
                    password: '',
                    certType: '',
                    keytLength: '',
                    rePassword: ''
                },
                businessFrom: {
                    id: '',
                    roleId: 2,
                    certSource: 1,
                    secretKey: 'SM2',
                    digestEncAlg: 'SM3',
                    dn: '',
                    fileList: [],
                    username: '',
                    realName: '',
                    phone: '',
                    password: '',
                    certType: '',
                    keytLength: '',
                    rePassword: ''
                },
                auditFrom: {
                    id: '',
                    roleId: 3,
                    certSource: 1,
                    secretKey: 'SM2',
                    digestEncAlg: 'SM3',
                    dn: '',
                    fileList: [],
                    username: '',
                    realName: '',
                    phone: '',
                    password: '',
                    certType: '',
                    keytLength: '',
                    rePassword: ''
                },
                total: 10,
                active: 0,
                tableHeight: 0,
                rules: {
                    // 密码主管校验
                    serial: [{required: true, message: '请选择UKey', trigger: 'change'}],
                    // passwd: [
                    //     {required: true, message: '请输入口令', trigger: 'blur'},
                    //     {min: 8, max: 15, message: '请输入8-15位字符', trigger: 'blur'},
                    //     {validator: checkPassword, trigger: 'blur'}
                    // ],
                    // confirmPassword: [
                    //     {required: true, message: '请再次输入口令', trigger: 'blur'},
                    //     {validator: checkPassword1, trigger: 'blur'}
                    // ],

                    // networkPort: [{required: true, message: "请选择网口", trigger: "blur"}],

                    ipaddr: [
                        {required: true, message: "请输入正确的Ip地址", trigger: "blur"},
                        {validator: validatorIp, trigger: 'blur'}
                    ],
                    // pin: [{required: true, message: "PIN码不能为空", trigger: "blur"}],
                    // mask: [
                    //     {required: true, message: "请输入正确的掩码", trigger: "blur"},
                    //     {validator: validatorMask, trigger: 'blur'}
                    // ],
                    netmask: [
                        {required: true, message: "请输入正确的掩码", trigger: "blur"},
                        {validator: validatorMask, trigger: 'blur'}
                    ],
                    gateway: [
                        {required: true, message: "请输入正确的网关地址", trigger: "blur"},
                        {validator: validatorGW, trigger: 'blur'}
                    ],
                    ipv6addr: [
                        {required: true, message: "请输入正确的IPV6地址", trigger: "blur"},
                        {validator: checkIPV6, trigger: 'blur'}
                    ],
                    ipv6mask: [
                        {required: true, message: "请输入正确的前缀长度", trigger: "blur"},
                        {validator: checkIPV6Mask, trigger: 'blur'}
                    ],
                    ipv6Defaultgw: [
                        {required: true, message: "请输入正确的网关地址", trigger: "blur"},
                        {validator: checkIPV6, trigger: 'blur'}
                    ],


                    dn: [
                        {required: true, message: '请输入申请证书DN', trigger: 'blur'},
                        {validator: validatorAccountDN, trigger: 'blur'}
                    ],
                    username: [
                        {required: true, message: '请输入用户名', trigger: 'blur'},
                        {min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur'},
                        {validator: validatorName, trigger: 'blur'}
                    ],
                    realName: [
                        {required: true, message: '请输入姓名', trigger: 'blur'},
                        {min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur'},
                        {validator: validatorName, trigger: 'blur'}
                    ],
                    password: [
                        {required: true, message: '请输入密码', trigger: 'blur'},
                        {min: 6, max: 16, message: '请输入8-16位字符', trigger: 'blur'},
                        {validator: validatePass, trigger: 'blur'}
                    ],
                    rePassword: [
                        {required: true, message: '请再次输入密码', trigger: 'blur'},
                        {validator: validatePass2, trigger: 'blur'}
                    ],
                    pin: [{required: true, message: '请输入PIN码', trigger: 'blur'}],
                    name: [
                        {required: true, message: '请输入名字', trigger: 'blur'}
                    ],
                    phone: [
                        {required: true, message: '请输入正确的手机号', trigger: 'blur'},
                        {validator: iphone1, trigger: 'blur'}
                    ],
                    base64Text: [{required: true, message: '请上传文件', trigger: 'blur'}],
                    fileList: [{required: true, message: '请上传文件', trigger: 'blur'}]
                },
                certRules: {
                    dn: [
                        {required: true, message: '请输入证书DN', trigger: 'blur'},
                        {validator: validatorDN, trigger: 'blur'}
                    ],
                    pin: [{required: true, message: '请输入PIN码', trigger: 'blur'}],
                    base64Text: [{required: true, message: '请上传文件', trigger: 'blur'}]
                },
                manageRules: {
                    pin: [
                        {required: true, message: '请输入口令', trigger: 'blur'},
                        {min: 8, max: 15, message: '请输入8-15位字符', trigger: 'blur'},
                        {validator: checkPIN, trigger: 'blur'}
                    ],
                    confirmPassword: [
                        {required: true, message: '请再次输入口令', trigger: 'blur'},
                        {validator: checkPWD, trigger: 'blur'}
                    ],
                },
            }
        },
        created() {
            this.checkInit();
            this.queryInitStatus();
            this.currentStrategy();
        },
        methods: {
            downloadPackage() {
                const url = process.env.NODE_ENV === 'production' ? '/svs' : '/svs';
                downloadPackage("get", `${url}/download/uktool`)
            },
            // 获取初始化状态, 已初始化跳转登录界面
            checkInit() {
                userMG.statusInit().then(res => {
                    if (res.code === 0) {
                        if (res.data !== "0") {
                            this.$router.push('/');
                        }
                    } else {
                        this.$message.error(res.msg)
                    }
                })
            },
            // 获取当前启用策略
            currentStrategy() {
                userMG.queryEnableStrategy().then(({data, code}) => {
                    if (code === 0) {
                        this.passwordStrength = data.passwordStrength;
                        this.passwordText = this.passwordStrength === 1 ? '*密码长度8-16位，必须包含大小写、数字和特殊字符！' : '*密码长度8-16位，必须包含大小写和数字！';
                    }
                })
            },
            // 验证DN
            verifyAccountDN(dn, callback) {
                userMG.verifyAccountDN({dn: dn}).then(({code, msg}) => {
                    if (code == 0) {
                        // debugger;
                        callback();
                    } else {
                        callback(msg);
                    }
                });
            },
            verifyDN(dn, callback) {
                systemMG.verifyDN(dn).then(({code, msg}) => code === 0 ? callback() : callback(msg))
            },
            // 公共上传文件
            comUploadChange(file) {
                let formName = this.showName === 'system' ? 'systemFrom' : this.showName === 'business' ? 'businessFrom' : 'auditFrom';
                let index = file.name.lastIndexOf(".");
                let ext = file.name.substr(index + 1);
                this[formName].fileList = [];
                if (file.size === 0) {
                    this.$message.error('选择文件大小不能为0！');
                    this[formName].fileList = [];
                    return false
                } else if (ext !== "cer") {
                    this.$message.error('请上传cer格式！');
                    this[formName].fileList = [];
                    return false
                } else {
                    this[formName].fileList.push(file);
                    this.$refs[formName].validateField('fileList');
                    this.uploadForm.append('adminFile', file.raw);
                }
            },
            // systemRemove() {
            comRemoveFile() {
                let formName = this.showName === 'system' ? 'systemFrom' : this.showName === 'business' ? 'businessFrom' : 'auditFrom';
                this[formName].fileList = [];
                this.uploadForm.delete('adminFile');
            },
            // 保存网卡信息
            changeIpType() {
                this.$refs["netForm"].resetFields();
            },
            // 网络配置
            saveNetwork() {
                this.$refs["netForm"].validate((valid) => {
                    if (valid) {
                        userMG.initEth(this.netForm).then(({code, msg}) => {
                            if (code == 0) {
                                this.showName = 'certificate';
                                this.active = 1;
                                this.setInitInfo("initing");
                            } else {
                                this.$message.error(msg)
                            }
                        });
                    }
                })
            },
            /**
             * 配置站点证书 创建设备密钥
             * */
            saveCertificate() {
                let _this = this;
                this.$refs["certificateFrom"].validate((valid) => {
                    if (valid) {
                        _this.buttionLoading = true;
                        let params = new FormData();
                        Object.keys(this.certificateFrom).forEach(key => {
                            if (this.certificateFrom[key] !== null && this.certificateFrom[key] !== 'null') {
                                params.append(key, this.certificateFrom[key])
                            }
                        });
                        initGm.initSSl(this.certificateFrom).then(
                            resolve => {
                                const code = resolve.code;
                                this.buttionLoading = false;
                                if (code == 0) {
                                    this.showName = 'cryptographer';
                                    this.active = 2;
                                    // 查询密码主管状态
                                    this.viewAdminStatus('');

                                    this.setInitInfo('initing');
                                } else {
                                    this.$message.error(resolve.msg)
                                }
                            },
                            reject => {
                                this.buttionLoading = false;
                            }
                        );
                    }
                })
            },
            /**
             * **************************************** 添加密码主管开始 ********************************************
             * 创建密码主管
             * 判断当前卡的状态;
             * 调用两个接口 1. 查看卡状态; 2. 状态符合继续操作, 否则 调用重置接口清除历史数据 跳转初始化第一步;
             * */
            saveDevManage() {
                this.viewAdminStatus('next')
            },
            // 查看密码主管初始化状态 managerStatus === 3 直接跳转到下一步
            viewAdminStatus(str) {
                initGm.initGetState().then(({code, data, msg}) => {
                    console.log(code, data, msg);
                    const managerStatus = data;
                    if (!code) {
                        if (data === -1) {
                            this.active = 1;
                            this.showName = 'certificate';
                            this.$message.warning(msg);
                            return
                        }
                        if (managerStatus === 0 && str !== 'add') return this.$message.info('请创建3位密码主管!');
                        if (1 < managerStatus < 3 && str === 'next') {
                            this.$message.info('创建密码主管数量不足, 请创建密码主管!')
                        } else if (managerStatus === 3) {
                            this.active = 3;
                            this.showName = 'system';
                            this.addArr = [];
                            this.activeNum = 0;
                            this.setInitInfo("initing");
                        }

                        if (str !== 'next') {
                            this.addArr = [];
                            for (let i = 0; i < managerStatus; i++) {
                                this.addArr.push({id: i + 1, name: "密码主管", isShow: true});
                            }
                        }
                        this.devKeyForm.ukeyIndex = managerStatus + 1;
                    } else {
                        this.$message.warning(msg)
                    }
                });
            },
            // 添加密码主管
            addItemFun() {
                initGm.initUKeyList().then(({code, msg, data}) => {
                    console.log(code, data, msg);
                    if (code === 0 && data.length !== 0) {
                        this.initUKeyList = data;
                        this.addMangeShow = true;
                        this.$nextTick(() => {
                            this.$refs["mangeForm"].clearValidate();
                        });
                        this.viewAdminStatus('add');
                    } else {
                        this.$message.warning('UKey未插入, 请先插入UKey!')
                    }
                });


                // this.addArr.push({id: 1, name: "密码主管", isShow: true});

                // authManage.queryCardStatus().then(({data}) => {
                //     if (data === 1) {
                //         if (this.activeNum === 3) {
                //             this.$message.warning('465456456456')
                //         } else {
                //             this.addMangeShow = true;
                //         }
                //     } else {
                //         this.$confirm('设备状态异常, 是否重置至初始化状态?', '警告', {
                //             confirmButtonText: '确定',
                //             cancelButtonText: '取消',
                //             type: 'warning'
                //         }).then(() => {
                //             authManage.resetApi().then(({code}) => {
                //                 if (!code) this.active = 0
                //             })
                //         }).catch(err => {
                //             console.log(err)
                //         })
                //     }
                // });

            },
            changeSerialHandle() {
                this.devKeyForm.pin = '';
                this.devKeyForm.confirmPassword = '';
                this.$refs["mangeForm"].clearValidate();
            },
            // 提交密码主管
            submitAddManageForm() {
                this.$refs["mangeForm"].validate((valid) => {
                    if (valid) {
                        initGm.initAddManage(JSON.stringify(this.devKeyForm)).then(({code, msg, data}) => {
                            if (!code) {
                                // this.addMangeShow = false;
                                this.activeNum++;
                                this.devKeyForm.ukeyIndex++;
                                this.setInitInfo('initing');
                                // this.devKeyForm.passwd = '';
                                // this.devKeyForm.confirmPassword = '';
                                this.viewAdminStatus('');
                                this.closeDialog('')
                                // Object.assign(this.$data.devKeyForm, this.$options.data().devKeyForm);
                            } else {
                            }
                        });
                    }
                })
            },
            closeDialog() {
                this.addMangeShow = false;
                this.devKeyForm.serial = '';
                this.devKeyForm.pin = '';
                this.devKeyForm.confirmPassword = '';
                this.$refs["mangeForm"].clearValidate();
            },
            //************************************** 添加密码主管结束 ****************************************//
            // 初始化创建系统管理员 INIT_UKEY_FUN
            saveSystem() {
                this.cleanUploadFormFUN();
                this.$refs["systemFrom"].validate((valid) => {
                    if (valid) {
                        let tempSystemFrom = JSON.stringify(this.systemFrom);
                        let obj = JSON.parse(tempSystemFrom);
                        obj.password = doSM3(obj.password);
                        obj.rePassword = doSM3(obj.rePassword);
                        this.uploadForm.append("account", JSON.stringify(obj));
                        this.uploadForm.append("pin", this.systemFrom.pin);
                        if (this.systemFrom.certSource === 1) {
                            // 证书申请流程 清空UKey 初始化
                            // INIT_UKEY_FUN(this.systemFrom.pin, ({code, data, msg}) => {
                            // if (code === 0) {
                            const pin = this.systemFrom.pin;
                            const addDn = this.systemFrom.dn;
                            const dn = '/' + addDn.replaceAll(',', '/');
                            this.$confirm('系统注册会覆盖Ukey中的cer证书，确认注册吗?', '编辑确认', {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning'
                            }).then(() => {
                                testGetCSR(pin, dn, (res) => {
                                    if (res.errCode === 0) {
                                        this.uploadForm.append('csr', res.b64CertCSR);
                                        this.INIT_ADMIN_FUN(this.uploadForm, this.systemFrom.pin, 'business', 4, "initing", this.systemFrom, '系统');
                                    } else {
                                        this.cleanUploadForm(this.systemFrom);
                                        this.$message.error("CSR生成异常！");
                                    }
                                })
                            })
                            // } else {
                            //     this.cleanUploadForm(this.systemFrom);
                            //     this.$message.error(msg);
                            //     return false
                            // }
                            // });
                        } else {
                            // 证书上传 流程
                            this.getUKeyCertFun(this.uploadForm, "初始化管理员成功", 4, 'business', this.systemFrom);
                        }
                    }
                })
            },
            // 初始化业务管理员
            saveBusiness() {
                this.cleanUploadFormFUN();
                this.$refs["businessFrom"].validate((valid) => {
                    if (valid) {
                        let tempBusinessFrom = JSON.stringify(this.businessFrom);
                        let obj = JSON.parse(tempBusinessFrom);
                        obj.password = doSM3(obj.password);
                        obj.rePassword = doSM3(obj.rePassword);

                        this.uploadForm.append("account", JSON.stringify(obj));
                        this.uploadForm.append("pin", this.businessFrom.pin);
                        if (this.businessFrom.certSource === 1) {
                            // INIT_UKEY_FUN(this.businessFrom.pin, ({code, data, msg}) => {
                            //     if (code === 0) {
                            const pin = this.businessFrom.pin;
                            const addDn = this.businessFrom.dn;
                            const dn = '/' + addDn.replaceAll(',', '/');

                            this.$confirm('系统注册会覆盖Ukey中的cer证书，确认注册吗?', '编辑确认', {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning'
                            }).then(() => {
                                testGetCSR(pin, dn, (res) => {
                                    if (res.errCode === 0) {
                                        this.uploadForm.append('csr', res.b64CertCSR);
                                        this.INIT_ADMIN_FUN(this.uploadForm, this.businessFrom.pin, 'audit', 5, "initing", this.businessFrom, '业务');
                                    } else {
                                        this.cleanUploadForm(this.businessFrom);
                                        this.$message.error("CSR生成异常！");
                                    }
                                })
                            })
                            //     } else {
                            //         this.$message.error(msg);
                            //         this.cleanUploadForm(this.businessFrom);
                            //         return false
                            //     }
                            // });
                        } else {
                            this.getUKeyCertFun(this.uploadForm, "初始化管理员成功", 5, 'audit', this.businessFrom);
                        }
                    }
                })
            },
            // 初始化审计管理员
            saveAudit() {
                this.cleanUploadFormFUN();
                this.$refs["auditFrom"].validate((valid) => {
                    if (valid) {
                        let tempAuditFrom = JSON.stringify(this.auditFrom);
                        let obj = JSON.parse(tempAuditFrom);
                        obj.password = doSM3(obj.password);
                        obj.rePassword = doSM3(obj.rePassword);
                        this.uploadForm.append("account", JSON.stringify(obj));
                        this.uploadForm.append("pin", this.auditFrom.pin);
                        if (this.auditFrom.certSource === 1) {
                            // INIT_UKEY_FUN(this.auditFrom.pin, ({code, data, msg}) => {
                            //     if (code === 0) {
                            const pin = this.auditFrom.pin;
                            const addDn = this.auditFrom.dn;
                            const dn = '/' + addDn.replaceAll(',', '/');
                            this.$confirm('系统注册会覆盖Ukey中的cer证书，确认注册吗?', '编辑确认', {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning'
                            }).then(() => {
                                testGetCSR(pin, dn, (res) => {
                                    if (res.errCode === 0) {
                                        this.uploadForm.append('csr', res.b64CertCSR);
                                        this.INIT_ADMIN_FUN(this.uploadForm, this.auditFrom.pin, 'success', 6, "success", this.auditFrom, '审计');
                                    } else {
                                        this.cleanUploadForm(this.auditFrom);
                                        this.$message.error("CSR生成异常！");
                                    }
                                })
                            }).catch(() => {
                                this.cleanUploadForm();
                            })
                            //     } else {
                            //         this.$message.error(msg);
                            //         this.cleanUploadForm(this.auditFrom);
                            //     }
                            // });
                        } else {
                            this.getUKeyCertFun(this.uploadForm, "初始化管理员成功", 6, 'success', this.auditFrom);
                        }
                    }
                })
            },
            /**
             * 初始化 系统管理员/业务管理员/审计管理员
             * */
            INIT_ADMIN_FUN(uploadForm, PIN, showName, active, setInitInfo, currForm, name) {
                userMG.initUser(this.uploadForm).then((res) => {
                    let code = res.code;
                    if (code === 0) {
                        const certByte = res.data.certByte;
                        const adminId = res.data.adminId;
                        testImportCert(PIN, certByte, (res) => {
                            const errCode = res.errCode;
                            if (errCode === 0) {
                                this.showName = showName;
                                this.active = active;
                                this.setInitInfo("initing");
                                this.$message.success(`初始化${name}管理员成功，请更换UKey！`);
                            } else {
                                this.deleteAccount(adminId);
                                this.$message.error("导入证书失败！")
                            }
                            this.cleanUploadForm(currForm);
                        });
                    } else {
                        this.cleanUploadForm(currForm);
                        this.$message.error(res.msg);
                    }
                })
            },
            /**
             * 初始化系统管理员/业务管理员/审计管理员  证书上传
             * @param {[Object]} uploadForm [表单参数]
             * @param {String} msg [提示信息]
             * @param {Number} activeNum [当前步骤]
             * @param {String} showName [当前显示表单]
             * @param {[Object]} dataForm [当前表单]
             * */
            initUserFun(uploadForm, msg, activeNum, showName, dataForm) {
                userMG.initUser(uploadForm).then((res) => {
                    const code = res.code;
                    if (code == 0) {
                        this.$message.success(msg);
                        this.showName = showName;
                        this.active = activeNum;
                        this.cleanUploadForm();
                        this.setInitInfo("initing");
                    } else {
                        this.cleanUploadForm(dataForm);
                        this.$message.error(res.msg)
                    }
                })
            },
            // 拉取证书
            getUKeyCertFun(uploadForm, msg, activeNum, showName, dataForm) {
                if (this.readStatus) {
                    this.uploadForm.delete('certData');
                    GET_KEY_STATE(() => {
                        testExportCert((res) => {
                            const code = res.errCode;
                            if (code === 0) {
                                const certData = res.b64CertData;
                                this.uploadForm.append('certData', certData);
                                this.initUserFun(uploadForm, msg, activeNum, showName, dataForm)
                            } else {
                                this.$message.error("Ukey 证书异常，请检查Ukey！")
                            }
                        })
                    })
                } else {
                    this.initUserFun(uploadForm, msg, activeNum, showName, dataForm)
                }
            },
            // 重启服务
            restartService() {
                this.setInitInfo("success");
                systemMG.rebootDevice().then((res) => {
                    const code = res.code;
                    if (code === 0) {
                        this.$message.success("操作成功，等待重启后重新登录");
                        this.$router.push('/');
                        /*systemMG.setFirewall().then((res) => {
                          const code1 = res.code;
                          if (code1 === 0){
                            this.$router.push('/');
                          } else {
                            this.$message.error(res.msg)
                          }
                        })*/
                    } else {
                        this.$message.error(res.msg)
                    }
                });
            },
            // 清除信息
            cleanUploadFormFUN() {
                this.uploadForm.delete("account");
                // this.uploadForm.delete("adminFile");
                this.uploadForm.delete("certData");
                this.uploadForm.delete("pin");
                this.uploadForm.delete("csr");
                // if (formName) {
                //     formName.password = null;
                //     formName.rePassword = null;
                // }
            },
            cleanUploadForm(formName) {
                this.uploadForm.delete("account");
                this.uploadForm.delete("adminFile");
                this.uploadForm.delete("certData");
                this.uploadForm.delete("pin");
                this.uploadForm.delete("csr");
                if (formName) {
                    formName.password = null;
                    formName.rePassword = null;
                }
            },
            cleanPinAndAccount() {
                this.uploadForm.delete("account");
                this.uploadForm.delete("pin");
            },
            setInitInfo(status) {
                let opt = {
                    'showName': JSON.stringify(this.showName),
                    'active': JSON.stringify(this.active),
                    'netForm': JSON.stringify(this.netForm),
                    'certificateFrom': JSON.stringify(this.certificateFrom),

                    'systemFrom': JSON.stringify(this.systemFrom),
                    'businessFrom': JSON.stringify(this.businessFrom),
                    'auditFrom': JSON.stringify(this.auditFrom)
                };

                let formData = {
                    "step": "init",
                    "status": status,
                    "data": JSON.stringify(opt)
                };
                userMG.setStatus(formData).then((res) => {
                    const code = res.code;
                    if (code == 0) {

                    }
                })
            },
            // 查看初始化的
            queryInitStatus() {
                // this.active = 2;
                // this.showName = 'cryptographer';
                userMG.queryInitInfo().then((res) => {
                    const code = res.code;
                    if (code === 0) {
                        const data = res.data;
                        if (data == null) return;
                        const {showName, active, netForm, certificateFrom, systemFrom, businessFrom, auditFrom} = JSON.parse(data.data);
                        this.showName = JSON.parse(showName);
                        this.active = JSON.parse(active);
                        console.log(showName, active);
                        // this.active = 3;
                        // this.showName = 'system';
                        // showName: 'network',
                        // showName: 'certificate',
                        // showName: 'system',
                        // showName: 'business',
                        // showName: 'audit',
                        // showName: 'success',
                        // this.showName = 'system';
                        // this.active = 2;
                        console.log(JSON.parse(netForm));
                        if (this.active === 2) this.viewAdminStatus();
                        // this.netForm = JSON.parse(netForm);
                        this.certificateFrom = JSON.parse(certificateFrom);
                        this.systemFrom = JSON.parse(systemFrom);
                        this.businessFrom = JSON.parse(businessFrom);
                        this.auditFrom = JSON.parse(auditFrom);
                    } else {
                        this.$message.error(res.msg);
                    }
                })
            },
            onUploadChange(file) {
                const isPkcs12 = (file.raw.type === 'application/x-pkcs12');
                if (!isPkcs12) {
                    this.$message.error('上传文件只能是PKCS12格式!');
                    return;
                }
                let _this = this;
                const isLt1M = file.size / 1024 / 1024 < 1;
                let reader = new FileReader();
                reader.readAsDataURL(file.raw);
                reader.onload = function (e) {
                    _this.certificateFrom.base64Text = this.result.split(",")[1];
                }
            },
            certUploadChange(file) {
                this.uploadButtionLoading = true;
                let defaultButtionText = this.uploadButtionText;
                this.uploadButtionText = "文件上传中...";
                const iscert = "application/x-x509-ca-cert";
                if (!iscert) {
                    this.$message.error('上传文件只能是x509格式!');
                    _this.uploadButtionLoading = false;
                    _this.uploadButtionText = defaultButtionText;
                    return;
                }
                let _this = this;
                const isLt1M = file.size / 1024 / 1024 < 1;
                let reader = new FileReader();
                reader.readAsDataURL(file.raw);
                reader.onload = function (e) {
                    console.log(this.result); //图片的base64数据
                    _this.certificateFrom.base64Text = this.result.split(",")[1];
                    _this.uploadButtionLoading = false;
                    _this.uploadButtionText = defaultButtionText;
                }
            },
            createCsr() {
                this.$refs["certificateFrom"].validateField('dn', (valid) => {
                    if (valid == null || valid === '') {
                        initGm.createCsr(this.certificateFrom).then(response => {
                            let blob = new Blob([response], {
                                type: 'application/force-download'
                            });
                            let fileName = Date.parse(new Date()) + '.csr';
                            if (window.navigator.msSaveOrOpenBlob) {
                                navigator.msSaveBlob(blob, fileName)
                            } else {
                                let link = document.createElement('a');
                                link.href = window.URL.createObjectURL(blob);
                                link.download = fileName;
                                link.click();
                                //释放内存
                                window.URL.revokeObjectURL(link.href);
                                this.setInitInfo('initing');
                            }
                        })
                    }
                })
            },
            cerSourceChange() {
                this.certificateFrom.fileList = [];
                this.certificateFrom.base64Text = "";
                this.certificateFrom.alias = "";
                this.certificateFrom.dn = "";
                this.certificateFrom.pin = "";
                this.$refs["certificateFrom"].clearValidate();
            },
            importCert(pin, base64Cert) {
                testImportCert(pin, base64Cert, res => {
                    const code = res.errCode;
                    if (code != 0) {
                        console.log(res)
                    }
                })
            },
            deleteAccount(id) {
                userMG.deleteAccount({adminId: id}).then((res) => {
                    const code = res.code;
                    if (code != 0) {
                        this.$message.error(res.msg);
                    }
                })
            },
            // 读取UKey 获取证书
            readUKey(value) {
                if (value === 2) {
                    testGetKeyState(({state}) => {
                        if (state !== '1') {
                            this.readStatus = false;
                            this.uploadForm.delete('certData');
                        } else {
                            testExportCert((res) => {
                                if (res.errCode === 0) {
                                    this.readStatus = true;
                                } else {
                                    this.$message.error("Ukey 证书异常，请检查Ukey！")
                                }
                            })
                        }
                    })
                } else {
                    this.readStatus = false;
                    this.uploadForm.delete('certData');
                }
                this.comRemoveFile();
                // this.uploadForm.delete("adminFile");
                if (this.showName === 'system') this.$refs["systemFrom"].clearValidate();
                if (this.showName === 'business') this.$refs["businessFrom"].clearValidate();
                if (this.showName === 'audit') this.$refs["auditFrom"].clearValidate();
            }
        }
    }

    // const convertJson = (param) => {
    //     if (typeof param == "object") {
    //         return JSON.stringify(param);
    //     } else {
    //         return param;
    //     }
    // }
</script>

<style lang="less" scoped>
    .myUpload {
        /deep/ .el-upload-list {
            height: 25px;
            display: inline-block;
            vertical-align: middle;
            margin-left: 10px;

            .el-upload-list__item {
                margin-top: 0;
                transition: none;
            }
        }
    }

    /deep/ .el-input .el-input__count .el-input__count-inner {
        background: #f0f0f0;
        padding: 5px 5px;
    }

    .top_step {
        padding: 150px 200px 0;
        text-align: center;
    }

    .step_item_con {
        text-align: initial;

        .init_from {
            width: 650px;
            position: relative;
            left: 50%;
            transform: translateX(-50%);

            .el-select, .el-input {
                width: 255px;
            }
        }
    }

    .el-form-item {
        margin-bottom: 15px;
    }

    .el-divider--horizontal {
        margin: 8px 0;
        background: 0 0;
        border-top: 1px dashed #e8eaec;
    }

    .RSA4096 {
        color: #13ce66;
    }

    .passwordText {
        margin-top: 20px;
        font-size: 12px;
        color: #8c939d;
        line-height: 1px;
    }

    .form-item-left {
        text-align: left;
    }

    p {
        text-align: center
    }

    .add_con {
        padding-left: 0;
        display: flex;
        list-style: none;
        align-items: center;
        justify-content: center;

        .add_item {
            padding: 10px;
            margin: 0 10px;
            /*border: 1px dashed ;*/
            border-radius: 5px;
            box-shadow: 0 0 5px 2px #eee;

            .el-icon-user-solid {
                font-size: 70px;
            }
        }

        .add_btn {
            width: 50px;
            height: 50px;
            padding: 0;
            border-radius: 50%;

            span {
                font-size: 40px;
            }
        }
    }
</style>

<template>
    <el-dialog title="历史证书" :visible.sync="certHistoryOpen" width="1100px" append-to-body :close-on-click-modal="false">
        <el-table size="small" class="comTab" :data="historyCert.tableData" highlight-current-row element-loading-text="拼命加载中" style="width: 100%;">
            <el-table-column prop="dn" label="证书主题" align="center" show-overflow-tooltip :resizable="false" width="300"></el-table-column>
            <el-table-column prop="sn" label="序列号" align="center" show-overflow-tooltip :resizable="false" width="180"></el-table-column>
            <el-table-column prop="startTime" label="生效时间" align="center" show-overflow-tooltip :resizable="false" width="100">
                <template slot-scope="{row}">
                    {{ row.startTime ? dateFormat("YYYY-mm-dd HH:MM:SS", new Date(row.startTime)) : '-' }}
                </template>
            </el-table-column>
            <el-table-column prop="endTime" label="失效时间" align="center" show-overflow-tooltip :resizable="false" width="100">
                <template slot-scope="{row}">
                    {{ row.endTime ? dateFormat("YYYY-mm-dd HH:MM:SS", new Date(row.endTime)) : '-' }}
                </template>
            </el-table-column>
            <el-table-column prop="keyDesc" label="证书类型" align="center" show-overflow-tooltip :resizable="false" width="90">
                <template slot-scope="{row}">
                    {{ row.keyDesc === 'SM2' ? "SM2" : "RSA" }}
                </template>
            </el-table-column>
            <el-table-column prop="certSource" label="证书来源" align="center" show-overflow-tooltip :resizable="false" width="90">
                <template slot-scope="{row}">
                    {{ row.certSource === 1 ? "本地产生" : row.certSource === 3 ? "P10 请求" : "证书上传" }}
                </template>
            </el-table-column>
            <el-table-column prop="appName" label="绑定应用" align="center" show-overflow-tooltip :resizable="false" width="90">
                <template slot-scope="{row}">
                    {{ row.appName === 1 ? row.appName : "-" }}
                </template>
            </el-table-column>
            <el-table-column prop="usage" label="用途" align="center" show-overflow-tooltip :resizable="false" width="75">
                <template slot-scope="{row}">
                    {{ row.usage | usage-type-filter }}
                </template>
            </el-table-column>
            <el-table-column prop="keyIndex" label="密钥索引" align="center" show-overflow-tooltip :resizable="false" width="85">
                <template slot-scope="{row}">
                    {{ row.keyIndex === "" || row.keyIndex === -1 ? '-' : row.keyIndex }}
                </template>
            </el-table-column>
            <el-table-column align="center" label="操作" show-overflow-tooltip :resizable="false" width="100px" fixed="right">
                <template slot-scope="{$index, row}">
                    <el-button type="text" size="small" icon="el-icon-download" :disabled="row.certResStatus !== 3 && row.certResStatus !== '' && row.certResStatus != null" @click="downloadHistoryCert(row.id)">下载证书</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 分页组件 -->
        <Pagination v-bind:child-msg="pageparm" @callFather="callFather"></Pagination>


        <div slot="footer" class="dialog-footer">
            <el-button size="small" class="comBtn com_reset_btn" @click="certHistoryOpen=false;historyCert.queryParams.id=-1;">关闭</el-button>
        </div>
    </el-dialog>
</template>

<script>
    import bussMG from "@/api/bussMG";
    import Pagination from '../../../components/my-pagination'

    export default {
        components: {Pagination},
        data() {
            return {
                certHistoryOpen: false,
                // 历史证书
                historyCert: {
                    tableData: [],
                    isPage: false,
                    queryParams: {
                        pageNo: 1,
                        pageSize: 10,
                        id: -1
                    },
                },
                // 分页参数
                pageparm: {
                    currentPage: 1,
                    pageSize: 10,
                    total: 0
                }
            }
        },
        methods: {
            dateFormat(fmt, date) {
                let ret;
                const opt = {
                    "Y+": date.getFullYear().toString(),        // 年
                    "m+": (date.getMonth() + 1).toString(),     // 月
                    "d+": date.getDate().toString(),            // 日
                    "H+": date.getHours().toString(),           // 时
                    "M+": date.getMinutes().toString(),         // 分
                    "S+": date.getSeconds().toString()          // 秒
                    // 有其他格式化字符需求可以继续添加，必须转化成字符串
                };
                for (let k in opt) {
                    ret = new RegExp("(" + k + ")").exec(fmt);
                    if (ret) {
                        fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, "0")))
                    }
                }
                return fmt;
            },
            initHistoryCertFun(id) {
                this.historyCert.queryParams.id = id;
                this.queryHistoryById(this.historyCert.queryParams);
                this.certHistoryOpen = true;
            },
            // 历史证书
            queryHistoryById(params) {
                let _this = this;
                bussMG.queryHistoryById(params).then(res => {
                    _this.historyCert.tableData = res.data;
                    _this.pageparm.total = res.row;
                    if (_this.row == 0) _this.historyCert.isPage = false;
                    else _this.historyCert.isPage = true;
                });
            },
            async loadHistoryCertData() {
                let _this = this;
                _this.historyCert.tableData = [];
                await bussMG.queryHistoryById(_this.historyCert.queryParams).then(res => {
                    _this.historyCert.tableData = res.data;
                    _this.pageparm.total = res.row;
                    if (_this.row == 0) _this.historyCert.isPage = false;
                    else _this.historyCert.isPage = true;
                });
            },
            refreshHistoryCert: function () {
                this.loadHistoryCertData()
            },
            // 历史证书列表 下载
            downloadHistoryCert(id) {
                bussMG.downloadHistoryCert(id).then(response => {
                    let blob = new Blob([response], {
                        type: 'application/force-download'
                    });
                    let fileName = Date.parse(new Date()) + '.cer';
                    if (window.navigator.msSaveOrOpenBlob) {
                        navigator.msSaveBlob(blob, fileName)
                    } else {
                        // console.log(3)
                        var link = document.createElement('a');
                        link.href = window.URL.createObjectURL(blob);
                        link.download = fileName;
                        link.click();
                        //释放内存
                        window.URL.revokeObjectURL(link.href)
                    }
                })
            },
            // 分页插件事件
            callFather(params) {
                this.historyCert.queryParams.pageNo = params.currentPage;
                this.historyCert.queryParams.pageSize = params.pageSize;
                this.refreshHistoryCert()
            },
        }
    }
</script>

<style scoped>

</style>

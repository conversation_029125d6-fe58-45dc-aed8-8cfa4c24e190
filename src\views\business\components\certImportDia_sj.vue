<template>
    <el-dialog :title="title" :visible.sync="show" width="600px" append-to-body :close-on-click-modal="false" @close="closeCertDia">
        <el-form ref="certForm" :model="certForm" :rules="rules" label-width="130px">
            <!--<el-form-item prop="certSource" label="导入方式：" v-if="isShowStr !== 'updateCert'" key="certSource">-->
                <!--<el-select v-model="certForm.certSource" placeholder="请选择" size="small" @change="changeImportMode">-->
                    <!--<el-option label="公钥证书" :value="1"></el-option>-->
                    <!--<el-option label="P12 证书" :value="2"></el-option>-->
                    <!--<el-option label="P10 响应" :value="3"></el-option>-->
                <!--</el-select>-->
            <!--</el-form-item>-->
            <el-form-item prop="certSource" label="证书来源：" v-if="isShowStr !== 'updateCert'" key="certSource">
                <el-select v-model="certForm.certSource" placeholder="请选择" size="small" @change="changeImportMode">
                    <el-option label="文件证书" :value="1"></el-option>
                    <el-option label="P12 证书" :value="2"></el-option>
                    <el-option label="P10 响应" :value="3"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="PIN 码：" prop="certPin" v-if="certForm.certSource === 2" key="certPin" style="width: 88%">
                <el-input size="small" v-model="certForm.certPin" type="passwork" placeholder="请输入PIN码"></el-input>
            </el-form-item>
            <!--<el-form-item label="选择验证方式：" prop="doubleCert" v-if="certForm.certSource === 3 && isShowStr !== 'updateCert'" key="doubleCert">-->
                <!--<el-radio-group v-model="certForm.doubleCert" @change="changeRadioHandle">-->
                    <!--<el-radio :label="0">单证</el-radio>-->
                    <!--<el-radio :label="1">双证</el-radio>-->
                <!--</el-radio-group>-->
            <!--</el-form-item> -->
            <el-form-item label="证书类型：" prop="doubleCert" key="doubleCert">
                <el-radio-group v-model="certForm.doubleCert" @change="changeRadioHandle">
                    <el-radio :label="0">应用实体证书</el-radio>
                    <el-radio :label="1">用户证书</el-radio>
                </el-radio-group>
            </el-form-item>
            <!--<el-form-item :label="certForm.doubleCert === 0 || certForm.certSource === 2 ? '上传证书：' : '上传签名证书：'" prop="certFile" >-->
            <!--<el-form-item :label="certForm.doubleCert === 0 || certForm.certSource !== 3 ? '上传证书：' : '上传签名证书：'" prop="certFile" >-->
            <el-form-item :label="'上传签名证书：'" prop="certFile" >
                <el-upload
                        class="upload-demo myUpload"
                        action="#"
                        accept='.cer, .crt, .pem'
                        :on-remove="signCertRemove"
                        :on-change="signCertChange"
                        :multiple='false'
                        :auto-upload="false"
                        :file-list="certForm.signList">
                    <el-button class="comBtn com_send_btn" size="small" type="primary">导入证书</el-button>
                </el-upload>

                <!--<el-upload-->
                        <!--class="upload-demo myUpload"-->
                        <!--drag-->
                        <!--action=""-->
                        <!--:accept="certForm.certSource === 2 ? '.p12, .pfx' : '.cer, .crt, .pem'"-->
                        <!--:on-remove="signCertRemove"-->
                        <!--:on-change="signCertChange"-->
                        <!--:multiple='false'-->
                        <!--:file-list="certForm.signList"-->
                        <!--:auto-upload="false">-->
                    <!--<i class="el-icon-upload"></i>-->
                    <!--<div class="el-upload__text">将文件拖到此处，或<em>点击上传</em> <span class="el-upload__tip" slot="tip" v-show="certForm.certSource === 2">请上传 .p12, .pfx 文件</span><span class="el-upload__tip" slot="tip">请上传 .cer, .crt, .pem 文件</span></div>-->
                    <!--&lt;!&ndash;<div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>&ndash;&gt;-->
                    <!--&lt;!&ndash;<div class="el-upload__tip" slot="tip">提示只能上传zip文件，且不超过5G</div>&ndash;&gt;-->
                <!--</el-upload>-->

            </el-form-item>
            <div v-if="certForm.doubleCert === 1 && certForm.certSource === 3">
                <!--<el-form-item label="上传签名证书：" prop="certFile" key="certFile">-->
                    <!--<el-upload-->
                            <!--class="upload-demo myUpload"-->
                            <!--action="#"-->
                            <!--accept='.cer, .crt, .pem'-->
                            <!--:on-remove="signCertRemove"-->
                            <!--:on-change="signCertChange"-->
                            <!--:multiple='false'-->
                            <!--:auto-upload="false"-->
                            <!--:file-list="certForm.signList">-->
                        <!--<el-button class="comBtn com_send_btn" size="small" type="primary">点击上传</el-button>-->
                    <!--</el-upload>-->
                <!--</el-form-item>-->
                <el-form-item label="上传加密证书：" prop="encCertFile" key="encCertFile">
                    <!--<el-upload-->
                            <!--class="upload-demo myUpload"-->
                            <!--action="#"-->
                            <!--accept='.cer, .crt, .pem'-->
                            <!--:on-remove="encCertRemove"-->
                            <!--:on-change="encCertChange"-->
                            <!--:multiple='false'-->
                            <!--:auto-upload="false"-->
                            <!--:file-list="certForm.encList">-->
                        <!--<el-button class="comBtn com_send_btn" size="small" type="primary">点击上传</el-button>-->
                    <!--</el-upload>-->


                    <el-upload
                            class="upload-demo myUpload"
                            drag
                            action=""
                            accept=".cer, .crt, .pem"
                            :on-remove="encCertRemove"
                            :on-change="encCertChange"
                            :multiple='false'
                            :file-list="certForm.encList"
                            :auto-upload="false">
                        <i class="el-icon-upload"></i>
                        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em> <br/> <span class="el-upload__tip" slot="tip">请上传 .cer, .crt, .pem 文件</span></div>
                        <!--<div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>-->
                    </el-upload>

                </el-form-item>
                <el-form-item label="上传加密密钥：" prop="encKeyFile" key="encKeyFile">
                    <!--<el-upload-->
                            <!--class="upload-demo myUpload"-->
                            <!--action="#"-->
                            <!--drag-->
                            <!--accept='.p7b, .pem'-->
                            <!--:on-remove="encKeyRemove"-->
                            <!--:on-change="encKeyChange"-->
                            <!--:multiple='false'-->
                            <!--:auto-upload="false"-->
                            <!--:file-list="certForm.keyList">-->
                        <!--<el-button class="comBtn com_send_btn" size="small" type="primary">点击上传</el-button>-->
                    <!--</el-upload>-->

                    <el-upload
                            class="upload-demo myUpload"
                            drag
                            action=""
                            accept=".p7b, .pem"
                            :on-remove="encKeyRemove"
                            :on-change="encKeyChange"
                            :multiple='false'
                            :file-list="certForm.keyList"
                            :auto-upload="false">
                        <i class="el-icon-upload"></i>
                        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em> <br/> <span class="el-upload__tip" slot="tip">请上传 .p7b, .pem 文件</span></div>
                        <!--<div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>-->
                    </el-upload>
                </el-form-item>
            </div>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button size="small" class="comBtn com_reset_btn" @click="closeCertDia">取 消</el-button>
            <el-button size="small" class="comBtn com_send_btn" type="primary" @click="submitFormFun">确 定</el-button>
        </div>
    </el-dialog>
</template>

<script>
    import {getStore} from "@/utils/util";
    import bussMG from "@/api/bussMG";
    export default {
        data() {
            return {
                headerObj: {
                    'token': 'Bearer ' + getStore('token')
                },
                title: '证书导入',
                show: false,
                isShowStr: '',
                certForm: {
                    // certSource: 2, // 2 -> 证书上传  3 -> 证书响应
                    certSource: 1, // 1 -> 公钥证书   2 -> P12证书  3 -> P10响应
                    certPin: '', // p12证书 PIN
                    doubleCert: 0, // 0 -> 单证   1 -> 双证
                    certFile: null, // 签名
                    signList: [],
                    encCertFile: null, // 加密
                    encList: [],
                    encKeyFile: null, // 密钥
                    keyList: [],
                    id: '',
                    optionType: 'add',
                },
                rules: {
                    certPin: [
                        {required: true, message: "请输入PIN码", trigger: "blur"}
                    ],

                    doubleCert: [
                        {required: true, message: "请输入PIN码", trigger: "blur"}
                    ],
                    certFile: [
                        {required: true, message: "请上传证书", trigger: "chang"}
                    ],
                    encCertFile: [
                        {required: true, message: "请上传证书", trigger: "chang"}
                    ],
                    encKeyFile: [
                        {required: true, message: "请上传证书", trigger: "chang"}
                    ]
                },
            }
        },
        methods: {
            initCertImportFun(id, str) {
                this.show = true;
                this.isShowStr = str;
                this.certForm.optionType = str;
                this.certForm.id = id
            },
            changeImportMode() {
                this.$refs['certForm'].clearValidate();
            },
            changeRadioHandle () {
                // Object.assign(this.certForm, this.$options.data().certForm)
                this.certForm.certFile = null;
                this.certForm.signList = [];
                this.certForm.encCertFile = null;
                this.certForm.encList = [];
                this.certForm.encKeyFile = null;
                this.certForm.keyList = [];
                this.$refs['certForm'].clearValidate();
            },
            // 上传签名证书
            signCertChange(file) {
                let index = file.name.lastIndexOf(".");
                let certExt = file.name.substr(index + 1);
                this.certForm.signList = [];
                if (file.size === 0) {
                    this.$message.error('选择文件大小不能为0！');
                    this.certForm.signList = [];
                    return false
                } else if (certExt !== "cer" && certExt !== "crt" && certExt !== "pem" && this.certForm.certSource === 1) {
                    this.$message.error('请上传公钥证书格式！');
                    this.certForm.signList = [];
                    return false
                } else if (this.certForm.certSource === 2 && certExt !== "p12" && certExt !== "pfx") {
                    this.$message.error('格式错误！');
                    this.certForm.signList = [];
                    return false
                } else {
                    this.certForm.certFile = file.raw;
                    this.certForm.signList.push(file);
                    this.$refs["certForm"].validateField('certFile');
                }
            },
            signCertRemove() {
                this.certForm.signList = [];
                this.certForm.certFile = null;
            },

            // 上传加密证书
            encCertChange(file) {
                let index = file.name.lastIndexOf(".");
                let certExt = file.name.substr(index + 1);
                this.certForm.encList = [];
                if (file.size === 0) {
                    this.$message.error('选择文件大小不能为0！');
                    this.certForm.encList = [];
                    return false
                } else if (certExt !== "cer" && certExt !== "crt" && certExt !== "pem") {
                    this.$message.error('请上传公钥证书格式！');
                    this.certForm.encList = [];
                    return false
                } else {
                    this.certForm.encCertFile = file.raw;
                    this.certForm.encList.push(file);
                    this.$refs["certForm"].validateField('encCertFile');
                }
            },
            encCertRemove() {
                this.certForm.encList = [];
                this.certForm.encCertFile = null;
            },
            // 上传加密密钥
            encKeyChange(file) {
                let index = file.name.lastIndexOf(".");
                let certExt = file.name.substr(index + 1);
                this.certForm.keyList = [];
                if (file.size === 0) {
                    this.$message.error('选择文件大小不能为0！');
                    this.certForm.keyList = [];
                    return false
                } else if (certExt !== "p7b" && certExt !== "pem") {
                    this.$message.error('请上传公钥证书格式！');
                    this.certForm.keyList = [];
                    return false
                } else {
                    this.certForm.encKeyFile = file.raw;
                    this.certForm.keyList.push(file);
                    this.$refs["certForm"].validateField('encKeyFile');
                }
            },
            encKeyRemove() {
                this.certForm.keyList = [];
                this.certForm.encKeyFile = null;
            },
            // 提交上传证书
            submitFormFun() {
                this.$refs["certForm"].validate((valid) => {
                    if (valid) {
                        console.log(this.certForm);
                        let params = new FormData();
                        Object.keys(this.certForm).forEach(key => {
                            params.append(key, this.certForm[key])
                        });
                        if (this.certForm.certSource !== 3 || this.certForm.doubleCert === 0 ) {
                            params.delete('encCertFile');
                            params.delete('encKeyFile')
                        }
                        bussMG.certImportApi(params).then(({code, data, msg}) => {
                            console.log(code, data, msg);
                            if (code === 0) {
                                this.closeCertDia();
                                this.$message.success('上传成功!');
                                this.$emit('parentData')
                            }
                        })
                    }
                })
            },
            closeCertDia() {
                this.show = false;
                Object.assign(this.certForm, this.$options.data().certForm);
                this.$refs['certForm'].clearValidate();
            }
        }
    }
</script>

<style lang="less" scoped>
    .myUpload{
        /deep/.el-upload-dragger {
            height: 150px;
            .el-icon-upload {
                margin: 24px 0;
            }
        }
        /deep/ .el-upload-list {
            height: 25px;
            display: inline-block;
            vertical-align: middle;
            margin-left: 10px;
            .el-upload-list__item {
                margin-top: 0;
                transition: none ;
            }
        }
        .el-upload__text {
            line-height: 15px;
        }

        .el-upload__tip {
            display: block;
            height: 20px;
            line-height: 20px;
            color: #8b8b8b;
        }
    }
</style>

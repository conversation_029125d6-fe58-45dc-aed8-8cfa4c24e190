<template>
  <div
    v-loading.fullscreen.lock="fullscreenLoading"
    element-loading-text="解析中..."
  >
    <el-card>
      <el-row>
        <label>以太网接口：</label>
        <el-select size="small" v-model="eth" placeholder="请选择" clearable>
          <el-option
            v-for="item in networkList"
            v-if="item.bondMode == null"
            :label="item.name"
            :value="item.name"
            :key="item.name"
          ></el-option>
          <el-option label="lo" value="lo"></el-option>
        </el-select>
        <el-button
          style="margin-left: 20px"
          type="primary"
          size="small"
          v-if="isShow && !isStart"
          @click="getPacket"
          >抓包</el-button
        >
        <el-button
          style="margin-left: 20px"
          type="primary"
          size="small"
          v-if="isShow && isStart"
          :loading="true"
          >抓包中...</el-button
        >
        <el-button
          type="danger"
          size="small"
          v-if="isShow && isStart"
          @click="stopPacket"
          >停止</el-button
        >
      </el-row>

      <el-table
        :data="packetList"
        row-key="id"
        class="comTab"
        :indent="0"
        style="margin-top: 10px"
        height="550"
      >
        <el-table-column align="left" prop="index" label="序号" width="55">
          <template slot-scope="{ row, $index }">
            <span>{{ $index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          prop="pcapfileName"
          label="数据包名"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          align="center"
          prop="startTime"
          label="抓包开始时间"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          align="center"
          prop="endTime"
          label="抓包结束时间"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          align="center"
          prop="statusStr"
          label="状态"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column align="center" prop="handel" label="操作" width="300">
          <template slot-scope="{ row, $index }">
            <el-button
              type="text"
              icon="el-icon-search"
              @click="parseHandle(row, $index)"
              >在线解析</el-button
            >
            <el-button
              type="text"
              icon="el-icon-delete"
              @click="deleteHandle(row)"
              >删除</el-button
            >
            <el-button
              type="text"
              icon="el-icon-download"
              @click="downloadHandle(row)"
              >下载</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <Pagination
        v-bind:child-msg="pageParams"
        @callFather="callPage"
      ></Pagination>
    </el-card>
    <el-drawer
      title="解析结果"
      :visible.sync="drawer"
      :direction="direction"
      :before-close="handleClose"
      :destroy-on-close="true"
      size="90%"
    >
      <!-- <input
        style="margin-left: 20px; margin-bottom: 20px"
        type="file"
        @change="handleFileUpload"
      /> -->
      <el-card class="box-card" shadow="always">
        <el-form
          :inline="true"
          :model="searchParame"
          class="user-search comForm"
          size="small"
        >
          <el-form-item label="" style="background-color: unset">
            <div style="display: flex; flex-direction: row">
              <el-input
                size="small"
                placeholder="请输入源地址"
                style="width: 150px"
                v-model="searchParame.src"
                clearable
              ></el-input>
              <el-input
                size="small"
                placeholder="请输入源端口"
                style="width: 150px; margin-left: 20px"
                v-model="searchParame.srcPort"
                clearable
              ></el-input>
              <el-input
                size="small"
                placeholder="请输入目的地址"
                style="width: 150px; margin-left: 20px"
                v-model="searchParame.dst"
                clearable
              ></el-input>
              <el-input
                size="small"
                placeholder="请输入目的端口"
                style="width: 150px; margin-left: 20px"
                v-model="searchParame.dstPort"
                clearable
              ></el-input>
            </div>
          </el-form-item>
          <el-form-item style="background-color: transparent">
            <el-button
              class="comBtn com_send_btn"
              size="small"
              icon="el-icon-search"
              @click="searchF"
              >搜索</el-button
            >
            <el-button
              class="comBtn com_reset_btn"
              size="small"
              icon="el-icon-refresh"
              @click="cleanSearch"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </el-card>
      <el-table
        ref="tableRef"
        :data="searchData"
        style="width: 100%"
        max-height="750"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        :row-key="(row) => row.id"
      >
        <el-table-column type="expand">
          <template slot-scope="props">
            <el-form
              label-position="right"
              label-width="200px"
              inline
              class="demo-table-expand"
            >
              <el-form-item
                v-for="(value, key, i) in props.row.moreDatas"
                :key="i"
                :label="key"
              >
                <span style="line-break: anywhere; white-space: break-spaces">{{
                  value
                }}</span>
              </el-form-item>
            </el-form>
          </template>
        </el-table-column>
        <el-table-column align="center" label="编号" prop="id">
        </el-table-column>
        <el-table-column align="center" label="源地址" prop="src">
        </el-table-column>
        <el-table-column align="center" label="源端口" prop="srcPort">
        </el-table-column>
        <el-table-column align="center" label="目的地址" prop="dst">
        </el-table-column>
        <el-table-column align="center" label="目的端口" prop="dstPort">
        </el-table-column>
        <el-table-column align="center" label="协议" prop="protocol">
        </el-table-column>
        <el-table-column align="center" label="序列号" prop="seq">
        </el-table-column>
        <el-table-column align="center" label="确认号" prop="ack">
        </el-table-column>
        <el-table-column align="center" label="头部长度" prop="headerLength">
        </el-table-column>
      </el-table>
    </el-drawer>
  </div>
</template>

<script>
import Pagination from "@/components/my-pagination";
import pako from "pako";

export default {
  data() {
    return {
      pageParams: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      searchParame: {
        src: "",
        srcPort: "",
        dst: "",
        dstPort: "",
      },
      drawer: false,
      direction: "rtl",
      isShow: false,
      isStart: false,
      fullscreenLoading: false,
      eth: "",
      networkList: [],
      packetList: [],
      searchData: [],
      tableData: [],
      startIndex: 0,
      vEle: undefined,
    };
  },
  components: { Pagination },
  created() {
    this.vEle = document.createElement("div");
  },
  mounted() {
    this.isShow = true;
    this.vEle = document.createElement("div");
    this.tcpPage();
    this.getNetworkList();
  },
  // computed: {
  //   sliceTable() {
  //     return this.tableData.slice(
  //       this.startIndex,
  //       this.tableData.length - this.startIndex > 20
  //         ? this.startIndex + 20
  //         : this.tableData.length
  //     );
  //   },
  // },
  methods: {
    getNetworkList() {
      this.$http.systemMG.networkListApi().then(({ code, data, msg }) => {
        if (!code) {
          this.networkList = data;
        }
      });
    },
    tableScroll() {
      let bodyWrapperEle = this.$refs.tableRef.$el.querySelector(
        ".el-table__body-wrapper"
      );
      let scrollTop = bodyWrapperEle.scrollTop;
      this.startIndex = Math.floor(scrollTop / 48);
      bodyWrapperEle.querySelector(
        ".el-table__body"
      ).style.transform = `translateY(${this.startIndex * 48}px)`;
      if (
        bodyWrapperEle.scrollHeight <=
        scrollTop + bodyWrapperEle.clientHeight
      ) {
        this.$message.warning("已经到底了");
        return;
      }
    },
    searchF() {
      this.searchData = this.tableData.filter((item) => {
        return (
          item.src.includes(this.searchParame.src) &&
          item.srcPort.toString().includes(this.searchParame.srcPort) &&
          item.dst.includes(this.searchParame.dst) &&
          item.dstPort.toString().includes(this.searchParame.dstPort)
        );
      });
    },
    cleanSearch() {
      this.searchParame.dstPort = "";
      this.searchData = this.tableData;
    },
    handleFileUpload(event) {
      this.parseFile(event.target.files[0]);
    },
    parseFile(fileData) {
      this.fullscreenLoading = true;
      this.tableData = [];
      let file = fileData;
      const GLOBAL_HEADER_LENGTH = 24;
      const PACKET_HEADER_LENGTH = 16;
      let chunkSize = 0;
      let num = 1;
      const totalSize = file.size;
      let offset = 0;
      let endianness = "BE"; // 定义字节序, 暂设默认值
      let datas = [];

      if (totalSize >= GLOBAL_HEADER_LENGTH) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const arrayBuffer = e.target.result;
          const dataView = new DataView(arrayBuffer);
          var magicNumber = dataView.getUint32(0, true).toString(16);
          if (magicNumber == "a1b2c3d4") {
            endianness = "BE";
          } else if (magicNumber == "d4c3b2a1") {
            endianness = "LE";
          }
          const globalHeader = {
            magicNumber: dataView.getUint32(0, endianness === "BE"),
            versionMajor: dataView.getUint16(4, endianness === "BE"),
            versionMinor: dataView.getUint16(6, endianness === "BE"),
            thiszone: dataView.getInt32(8, endianness === "BE"),
            sigfigs: dataView.getUint32(12, endianness === "BE"),
            snaplen: dataView.getUint32(16, endianness === "BE"),
            network: dataView.getUint32(20, endianness === "BE"),
          };
          if (
            globalHeader.versionMajor != 2 &&
            globalHeader.versionMinor != 4
          ) {
            this.fullscreenLoading = false;
            this.$message.warning("不是有效的pcap文件！");
            return;
          }

          offset = GLOBAL_HEADER_LENGTH;
          const readFileInChunks = () => {
            if (PACKET_HEADER_LENGTH < totalSize - offset) {
              const readerH = new FileReader();
              readerH.onload = (e) => {
                const arrayBuffer = e.target.result;
                const dataView = new DataView(arrayBuffer);
                const packetHeader = {
                  timestampSeconds: dataView.getUint32(0, endianness === "BE"),
                  timestampMicroseconds: dataView.getUint32(
                    4,
                    endianness === "BE"
                  ),
                  capturedLength: dataView.getUint32(8, endianness === "BE"),
                  originalLength: dataView.getUint32(12, endianness === "BE"),
                };
                offset += PACKET_HEADER_LENGTH;
                chunkSize = packetHeader.capturedLength;
                const blob = file.slice(offset, offset + chunkSize);
                const reader = new FileReader();
                reader.onload = (e) => {
                  const arrayBuffer = e.target.result;
                  if (arrayBuffer.byteLength >= 50) {
                    const dataView = new DataView(arrayBuffer);
                    var etherType = dataView.getUint16(12, endianness === "BE");
                    if (etherType === 8) {
                      if (dataView.getUint8(23, endianness === "BE") === 6) {
                        let rowData = {
                          id: num,
                          src:
                            dataView.getUint8(26, endianness === "BE") +
                            "." +
                            dataView.getUint8(27, endianness === "BE") +
                            "." +
                            dataView.getUint8(28, endianness === "BE") +
                            "." +
                            dataView.getUint8(29, endianness === "BE"),
                          dst:
                            dataView.getUint8(30, endianness === "BE") +
                            "." +
                            dataView.getUint8(31, endianness === "BE") +
                            "." +
                            dataView.getUint8(32, endianness === "BE") +
                            "." +
                            dataView.getUint8(33, endianness === "BE"),
                          srcPort: dataView.getUint16(34, false),
                          dstPort: dataView.getUint16(36, false),
                          protocol:
                            dataView.getUint16(34, false) === 3306 ||
                            dataView.getUint16(36, false) === 3306
                              ? "MySQL"
                              : "TCP",
                          seq: dataView.getUint32(38, false),
                          ack: dataView.getUint32(42, false),
                          headerLength: dataView.getUint8(46, false),
                          moreDatas: {},
                        };

                        if (
                          dataView.getUint16(34, false) === 3306 ||
                          dataView.getUint16(36, false) === 3306
                        ) {
                          rowData.moreDatas = {
                            "MySQL-Protocol": "暂不提供解析功能！",
                          };
                        }

                        if (
                          arrayBuffer.slice(54).toString() != "" &&
                          arrayBuffer.slice(54).length === 12
                        ) {
                          rowData.moreDatas = {
                            "TCP-Option-Maximum-segment-size": "(3项)",
                            "Kind:": dataView.getUint8(54, false),
                            "Length:": dataView.getUint8(55, false),
                            "MSS-Value:": dataView.getUint16(56, false),
                          };
                        } else if (
                          arrayBuffer.slice(54).toString() != "" &&
                          arrayBuffer.slice(54).byteLength === 20
                        ) {
                          rowData.moreDatas = {
                            "TCP-Option-Window-scale": "(3项)",
                            "Kind:": dataView.getUint8(54, false),
                            "Length:": dataView.getUint8(55, false),
                            "Window-scale-value:": dataView.getUint8(56, false),
                          };
                        } else if (
                          arrayBuffer.slice(54).toString() != "" &&
                          arrayBuffer.slice(54).byteLength > 6
                        ) {
                          const bToS = this.bufferToString(
                            arrayBuffer.slice(54)
                          );
                          if (bToS.type === "HTTP/JSON") {
                            let httpData = this.bufferToString(
                              arrayBuffer.slice(0, 4)
                            ).str;
                            if (httpData === "00000000") {
                              rowData.moreDatas = {
                                "数据:":
                                  this.bufferToString(arrayBuffer.slice(66))
                                    .str || "无数据",
                              };
                            } else {
                              rowData.moreDatas = {
                                "数据:":
                                  this.bufferToString(arrayBuffer.slice(54))
                                    .str || "无数据",
                              };
                            }
                          }
                          if (bToS.type === "RESP") {
                            rowData.moreDatas = {
                              "数据:":
                                this.bufferToString(arrayBuffer.slice(66))
                                  .str || "无数据",
                            };
                          }
                          if (bToS.type === "TCP") {
                            rowData.moreDatas = {
                              // "TCP-Option-No-0peration-1": "(1项)",
                              // "Kind-1:": dataView.getUint8(54, false),
                              // "TCP-Option-No-0peration-2": "(1项)",
                              // "Kind-2:": dataView.getUint8(55, false),
                              // "TCP-Option-Timestamps": "(4项)",
                              // "Kind:": dataView.getUint8(56, false),
                              // "Length:": dataView.getUint8(57, false),
                              // "Timestamp-value:": dataView.getUint32(58, false),
                              // "Timestamp-echo-reply:": dataView.getUint32(
                              //   62,
                              //   false
                              // ),
                              "数据:":
                                this.bufferToString(arrayBuffer.slice(66))
                                  .str || "无数据",
                            };
                          }
                          rowData.protocol = bToS.type;
                        } else {
                          rowData.moreDatas = {
                            "数据:":
                              this.bufferToString(arrayBuffer.slice(54)).str ||
                              "无数据",
                          };
                        }
                        datas.push(rowData);
                        num++;
                      } else {
                        num++;
                      }
                    } else {
                      num++;
                    }
                  } else {
                    num++;
                  }
                  offset += chunkSize;
                  readFileInChunks();
                };
                reader.readAsArrayBuffer(blob);
              };
              readerH.readAsArrayBuffer(file.slice(offset));
            } else {
              this.drawer = true;
              this.fullscreenLoading = false;
              this.tableData = datas;
              this.searchData = datas;
              // 暂且取消虚拟滚动
              // this.$nextTick(() => {
              //   this.$refs.tableRef.$el.querySelector(
              //     ".el-table__body"
              //   ).style.position = "absolute";
              //   this.vEle.style.height = this.tableData.length * 48 + "px";
              //   this.$refs.tableRef.$el
              //     .querySelector(".el-table__body-wrapper")
              //     .appendChild(this.vEle);
              // });
              // this.$refs.tableRef.$el
              //   .querySelector(".el-table__body-wrapper")
              //   .addEventListener("scroll", this.tableScroll, {
              //     passive: true,
              //   });
              this.$message.success("文件解析完毕！");
            }
          };
          readFileInChunks();
        };
        reader.readAsArrayBuffer(file.slice(0, GLOBAL_HEADER_LENGTH));
      } else {
        this.fullscreenLoading = false;
        this.$message.warning("不是有效的pcap文件！");
        return;
      }
    },
    getGzipDataToDecompress(arrayBuffer) {
      let httpResponseBuffer = new Uint8Array(arrayBuffer);
      let startIndex = -1;
      for (let i = 0; i < httpResponseBuffer.length - 1; i++) {
        if (
          httpResponseBuffer[i] === 0x0d &&
          httpResponseBuffer[i + 1] === 0x0a
        ) {
          // 找到连续的\r\n（表示空行），下一个位置就是数据部分开始
          startIndex = i + 2;
          break;
        }
      }
      if (startIndex === -1) {
        throw new Error("无法定位到数据起始位置");
      }
      return httpResponseBuffer.slice(startIndex);
    },
    decompressGzip(compressedData) {
      let decompressedData = pako.inflate(compressedData);
      return new TextDecoder("utf8").decode(decompressedData);
    },
    bufferToString(buffer) {
      let decoder = new TextDecoder("utf8");
      let str = decoder.decode(new Uint8Array(buffer));
      let type = "TCP";
      if (str.includes("Content-Encoding: gzip")) {
        const uint8Array = new Uint8Array(buffer);
        let startIndex = 0;
        let flagIndex = 0;
        for (let i = 0; i < uint8Array.length - 3; i++) {
          if (
            uint8Array[i] === 13 &&
            uint8Array[i + 1] === 10 &&
            uint8Array[i + 2] === 13 &&
            uint8Array[i + 3] === 10
          ) {
            flagIndex = i;
          }
          if (
            uint8Array[i] === 31 &&
            uint8Array[i + 1] === 139 &&
            uint8Array[i + 2] === 8 &&
            uint8Array[i + 3] === 0
          ) {
            startIndex = i + 4;
            break;
          }
        }
        let newStr = this.decompressGzip(
          new Uint8Array(buffer.slice(startIndex - 4, uint8Array.length - 7))
        );
        type = "HTTP/JSON";
        const beforeUint8Array = new Uint8Array(buffer.slice(0, flagIndex));
        let decoder = new TextDecoder("utf8");
        let beforeString = decoder.decode(new Uint8Array(beforeUint8Array));
        str = beforeString + "\n" + newStr;
      } else if (
        str.includes("GET") ||
        str.includes("POST") ||
        str.includes("HTTP")
      ) {
        type = "HTTP/JSON";
      } else if (str.includes("spring:session")) {
        type = "RESP";
      } else {
        let hexString = "";
        const uint8Array = new Uint8Array(buffer);
        for (let i = 0; i < uint8Array.length; i++) {
          const hexByte = uint8Array[i].toString(16).padStart(2, "0");
          hexString += hexByte;
        }
        str = hexString;
      }
      return { str: str, type: type };
    },
    getPacket() {
      if (this.eth === "") {
        this.$message.warning("请选择以太网接口！");
        return;
      }
      this.$http.oamMG
        .tcpdumpStart(JSON.stringify({ networkCard: this.eth }))
        .then(({ code }) => {
          if (code === 0) {
            this.isStart = true;
          }
        });
    },
    stopPacket() {
      this.$http.oamMG.tcpdumpStop().then(({ code }) => {
        if (code === 0) {
          this.isStart = false;
          this.tcpPage();
        }
      });
    },
    callPage(param) {
      this.pageParams.currentPage = param.currentPage;
      this.pageParams.pageSize = param.pageSize;
      this.tcpPage();
    },
    tcpPage() {
      console.log(this.pageParams);
      this.$http.oamMG
        .tcpdumpPage(this.pageParams)
        .then(({ code, msg, data, row }) => {
          if (code === 0) {
            this.packetList = data;
            this.pageParams.total = row;
            if (data.length > 0 && data[0].statusStr === "进行中") {
              this.isStart = true;
            }
            if (data.length > 0 && data[0].statusStr === "已完成") {
              this.isStart = false;
            }
          }
        });
    },
    deleteHandle(row) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$http.oamMG.tcpdumpDel(row.id).then(({ code, msg, data }) => {
          if (code === 0) {
            this.$message.success(msg);
            this.tcpPage();
          }
        });
      });
    },
    downloadHandle(row) {
      this.$http.oamMG.tcpdumpDownload(row.id).then((res) => {
        let reader = new FileReader();
        reader.readAsText(res, "utf-8");
        reader.onload = function (e) {
          let blob = new Blob([res], {
            type: "application/force-download",
          });
          let fileName = row.pcapfileName;
          if (window.navigator.msSaveOrOpenBlob) {
            navigator.msSaveBlob(blob, fileName);
          } else {
            let link = document.createElement("a");
            link.href = window.URL.createObjectURL(blob);
            link.download = fileName;
            link.click();
            window.URL.revokeObjectURL(link.href);
          }
        };
      });
    },
    parseHandle(row, index) {
      this.$http.oamMG.tcpdumpDownload(row.id).then((res) => {
        this.parseFile(res);
      });
    },
    handleClose(done) {
      done();
    },
  },
};
</script>

<style lang="less" scoped>
.demo-table-expand {
  font-size: 0;
}
.demo-table-expand label {
  width: 100px;
  color: #99a9bf;
}
.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 100%;
  padding-left: 20px;
}
// /deep/ .el-form-item__content {
//   width: 80%;
// }
.user-search {
  /*margin-top: 20px;*/
  float: left;
}
</style>

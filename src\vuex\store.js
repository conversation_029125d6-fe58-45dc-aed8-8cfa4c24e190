import Vue from 'vue';
import Vuex from 'vuex';
// import customGM from "@/api/customMG";
// import strategyMG from "@/api/strategyMG";
// import systemMG from "@/api/systemMG";
// import {getDevFlagApi} from "@/api/device/device";
import ElementUI from "element-ui";

Vue.use(Vuex);
// 登录验证
export default new Vuex.Store({
    state: {
        user: false,
        customConfig: {},
        menusArr: [], // 路由列表
        UKeyList: [], // 用于存储UKey设备的列表
        isShowMenu: false,
        IS_No_VISIT_MODE: '',
        CURRENT_DEV_STATE: '',
        WATCH_TOKEN: '',
        ADD_MANAGE_LOADING: false,
    },
    mutations: {
        // 登录
        login(state, user) {
            state.user = user;
            localStorage.setItem("userInfo", user);
        },
        // 退出
        logout(state, user) {
            state.user = "";
            localStorage.setItem("userInfo", "");
        },
        // 设置路由 route
        SET_MENUS(state, menusArr) {
            state.menusArr = menusArr;
        },
        SET_CONFIG(state, data) {
            // console.log(data);
            state.customConfig = data;
        },
        ADD_UKEY_NAME(state, SerialNum) {
            state.UKeyList.push(SerialNum);
        },
        REMOVE_UKEY_NAME(state, SerialNum) {
            while (state.UKeyList.indexOf(SerialNum) !== -1) {
                state.UKeyList.splice(state.UKeyList.indexOf(SerialNum), 1);
            }
        },
        SET_IS_SHOW_MENU(state, isShowMenu) {
            state.isShowMenu = isShowMenu
        },
        IS_No_VISIT_MODE_FUN(state, isMode) {
            state.IS_No_VISIT_MODE = isMode
        },
        SET_CURRENT_DEV_STATE(state, status) {
            state.CURRENT_DEV_STATE = status
        },
        SET_WATCH_TOKEN(state, status) {
            state.WATCH_TOKEN = status
        },
        SET_ADD_MANAGE_LOADING(state, ADD_MANAGE_LOADING) {
            state.ADD_MANAGE_LOADING = ADD_MANAGE_LOADING
        },
    },
    actions: {
        setAllMenu(context, menusArr) {
            context.commit('SET_MENUS', menusArr)
        },
        SET_WATCH_TOKEN_FUN(context, TOKEN) {
            context.commit('SET_WATCH_TOKEN', TOKEN)
        },
        // 是否是无访态
        getStatusFun(context) {
            return new Promise((resolve, reject) => {
                Vue.prototype.$http.systemMG.getStatus().then(({code, data, msg}) => {
                    console.log(code, data, msg);
                    if (code === 0) {
                        context.commit('IS_No_VISIT_MODE_FUN', data);
                        resolve(data)
                    } else {
                        ElementUI.Message({
                            message: msg,
                            type: 'warning'
                        });
                    }
                }).catch(err => {
                    reject(err)
                });
            });
        },
        // 获取设备状态
        getCardStatusFun(context) {
            return new Promise((resolve, reject) => {
                Vue.prototype.$http.systemMG.devStatusApi().then(({code, data, msg}) => {
                    console.log(code, data, msg);
                    if (code === 0) {
                        context.commit('SET_CURRENT_DEV_STATE', data);
                        resolve(data)
                    } else {
                        ElementUI.Message({
                            message: msg,
                            type: 'warning'
                        });
                    }
                }).catch(err => {
                    reject(err)
                });
            });
        },
        // 获取当前 个性化配置
        getCurrConfigFun(context) {
            return new Promise((resolve, reject) => {
                Vue.prototype.$http.customMG.currentConfig().then(({code, data, msg}) => {
                    // console.log(code, data, msg);
                    if (code === 0) {
                        context.commit('SET_CONFIG', data);
                        resolve(data)
                    } else {
                        ElementUI.Message({
                            message: msg,
                            type: 'warning'
                        });
                    }
                }).catch(err => {
                    reject(err)
                });
            });
        },
        // 查询策略是否改变
        sendPolicyFun(context) {
            // console.log(policyType, ElementUI);
            return new Promise((resolve, reject) => {
                // 调用后端接口当返回不为0时 弹窗提示
                // 查询策略是否改变
                Vue.prototype.$http.strategyMG.policyStatusApi().then(({code, data}) => {
                    console.log(code, data);
                    // if (code === 0) {
                    // if (code === 401) resolve(true);
                    if (data !== 0 && code === 0) {
                        resolve(true);
                        // 弹窗提示
                        ElementUI.MessageBox.confirm("策略已发生改变，请及时下发！", "提示", {
                            confirmButtonText: "下发策略",
                            showCancelButton: false,
                            closeOnClickModal: false, // 是否可通过点击遮罩关闭 MessageBox
                            closeOnPressEscape: false, // 是否可通过按下 ESC 键关闭 MessageBox
                            type: "warning"
                        }).then(() => {
                            // 下发策略  清除数据
                            Vue.prototype.$http.strategyMG.distribution().then(() => {
                                ElementUI.Message({
                                    message: "策略下发成功！",
                                    showClose: true,
                                    type: 'success'
                                });
                                Vue.prototype.$http.strategyMG.clearDataApi().then(res => {
                                    console.log(res);
                                    resolve(true)
                                });
                            });
                        }).catch(() => {
                            // 直接跳转
                            resolve(true)
                        })
                    } else {
                        resolve(true)
                    }
                    // } else {
                    //     resolve(true)
                    // }
                }).catch(() => {
                    resolve(true)
                });
            });
        },
        clearPolicyDataFun(context, policyType) {
            Vue.prototype.$http.strategyMG.clearDataApi(policyType).then(res => {
                // console.log(res);
                // resolve(true)
            });
        }
    }
})
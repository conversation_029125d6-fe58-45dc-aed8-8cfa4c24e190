.pagination-container {
  background: #fff;
  padding: 10px 16px;
}
.pagination-container.hidden {
  display: none;
}
.pagination-container .el-pagination {
  /*float: right;*/
  text-align: right;
  overflow: hidden;
}

.el-dialog__wrapper .el-dialog .el-dialog__header {
  border-bottom: 1px solid #ebebeb;
}

.el-dialog__wrapper .el-dialog .el-dialog__footer {
  border-top: 1px solid #ebebeb;
}
.tabDialog .el-dialog__body{
  padding: 10px 5px 10px 5px;
}


.el-scrollbar__wrap{
  overflow-x: hidden!important;
}


.el-card__body{

}


.container .el-card__body{
  padding: 10px;
  /*padding-top: 10px;*/
  /*padding-left: 10px;*/
  /*padding-right: 10px;*/
  /*padding-bottom: 10px;*/
}

.select-list ul {
  margin: 0;
  padding: 0;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0)
}

.select-list li {
  list-style: none
}

.select-time .time-input {
  display: block;
  width: 100%;
  padding-left: 10px
}

@media (min-width: 768px) {
  .select-list li {
    float: left
  }
}

.select-list{
  font-family:"open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size:13px;
  font-stretch:100%;
  font-style:normal;
  font-variant-caps:normal;
  font-variant-east-asian:normal;
  font-variant-ligatures:normal;
  font-variant-numeric:normal;
  font-weight:400;
}

.select-list li {
  color: #333;
  margin: 5px 15px 5px 0;
  text-align: left;
}

.select-list li p, .select-list li label:not(.radio-box) {
  float: left;
  width: 65px;
  margin: 5px 0 0 0;
  text-align: right
}

.select-list li input {
  border: 1px solid #ddd;
  border-radius: 4px;
  background: transparent;
  outline: 0;
  height: 27px;
  width: 200px;
  padding-left: 5px
}

.select-list li .submit-btn {
  border: 0;
  border-radius: 4px;
  background: transparent;
  outline: 0;
  width: 40px;
  height: 23px
}

.select-list li select {
  border: 1px solid #ddd;
  border-radius: 4px;
  background: transparent;
  outline: 0;
  height: 30px;
  width: 200px
}

.searchButton{
  border-radius: 50px;
  background-color: #1ab394!important;
  border-color: #1ab394!important;
}

.resetButtion{
  border-radius: 50px;
  background-color: #f8ac59!important;
  border-color: #f8ac59!important;
}

.el-select-dropdown__wrap,.el-scrollbar__wrap{
  margin-bottom: 0px!important;
}


.footer {
  background: none repeat scroll 0 0 white;
  border-top: 1px solid #e7eaec;
  overflow: hidden;
  padding-top: 10px;
  padding-bottom: 10px;
}

.footer.fixed_full {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: white;
  border-top: 1px solid #e7eaec
}

.footer.fixed {
  height: 100px;
  width: 100%;
  background-color: #ddd;
  position: fixed;
  bottom: 0;
}

.pull-right {
  text-align: right;
  margin-right: 10px;
  font-size:13px;
}

.el-table__row .el-dropdown{
  margin-left: 10px;
}

aside::-webkit-scrollbar{
  width:5px;
  height:5px;
  /**/
}
aside::-webkit-scrollbar-track{
  /*background: rgb(239, 239, 239);*/
  background-color: transparent;
  border-radius:2px;
}
aside::-webkit-scrollbar-thumb{
  /*background: #bfbfbf;*/
  background-color: rgba(0, 0, 0, .3);
  border-radius:10px;
}
aside::-webkit-scrollbar-thumb:hover{
  background: #909399;
}
aside::-webkit-scrollbar-corner{
  background: #179a16;
}



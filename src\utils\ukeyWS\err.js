
var SAR_OK = 0x00000000 //成功
var SAR_FAIL = 0x0A000001 //失败
var SAR_UNKNOWNERR = 0x0A000002 //异常错误
var SAR_NOTSUPPORTYETERR = 0x0A000003 //不支持的服务
var SAR_FILEERR = 0x0A000004 //文件操作错误
var SAR_INVALIDHANDLEERR = 0x0A000005 //无效的句柄
var SAR_INVALIDPARAMERR = 0x0A000006 //无效的参数
var SAR_READFILEERR = 0x0A000007 //读文件错误
var SAR_WRITEFILEERR = 0x0A000008 //写文件错误
var SAR_NAMELENERR = 0x0A000009 //名称长度错误
var SAR_KEYUSAGEERR = 0x0A00000A //密钥用途错误
var SAR_MODULUSLENERR = 0x0A00000B //模的长度错误
var SAR_NOTINITIALIZEERR = 0x0A00000C //未初始化
var SAR_OBJERR = 0x0A00000D //对象错误
var SAR_MEMORYERR = 0x0A00000E //内存错误
var SAR_TIMEOUTERR = 0x0A00000F //超时
var SAR_INDATALENERR = 0x0A000010 //输入数据长度错误
var SAR_INDATAERR = 0x0A000011 //输入数据错误
var SAR_GENRANDERR = 0x0A000012 //生成随机数错误
var SAR_HASHOBJERR = 0x0A000013 //HASH对象错误
var SAR_HASHERR = 0x0A000014 //HASH运算错误
var SAR_GENRSAKEYERR = 0x0A000015 //产生RSA密钥错误
var SAR_RSAMODULUSLENERR = 0x0A000016 //RSA密钥模长错误
var SAR_CSPIMPRTPUBKEYERR = 0x0A000017 //CSP服务导入公钥错误
var SAR_RSAENCERR = 0x0A000018 //RSA加密错误
var SAR_RSADECERR = 0x0A000019 //RSA解密错误
var SAR_HASHNOTEQUALERR = 0x0A00001A //HASH值不相等
var SAR_KEYNOTFOUNTERR = 0x0A00001B //未发现密钥
var SAR_CERTNOTFOUNTERR = 0x0A00001C //未发现证书
var SAR_NOTEXPORTERR = 0x0A00001D //对象未导出
var SAR_DECRYPTPADERR = 0x0A00001E //解密时做补丁错误
var SAR_MACLENERR = 0x0A00001F //MAC长度错误
var SAR_BUFFER_TOO_SMALL = 0x0A000020 //缓冲区不足
var SAR_KEYINFOTYPEERR = 0x0A000021 //密钥类型错误
var SAR_NOT_EVENTERR = 0x0A000022 //无事件错误
var SAR_DEVICE_REMOVED = 0x0A000023 //设备已移除
var SAR_PIN_INCORRECT = 0x0A000024 //PIN错误
var SAR_PIN_LOCKED = 0x0A000025 //PIN锁死
var SAR_PIN_INVALID = 0x0A000026 //PIN无效
var SAR_PIN_LEN_RANGE = 0x0A000027 //PIN长度错误
var SAR_USER_ALREADY_LOGGED_IN = 0x0A000028 //用户已经登录
var SAR_USER_PIN_NOT_INITIALIZED = 0x0A000029 //没有初始化用户口令
var SAR_USER_TYPE_INVALID = 0x0A00002A //PIN类型错误
var SAR_APPLICATION_NAME_INVALID = 0x0A00002B //应用名称无效
var SAR_APPLICATION_EXISTS = 0x0A00002C //应用已经存在
var SAR_USER_NOT_LOGGED_IN = 0x0A00002D //用户没有登录
var SAR_APPLICATION_NOT_EXISTS = 0x0A00002E //应用不存在
var SAR_FILE_ALREADY_EXIST = 0x0A00002F //文件已经存在
var SAR_NO_ROOM = 0x0A000030 //存储空间不足
var SAR_FILE_NOT_EXIST = 0x0A000031 //文件不存在
var SAR_REACH_MAX_CONTAINER_COUNT = 0x0A000032 //已达到最大可管理容器数

var SAR_SECURITY_INVALID = 0x0B000033 //安全状态不满足
var SAR_OFFSET_VOER_FILE = 0x0B000034 //指针移到超过文件长度
var SAR_CONTAINER_NOT_FOUND = 0x0B000035 //容器不存在
var SAR_CONTAINER_EXIST = 0x0B000036 //容器已存在
var SAR_AUTH_LOCKED = 0x0B000037 //设备认证锁定
var SAR_ECCENCERR = 0x0B000038 //ECC加密错误
var SAR_REQPARAMERR = 0x0B000090 //"请就报文错误";//json格式错误，缺少参数项，参数项的数据格式错误，不可为空参数为空
var SAR_REQUNSUPPORTED = 0x0B000091 //"不支持的请求";
var SAR_BUFFNEWERR = 0x0B000092 //"内存申请错误";


//错误描述
var SAR_OK_MSG = "成功"
var SAR_FAIL_MSG = "失败"
var SAR_UNKNOWNERR_MSG = "异常错误"
var SAR_NOTSUPPORTYETERR_MSG = "不支持的服务"
var SAR_FILEERR_MSG = "文件操作错误"
var SAR_INVALIDHANDLEERR_MSG = "无效的句柄"
var SAR_INVALIDPARAMERR_MSG = "无效的参数"
var SAR_READFILEERR_MSG = "读文件错误"
var SAR_WRITEFILEERR_MSG = "写文件错误"
var SAR_NAMELENERR_MSG = "名称长度错误"
var SAR_KEYUSAGEERR_MSG = "密钥用途错误"
var SAR_MODULUSLENERR_MSG = "模的长度错误"
var SAR_NOTINITIALIZEERR_MSG = "未初始化"
var SAR_OBJERR_MSG = "对象错误"
var SAR_MEMORYERR_MSG = "内存错误"
var SAR_TIMEOUTERR_MSG = "超时"
var SAR_INDATALENERR_MSG = "输入数据长度错误"
var SAR_INDATAERR_MSG = "输入数据错误"
var SAR_GENRANDERR_MSG = "生成随机数错误"
var SAR_HASHOBJERR_MSG = "HASH对象错误"
var SAR_HASHERR_MSG = "HASH运算错误"
var SAR_GENRSAKEYERR_MSG = "产生RSA密钥错误"
var SAR_RSAMODULUSLENERR_MSG = "RSA密钥模长错误"
var SAR_CSPIMPRTPUBKEYERR_MSG = "CSP服务导入公钥错误"
var SAR_RSAENCERR_MSG = "RSA加密错误"
var SAR_RSADECERR_MSG = "RSA解密错误"
var SAR_HASHNOTEQUALERR_MSG = "HASH值不相等"
var SAR_KEYNOTFOUNTERR_MSG = "未发现密钥"
var SAR_CERTNOTFOUNTERR_MSG = "未发现证书"
var SAR_NOTEXPORTERR_MSG = "对象未导出"
var SAR_DECRYPTPADERR_MSG = "解密时做补丁错误"
var SAR_MACLENERR_MSG = "MAC长度错误"
var SAR_BUFFER_TOO_SMALL_MSG = "缓冲区不足"
var SAR_KEYINFOTYPEERR_MSG = "密钥类型错误"
var SAR_NOT_EVENTERR_MSG = "无事件错误"
var SAR_DEVICE_REMOVED_MSG = "设备已移除"
var SAR_PIN_INCORRECT_MSG = "PIN错误"
var SAR_PIN_LOCKED_MSG = "PIN锁死"
var SAR_PIN_INVALID_MSG = "PIN无效"
var SAR_PIN_LEN_RANGE_MSG = "PIN长度错误"
var SAR_USER_ALREADY_LOGGED_IN_MSG = "用户已经登录"
var SAR_USER_PIN_NOT_INITIALIZED_MSG = "没有初始化用户口令"
var SAR_USER_TYPE_INVALID_MSG = "PIN类型错误"
var SAR_APPLICATION_NAME_INVALID_MSG = "应用名称无效"
var SAR_APPLICATION_EXISTS_MSG = "应用已经存在"
var SAR_USER_NOT_LOGGED_IN_MSG = "用户没有登录"
var SAR_APPLICATION_NOT_EXISTS_MSG = "应用不存在"
var SAR_FILE_ALREADY_EXIST_MSG = "文件已经存在"
var SAR_NO_ROOM_MSG = "存储空间不足"
var SAR_FILE_NOT_EXIST_MSG = "文件不存在"
var SAR_REACH_MAX_CONTAINER_COUNT_MSG = "已达到最大可管理容器数"

var SAR_SECURITY_INVALID_MSG = "安全状态不满足"
var SAR_OFFSET_VOER_FILE_MSG = "指针移到超过文件长度"
var SAR_CONTAINER_NOT_FOUND_MSG = "容器不存在"
var SAR_CONTAINER_EXIST_MSG = "容器已存在"
var SAR_AUTH_LOCKED_MSG = "设备认证锁定"
var SAR_ECCENCERR_MSG = "ECC加密错误"
var SAR_REQPARAMERR_MSG = "请求报文错误"; //json格式错误，缺少参数项，参数项的数据格式错误，不可为空参数为空
var SAR_REQUNSUPPORTED_MSG = "不支持的请求";
var SAR_BUFFNEWERR_MSG = "内存申请错误";


const getErrMsg = function getErrMsg(code) {
    var errCode = parseInt(code);
    var resp = '未知！';
    switch (errCode) {
        case SAR_OK:
            resp = SAR_OK_MSG;
            break;
        case SAR_FAIL:
            resp = SAR_FAIL_MSG;
            break;
        case SAR_UNKNOWNERR:
            resp = SAR_UNKNOWNERR_MSG;
            break;
        case SAR_NOTSUPPORTYETERR:
            resp = SAR_NOTSUPPORTYETERR_MSG;
            break;
        case SAR_FILEERR:
            resp = SAR_FILEERR_MSG;
            break;
        case SAR_INVALIDHANDLEERR:
            resp = SAR_INVALIDHANDLEERR_MSG;
            break;
        case SAR_INVALIDPARAMERR:
            resp = SAR_INVALIDPARAMERR_MSG;
            break;
        case SAR_READFILEERR:
            resp = SAR_READFILEERR_MSG;
            break;
        case SAR_WRITEFILEERR:
            resp = SAR_WRITEFILEERR_MSG;
            break;
        case SAR_NAMELENERR:
            resp = SAR_NAMELENERR_MSG;
            break;
        case SAR_KEYUSAGEERR:
            resp = SAR_KEYUSAGEERR_MSG;
            break;
        case SAR_MODULUSLENERR:
            resp = SAR_MODULUSLENERR_MSG;
            break;
        case SAR_NOTINITIALIZEERR:
            resp = SAR_NOTINITIALIZEERR_MSG;
            break;
        case SAR_OBJERR:
            resp = SAR_OBJERR_MSG;
            break;
        case SAR_MEMORYERR:
            resp = SAR_MEMORYERR_MSG;
            break;
        case SAR_TIMEOUTERR:
            resp = SAR_TIMEOUTERR_MSG;
            break;
        case SAR_INDATALENERR:
            resp = SAR_INDATALENERR_MSG;
            break;
        case SAR_INDATAERR:
            resp = SAR_INDATAERR_MSG;
            break;
        case SAR_GENRANDERR:
            resp = SAR_GENRANDERR_MSG;
            break;
        case SAR_HASHOBJERR:
            resp = SAR_HASHOBJERR_MSG;
            break;
        case SAR_HASHERR:
            resp = SAR_HASHERR_MSG;
            break;
        case SAR_GENRSAKEYERR:
            resp = SAR_GENRSAKEYERR_MSG;
            break;
        case SAR_RSAMODULUSLENERR:
            resp = SAR_RSAMODULUSLENERR_MSG;
            break;
        case SAR_CSPIMPRTPUBKEYERR:
            resp = SAR_CSPIMPRTPUBKEYERR_MSG;
            break;
        case SAR_RSAENCERR:
            resp = SAR_RSAENCERR_MSG;
            break;
        case SAR_RSADECERR:
            resp = SAR_RSADECERR_MSG;
            break;
        case SAR_HASHNOTEQUALERR:
            resp = SAR_HASHNOTEQUALERR_MSG;
            break;
        case SAR_KEYNOTFOUNTERR:
            resp = SAR_KEYNOTFOUNTERR_MSG;
            break;
        case SAR_CERTNOTFOUNTERR:
            resp = SAR_CERTNOTFOUNTERR_MSG;
            break;
        case SAR_NOTEXPORTERR:
            resp = SAR_NOTEXPORTERR_MSG;
            break;
        case SAR_DECRYPTPADERR:
            resp = SAR_DECRYPTPADERR_MSG;
            break;
        case SAR_MACLENERR:
            resp = SAR_MACLENERR_MSG;
            break;
        case SAR_BUFFER_TOO_SMALL:
            resp = SAR_BUFFER_TOO_SMALL_MSG;
            break;
        case SAR_KEYINFOTYPEERR:
            resp = SAR_KEYINFOTYPEERR_MSG;
            break;
        case SAR_NOT_EVENTERR:
            resp = SAR_NOT_EVENTERR_MSG;
            break;
        case SAR_DEVICE_REMOVED:
            resp = SAR_DEVICE_REMOVED_MSG;
            break;
        case SAR_PIN_INCORRECT:
            resp = SAR_PIN_INCORRECT_MSG;
            break;
        case SAR_PIN_LOCKED:
            resp = SAR_PIN_LOCKED_MSG;
            break;
        case SAR_PIN_INVALID:
            resp = SAR_PIN_INVALID_MSG;
            break;
        case SAR_PIN_LEN_RANGE:
            resp = SAR_PIN_LEN_RANGE_MSG;
            break;
        case SAR_USER_ALREADY_LOGGED_IN:
            resp = SAR_USER_ALREADY_LOGGED_IN_MSG;
            break;
        case SAR_USER_PIN_NOT_INITIALIZED:
            resp = SAR_USER_PIN_NOT_INITIALIZED_MSG;
            break;
        case SAR_USER_TYPE_INVALID:
            resp = SAR_USER_TYPE_INVALID_MSG;
            break;
        case SAR_APPLICATION_NAME_INVALID:
            resp = SAR_APPLICATION_NAME_INVALID_MSG;
            break;
        case SAR_APPLICATION_EXISTS:
            resp = SAR_APPLICATION_EXISTS_MSG;
            break;
        case SAR_USER_NOT_LOGGED_IN:
            resp = SAR_USER_NOT_LOGGED_IN_MSG;
            break;
        case SAR_APPLICATION_NOT_EXISTS:
            resp = SAR_APPLICATION_NOT_EXISTS_MSG;
            break;
        case SAR_FILE_ALREADY_EXIST:
            resp = SAR_FILE_ALREADY_EXIST_MSG;
            break;
        case SAR_NO_ROOM:
            resp = SAR_NO_ROOM_MSG;
            break;
        case SAR_FILE_NOT_EXIST:
            resp = SAR_FILE_NOT_EXIST_MSG;
            break;
        case SAR_REACH_MAX_CONTAINER_COUNT:
            resp = SAR_REACH_MAX_CONTAINER_COUNT_MSG;
            break;
        case SAR_SECURITY_INVALID:
            resp = SAR_SECURITY_INVALID_MSG;
            break;
        case SAR_OFFSET_VOER_FILE:
            resp = SAR_OFFSET_VOER_FILE_MSG;
            break;
        case SAR_CONTAINER_NOT_FOUND:
            resp = SAR_CONTAINER_NOT_FOUND_MSG;
            break;
        case SAR_CONTAINER_EXIST:
            resp = SAR_CONTAINER_EXIST_MSG;
            break;
        case SAR_AUTH_LOCKED:
            resp = SAR_AUTH_LOCKED_MSG;
            break;
        case SAR_ECCENCERR:
            resp = SAR_ECCENCERR_MSG;
            break;
        case SAR_REQPARAMERR:
            resp = SAR_REQPARAMERR_MSG;
            break;
        case SAR_REQUNSUPPORTED:
            resp = SAR_REQUNSUPPORTED_MSG;
            break;
        case SAR_BUFFNEWERR:
            resp = SAR_BUFFNEWERR_MSG;
            break;

        default:
            break;
    }

    return resp;
}

export {
    getErrMsg
}

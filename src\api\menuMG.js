import API from "@/api/apiuri";

import {req, reqGet} from './axiosFun';
let menuApi = API.menuApi;

export default {
  pageMenu(params) {
    return req("post", menuApi.pageMenu, params);
  },
  menuTree() {
    return reqGet("get", menuApi.treeMenu);
  },
  insertMenu(menu) {
    return req("post", menuApi.insertMenu, menu);
  },
  updateMenu(menu) {
    return req("post", menuApi.updateMenu, menu);
  },
  deleteMenu(id) {
    return req("post", menuApi.deleteMenu + '/' + id);
  },
  pageParentList() {
    return reqGet("get", menuApi.pageParentList);
  },
  rootMenuList() {
    return reqGet("get", menuApi.menuParent);
  },
  getMenuIdsByRoleId(id) {
    return reqGet("get", menuApi.roleMenuIds + '/' + id);
  }

}

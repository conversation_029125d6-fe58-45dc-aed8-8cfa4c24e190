<template>
    <el-dialog :title="title" :visible.sync="networkShow" width="650px" append-to-body :close-on-click-modal="false"
        @close="closeHandle">
        <el-form ref="boundForm" :model="boundForm" :rules="rules" label-width="150px" size="medium"
            style="margin-bottom: 10px">
            <el-form-item label="网口名称：" prop="name">
                <el-input size="small" v-model="boundForm.name" placeholder="请输入网口名称" clearable />
            </el-form-item>
            <el-row>
                <el-col :span="12">
                    <el-form-item label="选择模式：" prop="bondMode">
                        <el-select v-model="boundForm.bondMode" placeholder="请选择模式" size="small">
                            <el-option value="0" label="均衡(bond0)"></el-option>
                            <el-option value="1" label="主备(bond1)"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="协议类型：">
                        <el-select v-model="boundForm.ipType" size="small" style="width: 100%" @change="changeIpType">
                            <el-option label="IPv4 & IPv6" value="all" key="all"></el-option>
                            <el-option label="IPv4" value="ipv4" key="ipv4"></el-option>
                            <el-option label="IPv6" value="ipv6" key="ipv6"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <div v-if="boundForm.ipType === 'ipv4' || boundForm.ipType === 'all'">
                <el-form-item label="IPv4地址：" prop="ipaddr">
                    <el-input size="small" v-model="boundForm.ipaddr"
                        @blur="checkIp(boundForm.ipaddr, boundForm.gateway, boundForm.netmask, currentIndex, 0)"
                        style="width: 100%" clearable />
                </el-form-item>
                <el-form-item label="IPv4子网掩码：" prop="netmask">
                    <el-input size="small" v-model="boundForm.netmask" clearable />
                </el-form-item>
                <el-form-item label="IPv4网关地址：" prop="gateway">
                    <el-input size="small" v-model="boundForm.gateway"
                        @blur="isEqualIPAddress(boundForm.ipaddr, boundForm.gateway, boundForm.netmask, currentIndex, 2)"
                        clearable />
                </el-form-item>
            </div>
            <div v-if="boundForm.ipType === 'ipv6' || boundForm.ipType === 'all'">
                <el-form-item label="IPv6地址：" prop="ipv6addr">
                    <el-input size="small" v-model="boundForm.ipv6addr" style="width: 100%" clearable />
                </el-form-item>
                <el-form-item label="IPv6前缀：" prop="ipv6mask">
                    <el-input size="small" v-model="boundForm.ipv6mask" clearable />
                </el-form-item>
                <el-form-item label="IPv6网关地址：" prop="ipv6Defaultgw">
                    <el-input size="small" v-model="boundForm.ipv6Defaultgw" clearable />
                </el-form-item>
            </div>
        </el-form>
        <el-table :data="tableData" class="comTab" :indent="0"  style="margin-top: 10px"  @selection-change="handleSelectionChange">
            <el-table-column align="center" type="selection" width="55"></el-table-column>
            <el-table-column align="center" prop="name" label="网口" show-overflow-tooltip></el-table-column>
            <el-table-column align="center" prop="ipaddr" label="IPv4地址" min-width="100" show-overflow-tooltip></el-table-column>
            <el-table-column align="center" prop="ipv6addr" label="IPv6地址" min-width="120" show-overflow-tooltip></el-table-column>
            <!-- <el-table-column align="center" prop="optical" label="类型" min-width="60">
                <template slot-scope="{row}">
                    <span v-if="row.optical === 1">光口</span>
                    <span v-else-if="row.optical === 0">电口</span>
                    <span v-else>-</span>
                </template>
            </el-table-column> -->
            <el-table-column align="center" prop="status" label="状态" min-width="50">
                <template slot-scope="{row}">
                    <el-button v-if="row.isLinked === 1" type="success" circle></el-button>
                    <el-button v-else type="info" circle></el-button>
                </template>
            </el-table-column>
        </el-table>
        <div slot="footer" class="dialog-footer">
            <el-button class="comBtn com_reset_btn" size="small" @click="closeHandle">取 消</el-button>
            <el-button class="comBtn com_send_btn" size="small" @click="submitHandle">确 定</el-button>
        </div>
    </el-dialog>
</template>

<script>
// import systemMG from "@/api/systemMG";

export default {
    name: "networkBoundDia",
    props: ['networkList'],
    data() {
        const validatorName = (rule, value, callback) => {
            if (value !== "") {
                let ip = /^[a-zA-Z0-9]\w{3,14}$/;
                if (!ip.test(value)) {
                    callback(new Error('格式错误, 4-15个字符(可包含"_", 但不能以"_"开头)!'))
                } else {
                    callback()
                }
            } else {
                callback();
            }
        };
        const validatorIp = (rule, value, callback) => {
            if (value !== "") {
                let ip = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
                if (!ip.test(value)) {
                    callback(new Error('请输入正确的IPv4地址!'))
                } else {
                    callback()
                }
            } else {
                callback();
            }
        };
        const validatorMask = (rule, value, callback) => {
            if (value !== "") {
                let mask = /^(254|252|248|240|224|192|128|0)\.0\.0\.0|255\.(254|252|248|240|224|192|128|0)\.0\.0|255\.255\.(254|252|248|240|224|192|128|0)\.0|255\.255\.255\.(254|252|248|240|224|192|128|0)$/;
                if (!mask.test(value)) {
                    callback(new Error("请输入正确的子网掩码！"));
                } else {
                    callback();
                }
            } else {
                callback();
            }
        };
        const validatorGW = (rule, value, callback) => {
            if (value !== "") {
                let ip = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
                if (!ip.test(value)) {
                    callback(new Error('请输入正确的网关地址!'))
                } else {
                    callback()
                }
            } else {
                callback();
            }
        };
        const checkIPV6 = (rule, value, callback) => {
            if (value !== "") {
                let IPV6 = /(^(?:(?:(?:[0-9A-Fa-f]{1,4}:){7}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}:[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){5}:([0-9A-Fa-f]{1,4}:)?[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:){0,2}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,3}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,4}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){0,5}:((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(::([0-9A-Fa-f]{1,4}:){0,5}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|([0-9A-Fa-f]{1,4}::([0-9A-Fa-f]{1,4}:){0,5}[0-9A-Fa-f]{1,4})|(::([0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){1,7}:))$)|(^\[(?:(?:(?:[0-9A-Fa-f]{1,4}:){7}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}:[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){5}:([0-9A-Fa-f]{1,4}:)?[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:){0,2}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,3}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,4}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){0,5}:((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(::([0-9A-Fa-f]{1,4}:){0,5}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|([0-9A-Fa-f]{1,4}::([0-9A-Fa-f]{1,4}:){0,5}[0-9A-Fa-f]{1,4})|(::([0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){1,7}:))\](?::(?:[0-9]|[1-9][0-9]{1,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5]))?$)/;
                if (!IPV6.test(value)) {
                    callback(new Error('请输入正确的IPv6地址!'))
                } else {
                    callback()
                }
            } else {
                callback();
            }
        };
        const checkIPV6Gw = (rule, value, callback) => {
            if (value !== "") {
                let IPV6 = /(^(?:(?:(?:[0-9A-Fa-f]{1,4}:){7}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}:[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){5}:([0-9A-Fa-f]{1,4}:)?[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:){0,2}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,3}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,4}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){0,5}:((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(::([0-9A-Fa-f]{1,4}:){0,5}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|([0-9A-Fa-f]{1,4}::([0-9A-Fa-f]{1,4}:){0,5}[0-9A-Fa-f]{1,4})|(::([0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){1,7}:))$)|(^\[(?:(?:(?:[0-9A-Fa-f]{1,4}:){7}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}:[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){5}:([0-9A-Fa-f]{1,4}:)?[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:){0,2}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,3}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,4}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){0,5}:((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(::([0-9A-Fa-f]{1,4}:){0,5}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|([0-9A-Fa-f]{1,4}::([0-9A-Fa-f]{1,4}:){0,5}[0-9A-Fa-f]{1,4})|(::([0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){1,7}:))\](?::(?:[0-9]|[1-9][0-9]{1,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5]))?$)/;
                if (!IPV6.test(value)) {
                    callback(new Error('请输入正确的网关地址!'))
                } else {
                    callback()
                }
            } else {
                callback();
            }
        };
        const checkIPV6Mask = (rule, value, callback) => {
            if (value !== "") {
                let reg = /^\+?[1-9]\d*$/; // /^\+?[1-9]\d*$/
                if (!reg.test(+value)) {
                    callback(new Error('请输入1~128之间的整数!'));
                } else if (+value < 1 || +value > 128) {
                    callback(new Error('请输入1~128之间的整数!'));
                } else {
                    callback();
                }
            } else {
                callback();
            }
        };
        return {
            networkShow: false,
            title: '网口绑定',
            boundForm: {
                bondMode: '0',
                ipType: 'all',
                name: '',
                ipaddr: '',
                netmask: '',
                gateway: '',
                ipv6addr: '',
                ipv6mask: '',
                ipv6Defaultgw: '',
                eths: []
            },
            currentIndex: null,
            tableData: [],
            isPage: false,
            total: 0,
            rules: {
                name: [
                    { required: true, message: "请输入网口名称", trigger: "blur" },
                    { validator: validatorName, trigger: 'blur' }
                ],
                bondMode: [
                    { required: true, message: "请选择模式", trigger: "change" },
                ],
                ipaddr: [
                    { required: true, message: "请输入IPv4地址", trigger: "blur" },
                    { validator: validatorIp, trigger: 'blur' }
                ],
                netmask: [
                    { required: true, message: "请输入IPv4子网掩码", trigger: "blur" },
                    { validator: validatorMask, trigger: 'blur' }
                ],
                gateway: [
                    { required: true, message: "请输入IPv4网关地址", trigger: "blur" },
                    { validator: validatorGW, trigger: 'blur' }
                ],
                ipv6addr: [
                    { required: true, message: "请输入IPv6地址", trigger: "blur" },
                    { validator: checkIPV6, trigger: 'blur' }
                ],
                ipv6mask: [
                    { required: true, message: "请输入前缀长度", trigger: "blur" },
                    { validator: checkIPV6Mask, trigger: 'blur' }
                ],
                ipv6Defaultgw: [
                    { required: true, message: "请输入IPv6网关地址", trigger: "blur" },
                    { validator: checkIPV6Gw, trigger: 'blur' }
                ]
            }
        }
    },
    methods: {
        // 分页查询
        bindableList() {
            this.tableData = [];
            this.$http.systemMG.bindableList().then(({ code, data, msg }) => {
                if (!code) {
                    this.tableData = data
                } else {
                    this.$message.warning(msg)
                }
            })
        },
        initNetworkBoundFun() {
            this.networkShow = true;
            this.bindableList();
        },
        changeIpType() {
            // this.$nextTick(() => {
            this.$refs["boundForm"].resetFields();
            // })
        },
        checkIp(ip, gw, mask, index, num) {
            console.log(ip, gw, mask, index, num);
            console.log(this.networkList);
            this.networkList.map((item, i) => {
                if (ip === item.ip && index !== i) {
                    console.log(this.$refs['boundForm'].fields[num]);
                    this.$nextTick(() => {
                        this.$refs['boundForm'].fields[num].validateMessage = 'IP重复, 请重新输入!';
                        this.$refs['boundForm'].fields[num].validateState = 'error'
                    });
                }
            });
            // console.log(this.$refs['networkForm'].fields[num].validateState);
            // if (this.$refs['networkForm'][index].fields[num].validateState !== 'error') this.isEqualIPAddress(ip, gw, mask, index, num)
            this.isEqualIPAddress(ip, gw, mask, index, num)
        },
        /**
         * [isEqualIPAddress 判断两个IP地址是否在同一个网段]
         * @param {[String]} addr1 [ip地址]
         * @param {[String]} addr2 [网关地址]
         * @param {[String]} mask [子网掩码]
         * @param {[Number]} index [动态表单索引]
         * @param {[Number]} num [表单项索引]
         * @return {Boolean} [true or false]
         */
        isEqualIPAddress(addr1, addr2, mask, index, num) {
            console.log(addr1, addr2, mask, index, num);
            // let num = index === 0 ? 0 : 2;
            console.log(this.$refs['boundForm'].fields[num].validateState);
            // if (this.$refs['networkForm'][index].fields[num].validateState === 'error' ) return false;
            if (!this.checkIpOrGw(addr2) || !this.checkIpOrGw(addr1)) return false;
            if (!addr1 || !addr2 || !mask) {
                console.log("各参数不能为空");
                return false;
            }
            var
                res1 = [],
                res2 = [];
            addr1 = addr1.split(".");
            addr2 = addr2.split(".");
            mask = mask.split(".");
            for (var i = 0, ilen = addr1.length; i < ilen; i += 1) {
                res1.push(parseInt(addr1[i]) & parseInt(mask[i]));
                res2.push(parseInt(addr2[i]) & parseInt(mask[i]));
            }
            if (res1.join(".") == res2.join(".")) {
                console.log("在同一个网段");
                let n = num === 0 ? 2 : 0;
                this.$refs['boundForm'].fields[n].validateState = 'success';
                return true;
            } else {
                console.log("不在同一个网段");
                this.$nextTick(() => {
                    this.$refs['boundForm'].fields[num].validateState = 'error';
                    this.$refs['boundForm'].fields[num].validateMessage = '网关和IP不在同一网段, 请重新输入!';
                });
                return false;
            }
        },
        checkIpOrGw(value) {
            let ip = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
            return !ip.test(value);
        },
        // 多选
        handleSelectionChange(val) {
            this.boundForm.eths = [];
            val.forEach((item) => {
                this.boundForm.eths.push(item.name);
            });
        },
        submitHandle() {
            let _this = this;
            this.$refs['boundForm'].validate(valid => {
                if (valid) {
                    if (_this.boundForm.eths.length <= 1) {
                        _this.$message.warning("网卡选择不能低于两个");
                        return false;
                    }
                    this.$confirm('请确认是否要绑定, 绑定后立刻生效!', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        closeOnClickModal: false,
                        closeOnPressEscape: false,
                        type: 'warning'
                    }).then(() => {
                        this.$http.systemMG.setBond(JSON.stringify(_this.boundForm)).then(({ code, data, msg }) => {
                            if (!code) {
                                this.$message.success('配置成功！');
                                // console.log(this.$parent);
                                this.$parent.$parent.getNetworkList();
                                // this.$emit('parentHandle');
                                this.closeHandle()
                                // 调用重启设备方法
                                // this.restartDeviceFun();
                            } else {
                                // if (code !== 500) this.$message.error(msg)
                            }
                        })
                    }).catch(()=> {
                        this.closeHandle()
                    })
                }
            })
        },
        closeHandle() {
            this.networkShow = false;
            this.boundForm.ipType = 'all';
            this.$nextTick(() => {
                this.$refs["boundForm"].resetFields();
            })
        }
    }
}
</script>

<style scoped></style>

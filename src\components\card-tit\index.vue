<template>
  <div class="card-tit">
    <span class="icon"></span>
    <span class="title">{{ title }}</span>
    <span v-if="isShow" class="icon arr_right"></span>
  </div>
</template>

<script>
    export default {
        props: {
            title: {
                type: String,
                default: '任务概况'
            },
            isShow: {
              type: Boolean,
              default: true
            }
        }
    }
</script>

<style lang="less">
.card-tit {
  // color: #f5784b;
  // color: #1887ee;
  color: #333;
  font-size: 15px;
  font-weight: 700;
  /*margin-bottom: 5px;*/
  text-align: center;
  background-color: #f1f1f2;
  padding: 8px 10px;
  .icon {
    display: inline-block;
    width: 40px;
    height: 18px;
    /*background: url("../../assets/img/Home_left.png") no-repeat 0 6px;*/
    /*margin-right: 6px;*/
  }
  .arr_right {
    /*background: url("../../assets/img/Home_right.png") no-repeat 0 6px;*/
  }
}
</style>

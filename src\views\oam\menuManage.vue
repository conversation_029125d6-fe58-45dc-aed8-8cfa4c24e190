<template>
  <div>
    <el-card class="box-card" shadow="always" style="padding-bottom: 10px;">
      <el-row>
        <el-col span="6">
          <el-card class="box-card" shadow="always" style="padding-bottom: 10px; min-height: 500px;">
            <div style="border-bottom:1px solid #cccccc">
              <p style="margin-left: 1%">菜单结构</p>
            </div>
            <el-tree :data="treeData" :props="defaultProps" style="margin-top: 10px"></el-tree>
          </el-card>
        </el-col>


        <el-col span="18">
          <el-card v-show="showSearch" class="box-card" shadow="always">
            <el-form :inline="true" :show-message="false" label-width="auto" class="user-search comForm">
              <el-form-item label="菜单名称：">
                <el-input size="mini" v-model="queryParam.menuName" clearable></el-input>
              </el-form-item>
              <el-form-item label="父级菜单:">
                <el-select v-model="queryParam.parentId" placeholder="请选择" size="small" style="width: 240px">
                  <el-option v-for="item in pageParentOptions" :key="item.id" :label="item.menuName"
                    :value="item.id"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item style="background-color: transparent;">
                <el-button class="comBtn com_send_btn" size="mini" type="primary" icon="el-icon-search"
                  @click="refreshMenus">搜索</el-button>
                <el-button class="comBtn com_reset_btn" size="mini" type="primary" icon="el-icon-refresh"
                  @click="resetQueryParam">重置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
          <div v-show="showSearch" style="padding: 5px"></div>
          <el-card class="box-card" shadow="always" style="padding-bottom: 10px">

            <el-row>
              <el-col :span="14" style="text-align: left">
                <el-button class="comBtn com_send_btn" size="mini" type="success" @click="openAddView">新增</el-button>
              </el-col>
              <el-col :span="10">
                <div style="text-align: right">
                  <el-button-group>
                    <el-button size="mini" icon="el-icon-search" @click="showAndSearch"></el-button>
                    <el-button size="mini" icon="el-icon-refresh-left" @click="refreshMenus"></el-button>
                  </el-button-group>
                </div>
              </el-col>
            </el-row>

            <div v-show="showSearch" style="padding: 5px"></div>

            <createTable :tableData="tableData" :tableHeader="tableHeader" :isPage="isPage"
              :pageAttributes="{ total: total, currentPage: queryParam.pageNo, pageSize: queryParam.pageSize }"
              :current-change="currentChange" :sizeChange="sizeChange">
            </createTable>
          </el-card>
        </el-col>
      </el-row>

    </el-card>

    <!-- 新增菜单 -->
    <el-dialog title="新增菜单" :visible.sync="addVisible" width="500px" @close="$refs.addMenuForm.resetFields()">
      <el-form label-width="150px" ref="addMenuForm" :model="addMenuForm" :rules="rules">
        <el-form-item label="菜单名称:" prop="menuName">
          <el-input size="small" v-model="addMenuForm.menuName" placeholder="请输入菜单名称" />
        </el-form-item>
        <el-form-item label="菜单层级:" prop="menuType">
          <el-select v-model="addMenuForm.menuType" placeholder="请选择" size="small" style="width: 240px">
            <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="addMenuForm.menuType == 2" label="路由地址:" prop="menuUrl">
          <el-input size="small" v-model="addMenuForm.menuUrl" placeholder="请输入路由地址" />
        </el-form-item>


        <el-form-item v-if="addMenuForm.menuType == 0" label="父级菜单:" prop="parentId">
          <el-select v-model="editMenuForm.parentId" placeholder="请选择" size="small" style="width: 240px">
            <el-option v-for="item in rootOptions" :key="item.id" :label="item.menuName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="addMenuForm.menuType == 1" label="父级菜单:" prop="parentId">
          <el-select v-model="addMenuForm.parentId" placeholder="请选择" size="small" style="width: 240px">
            <el-option v-for="item in rootMenuOptions" :key="item.id" :label="item.menuName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="addMenuForm.menuType == 2" label="父级菜单:" prop="parentId">
          <el-select v-model="addMenuForm.parentId" placeholder="请选择" size="small" style="width: 240px">
            <el-option v-for="item in pageParentOptions" :key="item.id" :label="item.menuName"
              :value="item.id"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="菜单排序:" prop="menuSort">
          <el-input size="small" v-model="addMenuForm.menuSort" placeholder="轻输入排序号" />
        </el-form-item>
        <el-form-item label="菜单图标:" prop="menuClass">
          <el-input size="small" v-model="addMenuForm.menuClass" placeholder="请输入菜单图标" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button class="comBtn com_reset_btn" @click="closeAddMenuView">取 消</el-button>
        <el-button class="comBtn com_send_btn" @click="submitInsert">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 编辑菜单 -->
    <el-dialog title="编辑菜单" :visible.sync="editVisible" width="500px" @close="$refs.editMenuForm.resetFields()">
      <el-form label-width="150px" ref="editMenuForm" :model="editMenuForm" :rules="rules">
        <el-form-item label="菜单名称:" prop="menuName">
          <el-input size="small" v-model="editMenuForm.menuName" placeholder="请输入菜单名称" />
        </el-form-item>
        <el-form-item label="菜单层级:" prop="menuType">
          <el-select v-model="editMenuForm.menuType" placeholder="请选择" size="small" style="width: 240px">
            <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="editMenuForm.menuType == 2" label="路由地址:" prop="menuUrl">
          <el-input size="small" v-model="editMenuForm.menuUrl" placeholder="请输入路由地址" />
        </el-form-item>

        <el-form-item v-if="editMenuForm.menuType == 0" label="父级菜单:" prop="parentId">
          <el-select v-model="editMenuForm.parentId" placeholder="请选择" size="small" style="width: 240px">
            <el-option v-for="item in rootOptions" :key="item.id" :label="item.menuName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="editMenuForm.menuType == 1" label="父级菜单:" prop="parentId">
          <el-select v-model="editMenuForm.parentId" placeholder="请选择" size="small" style="width: 240px">
            <el-option v-for="item in rootMenuOptions" :key="item.id" :label="item.menuName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="editMenuForm.menuType == 2" label="父级菜单:" prop="parentId">
          <el-select v-model="editMenuForm.parentId" placeholder="请选择" size="small" style="width: 240px">
            <el-option v-for="item in pageParentOptions" :key="item.id" :label="item.menuName"
              :value="item.id"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="菜单排序:" prop="menuSort">
          <el-input size="small" v-model="editMenuForm.menuSort" placeholder="轻输入排序号" />
        </el-form-item>
        <el-form-item label="菜单图标:" prop="menuClass">
          <el-input size="small" v-model="editMenuForm.menuClass" placeholder="请输入菜单图标" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button class="comBtn com_reset_btn" @click="closeEditMenuView">取 消</el-button>
        <el-button class="comBtn com_send_btn" @click="submitEdit">确 定</el-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>
// import menuMG from "@/api/menuMG";
export default {
  name: "menuManage",
  data() {
    let _this = this;
    return {
      showSearch: true,
      addVisible: false,
      editVisible: false,
      tableData: {},
      total: 0,
      isPage: true,
      treeData: [],
      pageParentOptions: [],
      rootMenuOptions: [],
      rootOptions: [{ id: 0, menuName: '/' }],
      queryParam: {
        pageNo: 1,
        pageSize: 10,
        menuName: undefined,
        parentId: undefined,
      },
      addMenuForm: {
        menuName: "",
        menuUrl: "",
        menuSort: 0,
        menuType: 0,
        parentId: 0,
        menuClass: "",
      },
      editMenuForm: {
        id: undefined,
        menuName: "",
        menuUrl: "",
        menuSort: 0,
        menuType: 0,
        parentId: 0,
        menuClass: "",
      },
      typeOptions: [
        { label: "菜单", value: 0 },
        { label: "目录", value: 1 },
        { label: "页面", value: 2 },
      ],
      defaultProps: {
        children: 'menus',
        label: 'menuName'
      },
      rules: {
        menuName: [{ required: true, message: '请输入菜单名称', trigger: 'blur' }],
        menuUrl: [{ required: true, message: '请输入路由地址', trigger: 'blur' }],
        menuSort: [{ required: true, message: '请输入菜单排序', trigger: 'blur' }],
        menuType: [{ required: true, message: '请选择菜单类型', trigger: 'blur' }],
        parentId: [{ required: true, message: '请选择父级菜单', trigger: 'blur' }],
        menuClass: [{ required: true, message: '请输入菜单图标', trigger: 'blur' }],
      },
      tableHeader: [
        {
          type: "index",
          label: "序号",
        }, {
          type: "normal",
          label: "菜单名称",
          prop: "menuName",
        }, {
          type: "text_formatter",
          label: "菜单层级",
          prop: "menuType",
          formatter: function (value, row) {
            if (value == 0) {
              return "菜单";
            } else if (value == 1) {
              return "目录";
            } else if (value == 2) {
              return "页面";
            }
          }
        }, {
          type: "normal",
          label: "路由地址",
          prop: "menuUrl"
        }, {
          type: "normal",
          label: "菜单图标",
          prop: "menuClass",
        }, {
          type: "normal",
          label: "排序",
          prop: "menuSort",
        }, {
          type: "operation",
          label: "操作",
          width: "150",
          tag: [{
            name: "编辑",
            operType: "put",
            tagType: "el-button",
            attributes: {
              size: "mini",
              type: "text",
              icon: "el-icon-edit"
            },
            callback: function (row, opts, event) {
              _this.openEditView(row);
            }
          },
          {
            name: "删除",
            operType: "del",
            tagType: "el-button",
            attributes: {
              size: "mini",
              type: "text",
              icon: "el-icon-delete"
            },
            isShow: function (row) {
              return row.system;
            },
            callback: function (row, opts, event) {
              _this.deleteMenu(row);
            }
          }]
        }
      ],
    }
  },
  methods: {
    refreshMenus() {
      this.$http.menuMG.pageMenu(this.queryParam).then(res => {
        this.tableData = res.data.content;
        this.total = res.data.totalSize;
      })
    },
    resetQueryParam() {
      this.queryParam.pageNo = 1;
      this.queryParam.pageSize = 10;
      this.queryParam.menuName = undefined;
      this.queryParam.parentId = undefined;
      this.refreshMenus();
    },
    showAndSearch() {
      this.showSearch = !this.showSearch;
    },
    currentChange(res) {
      this.queryParam.pageNo = res;
      this.refreshMenus();
    },
    sizeChange(res) {
      this.queryParam.pageSize = res;
      this.refreshMenus();
    },
    getTreeMenu() {
      this.$http.menuMG.menuTree().then(res => {
        let code = res.code;
        if (code == 0) {
          this.treeData = res.data;
        } else {
          this.$message.warning(res.msg || "获取菜单树列表异常")
        }
      })
    },
    getRootMenuOptions() {
      this.$http.menuMG.rootMenuList().then(res => {
        this.rootMenuOptions = res.data;
      })
    },
    getPageParentOptions() {
      this.$http.menuMG.pageParentList().then(res => {
        this.pageParentOptions = res.data;
      })
    },
    submitInsert() {
      this.$refs["addMenuForm"].validate(vaild => {
        if (vaild) {
          this.$http.menuMG.insertMenu(this.addMenuForm).then(res => {
            let code = res.code;
            if (code === 0) {
              this.$message.success(res.msg || "新增菜单成功");
              this.renewMenuData();
              this.closeAddMenuView();
            } else {
              this.$message.warning(res.msg || "新增菜单异常");
            }
          })
        }
      })
    },
    submitEdit() {
      this.$refs["editMenuForm"].validate(vaild => {
        if (vaild) {
          this.$http.menuMG.updateMenu(this.editMenuForm).then(res => {
            let code = res.code;
            if (code === 0) {
              this.$message.success(res.msg || "编辑菜单成功");
              this.renewMenuData();
              this.closeEditMenuView();
            } else {
              this.$message.warning(res.msg || "编辑菜单异常");
            }
          })
        }
      })
    },
    deleteMenu(row) {
      this.$confirm('确定要删除菜单?', '确定', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.menuMG.deleteMenu(row.id).then(res => {
          let code = res.code;
          if (code === 0) {
            this.$message.success("删除菜单成功");
            this.renewMenuData();
          } else {
            this.$message.warning(res.msg || "删除菜单异常");
          }
        })
      })
    },
    renewMenuData() {
      this.getTreeMenu();
      this.refreshMenus();
      this.getRootMenuOptions();
      this.getPageParentOptions();
    },
    openEditView(row) {
      this.editMenuForm.id = row.id;
      this.editMenuForm.menuName = row.menuName;
      this.editMenuForm.menuUrl = row.menuUrl;
      this.editMenuForm.menuSort = row.menuSort;
      this.editMenuForm.menuType = row.menuType;
      this.editMenuForm.parentId = row.parentId;
      this.editMenuForm.menuClass = row.menuClass;
      this.editVisible = true;
    },
    closeEditMenuView() {
      this.editMenuForm.id = undefined;
      this.editMenuForm.menuName = undefined;
      this.editMenuForm.menuUrl = undefined;
      this.editMenuForm.menuSort = 0;
      this.editMenuForm.menuType = 0;
      this.editMenuForm.parentId = 0;
      this.editMenuForm.menuClass = undefined;
      this.editVisible = false;
    },
    openAddView() {
      this.addVisible = true;
    },
    closeAddMenuView() {
      this.editMenuForm.menuName = undefined;
      this.editMenuForm.menuUrl = undefined;
      this.editMenuForm.menuSort = 0;
      this.editMenuForm.menuType = 0;
      this.editMenuForm.parentId = 0;
      this.editMenuForm.menuClass = undefined;
      this.addVisible = false;
    },
  },
  created() {
    this.getRootMenuOptions();
    this.getPageParentOptions();
    this.getTreeMenu();
    this.refreshMenus();
  }
}
</script>

<style scoped></style>

<template>
  <div class="container">
    <el-card v-show="showSearch" class="box-card" shadow="always" style="padding-bottom: 10px">
      <el-form :inline="true" :show-message="false" label-width="120px" class="user-search comForm"
               style="text-align: left">
        <el-form-item label="算法类型：">
          <el-select size="small" v-model="queryParams.keyType">
            <el-option label="全部" :value="-1"></el-option>
            <el-option label="SM2" :value="2"></el-option>
            <el-option label="RSA" :value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否存在密钥：">
          <el-select size="small" v-model="queryParams.keyStatus">
            <el-option label="全部" :value="-1"></el-option>
            <el-option label="存在" :value="1"></el-option>
            <el-option label="不存在" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是已使用：">
          <el-select size="small" v-model="queryParams.keyUse">
            <el-option label="全部" :value="-1"></el-option>
            <el-option label="已使用" :value="1"></el-option>
            <el-option label="未使用" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item style="background-color: transparent;">
          <el-button class="comBtn com_send_btn" size="mini" type="primary" icon="el-icon-search" @click="refreshApp">
            搜索
          </el-button>
          <el-button class="comBtn com_reset_btn" size="mini" type="primary" icon="el-icon-refresh"
                     @click="Object.assign(queryParams,$options.data().queryParams);">重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <div v-show="showSearch" style="padding: 10px"></div>
    <el-card class="box-card" shadow="always" style="padding-bottom: 10px">
      <!-- 操作方法 -->
      <el-form label-width="100px">
        <el-row>
          <el-col :span="14" style="text-align: left">
            <el-button class="comBtn com_send_btn" size="mini" type="success" @click="eopenWindow">生成密钥</el-button>
            <el-button class="comBtn com_send_btn" size="mini" type="success" @click="ebatchOpenWindow">批量生成</el-button>
            <el-button class="comBtn com_send_btn" size="mini" type="success" @click="synCard">同步密钥状态</el-button>
            <el-button class="comBtn com_del_btn" size="mini" type="danger" @click="clearKey">清空所有未使用密钥</el-button>
          </el-col>
          <el-col :span="10">
            <div style="text-align: right">
              <el-button-group>
                <el-button size="mini" icon="el-icon-search" @click="showAndSearch"></el-button>
                <el-button size="mini" icon="el-icon-refresh-left" @click="refreshApp"></el-button>
              </el-button-group>
            </div>
          </el-col>
        </el-row>
      </el-form>

      <div id="keyTable" style="padding-top: 10px">
        <createTable
          :tableData="tableData"
          :tableHeader="tableDataHeader"
          :isPage="isPage"
          :pageAttributes="pageAttr"
          :current-change="currentChange"
          :sizeChange="sizeChange"
          tableRef="tableCot"
        >
        </createTable>
      </div>
    </el-card>

    <el-dialog title="生成" :visible.sync="openWindow" width="350px" append-to-body :close-on-click-modal="false"
               @close="Object.assign(form, $options.data().form)">
      <el-form label-width="100px;">
        <el-form-item label="加密算法:">
          <el-select v-model="form.certType">
            <el-option :value="1" label="RSA"></el-option>
            <el-option :value="2" label="SM2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="密钥长度:" v-if="form.certType == 1">
          <el-select v-model="form.keyLength">
            <el-option label="1024" :value="1024"></el-option>
            <el-option label="2048" :value="2048"></el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button class="comBtn com_reset_btn" @click="openWindow = false">取 消</el-button>
        <el-button class="comBtn com_send_btn" @click="generateSignKey" :loading="signKeyLoading">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="批量生成" :visible.sync="batchOpenWindow" width="350px" append-to-body :close-on-click-modal="false"
               @close="Object.assign(form, $options.data().form);websock.close();percentageShow=false;percentage=0;">
      <el-form :model="form" :rules="rules" ref="form">
        <el-form-item label="生成数量:" label-width="100px" prop="num">
          <el-input v-model="form.num" oninput="value=value.replace(/[^\d]/g,'')" :placeholder="placeholder" clearable></el-input>
        </el-form-item>
        <el-form-item label="加密算法:" label-width="100px">
          <el-select v-model="form.certType" @change="changeE">
            <el-option :value="1" label="RSA"></el-option>
            <el-option :value="2" label="SM2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="密钥长度:" v-if="form.certType == 1" label-width="100px">
          <el-select v-model="form.keyLength">
            <el-option label="1024" :value="1024"></el-option>
            <el-option label="2048" :value="2048"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="成功:" v-show="percentageShow" label-width="100px">
          <el-progress style="margin-top: 12px" :text-inside="true" :stroke-width="15"
                       :percentage="percentage"></el-progress>
        </el-form-item>
          <el-form-item label="完成进度:" v-show="percentageShow" label-width="100px">
              <el-progress style="margin-top: 12px" :text-inside="true" :stroke-width="15"
                           :percentage="percentage"></el-progress>
          </el-form-item>
          <el-form-item label="完成进度:" v-show="percentageShow" label-width="100px">
              <el-progress style="margin-top: 12px" :text-inside="true" :stroke-width="15"
                           :percentage="percentage"></el-progress>
          </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button class="comBtn com_reset_btn" @click="batchOpenWindow = false">取 消</el-button>
        <el-button class="comBtn com_send_btn" @click="generateMulKey" :loading="mulkeyLoading">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="清空密钥任务进度详情" :visible.sync="dshowWindows" width="350px" append-to-body :close-on-click-modal="false"
               @close="websock.close();dpercentage=0;">
      <el-form style="margin-top: -20px">
        <el-form-item label="清空密钥中，请稍后...">
          <el-progress style="margin-top: 12px" :text-inside="true" :stroke-width="15"
                       :percentage="dpercentage"></el-progress>
        </el-form-item>
      </el-form>

    </el-dialog>
  </div>
</template>

<script>
  // import keyMG from "@/api/keyMG";
  import {dateFormat, getStore, getWebSocketPre} from "@/utils/util";
  // import whiteListMG from "@/api/whiteListMG";
  // import bussMG from "@/api/bussMG";
  // import $ from "jquery";

  export default {
    name: "internal-keys-manage",
    data() {
      var _this = this;
      var cehckNum = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请输入生成数量'));
        } else {
          let sum = 0;
          _this.keyInfos.forEach(obj => {
            if (obj.keyType == _this.form.certType) {
              sum = obj.count;
            }
          });
          if (value <= 0 || value > sum) {
            callback(new Error('请输1~' + sum + "个"));
          } else {
            callback();
          }
        }
      };

      return {
        showSearch: true,
        placeholder: "",
        openWindow: false,
        batchOpenWindow: false,
        Height: 250,
        signKeyLoading: false,
        mulkeyLoading: false,
        initOpen: true,
        tableData: [],
        tableDataHeader: [],
        isPage: true,
        pageAttr: {},
        queryParams: {
          keyType: -1,
          keyStatus: -1,
          keyUse: -1,
          pageNo: 1,
          pageSize: 10
        },
        keyInfos: {},
        form: {
          certType: 2,
          keyLength: 2048,
          num: ""
        },
        wsServer: "",
        percentageShow: false,
        percentage: 0,
        dshowWindows: false,
        dpercentage: 0,
        rules: {
          "num": [
            {required: true, message: '生成数量不为为空！', trigger: 'blur'},
            {validator: cehckNum, trigger: 'blur'}
          ],
        }
      }
    },
    methods: {
      initWebSocket() { //初始化websocket
        let _this = this;
        this.$nextTick(() => {
          if (!this.websock) {
            this.websock = new WebSocket(_this.wsServer);
            this.websock.onmessage = this.websocketonmessage;
            this.websock.onopen = this.websocketonopen;
            this.websock.onerror = this.websocketonerror;
            this.websock.onclose = this.websocketclose;
          } else {
            if (this.websock.readyState != this.websock.CONNECTING) {
              this.websock = new WebSocket(_this.wsServer);
              this.websock.onmessage = this.websocketonmessage;
              this.websock.onopen = this.websocketonopen;
              this.websock.onerror = this.websocketonerror;
              this.websock.onclose = this.websocketclose;
            }
          }
        });
      },
      websocketonopen(e, c, d) { //连接建立之后执行send方法发送数据
        console.log("open session")
      },
      websocketonerror() {//连接建立失败重连
        this.initWebSocket();

      },
      websocketonmessage(e, l, d) { //数据接收
        let message = JSON.parse(e.data);
        let type = message.type;
        let value = message.data;
        if (type == "GK") {
          let number = parseFloat(value) / parseFloat(this.form.num);
          number = number.toFixed(2)
          this.percentage = parseInt(number * 100);
        } else if (type == "DK") {
          let number = parseFloat(value) / parseFloat(288);
          number = number.toFixed(2)
          this.dpercentage = parseInt(number * 100);
        }
      },
      websocketsend(Data) {//数据发送
        this.websock.send(Data);
      },
      websocketclose(e) {  //关闭
        console.log('断开连接', e);
      },
      clearKey() {
        this.initWebSocket();
        let _this = this;
        this.$confirm('确定要清空所有密钥?', '确定', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          _this.dshowWindows = true;
          this.$http.keyMG.init(function () {
            _this.dpercentage = 100;
            setTimeout(()=>{ _this.dshowWindows = false;},2000)

          }).then(res => {
            _this.refreshApp();
          })
        });
      },
      synCard() {
        let _this = this;
        const loading = this.$loading({
          lock: true,
          text: '数据同步中，请稍等...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        this.$http.keyMG.synCard(function () {
          loading.close();
        }).then(res => {
          _this.refreshApp();
        });
      },
      changeE() {
        let _this = this;
        let sum = 0;
        _this.keyInfos.forEach(obj => {
          if (obj.keyType == this.form.certType) {
            sum = obj.count;
          }
        });
        this.placeholder = "不能超过" + sum + "个";
      },
      generateSignKey() {
        let _this = this;
        this.signKeyLoading = true;
        this.$http.keyMG.generateSignKey(this.form.certType, this.form.keyLength).then(res => {
          _this.refreshApp();
          _this.openWindow = false;
          _this.signKeyLoading = false;
        }, error => {
          _this.signKeyLoading = false;
        });
      },
      generateMulKey() {
        let _this = this;
        this.$refs["form"].validate((valid) => {
          if (valid) {
            _this.mulkeyLoading = true;
            _this.percentageShow = true;
            this.$http.keyMG.generateMulKey(this.form.certType, this.form.keyLength, this.form.num).then(res => {
              _this.refreshApp();
              _this.mulkeyLoading = false;
              setTimeout(()=>{
                _this.batchOpenWindow = false;
              },2000)

            }, error => {
              _this.mulkeyLoading = false;
            });
          }
        });
      },
      ebatchOpenWindow() {
        this.keyUnUseInfo();
        this.batchOpenWindow = true;
        this.initWebSocket();
      },
      eopenWindow() {
        this.keyUnUseInfo();
        this.openWindow = true;
      },
      keyUnUseInfo() {
        let _this = this;
        this.$http.keyMG.keyUnUseInfo().then(res => {
          _this.keyInfos = res.data;
          _this.changeE();
        });
      },
      showAndSearch() {
        this.showSearch = !this.showSearch;
      },
      refreshApp: function () {
        let _this = this;
        _this.tableData = [];
        let r = this.$http.keyMG.list(this.queryParams);
        r.then(res => {
          _this.tableData = res.data;
          _this.pageAttr.total = res.row;
          _this.isPage = res.row > 0 ? true : false;
        });
      },
      bindSubmit: function () {

      },
      currentChange: function (val) {
        this.queryParams.pageNo = val;
        this.refreshApp();
      },
      sizeChange: function (val) {
        this.queryParams.pageSize = val
        this.refreshApp();
      }
    },
    created() {
      let _this = this;
      this.wsServer = getWebSocketPre() + "/keyWebsocket/100";
      this.refreshApp();
      this.tableDataHeader = [{
        type: "index",
        label: "序号",
        width: "100"
      }, {
        type: "normal",
        label: "索引号",
        prop: "keyIndex",
        width: "300"
      }, {
        type: "text_formatter",
        label: "算法",
        prop: "keyType",
        formatter: function (value, row) {
          if (value == 1) {
            return "RSA";
          } else {
            return "SM2";
          }
        }
      },
        {
          type: "text_formatter",
          label: "是否使用",
          prop: "keyUse",
          html: true,
          width: "100",
          formatter: function (value, row) {
            if (value) {
              return "已使用"
            } else {
              return "<span style='color: green'>未使用</span>";
            }
          }
        }, {
          type: "text_formatter",
          label: "密钥长度",
          prop: "keyLength",
          width: "100",
          formatter: function (value, row) {
            if (row.keyType == 1) {
              return value
            } else {
              return "-";
            }
          }
        }, {
          type: "text_formatter",
          label: "密钥状态",
          prop: "keyStatus",
          width: "100",
          html: true,
          formatter: function (value, row) {
            let html = "";
            if (value == 0) {
              return "<span  style='color: red'>不在位</span>";
            } else if (value == 1) {
              return "<span style='color: green'>在位</span>";
            } else {
              return "<span style='color: red'>不在位</span>";
            }
          }
        }, {
          type: "operation",
          label: "操作",
          width: "150",
          tag: [
            {
              name: "清空密钥",
              operType: "update",
              tagType: "el-button",
              attributes: {
                size: "mini",
                type: "text",
                icon: "el-icon-delete"
              },
              isShow(row) {
                if (row.keyUse == 0) return true
                else return false;
              },
              callback: function (row) {
                _this.$confirm('确定要清空密钥?', '确定', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                }).then(() => {
                  _this.$http.keyMG.del(row.id).then(res => {
                    _this.refreshApp();
                  });
                });
              }
            }
          ]
        }]

    }
    ,
    mounted() {

    }
  }
</script>

<style scoped>
  .container {
    padding: -10px;
  }

  .user-search .el-form-item {
    margin-bottom: 0px;
    margin-top: 10px;
  }
</style>

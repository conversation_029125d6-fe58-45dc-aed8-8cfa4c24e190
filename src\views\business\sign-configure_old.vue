<template>
    <div class="container">
        <el-card class="box-card" shadow="always" style="padding-bottom: 10px">
            <!-- 操作方法 -->
            <!--<el-form label-width="100px">-->
            <!--<el-row>-->
            <!--<el-col :span="14" style="text-align: left">-->
            <el-button class="comBtn com_send_btn" size="mini" type="success" @click="refreshApp()">刷新</el-button>
            <Strategy :policyType="3"></Strategy>
            <el-button class="comBtn com_send_btn" size="mini" type="success" :disabled="isDisabled" @click="switchingStateHandle()"> {{ switchingState }}</el-button>

            <el-button class="comBtn com_send_btn" size="mini" type="primary" @click="resetPinCard">重置密码卡</el-button>
            <el-button class="comBtn com_send_btn" :disabled="$store.state.CURRENT_DEV_STATE !== '出厂状态' && $store.state.CURRENT_DEV_STATE !== '初始化进行中状态'" size="mini" @click="createKeyManage">创建密钥主管</el-button>
            <!--<el-button class="comBtn com_send_btn" size="mini" @click="createKeyManage">创建密钥主管</el-button>-->
            <!--</el-col>-->
            <!--</el-row>-->
            <!--</el-form>-->
            <div id="server" style="padding-top: 10px">
                <createTable
                        :tableData="tableData"
                        :tableHeader="tableDataHeader"
                        :isPage="isPage"
                        :pageAttributes="pageAttr"
                >
                </createTable>
            </div>
        </el-card>
        <!--配置信息-->
        <el-dialog title="签名验签配置" :visible.sync="openWindow" width="600px" append-to-body :close-on-click-modal="false" @close="Object.assign(form, $options.data().form)">
            <el-form label-width="150px">
                <el-form-item label="名称" prop="serviceName">
                    <el-input v-model="form.serviceName" :disabled="true"/>
                </el-form-item>
                <el-form-item label="网口设置" prop="networkInterface">
                    <el-select v-model="form.networkInterface" :disabled="true">
                        <!--<el-option v-for="val in networkInterfaces" :value="val" :label="val"></el-option>-->
                    </el-select>
                </el-form-item>
                <el-form-item label="端口设置" prop="servicePort">
                    <el-input v-model="form.servicePort" :disabled="true"/>
                </el-form-item>
                <el-form-item label="日志级别" prop="logLevel">
                    <el-select v-model="form.logLevel">
                        <el-option label="关闭" :value="0"></el-option>
                        <el-option label="信息" :value="1"></el-option>
                        <!--            <el-option label="警告" :value="2"></el-option>-->
                        <el-option label="错误" :value="3"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="白名单未设置策略">
                    <el-radio :label="false" v-model="form.whiteStrategy">通过</el-radio>
                    <el-radio :label="true" v-model="form.whiteStrategy">禁用</el-radio>
                </el-form-item>

                <el-form-item label="应用编码" v-if="false">
                    <el-radio :label="false" v-model="form.appCodeStrategy">非必填</el-radio>
                    <el-radio :label="true" v-model="form.appCodeStrategy">必填</el-radio>
                </el-form-item>
            </el-form>

            <div slot="footer" class="dialog-footer">
                <el-button class="comBtn com_reset_btn" @click="openWindow=false">取 消</el-button>
                <el-button class="comBtn com_send_btn" type="primary" @click="submit">确 定</el-button>
            </div>
        </el-dialog>
        <reset-card ref="resetCard"></reset-card>
    </div>
</template>

<script>
    import svsServrMG from "@/api/svsServrMG";
    import systemMG from "../../api/systemMG";
    import Strategy from "@/components/strategy"
    import resetCard from "../oam/cryptoCard_old"

    export default {
        name: "svs-list",
        components: {Strategy, resetCard},
        data() {
            return {
                tableData: [],
                test: 0,
                tableDataHeader: [],
                openWindow: false,
                isPage: false,
                isDisabled: false,
                pageAttr: {},
                form: {
                    serviceName: "",
                    id: "",
                    networkInterface: "",
                    servicePort: "",
                    logLevel: "",
                    whiteStrategy: false,
                    appCodeStrategy: true
                },
                networkInterfaces: ["全部", "eth0", "eth1"],
                switchingState: '切换至无访态',
                flag: '',

                cardStatus: '', // 密码卡状态
            }
        },
        methods: {
            // 查询当前状态(是否无访态)
            // 0：有访态
            // 1：无访态
            getStateFun() {
                systemMG.getStatus().then(({code, data, msg}) => {
                    console.log(code, data, msg);
                    this.flag = data;
                    this.switchingState = data === 1 ? '切换至有访态' : '切换至无访态'
                })
            },
            /**
             * 重置密码卡
             * */
            resetPinCard() {
                this.$refs.resetCard.resetPinCard()
            },
            /**
             * 创建密码主管(出厂态可操作)
             * */
            createKeyManage() {
                this.$refs.resetCard.createKeyManage()
            },
            /**
             * 获取设备状态(卡状态)
             * */
            // getDeviceStatus() {
            //     systemMG.devStatusApi().then(({code, data}) => {
            //         this.cardStatus = data;
            //     })
            // },
            // 切换有无访态状态
            switchingStateHandle() {
                let num = this.flag === 1 ? 0 : 1;
                systemMG.setStatus(num).then(({code, data, msg}) => {
                    // isDisabled
                    console.log(code, data, msg);
                    if (code === 0) {
                        this.isDisabled = true;
                        this.$message.success(msg)
                    } else {
                        this.isDisabled = false;
                        // this.$message.warning(msg)
                    }
                    // code === 0 ? this.isDisabled = true : this.$message.warning(msg)
                }).catch(() => {
                    this.isDisabled = false
                })
            },

            refreshApp() {
                this.tableData = [];
                let _this = this;
                svsServrMG.list().then(res => {
                    _this.tableData = res.data;
                });
            },
            submit() {
                let _this = this;
                svsServrMG.edit(this.form).then(res => {
                    _this.openWindow = false;
                    _this.refreshApp();
                });
            }
        },
        created() {
            this.getStateFun();
            if (!this.$store.state.isShowMenu) this.$router.push({path: '/home'});
            let _this = this;
            this.refreshApp();
            this.tableDataHeader = [{
                type: "index",
                label: "序号",
                width: "100",
            }, {
                type: "normal",
                label: "服务名称",
                prop: "serviceName"
            }, {
                type: "normal",
                label: "网络接口",
                prop: "networkInterface"
            }, {
                type: "switch",
                label: "是否启动",
                // label: "服务状态",
                width: "80",
                prop: "serviceStatus",
                attrs: {"disabled": true},
                callback(row) {
                    // svsServrMG.changeStatus(!row.serviceStatus, row.id).then(res => {
                    //     _this.refreshApp();
                    // });
                },
            }, {
                type: "operation",
                label: "操作",
                width: "150",
                tag: [
                    {
                        name: "编辑",
                        operType: "update",
                        tagType: "el-button",
                        attributes: {
                            size: "mini",
                            type: "text",
                            icon: "el-icon-update"
                        },
                        callback: function (row) {
                            svsServrMG.queryById(row.id).then(res => {
                                _this.form = res.data;
                                _this.openWindow = true;
                            });
                        }
                    }
                ]
            }];
            systemMG.allEth().then(res => {
                const code = res.code;
                if (code == 0) {
                    let arrays = ["全部", ...res.data];
                    this.networkInterfaces = arrays;
                } else {
                    this.$message.error("获取网卡信息失败")
                }
            });
            // this.getDeviceStatus()
        }
    }

</script>

<style scoped>
    .container {
        padding: -10px;
    }
</style>

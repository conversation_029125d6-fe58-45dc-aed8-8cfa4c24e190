import API from "@/api/apiuri"
import {req, reqFormData, reqfrom, reqFrom, reqGet, reqFromPost, reqCommonForm} from "./axiosFun";
import {getStore} from "@/utils/util";

let symmetryApi = API.symmetryApi;

/**
 * SM9 API
 *  主密钥
 *  用户密钥
 * */
// 主密钥列表
const masterList = (params) => {
    return reqGet("get", "/sm9/master/page", params)
};
// 主密钥策略
const masterPolicy = () => {
    return reqGet("get", "/sm9/master/self/changeDistribution")
};
// 生成签名主密钥
const createMasterKey = (params) => {
    return reqFrom("post", "/sm9/master/generate/key", params)
};
// 生成签名主密钥
// const createSignKey = (params) => {
//     return reqFrom("post", "/dms/key/update/remark", params)
// };
// 生成加密主密钥
// const createEncKey = (params) => {
//     return reqFormData("post", "/dms/key/init", params)
// };
// 导出主密钥
const exportMasterKey = (params) => {
    // return reqfrom("post", "/sm9/master/export", params)
    return reqFromPost("post", "/svs/sm9/master/export", params)
    // return reqFrom("post", "/sm9/master/export", params)
};
// 导入主密钥
const importMasterKey = (params) => {
    return reqfrom("post", "/sm9/master/import", params)
};
// 删除主密钥
const delMasterKey = (id) => {
    return reqFormData("post", `/sm9/master/delete/${id}`)
};
const downloadApi = (params) => {
    let option = {
        headers: {
            "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            "token": "Bearer "+getStore('token')
        },
        responseType: 'blob'
    };
    return reqCommonForm("get", '/sm9/master/import/cert', params, option)
};

// 用户密钥列表
const userKeyList = (params) => {
    return reqGet("get", "/sm9/user/page", params)
};
// 用户密钥策略
const userKeyPolicy = () => {
    return reqGet("get", "/sm9/user/self/changeDistribution")
};
// 生成用户密钥
const createUserKey = (params) => {
    // return reqFrom("post", "/sm9/user/generate/key", params)
    return reqfrom("post", "/sm9/user/generate/key", params)
};
// 删除用户密钥
const delUserKey = (id) => {
    return reqFormData("post", `/sm9/user/delete/${id}`)
};
// 获取主密钥索引
const masterKeyIndex = () => {
    return reqGet("get", "/sm9/master/ids")
};


export default {
    // SM9主密钥
    masterList,
    masterPolicy,
    createMasterKey,
    // createSignKey,
    // createEncKey,
    exportMasterKey,
    importMasterKey,
    delMasterKey,
    downloadApi,
    // SM9用户密钥
    userKeyList,
    userKeyPolicy,
    createUserKey,
    delUserKey,
    masterKeyIndex,


    getSymmetryKeysMaxCount() {
        return req("get", symmetryApi.symmetryKeysMaxCount);
    },
    pageSymmetryKey(params) {
        return req("post", symmetryApi.pageSymmetryKey, params);
    },
    addSymmetryKey(param) {
        return req("post", symmetryApi.addSymmetryKey, param);
    },
    batchAddSymmetryKey(param) {
        return req("post", symmetryApi.batchAddSymmetryKey, param);
    },
    clearSymmetryKey(id) {
        return req("put", symmetryApi.clearSymmetryKey + '/' + id);
    },
    getUnUsedIndex(pageNo) {
        return req("get", symmetryApi.unUsedIndex + '/' + pageNo);
    },
    checkSymmetryKeyUpdate() {
        return req("post", symmetryApi.checkSymmetryKeyUpdate)
    }
}


// 导入文件(组件), 要声明name
// import about from './views/about/about.vue'
// import custom from './views/custom.vue'
// import com_system from './views/system'
// console.log(com_system);

// 导出API
import API from './api'

// 导出路由
import auditRouter from './router/auditRouter.js'
// import businessRouter from './router/businessRouter.js'
import OAMRouter from './router/OAMRouter.js'
import systemRouter from './router/systemRouter.js'
import monitorRouter from './router/monitorRouter.js'

// 导出
import createTable from "@/utils/createTable";

// 导出工具函数
import COM_FILTERS from './utils/filters.js';
// console.log(COM_FILTERS);


// const components = [
//   // about,
//   custom,
//   // API
// ]

// const COM_ARR = [...com_system];
// const COM_ARR = [];
// const modules = require.context('./views', true, /\w+.vue$/);
// modules.keys().forEach(filePath => {
//   const key = filePath.replace(/^\.\/(.*)\.\w+$/, '$1');
//   const com_item = modules(filePath).default;
//   COM_ARR.push(com_item)
// });

// const modules = require.context('./', true, /\w+.vue$/);
// const res = {};
// modules.keys().forEach(filePath => {
//     const key = filePath.replace(/^\.\/(.*)\.\w+$/, '$1');
//     const component = modules(filePath).default;
//     COM_ARR.push(component)
//     res[key] = component
// });


const COM_ARR =[createTable];
const modules = require.context('./views', true, /\w+.vue$/);
const res = {createTable};
// console.log(modules.keys());
modules.keys().forEach(filePath => {
    // console.log(filePath);
    const key = filePath.replace(/^\.\/(.*)\.\w+$/, '$1');
    // console.log();
    const component = modules(filePath).default;
    COM_ARR.push(component)
    let key_item = '';
    if(key.lastIndexOf('/') !== -1) {
        const index = key.lastIndexOf('/');
        key_item = key.substr(index + 1)
    } else {
        key_item = key
    }
    // console.log(key_item);
    res[key_item] = component
});
// console.log(res);
// console.log(COM_ARR);


// console.log(COM_ARR);

const install = function (Vue, opts = {}) {
  COM_ARR.forEach(component => {
    Vue.component(component.name, component);
  });
}
if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue);
}

// 默认导出组件
export default {
  install,
  // about,
  // custom,
  res,
  API,
  auditRouter,
  // businessRouter,
  OAMRouter,
  systemRouter,
  monitorRouter,
  createTable,
  COM_FILTERS
}

<template>
  <!--<div>-->
  <!--<el-card class="box-card" shadow="always" style="padding-bottom: 10px">-->
  <el-tabs type="border-card" v-model="activeName" @tab-click="handleClick">
    <el-tab-pane label="告警配置" name="first">
      <AlarmConfiguration ref="alarmConfiguration" />
    </el-tab-pane>

    <el-tab-pane label="告警阈值" name="second">
      <host-alarm ref="hostAlarm" />
    </el-tab-pane>

    <el-tab-pane label="邮箱配置" name="third">
      <email-alarm ref="emailAlarm" />
    </el-tab-pane>

    <el-tab-pane label="告警模板" name="fourth">
      <alarm-template ref="tempAlarm" />
    </el-tab-pane>

    <el-tab-pane label="告警记录" name="fifth">
      <alarm-record ref="recordAlarm" />
    </el-tab-pane>
  </el-tabs>
  <!--</el-card>-->
  <!--</div>-->
</template>

<script>
import HostAlarm from "@/views/system/alarm/host-alarm";
import EmailAlarm from "@/views/system/alarm/email-alarm";
import AlarmTemplate from "@/views/system/alarm/alarm-template";
import AlarmRecord from "@/views/system/alarm/alarm-record";
import AlarmConfiguration from "@/views/system/alarm/alarm-configuration";
export default {
  name: "alarm-configure",
  components: {
    AlarmRecord,
    AlarmTemplate,
    HostAlarm,
    EmailAlarm,
    AlarmConfiguration,
  },
  data() {
    return {
      activeName: "first",
    };
  },
  methods: {
    handleClick(tab, event) {
      if (tab.name === "first") {
        this.$refs.hostAlarm.getAlarmStatus();
      } else if (tab.name === "second") {
        this.$refs.hostAlarm.getAlarmInfo();
        this.$refs.hostAlarm.getServiceAlarmInfo();
      } else if (tab.name === "third") {
        this.$refs.emailAlarm.getEmailInfo();
      } else if (tab.name === "fourth") {
        this.$refs.tempAlarm.pageNoticeModel();
      } else if (tab.name === "fifth") {
        this.$refs.recordAlarm.getAlarmType();
        this.$refs.recordAlarm.pageAlarmRecord();
      }
    },
  },
};
</script>

<style scoped></style>

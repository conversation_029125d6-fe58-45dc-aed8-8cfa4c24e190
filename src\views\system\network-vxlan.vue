<template>
  <div class="container">
    <el-card class="box-card" shadow="always" style="padding-bottom: 10px">
      <el-button
        class="comBtn com_send_btn"
        size="small"
        @click="addVXLANHandle"
        >新增</el-button
      >
      <el-table
        class="comTab"
        :data="appList"
        stripe
        size="small"
        @selection-change="handleSelectionChange"
        style="margin-top: 10px"
      >
        <el-table-column align="center" prop="index" label="序号" width="80">
          <template slot-scope="scope">
            <span>{{ scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          prop="vxlanName"
          label="连接名"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="vni"
          label="VNI"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          align="center"
          prop="vtepPort"
          label="通信端囗"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="localVtepIp"
          label="当前地址"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          align="center"
          prop="remoteVtepIp"
          label="对端地址"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="vxlanIp"
          label="网络IP"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          align="center"
          prop="vxlanMask"
          label="子网掩码"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="createTime"
          label="创建时间"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column align="center" prop="handel" label="操作" width="200">
          <template slot-scope="{ row }">
            <el-button
              type="text"
              @click="delAppHandle(row)"
              :loading="row.loading"
              ><i class="el-icon-delete"></i>删除</el-button
            >
            <el-button type="text" @click="editAppHandle(row)"
              ><i class="el-icon-edit"></i>编辑</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <div style="text-align: right; margin-top: 10px">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="queryParams.pageNo"
          :page-size="queryParams.pageSize"
          :total="total"
          :page-sizes="[10, 20, 30, 40]"
          layout="total, sizes, prev, pager, next, jumper"
        >
        </el-pagination>
      </div>
    </el-card>
    <!-- 添加or编辑 -->
    <addVXLANDialog
      ref="addVXLANDialog"
      @parentHandle="getVXLANDataList"
    ></addVXLANDialog>
  </div>
</template>

<script>
import addVXLANDialog from "./components/addVXLANDialog";

export default {
  name: "app-manage",
  components: { addVXLANDialog },
  data() {
    return {
      queryParams: {
        pageNo: 1,
        pageSize: 10,
      },
      appList: [],
      idArr: [],
      // 分页参数
      total: 0,
    };
  },
  methods: {
    // 获取应用列表
    getVXLANDataList() {
      this.$http.systemMG
        .getVXLANList(this.queryParams)
        .then(({ code, data, row, msg }) => {
          data.forEach((item, index) => {
            item.loading = false;
          });
          this.appList = data;
          this.total = row;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    // 重置
    resetHandle() {
      Object.assign(this.queryParams, this.$options.data().queryParams);
      this.getVXLANDataList();
    },
    // 新增应用
    addVXLANHandle() {
      this.$refs.addVXLANDialog.addVXLANInit(null, "add");
    },
    // 编辑应用
    editAppHandle(row) {
      this.$refs.addVXLANDialog.addVXLANInit(row, "edit");
    },

    handleSelectionChange(val) {
      this.idArr = val;
    },
    // 删除应用
    delAppHandle(row) {
      this.$confirm("确定要删除吗?", "删除确认", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        row.loading = true;
        this.$http.systemMG.delVXLAN(row.id).then(({ code, data, msg }) => {
          row.loading = false;
          if (code === 0) this.$message.success(msg);
          this.getVXLANDataList();
        });
      });
    },
    // 分页插件事件
    handleSizeChange(val) {
      this.queryParams.pageSize = val;
      this.getVXLANDataList();
    },
    handleCurrentChange(val) {
      this.queryParams.pageNo = val;
      this.getVXLANDataList();
    },
  },
  mounted() {
    this.getVXLANDataList();
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-divider--horizontal {
  margin-top: 0;
}

.user-search .el-form-item {
  margin-bottom: 0;
}
/deep/ .el-pagination__total {
  float: left;
}
</style>

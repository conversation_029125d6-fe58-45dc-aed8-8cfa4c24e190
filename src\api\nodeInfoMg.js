import {req, reqParam<PERSON>o<PERSON>son} from './axiosFun';
import Api from "@/api/apiuri";

const nodeInfoMGApi = Api.nodeInfoMGApi;

export default {
  nodeInfo() {
    return req("get", nodeInfoMGApi.nodeInfo, "");
  },
  changeGoStatus(param) {
    return reqParamNo<PERSON><PERSON>("post", nodeInfoMGApi.changeGoStatus, param);
  },
  syncBatch() {
    return req("get", nodeInfoMGApi.syncBatch, "");
  },
  unLinkManager() {
    return req("get", nodeInfoMGApi.unLinkManager, "");
  },
  linkReply(param) {
    return reqParamNo<PERSON>son("post", nodeInfoMGApi.linkReply, param);
  }
}

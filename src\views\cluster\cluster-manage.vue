<template>
    <div class="container">
        <el-card v-show="showSearch" class="box-card" shadow="always" style="padding-bottom: 10px">
            <el-form :inline="true" :model="searchPage" class="user-search comForm" style="text-align: left">
                <el-form-item label="集群名称 :">
                    <el-input size="small" v-model="searchPage.clusterName" clearable></el-input>
                </el-form-item>
                <el-form-item label="端口 :">
                    <el-input size="small" v-model="searchPage.clusterPort" oninput="value=value.replace(/[^\d]/g,'')"
                              clearable></el-input>
                </el-form-item>
                <el-form-item style="background-color: transparent;">
                    <el-button class="comBtn com_send_btn" size="mini" icon="el-icon-search" @click="pageCluster">搜索</el-button>
                    <el-button class="comBtn com_reset_btn" size="mini" icon="el-icon-refresh" @click="resetSearch">重置
                    </el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <div v-show="showSearch" style="padding: 10px"></div>

        <el-card class="box-card" shadow="always" style="padding-bottom: 10px">
            <el-form label-width="100px">
                <el-row>
                    <el-col :span="14" style="text-align: left">
                        <el-button class="comBtn com_send_btn" size="mini" @click="pageCluster">刷新</el-button>
                        <el-button class="comBtn com_add_btn" size="mini" @click="openCluster">新增</el-button>
                        <el-button class="comBtn" size="mini" type="success" @click="updateLoadBalancing()">更新负载配置</el-button>
                    </el-col>
                    <div style="text-align: right; float: right">
                        <el-button-group>
                            <el-button size="mini" icon="el-icon-search" @click="showAndSearch"></el-button>
                        </el-button-group>
                    </div>
                </el-row>
            </el-form>

            <!-- 列表 -->
            <div style="padding-top: 10px">
                <create-table
                        :tableData="tableData"
                        :tableHeader="tableDataHeader"
                        :isPage="true"
                        :pageAttributes="{total: total, currentPage: searchPage.pageNo}"
                        :size-change="sizeChange"
                        :current-change="currentChange"
                        :prev-click="prevClick"
                        :next-click="nextClick"
                ></create-table>
            </div>
        </el-card>


        <!-- 新增 -->
        <el-dialog title="集群添加" :visible.sync="clusterVisible" width="35%" :before-close="closeCluster"
                   :close-on-click-modal="false">
            <el-form label-width="160px" ref="clusterFrom" :model="clusterFrom" :rules="rules">
                <el-form-item label="集群名称：" prop="clusterName">
                    <el-input size="small" v-model="clusterFrom.clusterName" placeholder="请输入集群名称" maxlength="50" show-word-limit></el-input>
                </el-form-item>
                <!-- oninput="value=value.replace(/[^\d]/g,'')" -->
                <el-form-item label="端口：" prop="clusterPort">
                    <el-input size="small" v-model="clusterFrom.clusterPort" placeholder="请输入集群端口"></el-input>
                </el-form-item>
                <el-form-item label="集群说明：" prop="clusterDes">
                    <el-input size="small" v-model="clusterFrom.clusterDes" placeholder="请输入集群说明" maxlength="255" show-word-limit></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button class="comBtn com_reset_btn" size="small" @click='closeCluster'>取消</el-button>
                <el-button class="comBtn com_send_btn" size="small" type="primary" @click="saveCluster('clusterFrom')">保存</el-button>
            </div>
        </el-dialog>

        <!-- 编辑 -->
        <el-dialog title="集群编辑" :visible.sync="editVisible" width="35%" :before-close="closeEditCluster"
                   :close-on-click-modal="false">
            <el-form label-width="120px" ref="clusterFrom" :model="clusterFrom" :rules="rules">
                <el-form-item label="集群名称：" prop="clusterName">
                    <el-input size="small" v-model="clusterFrom.clusterName" placeholder="请输入集群名称" maxlength="50" show-word-limit></el-input>
                </el-form-item>
                <!-- oninput="value=value.replace(/[^\d]/g,'')" -->
                <el-form-item label="端口：" prop="clusterPort">
                    <el-input size="small" v-model="clusterFrom.clusterPort" placeholder="请输入集群端口"></el-input>
                </el-form-item>
                <el-form-item label="集群说明：" prop="clusterDes">
                    <el-input size="small" v-model="clusterFrom.clusterDes" placeholder="请输入集群说明" maxlength="255" show-word-limit></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button class="comBtn com_reset_btn" size="small" @click='closeEditCluster'>取消</el-button>
                <el-button class="comBtn com_send_btn" size="small" type="primary" @click="editCluster">保存</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
    // import cluster from "@/api/cluster";
    import {elValidatePort} from '@/utils/myValidate'

    export default {
        name: "cluster-manage",
        data() {
            let _this = this;
            return {
                showSearch: true,
                clusterVisible: false,
                editVisible: false,
                // 分页参数
                searchPage: {
                    pageNo: 1,
                    pageSize: 10,
                    clusterName: null,
                    clusterPort: null
                },
                clusterFrom: {
                    id: null,
                    clusterName: null,
                    clusterPort: null,
                    clusterDes: null,
                    builtIn: null
                },
                total: null,
                tableData: [],
                tableDataHeader: [
                    {label: '序号', prop: 'id', type: "index"},
                    {label: '集群名称', prop: 'clusterName', type: "normal"},
                    {label: '集群端口', prop: 'clusterPort', type: "normal"},
                    {label: '集群说明', prop: 'clusterDes', type: "normal"},
                    {
                        label: "操作",
                        prop: "1",
                        type: "operation",
                        width: "160",
                        tag: [
                            {
                                name: "编辑",
                                operType: "put",
                                tagType: "el-button",
                                attributes: {
                                    size: "mini",
                                    type: "text",
                                    icon: "el-icon-edit"
                                },
                                callback: function (row, opts, event) {
                                    _this.openEditCluster(row);
                                }
                            }, {
                                name: "删除",
                                operType: "del",
                                tagType: "el-button",
                                attributes: {
                                    size: "mini",
                                    type: "text",
                                    icon: "el-icon-delete"
                                },
                                isShow: function (row) {
                                    return row.builtIn != 1;
                                },
                                callback: function (row, opts, event) {
                                    _this.deleteCluster(row);
                                }
                            }
                        ]
                    }
                ],
                rules: {
                    clusterName: [{required: true, message: '请输入集群名称', trigger: 'blur'}],
                    clusterPort: [
                        {required: true, message: '请输入集群端口', trigger: 'blur'},
                        {validator: elValidatePort, trigger: 'blur'}
                    ]
                }
            }
        },
        created() {
            this.pageCluster();
        },
        methods: {
            showAndSearch() {
                this.showSearch = !this.showSearch;
            },
            resetSearch() {
                this.searchPage.pageNo = 1;
                // this.searchPage.pageSize = 10;
                this.searchPage.clusterName = null;
                this.searchPage.clusterPort = null;
                this.pageCluster();
            },
            resetClusterFrom() {
                this.$refs.clusterFrom.clearValidate();
                Object.assign(this.$data.clusterFrom, this.$options.data().clusterFrom);
            },
            // 每页显示条数改变
            sizeChange(res) {
                this.searchPage.pageSize = res;
                this.pageCluster()
            },
            // 前往页
            currentChange(res) {
                this.searchPage.pageNo = res;
                this.pageCluster()
            },
            // 上一页
            prevClick(res) {
                this.searchPage.pageNo = res;
                this.pageCluster()
            },
            // 下一页
            nextClick(res) {
                this.searchPage.pageNo = res;
                this.pageCluster()
            },
            updateLoadBalancing: function () {
                let _this = this;
                this.$http.cluster.updateLoadBalancing().then(res => {
                    _this.$message.success("配置已更新");
                });
            },
            // 添加
            openCluster() {
                this.clusterVisible = true;
            },
            // 关闭弹窗
            closeCluster() {
                this.clusterVisible = false;
                this.resetClusterFrom();
            },
            // 编辑
            openEditCluster(row) {
                this.clusterFrom.id = row.id;
                this.clusterFrom.clusterName = row.clusterName;
                this.clusterFrom.clusterPort = row.clusterPort;
                this.clusterFrom.clusterDes = row.clusterDes;
                this.clusterFrom.builtIn = row.builtIn;
                this.editVisible = true;
            },
            closeEditCluster() {
                this.resetClusterFrom();
                this.editVisible = false;
            },
            // 分页查询集群
            pageCluster() {
                this.$http.cluster.pageManage(this.searchPage).then(({code, data, row, msg}) => {
                    if (code === 0) {
                        this.tableData = data;
                        this.total = row
                    } else {
                        this.$message.warning(msg || "查询失败！");
                    }
                })
            },
            refreshCluster() {
                this.closeCluster();
                this.closeEditCluster();
                this.resetSearch();
                this.pageCluster();
            },
            // 添加保存集群
            saveCluster(name) {
                this.$refs[name].validate(valid => {
                    if (valid) {
                        this.$http.cluster.insertManage(this.clusterFrom).then(res => {
                            let code = res.code;
                            if (code === 0) {
                                this.$message.success(res.msg || "添加成功！");
                                this.refreshCluster();
                            } else {
                                this.$message.warning(res.msg || "添加失败！");
                            }
                        })
                    }
                })
            },
            // 编辑保存集群
            editCluster() {
                this.$refs["clusterFrom"].validate(valid => {
                    if (valid) {
                        this.$http.cluster.editManage(this.clusterFrom).then(res => {
                            let code = res.code;
                            if (code === 0) {
                                this.$message.success(res.msg || "添加成功！");
                                this.refreshCluster();
                            } else {
                                this.$message.warning(res.msg || "添加失败！");
                            }
                        })
                    }
                })
            },
            // 删除集群
            deleteCluster(row) {
                let id = row.id;
                if (id) {
                    console.log(id);
                    this.$confirm('确定要删除吗?', '删除确认', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.$http.cluster.deleteManage(id).then(res => {
                            let code = res.code;
                            if (code === 0) {
                                this.$message.success(res.msg || "删除成功！");
                                this.pageCluster();
                            } else {
                                this.$message.warning(res.msg || "删除失败！");
                            }
                        })
                    })
                } else {
                    this.$message.warning("集群id为空！")
                }
            },
        }
    }
</script>
<style>
    .el-tooltip__popper {
        max-width: 450px;
    }
</style>
<style lang="less" scoped>
    /deep/ .el-input .el-input__count .el-input__count-inner {
        padding: 8px 4px;
        background-color: #f0f0f0;
    }

    .el-tooltip__popper {
        max-width: 450px;
    }

    /* 弹出框滚动条 */
    /* 设置滚动条的样式 */
    /**解决了滚动条之间发生错位的现象 */
    ::-webkit-scrollbar {
        width: 10px !important;
        height: 10px !important;
        border-radius: 5px;
    }

    ::-webkit-scrollbar-thumb {
        border-radius: 5px;
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.2);
        /* 滚动条的颜色 */
        background-color: #e4e4e4;
    }

    .user-search .el-form-item {
        margin-bottom: 0px;
        margin-top: 10px;
    }
</style>

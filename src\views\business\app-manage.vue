<template>
    <div class="container">
        <el-card v-show="showSearch" class="box-card" shadow="always" style="margin-bottom: 10px">
            <el-form :inline="true" :show-message="false" label-width="75px" class="user-search comForm" style="text-align: left">
                <el-form-item label="应用标识：">
                    <el-input size="small" v-model="app.queryParams.appCode" clearable></el-input>
                </el-form-item>
                <el-form-item label="应用名称：">
                    <el-input size="small" v-model="app.queryParams.appName" clearable></el-input>
                </el-form-item>
                <el-form-item style="background-color: transparent;">
                    <el-button class="comBtn com_send_btn" size="small" type="primary" icon="el-icon-search" @click="refreshApp">搜索</el-button>
                    <el-button class="comBtn com_reset_btn" size="small" type="primary" icon="el-icon-refresh" @click="resetHandle">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card class="box-card" shadow="always" style="padding-bottom: 10px">
            <el-button class="comBtn com_add_btn" size="mini" type="success" @click="addAppHandle">新增</el-button>
            <Strategy :policyType="1"></Strategy>
            <div style="float: right">
                <el-button-group>
                    <el-button size="mini" icon="el-icon-search" @click="showAndSearch"></el-button>
                    <el-button size="mini" icon="el-icon-refresh-left" @click="refreshApp"></el-button>
                </el-button-group>
            </div>

            <div id="appList" style="padding-top: 10px">
                <createTable
                        :tableData="app.tableData"
                        :tableHeader="app.tableDataHeader"
                        :isPage="app.isPage"
                        :pageAttributes="{total: app.total, currentPage: app.queryParams.pageNum, pageSize: app.queryParams.pageSize}"
                        :current-change="app_currentChange"
                        :sizeChange="app_sizeChange"
                >
                </createTable>
            </div>
        </el-card>

        <!-- 添加应用 -->
        <el-dialog title="应用新增" :visible.sync="addAppOpen" width="750px" append-to-body :close-on-click-modal="false" @close="closeAddAppDia">
            <el-form ref="aform" :model="form.app" :rules="rules" label-width="100px" size="medium">
                <el-form-item label="应用标识：" prop="appCode">
                    <el-input v-model="form.app.appCode" placeholder="2~10个字符，可使用字母、数字" clearable/>
                </el-form-item>
                <el-form-item label="应用名称：" prop="appName">
                    <el-input v-model="form.app.appName" placeholder="2~30个字符，可使用汉字、字母、数字、下划线" clearable/>
                </el-form-item>
                <!--<el-form-item label="IP地址：" prop="appIp">-->
                    <!--<el-input v-model="form.app.appIp" clearable/>-->
                <!--</el-form-item>-->
                <el-form-item label="IPV4地址：" prop="appIp">
                    <el-input v-model="form.app.appIp" clearable/>
                </el-form-item>
                <el-form-item label="IPV6地址：" prop="appIpIpv6">
                    <el-input v-model="form.app.appIpIpv6" clearable/>
                </el-form-item>
            </el-form>
            <!-- 分割线 -->
            <h4 style="margin: 25px 0 5px">选择证书</h4>
            <el-divider content-position="left"></el-divider>
            <!-- 操作方法 -->
            <el-form label-width="80px" :inline="true">
                <el-form-item label="证书 ID:" size="mini" prop="ids">
                    <el-input v-model="cert.queryParams.ids" clearable placeholder="请输入证书ID" style="width: 240px"></el-input>
                </el-form-item>
                <el-form-item label="是否双证:" size="mini">
                    <el-select v-model="cert.queryParams.doubleCert" placeholder="请选择" style="width: 180px">
                        <el-option label="全部" value=""></el-option>
                        <el-option label="否" :value="0"></el-option>
                        <el-option label="是" :value="1"></el-option>
                    </el-select>
                </el-form-item>
                <el-button class="comBtn com_send_btn" size="small" icon="el-icon-search" type="primary" @click="getCertList">搜索</el-button>
            </el-form>
            <createTable
                    :tableData="cert.tableData"
                    :tableHeader="cert.tableDataHeader"
                    :isPage="cert.isPage"
                    :pageAttributes="{total: cert.total, currentPage: cert.queryParams.pageNum, pageSize: cert.queryParams.pageSize}"
                    :current-change="add_cert_currentChange"
                    :sizeChange="add_cert_sizeChange"
                    :selectionChange="add_handleSelectionChange"
            >
            </createTable>
            <div slot="footer" class="dialog-footer">
                <el-button class="comBtn com_reset_btn" size="small" @click="addAppOpen=false">取 消</el-button>
                <el-button class="comBtn com_send_btn" size="small" @click="submitForm">确 定</el-button>
            </div>
        </el-dialog>
        <!--编辑应用-->
        <!--<edit-app-dialog ref="editAppDialog"></edit-app-dialog>-->
        <el-dialog title="修改应用" :visible.sync="editOpen" width="550px" append-to-body :close-on-click-modal="false" @close="closeEditDia">
            <el-form ref="editForm" :model="form.app" :rules="rules" label-width="100px" size="medium">
                <el-form-item label="应用标识：" prop="appCode">
                    <el-input size="small" v-model="form.app.appCode" placeholder="2~10个字符，可使用字母、数字" style="width: 100%" clearable/>
                </el-form-item>
                <el-form-item label="应用名称：" prop="appName">
                    <el-input size="small" v-model="form.app.appName" placeholder="2~30个字符，可使用汉字、字母、数字、下划线" clearable/>
                </el-form-item>
                <el-form-item label="IPV4地址：" prop="appIp">
                    <el-input size="small" v-model="form.app.appIp" clearable/>
                </el-form-item>
                <el-form-item label="IPV6地址：" prop="appIpIpv6">
                    <el-input size="small" v-model="form.app.appIpIpv6" clearable/>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button class="comBtn com_reset_btn" size="small" @click="editOpen=false">取 消</el-button>
                <el-button class="comBtn com_send_btn" size="small" @click="editSubmitForm">确 定</el-button>
            </div>
        </el-dialog>
        <!-- 证书绑定管理  -->
        <el-dialog title="证书绑定管理" :visible.sync="bindAppOpen" width="950px" append-to-body :close-on-click-modal="false" custom-class="loading_dia"
                   @close="Object.assign(cert.noSelect,$options.data().cert.noSelect);Object.assign(cert.selected,$options.data().cert.selected);refreshApp()">
            <el-tabs v-model="activeName" @tab-click="tabClick">
                <!-- 待选证书 -->
                <el-tab-pane :label="tobeselectedTitle" name="noSelect">
                    <el-form label-width="70px" :inline="true">
                        <el-form-item label="证书类型" size="mini">
                            <el-select v-model="cert.noSelect.queryParams.keyDesc" placeholder="请选择" style="width: 140px">
                                <el-option label="全部" value=""></el-option>
                                <el-option label="SM2" value="SM2"></el-option>
                                <el-option label="RSA" value="RSA"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="证书用途" size="mini">
                            <el-select v-model="cert.noSelect.queryParams.usage" style="width: 140px">
                                <el-option label="全部" value=""></el-option>
                                <el-option label="签名" :value="0"></el-option>
                                <el-option label="加密" :value="3"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="证书主题" size="mini">
                            <el-input v-model="cert.noSelect.queryParams.dn" placeholder="请输入DN" style="width: 100%" clearable></el-input>
                        </el-form-item>
                        <el-button class="comBtn com_send_btn" size="small" icon="el-icon-search" type="primary" @click="loadCertParam">搜索</el-button>
                        <el-button class="comBtn com_reset_btn" size="small" type="primary" icon="el-icon-refresh" @click="resetNoSelect">重置</el-button>
                    </el-form>
                    <!-- :selection-change="certTableChange" -->
                    <createTable
                            :tableData="cert.noSelect.tableData"
                            :tableHeader="cert.optionalTableHeader"
                            :isPage="cert.noSelect.isPage"
                            :pageAttributes="{total: cert.noSelect.total, currentPage: cert.noSelect.queryParams.pageNum, pageSize: cert.noSelect.queryParams.pageSize}"
                            :current-change="cert_currentChange"
                            :sizeChange="cert_sizeChange"
                    ></createTable>
                </el-tab-pane>
                <!-- 已选证书 -->
                <el-tab-pane :label="selectedTitle" name="selected">
                    <!-- 操作方法 -->
                    <!--<el-form label-width="80px" :inline="true">-->
                    <!--<el-form-item label="证书ID" size="mini">-->
                    <!--<el-input v-model="cert.selected.queryParams.ids" placeholder="支持区间搜索，~相隔" style="width: 100%" clearable></el-input>-->
                    <!--</el-form-item>-->
                    <!--<el-button class="comBtn com_send_btn" size="small" icon="el-icon-search" @click="loadCertParam">查询</el-button>-->
                    <!--<el-button class="comBtn com_del_btn" size="small" type="danger" icon="el-icon-delete" @click="bindingCertUntie">解绑</el-button>-->
                    <!--</el-form>-->
                    <!-- :selection-change="certTableChange" -->
                    <createTable
                            :tableData="cert.selected.tableData"
                            :tableHeader="cert.selectedTableHeader"
                            :isPage="cert.selected.isPage"
                            :pageAttributes="{total: cert.selected.total, currentPage: cert.selected.queryParams.pageNum, pageSize: cert.selected.queryParams.pageSize}"
                            :current-change="cert_currentChange"
                            :sizeChange="cert_sizeChange"
                    ></createTable>
                </el-tab-pane>
            </el-tabs>
            <div slot="footer" class="dialog-footer">
                <el-button class="comBtn com_send_btn" size="small" @click="bindAppOpen=false;refreshApp()">关闭</el-button>
            </div>
        </el-dialog>

    </div>
</template>

<script>
    import DividingLine from '@/components/DividingLine/index';
    import Strategy from "@/components/strategy";
    import {Message} from "element-ui";
    import {usageFilterFun} from '@/utils/filters';
    // import appUtil from "@/api/appMG";
    // import bussMG from "@/api/bussMG";
    // import appMG from "@/api/appMG";
    // import {getParams} from "@/api/about";
    // import editAppDialog from './components/editAppDialog'

    export default {
        name: "app-manage",
        components: {DividingLine, Strategy},
        data() {
            let _this = this;
            let appNameVal = (rule, value, callback) => {
                if (!value) {
                    return callback(new Error('应用名称不能为空！'));
                }
                let length = value.length;
                if (length < 2 || length > 30) {
                    return callback(new Error('长度在 2 ~ 30 个字符！'));
                }
                let patt = /^[a-zA-Z0-9_\u4e00-\u9fa5]+$/;
                if (!patt.test(value)) {
                    return callback(new Error('可使用汉字、字母、数字、下划线！'));
                }
                let exitsAppName = async function () {
                    await _this.$http.appMG.checkAppName(value, _this.form.app.id).then(res => {
                        if (!res.data) {
                            callback(new Error('应用名称已存在！'));
                        } else {
                            callback();
                        }
                    });
                };
                exitsAppName();
            };
            return {
                showSearch: true,
                bindAppOpen: false,
                bindLoading: null,
                // loading: true,
                editOpen: false,
                addAppOpen: false,
                appMaxCount: 0, // 应用最大数
                tobeselectedTitle: "",
                selectedTitle: "",
                // certUseStatisticsres: {},
                activeName: "selected",
                form: {
                    app: {},
                    certIds: []
                },
                // 应用
                app: {
                    isPage: true,
                    pageAttr: {},
                    total: 0,
                    queryParams: {
                        pageNo: 1,
                        pageSize: 10,
                        appName: "",
                        appCode: "",
                        certType: -1
                    },
                    tableDataHeader: [
                        {label: "序号", type: "index", width: "50"},
                        {label: "应用标识", prop: "appCode", type: "normal"},
                        {label: "应用名称", prop: "appName", type: "normal"},
                        {label: "IPV4地址", prop: "appIp", type: "text_formatter",
                            formatter: function (value) {
                                return value ? value : '-'
                            }
                        },
                        {label: "IPV6地址", prop: "appIpIpv6", type: "text_formatter",
                            formatter: function (value) {
                                return value ? value : '-'
                            }
                        },
                        {
                            label: "是否绑定证书",
                            prop: "isAssociated",
                            type: "text_formatter",
                            formatter: function (value) {
                                return value ? '是' : '否'
                            }
                        },
                        {
                            label: "是否启用",
                            prop: "isEnable",
                            type: "switch",
                            callback: function (row) {
                                _this.$http.appMG.update({id: row.id, isEnable: !row.isEnable}).then(res => {
                                    let flag = row.isEnable;
                                    row.isEnable = !flag;
                                });
                            }
                        }, {
                            label: "操作",
                            width: "200",
                            type: "operation",
                            tag: [{
                                name: "修改",
                                operType: "edit",
                                tagType: "el-button",
                                attributes: {
                                    size: "mini",
                                    type: "text",
                                    icon: "el-icon-edit"
                                },
                                callback: function (row) {
                                    _this.editAppHandle(row);
                                }
                            }, {
                                name: "删除",
                                operType: "del",
                                tagType: "el-button",
                                attributes: {
                                    size: "mini",
                                    type: "text",
                                    icon: "el-icon-delete"
                                },
                                callback: function (row) {
                                    _this.delApp(row.id);
                                }
                            }, {
                                name: "绑定证书",
                                operType: "del",
                                tagType: "el-button",
                                attributes: {
                                    size: "mini",
                                    type: "text",
                                    icon: "el-icon-delete"
                                },
                                callback: function (row) {
                                    _this.bindAppOpenFn(row);
                                }
                            }]
                        }
                    ],
                    tableData: []
                },

                cert: {
                    isPage: false,
                    pageAttr: {total: 0},
                    total: 0,
                    selectIds: "",
                    queryParams: {
                        pageNo: 1,
                        pageSize: 10,
                        ids: '',
                        doubleCert: '',
                        certType: -1
                    },
                    // 添加应用列表 表头
                    tableDataHeader: [
                        {label: "选择", type: "select"},
                        {label: "证书ID", prop: "id", type: "normal", width: "130"},
                        {label: "证书主题", prop: "dn", type: "normal", width: "200"},
                        {label: "序列号", prop: "sn", type: "normal", width: "110"},
                        {
                            label: "是否双证", prop: "doubleCert", type: "text_formatter", width: "90",
                            formatter: function (value) {
                                return value === 1 ? '是' : '否'
                            }
                        },
                        {
                            label: "双证ID", prop: "doubleCertId", type: "text_formatter", width: "70",
                            formatter: function (value) {
                                return value ? value : '-'
                            }
                        },
                        {
                            label: "用途", prop: "usage", type: "text_formatter", width: "62",
                            formatter(value) {
                                return usageFilterFun(value);
                            }
                        },
                        {
                            label: "证书类型",
                            prop: "keyDesc",
                            type: "text_formatter",
                            width: '90',
                            formatter: function (value) {
                                return value === 'SM2' ? 'SM2' : 'RSA'
                            }
                        }
                    ],
                    tableData: [],
                    // 已选
                    selected: {
                        tableData: [],
                        isPage: false,
                        pageAttr: {total: 0},
                        total: 0,
                        queryParams: {
                            pageNo: 1,
                            pageSize: 10,
                            tapActive: 'selected',
                            keyDesc: "", // 类型
                            usage: '', // 用途
                            appId: '',
                        }
                    },
                    // 待选
                    noSelect: {
                        tableData: [],
                        isPage: false,
                        pageAttr: {total: 0},
                        total: 0,
                        queryParams: {
                            pageNo: 1,
                            pageSize: 10,
                            tapActive: "noSelect",
                            keyDesc: "", // 类型
                            usage: '', // 用途
                            appId: '',
                            dn: '',
                        }
                    },
                    // 未绑证书
                    optionalTableHeader: [
                        {label: "序号", type: "index", width: "48"},
                        {label: "证书ID", prop: "id", type: "normal", width: "70"},
                        {label: "证书主题", prop: "dn", type: "normal", width: "210"},
                        {label: "序列号", prop: "sn", type: "normal", width: "165"},
                        {
                            label: "是否双证", prop: "doubleCert", type: "text_formatter", width: "86",
                            formatter: function (value) {
                                return value === 1 ? '是' : '否'
                            }
                        },
                        {
                            label: "双证ID", prop: "doubleCertId", type: "text_formatter", width: "70",
                            formatter: function (value) {
                                return value ? value : '-'
                            }
                        },
                        {
                            label: "证书类型",
                            prop: "keyDesc",
                            type: "text_formatter",
                            width: "90",
                            formatter: function (value) {
                                return value === 'SM2' ? 'SM2' : 'RSA'
                            }
                        },
                        {
                            label: "用途", prop: "usage", type: "text_formatter", width: "90",
                            formatter(value) {
                                return usageFilterFun(value);
                            }
                        },
                        {
                            label: "操作",
                            width: "80",
                            type: "operation",
                            tag: [{
                                name: "添加",
                                operType: "edit",
                                tagType: "el-button",
                                attributes: {
                                    size: "mini",
                                    type: "text",
                                    icon: "el-icon-plus"
                                },
                                callback: function (row) {
                                    _this.cert.selectIds = row.id;
                                    _this.bindingCertAdd();
                                }
                            }]
                        }
                    ],
                    // 已绑证书
                    selectedTableHeader: [
                        {label: "序号", type: "index", width: "48"},
                        {label: "证书ID", prop: "id", type: "normal", width: "70"},
                        {label: "证书主题", prop: "dn", type: "normal", width: "210"},
                        {label: "序列号", prop: "sn", type: "normal", width: "165"},
                        {
                            label: "是否双证", prop: "doubleCert", type: "text_formatter", width: "86",
                            formatter: function (value) {
                                return value === 1 ? '是' : '否'
                            }
                        },
                        {
                            label: "双证ID", prop: "doubleCertId", type: "text_formatter", width: "70",
                            formatter: function (value) {
                                return value ? value : '-'
                            }
                        },
                        {
                            label: "证书类型",
                            prop: "keyDesc",
                            type: "text_formatter",
                            width: "90",
                            formatter: function (value) {
                                return value === 'SM2' ? 'SM2' : 'RSA'
                            }
                        },
                        {
                            label: "用途", prop: "usage", type: "text_formatter", width: "90",
                            formatter(value) {
                                return usageFilterFun(value);
                            }
                        },
                        {
                            label: "操作",
                            width: "80",
                            type: "operation",
                            tag: [{
                                name: "解绑",
                                operType: "edit",
                                tagType: "el-button",
                                attributes: {
                                    size: "mini",
                                    type: "text",
                                    icon: "el-icon-delete"
                                },
                                callback: function (row) {
                                    _this.cert.selectIds = row.id;
                                    _this.bindingCertUntie();
                                }
                            }]
                        }
                    ]
                },
                certRules: {
                    ids: [
                        {
                            validator: function (rule, value, callback) {
                                if (value == "") {
                                    callback();
                                    return;
                                }
                                let ids = value.split("~");
                                let length = ids.length;
                                try {
                                    for (let i = 0; i < length; i++) {
                                        parseInt(ids[i].trim());
                                    }
                                    callback();
                                } catch (ex) {
                                    callback(new Error('只允许输入数字、~字符！'));
                                }
                            }, trigger: 'blur'
                        }
                    ]
                },
                rules: {
                    appName: [
                        {required: true, validator: appNameVal, trigger: 'blur'}
                    ],
                    appCode: [
                        {required: true, message: "应用标识不能为空", trigger: "blur"},
                        {min: 2, max: 10, message: '长度在 2 ~ 10 个字符', trigger: 'blur'},
                        {pattern: /^[a-zA-Z0-9_]+$/, message: '只能使用字母和数字', trigger: 'blur'},
                        {
                            validator: function (rule, value, callback) {
                                let isSucces = true;
                                let exitsAppCode = async function () {
                                    await _this.$http.appMG.checkCode(value, _this.form.app.id).then(res => {
                                        if (!res.data) {
                                            callback(new Error('应用标识已存在！'));
                                        } else {
                                            callback();
                                        }
                                    });
                                };
                                exitsAppCode();
                            }, trigger: 'blur'
                        }
                    ],
                    appIp: [
                        {
                            validator: function (rule, value, callback) {
                                if (value && value != "") {
                                    let reg = /^(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}$/;
                                    if (!reg.test(value)) {
                                        return callback(new Error('请输入正确的IP地址！'));
                                    }
                                }
                                callback();
                            }, trigger: 'blur'
                        }
                    ],
                    appIpIpv6: [
                        {
                            validator: function (rule, value, callback) {
                                if (value && value !== "") {
                                    let reg = /(^(?:(?:(?:[0-9A-Fa-f]{1,4}:){7}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}:[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){5}:([0-9A-Fa-f]{1,4}:)?[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:){0,2}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,3}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,4}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){5}:((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:)?((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,2}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,3}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:):([0-9A-Fa-f]{1,4}:){0,4}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(::([0-9A-Fa-f]{1,4}:){0,5}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|([0-9A-Fa-f]{1,4}::([0-9A-Fa-f]{1,4}:){0,5}[0-9A-Fa-f]{1,4})|(::([0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){1,7}:))$)/;
                                    if (!reg.test(value)) {
                                        return callback(new Error('请输入正确的IP地址！'));
                                    }
                                }
                                callback();
                            }, trigger: 'blur'
                        }
                    ]
                }
            }
        },
        methods: {
            // strategy() {
            //     strategyMG.distribution();
            // },
            showAndSearch() {
                this.showSearch = !this.showSearch;
            },
            // 获取创建应用最大个数
            getLicenseParams() {
                this.$http.about.getParams().then(({code, data, msg}) => {
                    let _data = eval('(' + data.data + ')');
                    this.appMaxCount = _data.appNum;
                })
            },
            // 待选证书 查询条件重置
            resetNoSelect () {
                this.cert.noSelect.queryParams.pageNo = 1;
                this.cert.noSelect.queryParams.pageSize = 10;
                this.cert.noSelect.queryParams.tapActive = "noSelect";
                this.cert.noSelect.queryParams.keyDesc = "";
                this.cert.noSelect.queryParams.usage = "";
                this.cert.noSelect.queryParams.dn = "";
                this.loadCertParam();
            },
            // 解绑
            bindingCertUntie() {
                let appId = this.cert[this.activeName].queryParams.appId;
                let ids = this.cert.selectIds;
                let _this = this;
                if (ids == "") {
                    _this.$alert("请选择证书！", "信息提示", {
                        cancelButtonText: '取消',
                        type: 'warning'
                    });
                    return;
                }
                this.$http.appMG.unbindAppAsCert(appId, ids).then(res => {
                    _this.updateTitle(appId);
                    _this.loadCertParam();
                });
            },
            // 待选证书 添加
            bindingCertAdd() {
                let appId = this.cert[this.activeName].queryParams.appId;
                let ids = this.cert.selectIds;
                this.$http.appMG.bindAppAsCert(appId, ids).then(({code, data}) => {
                    // let length = (ids + "").split(",").length;
                    if (code === 0) {
                        this.$message.success(data);
                        this.updateTitle(appId);
                        this.loadCertParam();
                    } else {
                        this.$message.warning(data)
                    }
                });
            },
            // 更新标题
            updateTitle(appId) {
                let _this = this;
                this.$http.appMG.queryCertUseStatistics(appId).then(res => {
                    let certUseStatisticsres = res.data;
                    _this.tobeselectedTitle = "可绑证书(" + certUseStatisticsres.unUseCount + ")";
                    _this.selectedTitle = "已绑证书(" + certUseStatisticsres.useCount + ")";
                });
            },

            // certTableChange(val) {
            //     let itemStr = "";
            //     let length = val.length;
            //     val.forEach((item, index) => {
            //         itemStr += item.id + ",";
            //     });
            //     if (length > 0) {
            //         itemStr = itemStr.substr(0, itemStr.length - 1);
            //     }
            //     this.cert.selectIds = itemStr;
            // },

            // 绑定证书 操作  获取已选和未选证书数量 调用获取已选证书列表
            bindAppOpenFn(row) {
                let appId = row.id;
                this.cert["selected"].queryParams.appId = appId;
                this.cert["noSelect"].queryParams.appId = appId;
                this.bindAppOpen = true;
                this.bindLoading = true;
                // :target= "document.querySelector('.loading_dia')"
                // console.log(document.querySelector('.loading_dia'));
                this.bindLoading = this.$loading({
                    // elementLoadingText:"拼命加载中",
                    // elementLoadingSpinner:"el-icon-loading",
                    text: '拼命加载中...',
                    spinner: 'el-icon-loading',
                    target: document.querySelector('.loading_dia')
                });
                this.$http.appMG.queryCertUseStatistics(row.id).then(res => {
                    let certUseStatisticsres = res.data;
                    this.tobeselectedTitle = "可绑证书(" + certUseStatisticsres.unUseCount + ")";
                    this.selectedTitle = "已绑证书(" + certUseStatisticsres.useCount + ")";
                    this.bindLoading.close();
                    this.loadCertParam();
                });
            },
            // 绑定证书 tab 切换操作
            tabClick() {
                this.loadCertParam();
            },
            // 获取已选和待选证书列表
            loadCertParam() {
                let _object = this.cert[this.activeName];
                this.cert.selectIds = [];
                // if (this.checkCertParam(_object.queryParams.ids)) {
                _object.tableData = [];
                this.$http.bussMG.queryCertByApp(_object.queryParams).then(res => {
                    let data = res.data;
                    _object.isPage = res.row > 0;
                    _object.total = res.row;
                    _object.tableData = data;
                });
                // }
            },

            // 获取应用列表
            refreshApp () {
                let queryParam = this.app.queryParams;
                this.app.tableData = [];
                this.$http.appMG.appList(queryParam).then(res => {
                    this.app.tableData = res.data;
                    this.app.total = res.row;
                    this.app.isPage = res.row > 0;
                });
            },
            // 重置
            resetHandle() {
                Object.assign(this.app.queryParams, this.$options.data().app.queryParams);
                this.refreshApp()
            },
            // 删除应用
            delApp(id) {
                this.$confirm('确定要删除吗?', '删除确认', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.appMG.del({id: id}).then(res => {
                        this.refreshApp();
                    });
                });
            },
            app_sizeChange: function (param) {
                this.app.queryParams.pageSize = param;
                this.refreshApp();
            },
            app_currentChange: function (param) {
                this.app.queryParams.pageNo = param;
                this.refreshApp();
            },

            checkCertParam(_value) {
                let flag = true;
                let reg = /^[1-9][0-9]*(~[1-9][0-9]*)*$/;
                if (_value != "" && !reg.test(_value)) {
                    flag = false;
                }
                if (!flag) {
                    Message({
                        message: "只允许输入数字、~字符,并且~在两数字之间！",
                        showClose: true,
                        type: 'warning',
                        dangerouslyUseHTMLString: true
                    })
                }
                return flag;
            },

            // ----------------  新增 ---------------------------
            addAppHandle() {
                if (!this.app.pageAttr.total) {
                    this.app.pageAttr.total = 0;
                }
                let sum = this.appMaxCount - this.app.pageAttr.total;
                if (this.appMaxCount != -1 && sum < 1) {
                    this.$message.info("应用个数已达到上限！");
                    return;
                }
                this.addAppOpen = true;
                this.getCertList();
            },
            // 添加应用 获取证书列表
            getCertList() {
                // let _value = this.cert.queryParams.ids;
                // if (this.checkCertParam(_value)) {
                this.cert.tableData = [];
                this.$http.bussMG.certListByParam(this.cert.queryParams).then(res => {
                    this.cert.tableData = res.data;
                    this.cert.isPage = res.row > 0;
                    this.cert.total = res.row;
                });
                // }
            },
            // 添加应用 选择证书
            add_handleSelectionChange(val) {
                this.form.certIds = [];
                val.forEach(item => {
                    this.form.certIds.push(item.id);
                });
            },
            add_cert_sizeChange: function (param) {
                this.cert.queryParams.pageSize = param;
                this.getCertList();
            },
            add_cert_currentChange: function (param) {
                this.cert.queryParams.pageNo = param;
                this.getCertList();
            },
            // 添加应用 提交
            submitForm: function () {
                let _this = this;
                this.$refs["aform"].validate((valid) => {
                    if (valid) {
                        this.$http.appMG.addApp(JSON.stringify(this.form)).then(res => {
                            if (res.code == 400) {
                                this.$message.error(res.msg);
                            }
                            _this.addAppOpen = false;
                            _this.refreshApp();
                        });
                    } else {
                        return false;
                    }
                });
            },
            closeAddAppDia() {
                Object.assign(this.form, this.$options.data().form);
                Object.assign(this.cert.queryParams, this.$options.data().cert.queryParams);
                this.$refs['aform'].resetFields();
            },
            cert_sizeChange: function (param) {
                this.cert[this.activeName].queryParams.pageSize = param;
                this.loadCertParam();
            },
            cert_currentChange: function (param) {
                this.cert[this.activeName].queryParams.pageNo = param;
                this.loadCertParam();
            },
            // handleSelectionChange(val) {
            //     this.form.certIds = [];
            //     val.forEach(item => {
            //         this.form.certIds.push(item.id);
            //     });
            // },

            // 编辑应用
            editAppHandle(row) {
                this.form.app = {...row};
                this.editOpen = true;
                // this.$refs.editAppDialog.editAppInitFun(row)
            },
            // 编辑提交
            editSubmitForm() {
                this.$refs["editForm"].validate((valid) => {
                    if (valid) {
                        this.$http.appMG.update(this.form.app).then(res => {
                            console.log(res);
                            this.editOpen = false;
                            this.refreshApp();
                        });
                    } else {
                        return false;
                    }
                });
            },
            closeEditDia() {
                Object.assign(this.form, this.$options.data().form);
                this.$refs['editForm'].clearValidate();
                this.editOpen = false;
            }
        },
        created() {
            this.refreshApp();
            this.getLicenseParams();
        }
    }
</script>

<style lang="less" scoped>
    /deep/ .el-divider--horizontal {
        margin-top: 0;
    }

    /* 使顶部进行吸顶 */
    .top {
        position: sticky;
        position: -webkit-sticky;
        top: 0;
    }

    /* 表单大小设置 */
    .el-dialog {
        margin: 0 auto !important;
        height: 80%;
        overflow: hidden;
    }

    .el-dialog__body {
        position: absolute;
        left: 0;
        top: 54px;
        bottom: 70px;
        right: 0;
        padding: 0;
        z-index: 1;
        overflow: hidden;
        overflow-y: auto;
    }

    /**表单 确定和取消 按钮的位置 */
    .el-dialog__footer {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
    }

    /* 表单输入框的大小 */
    .el-input {
        width: 50%;
    }

    /* 弹出框滚动条 */
    /* 设置滚动条的样式 */
    /**解决了滚动条之间发生错位的现象 */
    ::-webkit-scrollbar {
        width: 10px !important;
        height: 10px !important;
        border-radius: 5px;
    }

    ::-webkit-scrollbar-thumb {
        border-radius: 5px;
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.2);
        /* 滚动条的颜色 */
        background-color: #e4e4e4;
    }

    .el-input {
        width: 100%;
    }

    .user-search .el-form-item {
        margin-bottom: 0;
        /*margin-top: 10px;*/
    }
</style>

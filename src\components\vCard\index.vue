<template>
    <div class="card-wrapper" :style="styleObj">
        <div class="card-title" v-if="title">{{title}}</div>
        <span class="lt"></span>
        <span class="rt"></span>
        <span class="lb"></span>
        <span class="rb"></span>
        <slot/>
    </div>
</template>

<script>
    export default {
        props: {
            title: {
                type: String
            },
            height: {
                type: String
            },
            width: {
                type: String
            },
            mstyle: {
                type: Object,
                default: () => ({})
            }
        },
        computed: {
            styleObj() {
                return Object.assign({
                    width: this.width,
                    height: this.height
                }, this.mstyle)
            }
        }
    }
</script>

<style lang="less" scoped>
    /*@borderColor: #00ccf6;*/
    @borderColor: #3766b7;
    .card-wrapper {
        /*height: 100%;*/
        position: relative;
        /*padding: 10px;*/
        margin-bottom: 20px;
        border: 1px solid #99b1d9;
        /*border-top 1px solid #3c4b6e*/
        /*border-top: 1px solid transparent;*/
        /*border-right: 1px solid #ddd*/
        /*border-image: -webkit-linear-gradient(left, transparent, #08b2d6, transparent) 1 50 0;*/
        /*border-radius: 5px;*/
        /*font-size 16px*/
        /*color: #fff;*/
        /*background: rgba(8, 178, 214, .1);*/
        background-color: #fcfcfc;

        .card-title {
            padding: 8px;
            padding-left: 30px;
        }

        .lt, .rt, .lb, .rb {
            position: absolute;
            width: 12px;
            height: 12px;
            z-index: 5;
        }

        .lt {
            top: -1px;
            left: -1px;
            border-top: 3px solid @borderColor;
            border-left: 3px solid @borderColor;
        }

        .rt {
            right: -1px;
            top: -1px;
            border-right: 3px solid @borderColor;
            border-top: 3px solid @borderColor;
        }

        .rb {
            right: -1px;
            bottom: -1px;
            border-right: 3px solid @borderColor;
            border-bottom: 3px solid @borderColor;
        }

        .lb {
            left: -1px;
            bottom: -1px;
            border-left: 3px solid @borderColor;
            border-bottom: 3px solid @borderColor;
        }
    }

</style>

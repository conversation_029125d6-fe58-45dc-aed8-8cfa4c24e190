<template>
  <el-card class="box-card" shadow="always">
    <div slot="header" class="clearfix">
      <span>虚拟IP配置</span>
    </div>
    <el-form
      ref="policyForm"
      class="policy_form"
      :model="policyForm"
      label-width="120px"
      size="small"
      :rules="policyRules"
    >
      <el-form-item label="开启高可用：" prop="isEnable">
        <el-switch
          v-model="policyForm.isEnable"
          active-color="#13ce66"
          inactive-color="#C0CCDA"
        ></el-switch>
      </el-form-item>
      <div style="margin: 0px 0 10px 20px">
        <el-button type="primary" size="small">添加新组</el-button>
      </div>
      <el-collapse accordion v-model="activeName">
        <el-collapse-item name="1">
          <template slot="title">
            <span style="padding-left: 10px">第1组（虚拟IP配置）：</span>
            <el-button type="danger" size="small">删除本组</el-button>
          </template>
          <div style="display: flex; margin-top: 20px">
            <el-form-item label="虚拟IP：" prop="vip">
              <el-input
                size="small"
                v-model.trim="policyForm.vip"
                placeholder="请输入虚拟IP"
                style="width: 220px"
              ></el-input>
            </el-form-item>
            <el-form-item label="组号：" prop="vrId">
              <el-input-number
                v-model="policyForm.vrId"
                :min="1"
                :max="255"
                size="small"
              ></el-input-number>
            </el-form-item>
          </div>
          <el-table
            size="small"
            class="comTab"
            :data="policyForm.list"
            highlight-current-row
            style="width: 94%; margin-left: 3%;"
          >
            <el-table-column
              align="center"
              type="index"
              label="序号"
              width="60"
            ></el-table-column>
            <el-table-column prop="ip" label="主机IP" align="center">
              <template slot-scope="{ $index, row }">
                <el-form-item
                  label-width="0"
                  :prop="'list.' + $index + '.ip'"
                  class="table_input"
                  :rules="policyRules.ip"
                  style="margin-bottom: 0; line-height: 0"
                >
                  <el-input v-model="row.ip"></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="vipEth" label="虚拟接口" align="center">
              <template slot-scope="{ $index, row }">
                <el-form-item
                  label-width="0"
                  :prop="'list.' + $index + '.vipEth'"
                  class="table_input"
                  :rules="policyRules.vipEth"
                  style="margin-bottom: 0; line-height: 0"
                >
                  <el-input
                    size="small"
                    v-model="row.vipEth"
                    :maxlength="50"
                  ></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="heartbeatEth"
              label="心跳接口"
              align="center"
            >
              <template slot-scope="{ $index, row }">
                <el-form-item
                  label-width="0"
                  :prop="'list.' + $index + '.heartbeatEth'"
                  class="table_input"
                  :rules="policyRules.heartbeatEth"
                  style="margin-bottom: 0; line-height: 0"
                >
                  <el-input
                    size="small"
                    v-model="row.heartbeatEth"
                    :maxlength="50"
                  ></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="vipState" label="虚拟IP状态" align="center">
              <template slot-scope="{ row }">
                <span
                  class="status"
                  v-show="!row.vipState"
                  style="background-color: #909399"
                ></span>
                <span
                  class="status"
                  v-show="row.vipState"
                  style="background-color: #67c23a"
                ></span>
              </template>
            </el-table-column>
            <el-table-column
              prop="srvState"
              label="服务状态(keepalived)"
              align="center"
            >
              <template slot-scope="{ row }">
                <span
                  class="status"
                  v-show="!row.srvState"
                  style="background-color: #909399"
                ></span>
                <span
                  class="status"
                  v-show="row.srvState"
                  style="background-color: #67c23a"
                ></span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="操作">
            <template slot-scope="{ $index, row }">
                <el-button type="text" icon="el-icon-plus" @click="addItem" v-if="$index === 0">添加</el-button>
                <el-button
                  type="text"
                  icon="el-icon-delete"
                  @click="delHandle($index)"
                  v-if="$index !== 0"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-collapse-item>
      </el-collapse>
      <div style="text-align: right; margin-top: 10px">
        <el-button
          class="comBtn com_send_btn"
          size="small"
          @click="saveConfigHandle"
          >保存配置</el-button
        >
      </div>
    </el-form>
  </el-card>
</template>

<script>
export default {
  name: "virtualIPConfig",
  data() {
    const validatorIp = (rule, value, callback) => {
      if (value !== "") {
        let ip =
          /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
        if (!ip.test(value)) {
          callback(new Error("请输入正确的IP!"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    return {
      policyForm: {
        isEnable: false,
        vrId: "",
        vip: "",
        ip: "",
        list: [{}],
      },
      activeName: "1",
      listData: [],
      timer: null,
      policyRules: {
        vip: [
          { required: true, message: "请输入虚拟IP", trigger: "blur" },
          { validator: validatorIp, trigger: "blur" },
        ],
        vipEth: [
          { required: true, message: "请输入虚拟接口", trigger: "blur" },
        ],
        heartbeatEth: [
          { required: true, message: "请输入心跳接口", trigger: "blur" },
        ],
        ip: [
          { required: true, message: "请输入主机IP", trigger: "blur" },
          { validator: validatorIp, trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    // 获取高可用状态
    getStatusFun() {
      this.$http.vipApi.getState().then(({ code, data, msg }) => {
        // console.log(code, data, msg);
        let _data = JSON.parse(data);
        if (typeof _data !== "object" && _data === null) return;
        this.policyForm.isEnable = _data.isEnable;
        this.policyForm.vrId = _data.vrId;
        this.policyForm.vip = _data.vip;
        this.policyForm.list = _data.list || [{ ip: "", vipEth: "", heartbeatEth: "", vipState: false, srvState: false }];
      });
    },
    addItem() {
      this.policyForm.list.push({
        ip: "",
        vipEth: "",
        heartbeatEth: "",
        vipState: false,
        srvState: false,
      });
    },
    delHandle(index) {
      this.$confirm("删除后保存配置生效，是否确认删除?", "提示", {
        confirmButtonText: "删 除",
        cancelButtonText: "取 消",
        type: "warning",
      })
        .then(() => {
          if (this.policyForm.list.length === 1){
            this.$message.warning("至少保留一台主机!");
            return;
          };
          this.policyForm.list.splice(index, 1);
        })
        .catch(() => {});
    },
    // 保存配置
    saveConfigHandle() {
      this.$refs["policyForm"].validate((valid) => {
        if (!valid) return;
        if (this.policyForm.list.length < 2 && this.policyForm.isEnable)
          return this.$message.warning("至少添加两台主机!");
        this.$http.vipApi
          .saveConfig(JSON.stringify(this.policyForm))
          .then((res) => {
            if (res.code === 0) this.$message.success(res.msg || "配置成功!");
            this.getStatusFun();
          })
          .catch((err) => {
            console.log(err);
          });
      });
    },
  },
  mounted() {
    this.getStatusFun();
    this.timer = setTimeout(this.getStatusFun(), 6 * 1000);
  },
  destroyed() {
    if (this.timer) {
      clearTimeout(this.timer);
    }
  },
};
</script>

<style lang="less" scoped>
.table_input {
  /deep/ .el-form-item__content {
    line-height: normal !important;
    border-spacing: 0 !important;
  }
}

.custom_table {
  width: 800px;
  list-style: none;
  border: 1px solid #dddddd;
  border-right: none;
  border-bottom: none;
  padding: 0;

  .header_con {
    background-color: #efefef;

    span {
      font-weight: 600;
    }
  }

  li {
    display: flex;

    .item {
      width: calc((100% - 7px) / 6);
      padding: 0 2px;
      height: 40px;
      line-height: 40px;
      display: inline-block;
      text-align: center;
      border-bottom: 1px solid #dddddd;
      border-right: 1px solid #dddddd;
      vertical-align: middle;

      .status {
        display: inline-block;
        width: 20px;
        height: 20px;
        border-radius: 10px;
        vertical-align: middle;
      }
    }
  }
}

.status {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 10px;
  vertical-align: middle;
}
</style>

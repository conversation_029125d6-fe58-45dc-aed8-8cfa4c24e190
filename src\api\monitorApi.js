import { req } from "./axiosFun";

const query = (data) => {
  return req("post", "/dataSet/query", data);
};
const chartList = (data) => {
  return req("post", "/dataSet/chartList", data);
};
const dictType = (data) => {
  return req("get", "/dict/type/" + data);
};
const queryList = (data) => {
  return req("post", "/dataSet/queryList", data);
};
//首页网口状态接口
const getNetStatus = (data) => {
  return req("get", "/net/getNetStatus", data);
};

export default {
  query,
  chartList,
  dictType,
  queryList,
  getNetStatus
}

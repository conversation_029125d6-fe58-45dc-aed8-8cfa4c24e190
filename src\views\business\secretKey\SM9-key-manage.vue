<template>
    <el-tabs type="border-card" v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="主密钥管理" name="first">
            <master-key-manage ref="masterKeyManage"/>
        </el-tab-pane>
        <el-tab-pane label="用户密钥管理" name="second">
            <user-key-manage ref="userKeyManage"/>
        </el-tab-pane>
    </el-tabs>
</template>

<script>
    //非对称密钥管理
    import masterKeyManage from "@/views/business/secretKey/masterKeyManage";
    //对称密钥
    import userKeyManage from "@/views/business/secretKey/userKeyManage";

    export default {
        name: "keys-manage",
        components: {userKeyManage, masterKeyManage},
        data() {
            return {
                activeName: "first",
            }
        },
        methods: {
            handleClick(tab) {
                if (tab.name === "first") {
                    this.$refs.masterKeyManage.initMasterKeyManage();
                } else if (tab.name === "second") {
                    this.$refs.userKeyManage.initUserKeyManage();
                }
            }
        }
    }
</script>

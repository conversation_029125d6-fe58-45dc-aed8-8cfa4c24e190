<template>
  <div>
    <el-card class="box-card" shadow="always" style="padding-bottom: 10px">
      <el-form label-width="100px">
        <el-row>
          <el-col :span="14" style="text-align: left">
            <el-button class="comBtn com_reset_btn" size="mini" type="primary" @click="allDNS">刷新</el-button>
            <el-button class="comBtn com_send_btn" size="mini" type="success" @click="addView">新增</el-button>
          </el-col>
        </el-row>
      </el-form>

      <br>

      <create-table
        :tableData="tableData"
        :tableHeader="tableDataHeader"
      ></create-table>

      <!-- 新增 -->
      <el-dialog title="DNS管理" :visible.sync="dnsVisible" width="25%" @click='closeDialog'>
        <el-form label-width="100px" ref="dnsForm" :model="dnsForm" :rules="rules">
          <el-form-item label="DNS地址：" prop="dns">
            <el-input size="small" v-model="dnsForm.dns" placeholder="请输入DNS地址"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button class="comBtn com_reset_btn" size="small" @click='closeDialog'>取消</el-button>
          <el-button class="comBtn com_send_btn" size="small" type="primary" @click="saveDNS">保存</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
  // import systemMG from "../../api/systemMG";

  export default {
    name: "dns-configure",
    data() {
      let _this = this;
      var validatorIp = (rule, value, callback) => {
        if (value !== "") {
          var ip = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
          if (!ip.test(value)) {
            callback(new Error('请输入正确的IP!'))
          } else {
            callback()
          }
        } else {
          callback();
        }
      };
      return {
        tableDataHeader: [
          {label: '序号', type: "index", width: '100'},
          {label: 'DNS地址', prop: 'dns', type: "normal"},
          {
            label: "操作",
            prop: "1",
            type: "operation",
            width: "300",
            tag: [
              {
                name: "编辑",
                operType: "put",
                tagType: "el-button",
                attributes: {
                  size: "mini",
                  type: "text",
                  icon: "el-icon-edit"
                },
                callback: function (row, opts, event) {
                  _this.editView(row);
                }
              },
              {
                name: "删除",
                operType: "del",
                tagType: "el-button",
                attributes: {
                  size: "mini",
                  type: "text",
                  icon: "el-icon-edit"
                },
                callback: function (row, opts, event) {
                  _this.deleteDNS(row);
                }
              }
            ]
          }
        ],
        tableData: [],
        dnsVisible: false,
        dnsForm: {
          id: null,
          dns: null,
        },
        rules: {
          dns: [
            {required: true, message: "请输入正确的Ip地址", trigger: "blur"},
            {validator: validatorIp, trigger: 'blur'}
          ]
        }
      }
    },
    methods: {
      addView() {
        this.dnsVisible = true;
      },
      editView(row) {
        this.dnsForm.id = row.id;
        this.dnsForm.dns = row.dns;

        this.dnsVisible = true;
      },
      closeDialog() {
        this.dnsVisible = false;
        this.$refs["dnsForm"].clearValidate();
        this.dnsForm.id = null;
        this.dnsForm.dns = null;
      },
      saveDNS() {
        this.$refs['dnsForm'].validate((valid) => {
          if (valid) {
            const id = this.dnsForm.id;
            if (id == null || id == '') {
              this.$http.systemMG.addDNS(this.dnsForm).then((res) => {
                const code = res.code;
                if (code == 0) {
                  this.$message.success("添加成功！");
                  this.closeDialog();
                  this.allDNS();
                } else {
                  // this.$message.error(res.msg);
                }
              })
            } else {
              this.$http.systemMG.updateDNS(this.dnsForm).then((res) => {
                const code = res.code;
                if (code === 0) {
                  this.$message.success("修改成功！");
                  this.closeDialog();
                  this.allDNS();
                } else {
                  // this.$message.error(res.msg)
                }
              })
            }
          }
        })
      },
      allDNS() {
        this.$http.systemMG.allDNS().then((res) => {
          const code = res.code;
          if (code === 0) {
            this.tableData = res.data;
          }
        })
      },
      deleteDNS(row) {
        this.$confirm('确定要删除吗?', '删除确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const id = row.id;
          this.$http.systemMG.deleteDNS(id).then((res) => {
            const code = res.code;
            if (code === 0) {
              this.$message.success("删除成功！")
              this.allDNS();
            }
          })
        })
      }
    },
    created() {
      this.allDNS();
    }
  }
</script>

<style scoped>

.admin-button {
  margin-top: 20px;
  margin-bottom: 20px;
  float: left;
}

</style>

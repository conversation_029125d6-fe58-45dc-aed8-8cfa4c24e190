<template>
    <div>
        <el-card v-show="showSearch" class="box-card" shadow="always" style="padding-bottom: 10px">
            <div>
                <el-form :inline="true" :model="pageParam" class="user-search comForm">
                    <el-form-item label="告警类型" size="small">
                        <el-select size="small" v-model="pageParam.alarmType" placeholder="请选择" clearable>
                            <el-option v-for="item in typeOptions" :label="item.label" :value="item.value" :key="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="告警级别" size="small">
                        <el-select size="small" v-model="pageParam.alarmLevel" placeholder="请选择" clearable>
                            <el-option v-for="item in levelOptions" :label="item.label" :value="item.value" :key="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="告警时间" size="small">
                        <el-date-picker
                                v-model="pageParam.times" clearable
                                type="datetimerange" size="small"
                                format="yyyy - MM - dd HH:mm:ss"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                        </el-date-picker>
                    </el-form-item>

                    <el-form-item style="background-color: transparent;margin-bottom: 0!important;" size="small">
                        <el-button class="comBtn com_send_btn" size="small" icon="el-icon-search" @click="pageAlarmRecord">搜索</el-button>
                        <el-button class="comBtn com_reset_btn" size="small" icon="el-icon-refresh" @click="resetPageParams">重置</el-button>
                    </el-form-item>
                </el-form>
            </div>
        </el-card>
        <el-card class="box-card" shadow="always" style="margin-top: 10px">
            <!--<div style="text-align: right; float: right">-->
            <el-button-group style="float: right;margin-bottom: 10px">
                <el-button size="mini" icon="el-icon-search" @click="showAndSearch"></el-button>
                <el-button size="mini" icon="el-icon-refresh-left" @click="pageAlarmRecord"></el-button>
            </el-button-group>
            <!--</div>-->
            <create-table
                    :tableData="tableData"
                    :tableHeader="tableDataHeader"
                    :isPage="true"
                    :pageAttributes="{total: total, currentPage: pageParam.pageNo}"
                    :size-change="sizeChange"
                    :current-change="currentChange"
                    :prev-click="prevClick"
                    :next-click="nextClick"
            ></create-table>
        </el-card>
    </div>
</template>

<script>

    import alarmMG from "@/api/alarmMG";

    export default {
        name: "alarm-record",
        data() {
            let _this = this;
            return {
                showSearch: true,
                tableData: [],
                total: 0,
                pageParam: {
                    pageNo: 1,
                    pageSize: 10,
                    alarmType: "",
                    alarmLevel: "",
                    startTime: "",
                    endTime: "",
                    times: []
                },
                typeOptions: [],
                levelOptions: [
                    {value: '一般告警', label: '一般告警'},
                    // {value: '恢复告警', label: '恢复告警'},
                    {value: '严重告警', label: '严重告警'},
                ],
                tableDataHeader: [
                    {label: '序号', type: "index", width: '100'},
                    {label: '告警对象', prop: 'alarmDevice', type: 'normal'},
                    {label: '告警内容', prop: 'alarmText', type: "normal"},
                    {label: '告警级别', prop: 'alarmLevel', type: "normal"},
                    {label: '告警次数', prop: 'alarmCount', type: "normal"},
                    {
                        label: '告警类型',
                        prop: 'alarmType',
                        type: "text_formatter",
                        formatter: function (value, row) {
                            if ('CPU' == value) return 'CPU';
                            else if ('MEMORY' == value) return '内存';
                            else if ('DISK' == value) return '磁盘';
                            else if ('KEEPALIVED' == value) return 'Keepalived';
                            else if ('APP' == value) return '应用上限';
                            else if ('SECRET_KEY' == value) return '非对称密钥上限';
                            else if ('SYMMETRY_KEY' == value) return '对称密钥上限';
                            else if ('MGR_SERVER' == value) return '签名服务';
                            else if ('CLUSTER_NODE' == value) return '集群节点';
                            else if ('CERT_EXPIRE' == value) return '证书过期';
                            else if ('DATA_SYNC' == value) return '数据同步';
                            else if ('SIGN' == value) return '签名';
                            else if ('VERIFY' == value) return '验签';
                            else if ('ENVELOPE_ENCODE' == value) return '数字信封封装';
                            else if ('ENVELOPE_DECODE' == value) return '数字信封解封';
                            else if ('SYMMETRY' == value) return '对称加解密';
                            else if ('ASYM_ENCODE' == value) return '非对称加密';
                            else if ('ASYM_DECODE' == value) return '非对称解密';
                            else if ('HASH' == value) return '摘要';
                            else if ('MAC' == value) return 'MAC';
                        }
                    },
                    {label: '通知人', prop: 'alarmNotifier', type: "normal"},
                    {
                        label: '是否确认',
                        prop: 'handleResult',
                        type: "text_formatter",
                        formatter: function (value, row) {
                          if (1 == value) return '已确认';
                          else  return '未确认';
                        }
                    },
                    {label: '告警时间', prop: 'createTime', type: "normal"},
                    {
                        label: "操作",
                        prop: "1",
                        type: "operation",
                        width: "160",
                        tag: [
                            {
                                name: "确认",
                                operType: "put",
                                tagType: "el-button",
                                attributes: {
                                    size: "mini",
                                    type: "text",
                                    icon: "el-icon-delete"
                                },
                                isShow: function (row) {
                                    return row.handleResult != 1;
                                },
                                callback: function (row) {
                                    _this.confirmAlarmRecord(row);
                                }
                            }
                        ]
                    }
                ]
            }
        },
        methods: {
            showAndSearch() {
                this.showSearch = !this.showSearch;
            },
            // 每页显示条数改变
            sizeChange(res) {
                this.pageParam.pageSize = res;
                this.pageAlarmRecord()
            },
            // 前往页
            currentChange(res) {
                this.pageParam.pageNo = res;
                this.pageAlarmRecord()
            },
            // 上一页
            prevClick(res) {
                this.pageParam.pageNo = res;
                this.pageAlarmRecord()
            },
            // 下一页
            nextClick(res) {
                this.pageParam.pageNo = res;
                this.pageAlarmRecord()
            },
            resetPageParams() {
                this.pageNo = 1;
                this.pageSize = 10;
                this.pageParam.alarmType = null;
                this.pageParam.alarmLevel = null;
                this.pageParam.startTime = null;
                this.pageParam.endTime = null;
                this.pageParam.times = [];
                this.pageAlarmRecord();
            },
            confirmAlarmRecord(row) {
                let opt = {
                    id: row.id,
                }
                alarmMG.confirmAlarmRecord(opt).then(res => {
                    let code = res.code;
                    if (code == 0) {
                        this.$message.success("确认成功！");
                        this.resetPageParams();
                    } else {
                        this.$message.warning(res.msg || "确认告警记录异常！");
                    }
                })
            },
            pageAlarmRecord() {
                let times = this.pageParam.times;
                if (times != null && times.length > 1) {
                    this.pageParam.startTime = times[0];
                    this.pageParam.endTime = times[1];
                } else {
                    this.pageParam.startTime = '';
                    this.pageParam.endTime = '';
                }
                alarmMG.pageAlarmRecord(this.pageParam).then(res => {
                    let code = res.code;
                    if (code == 0) {
                        this.tableData = res.data;
                        this.total = res.row;
                    } else {
                        this.$message.warning(res.msg || "获取告警记录异常！");
                    }
                })
            },
            getAlarmType() {
                alarmMG.getAlarmType().then(res => {
                    let code = res.code;
                    if (code == 0) {
                        console.log(res.data);
                        this.typeOptions = res.data;
                    } else {
                        this.typeOptions = [
                            {value: 'CPU', label: 'CPU'},
                            {value: 'MEMORY', label: '内存'},
                            {value: 'DISK', label: '磁盘'},
                            {value: 'KEEPALIVED', label: 'Keepalived'},
                            {value: 'APP', label: '应用上限'},
                            {value: 'SECRET_KEY', label: '密钥上限'},
                            {value: 'MGR_SERVER', label: '签名服务'},
                            {value: 'CLUSTER_NODE', label: '集群节点'},
                            {value: 'CERT_EXPIRE', label: '证书过期'},
                            {value: 'DATA_SYNC', label: '数据同步'},
                        ];
                    }
                })
            }
        }
    }
</script>

<style lang="less" scoped>
    /deep/ .el-form-item {
        margin-bottom: 0 !important;
    }
</style>

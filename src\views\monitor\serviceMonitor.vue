<template>
  <div>
    <el-card style="margin-top: 10px">
      <el-table
        :data="listData"
        highlight-current-row
        class="comTab"
        style="margin-top: 10px"
      >
        <el-table-column align="center" prop="index" label="序号" width="50">
          <template slot-scope="scope">
            <span>{{ scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          prop="server_name"
          label="服务名称"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          align="center"
          prop="status"
          label="服务状态"
          width="150px"
        >
          <template slot-scope="scope">
            <span class="flexRow" slot="reference" v-if="scope.row.status == 1">
              <el-button class="buttonCircle" size="mini" type="success" circle></el-button>
              <span style="margin-left: 10px">运行中</span>
            </span>
            <span class="flexRow" slot="reference" v-if="scope.row.status == 0">
              <el-button class="buttonCircle" size="mini" type="danger" circle></el-button>
              <span style="margin-left: 10px">未运行</span>
            </span>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          prop="formatTime"
          label="检测时间"
          show-overflow-tooltip
        ></el-table-column>
      </el-table>
      <!-- 分页组件 -->
      <Pagination
        v-bind:child-msg="pageParams"
        @callFather="callFather"
      ></Pagination>
    </el-card>
  </div>
</template>

<script>
import Pagination from "@/components/my-pagination";

export default {
  data() {
    return {
      formInline: {
        pageNum: 1,
        pageSize: 10,
        setCode: "N_027",
        // server_name: '',
        // status: 0,
        // time: '',
      },
      listData: [],
      // 分页参数
      pageParams: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
    };
  },
  // 注册组件
  components: { Pagination },
  created() {
    this.getServiceRecordFun(this.formInline);
  },
  methods: {
    getServiceRecordFun(parameter) {
      this.$http.monitorApi.queryList(parameter).then(({ code, msg, data, row }) => {
        if (!code) {
          this.listData = data.detail;
          // 分页赋值
          this.pageParams.currentPage = this.formInline.pageNum;
          this.pageParams.pageSize = this.formInline.pageSize;
          this.pageParams.total = data.total;
        } else {
          this.$message.warning(msg);
        }
      });
    },
    // 分页插件事件
    callFather(param) {
      this.formInline.page = param.currentPage;
      this.formInline.size = param.pageSize;
      this.getServiceRecordFun(this.formInline);
    },
  },
};
</script>

<style scoped>
.flexRow {
  display: flex;
  flex-direction: row;
  justify-content: center;
}
.buttonCircle {
  height: 12px;
  width: 12px;
  align-self: center;
}
</style>

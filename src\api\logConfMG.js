import {goReq, req, reqGet} from './axiosFun';
import API from "@/api/apiuri";
// import {reqGet} from "@/api/axiosFun";

let logConfApi = API.logConfApi;

// 日志签名验签
export const logSign = () => { return reqGet("get", "/logServerManage/config") };
export const setLogSign = (params) => { return req("post", "/logServerManage/config", params) };


export default {

  //查询日志配置
  logConfigure(params) {
    // return goReq("get",  "http://10.0.80.151:8080/logConf/sys/category?params=" + params);
    return goReq("get", logConfApi.logConfigure + "?params=" + params);
  },
  //修改日志配置
  updateConfigure(params) {
    return req("post", logConfApi.setLogConfigure, params);
  },
  updateCleanupConfigure(params) {
    return req("post", logConfApi.cleanupConfigure, params);
  },
  cleanupConfigure() {
    return reqGet("get", logConfApi.cleanupConfigure);
  },

  logSign,
  setLogSign,
}

<template>
    <el-card>
        <div slot="header" class="clearfix">
            <span>服务管理</span>
        </div>
        <el-switch v-model="SNMPVal" @change="changeHandle('snmp', $event)" :disabled="SNMPState !== 'up' && SNMPState !== 'down'" inactive-text="SNMP V3 服务：" active-color="#13ce66"></el-switch>
        <span class="state_tip" v-if="SNMPState !== 'up' && SNMPState !== 'down'">{{ SNMPState }}</span>
        <br>
        <el-switch v-model="SSHVal" @change="changeHandle('ssh', $event)" inactive-text="SSH 服务：" active-color="#13ce66" style="margin: 15px 0"></el-switch>
        <br>
        <el-switch v-model="FTPVal" @change="changeHandle('ftp', $event)" inactive-text="FTP 服务：" :disabled="FTPState !== 'up' && FTPState !== 'down'" active-color="#13ce66" style="margin-bottom: 15px"></el-switch>
        <span class="state_tip" v-if="FTPState !== 'up' && FTPState !== 'down'">{{ FTPState }}</span>
        <br v-if="isShow">
        <el-switch v-if="isShow" v-model="secVal" @change="changeHandle('sec', $event)" inactive-text="毁钥 功能：" active-color="#13ce66" style="margin-bottom: 15px"></el-switch>
        <br>
        <el-switch v-model="pingVal" @change="changeHandle('ping', $event)" inactive-text="PING 服务：" active-color="#13ce66"></el-switch>
        <slot></slot>
    </el-card>
</template>

<script>
    // import serviceMG from '@/api/serviceMG'
    export default {
        data() {
            return {
                SNMPVal: false,
                SSHVal: false,
                isShow: false,
                FTPVal: false,
                pingVal: false,
                secVal: false,
                SNMPState: '',
                FTPState: '',
            }
        },
        methods: {
            // 获取应用服务状态
            getAppServiceStatus() {
                this.$http.serviceMG.queryStatus().then(({ code, data, msg }) => {
                    if (!code) {
                        data.map(item => {
                            if (item.service === 'ssh') this.SSHVal = item.status === 'up';
                            if (item.service === 'ping') this.pingVal = item.status === 'up';
                            if (item.service === 'snmp') {
                                this.SNMPVal = item.status === 'up';
                                this.SNMPState = item.status
                            }
                            if (item.service === 'ftp') {
                                this.FTPVal = item.status === 'up';
                                this.FTPState = item.status
                            }
                            if (item.service === 'sec'){
                                this.isShow = true;
                                this.secVal = item.status === 'up'
                            };
                        })
                    } else {
                        this.$message.warning(msg)
                    }
                })
            },
            changeHandle(value, e) {
                let opt = {
                    service: value,
                    status: e ? 'up' : 'down'
                };
                if(value === 'sec' && e  === true){
                    this.$confirm('开启物理毁钥功能，一旦触发物理毁钥，会造成设备数据丢失，且不可恢复，请慎重操作！修改后需断电启动后生效。', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.$http.serviceMG.setStatus(JSON.stringify(opt)).then(res => {
                            if (res.code == 0) {
                                this.getAppServiceStatus();
                                this.$message.success("修改成功，需断电重启后生效。")
                            }
                        })
                    }).catch(() => {
                        this.secVal = false;
                    })
                }else{
                    this.$http.serviceMG.setStatus(JSON.stringify(opt)).then(res => {
                        if (res.code == 0) {
                            this.getAppServiceStatus();
                            if(value ==='sec'){
                                this.$message.success("修改成功，需断电重启后生效。")
                            }else{
                            this.$message.success(res.msg)
                            }
                        }
                    })
                }
            }
        },
        mounted() {
            this.getAppServiceStatus()
        }
    }
</script>

<style lang="less" scoped>
    .state_tip {
        font-size: 14px;
        color: #F56C6C;
        vertical-align: top;
    }
    /deep/ .el-switch__label {
        width: 140px;
        text-align: right;
        span {
            font-size: 16px;
        }
    }

    /deep/ .el-switch__label.is-active {
        color: #303133;
    }
</style>

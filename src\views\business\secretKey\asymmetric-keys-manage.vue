<template>
  <div class="container" v-if="useExtKeys==2">
    <ext-keys-manage ref="extKeys"></ext-keys-manage>
  </div>
  <div class="container"  v-else-if="useExtKeys==1">
    <internal-keys-manage ref="internalKeys"></internal-keys-manage>
  </div>
  <div class="container"  v-else>
  </div>
</template>

<script>

//外部密钥管理
import ExtKeysManage from "./external-keys-manage";
//内部密钥管理
import InternalKeysManage from "./internal-keys-manage";
// import keyMG from "@/api/keyMG";

export default {
  name: "asymmetric-keys-manage",
  components: {InternalKeysManage, ExtKeysManage},
  data() {
    // let useExtKey = false;
    const loading = this.$loading({
      lock: true,
      text: '加载中，请稍等...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    let _this = this;
    _this.$http.keyMG.getKeyManageType(function () {
      loading.close();
    }).then(res => {
      _this.useExtKeys=res.data;
    },err=> {
      loading.close();
    });
    return {
      useExtKeys:-1
    }
  },
  methods: {
    renewData() {
      const useExtKeys = this.useExtKeys;
      if (useExtKeys == 2) {
        this.$refs.extKeys.refreshQueryParam();
        this.$refs.extKeys.refreshApp();
      } else if (useExtKeys == 1) {
        this.$refs.internalKeys.refreshApp();
      }
    }
  },
  mounted() {
  }
}
</script>

<style scoped>

</style>

<template>
    <div>
        <el-row :gutter="10">
            <el-col :sm="15" :lg="18" :xl="19">
                <el-row :gutter="10" class="first_row">
                    <el-col :sm="12">
                        <el-card>
                            <div slot="header">磁盘使用情况</div>
                            <disk :disk="disk"></disk>
                        </el-card>
                    </el-col>
                    <el-col :sm="12">
                        <el-card>
                            <div slot="header">内存使用率</div>
                            <memory :cacheUsed="cacheUsed"></memory>
                        </el-card>
                    </el-col>
                </el-row>
                <!--第二行-->


              <el-row :gutter="10" class="first_row">
                <el-col :sm="12">
                  <el-card>
                    <div slot="header">CPU使用率</div>
                    <cpu :cpu="cpu"></cpu>
                    <span style="margin-left: 43%">CPU使用率 {{ cpushi }}%</span>
                  </el-card>
                </el-col>
                <el-col :sm="12">
                    <el-card>
                      <div slot="header">网络流量使用情况</div>
                      <flow :flow="flow"></flow>
                      <span style="margin-left: 43%"></span>
                    </el-card>
                </el-col>
              </el-row>
              <!--
                <el-row :gutter="10" class="second_row">
                    <el-col :span="24">
                        <el-card>
                            <div slot="header">CPU使用率</div>
                            <cpu :cpu="cpu"></cpu>
                            <span style="margin-left: 43%">CPU使用率 {{ cpushi }}%</span>
                        </el-card>
                    </el-col>
                </el-row>
                -->
            </el-col>
            <el-col :sm="9" :lg="6" :xl="5">
                <el-card style="margin-bottom: 11px">
                    <div slot="header">系统使用情况</div>
                    <usage></usage>
                </el-card>
                <el-card style="margin-bottom: 11px">
                    <div slot="header">系统基本配置</div>
                    <basic></basic>
                </el-card>
                <el-card>
                    <div slot="header">系统基本信息</div>
                    <essential></essential>
                </el-card>
            </el-col>
        </el-row>
        <el-dialog title="修改密码" :visible.sync="rePasswordVisible" width="35%" :close-on-click-modal="false">
            <el-form label-width="160px" ref="editPassword" :model="editPassword" :rules="rules">
                <el-form-item label="原密码：" prop="oldPwd">
                    <el-input size="small" type="password" v-model="editPassword.oldPwd"/>
                </el-form-item>
                <el-form-item label="新密码：" prop="password">
                    <el-input size="small" type="password" v-model="editPassword.password"/>
                </el-form-item>
                <el-form-item label="确认密码：" prop="rePassword">
                    <el-input size="small" type="password" v-model="editPassword.rePassword"/>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button size="small" type="primary" class="title" @click="reAccountPassword">修改</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import cpu from "./charts/cpu.vue"
    import memory from "./charts/memory.vue"
    import disk from './charts/disk.vue'
    import flow from './charts/flow.vue'
    import usage from './charts/usage.vue'
    import basic from './charts/basic.vue'
    import essential from './charts/essential.vue'
    import {doSM3, getStore, getWebSocketPre, setStore, removeStore} from "../utils/util";
    import {MessageBox} from "element-ui";
    // import userMG from "../api/userMG";
    import vCard from "@/components/vCard"

    export default {
        name: 'home',
        data() {
            const validatePass = (rule, value, callback) => {
                var pass = /^(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])(?=.*[!"#$%&'()*+,\-@/\.])[0-9a-zA-Z!"#$%&'()*+,\-@/\\.]{8,16}$/;
                var middlePass = /^(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])[0-9a-zA-Z]{8,16}$/;
                if (value === '') {
                    callback(new Error('请输入密码'));
                } else if (value != '') {
                    const b = this.passwordStrength == 1;
                    if (b && !pass.test(value)) {
                        callback(new Error('密码格式为8-16位，包含大小写、数字和特殊字符!'));
                    } else if (!b && !middlePass.test(value)) {
                        callback(new Error('密码格式为8-16位，包含大小写和数字!'));
                    } else {
                        if (this.editPassword.rePassword == null || this.editPassword.rePassword == '') {
                            callback();
                        } else {
                            if (value != this.editPassword.rePassword) {
                                callback(new Error('两次输入密码不一致!'));
                            } else {
                                callback();
                            }
                        }
                    }
                } else {
                    callback();
                }
            };
            const validatePass2 = (rule, value, callback) => {
                if (value === '') {
                    callback(new Error('请再次输入密码'));
                } else {
                    if (this.addVisible && value !== this.editPassword.password) {
                        callback(new Error('两次输入密码不一致!'));
                    } else if (this.editPassword.rePassword && value !== this.editPassword.rePassword) {
                        callback(new Error('两次输入密码不一致!'));
                    } else {
                        callback();
                    }
                }
            };
            return {
                cpu: [0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00],
                disk: [0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00],
                flow: [0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00],
                time: [0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00],
                cacheUsed: '',
                memoryUsed: '',
                cpuRate: '',
                cpushi: '',
                wsIP: '',
                passwordStrength: 1,
                rePasswordVisible: false,
                editPassword: {
                    id: '',
                    oldPwd: '',
                    password: '',
                    rePassword: ''
                },
                rules: {
                    oldPwd: [{required: true, message: '请输入之前的密码', trigger: 'blur'}],
                    password: [
                        {required: true, message: '请输入密码', trigger: 'blur'},
                        {min: 6, max: 16, message: '请输入8-16位字符', trigger: 'blur'},
                        {validator: validatePass, trigger: 'blur'}
                    ],
                    rePassword: [
                        {required: true, message: '请再次输入密码', trigger: 'blur'},
                        {validator: validatePass2, trigger: 'blur'}
                    ]
                },
            }
        },
        // 注册组件
        components: {
            cpu, memory, usage, disk,flow, basic, essential, vCard
        },
        created() {
            this.rePassword();
            /*getwsIp().then(res=>{
              if (res.code=='0'){
                this.initWebSocket(res.data.wsIp);
              }else {
                this.$message.error(res.msg)
              }

            })*/
            this.wsIP = getWebSocketPre() + "/websocket/99";
            // this.wsIP = "ws://**********:6090/signWebsocket/99"; // 临时
            this.initWebSocket();
            // configMG.getWebSocketAddress().then((res) => {
            //   const code = res.code;
            //   if (code === 0) {
            //
            //     // this.initWebSocket();
            //   } else {
            //     this.$message.error(res.msg);
            //   }
            //
            // });
        },
        destroyed() {
            this.websock.close() //离开路由之后断开websocket连接
        },
        methods: {
            rePassword() {
                const store = getStore("rePassword");
                console.log(getStore("rePassword"));
                console.log(getStore("roleId"));
                console.log(store === 'true');

                if (store == 'true' && getStore("roleId") != 0) {
                    MessageBox.alert('密码已过期，请重新修改密码', '系统提示', {
                            confirmButtonText: '修改密码',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }
                    ).then((res) => {
                        this.rePasswordVisible = true;
                        setStore("rePassword", false);
                    })
                }
            },
            initWebSocket() { //初始化websocket
                this.websock = new WebSocket(this.wsIP);
                this.websock.onmessage = this.websocketonmessage;
                this.websock.onopen = this.websocketonopen;
                this.websock.onerror = this.websocketonerror;
                this.websock.onclose = this.websocketclose;
            },
            websocketonopen() { //连接建立之后执行send方法发送数据
                let actions = {"test": "12345"};
                this.websocketsend(JSON.stringify(actions));
            },
            websocketonerror() {//连接建立失败重连
                this.initWebSocket();
            },
            websocketonmessage(e) { //数据接收
                const redata = eval('(' + e.data + ')');
                // console.log(redata)
                this.cpu = redata.cpuRate;
                this.cacheUsed = redata.memoryPie;
                this.disk = redata.filePie;
                if (redata.cpuRate != null) {
                    this.cpushi = redata.cpuRate[redata.cpuRate.length - 1];
                }
                this.flow ={
                  readNum:  redata.accNet,
                  writeNum: redata.sendNet
                }

                  ///redata.NetInfo;
                this.cpuRate = redata.memoryRate;
            },
            websocketsend(Data) {//数据发送
                this.websock.send(Data);
            },
            websocketclose(e) {  //关闭
                console.log('断开连接', e);
            },


            // 获取当前启用策略
            currentStrategy() {
                this.$http.userMG.queryEnableStrategy().then((res) => {
                    const code = res.code;
                    if (code === 0) {
                        this.passwordStrength = res.data.passwordStrength;
                    } else {
                        this.$message.error(res.msg
                        )
                    }
                })
            },
            //修改密码
            reAccountPassword() {
                this.$refs['editPassword'].validate((valid) => {
                    if (valid) {
                        let opt = {
                            'oldPassword': doSM3(this.editPassword.oldPwd),
                            'password': doSM3(this.editPassword.password)
                        };
                        const userId = getStore('userId');
                        this.$http.userMG.rePassword(userId, opt).then((res) => {
                            const code = res.code;
                            if (code == 0) {
                                this.$message.success("密码修改成功！");
                                this.clearUserInfo();
                                this.$router.push("/");
                            } else {
                                this.$message.error(res.msg);
                            }
                        })
                    }
                })
            },
            clearUserInfo() {
                removeStore("token");
                removeStore("roleId");
                removeStore("userId");
                removeStore("roleName");
                removeStore("userName");
                removeStore("menus");
                removeStore("rePassword")
            }
        },
        watch: {
            cpuRate(newName, oldName) {
                this.cpuRate = newName
            }
        }
    }
</script>

<style scoped lang="less">
    .first_row {
        margin-bottom: 10px;
    }

    /deep/ .el-card__header {
        padding: 10px 20px;
    }

    .border {
        border-bottom: 1px solid #ccc
    }
</style>


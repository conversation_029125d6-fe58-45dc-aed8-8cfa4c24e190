/**
 * 审计管理 路由
 * */
const auditRouter = [
    {
        path: '/operate-audit',
        name: '操作审计',
        component: () => import('@/views/audit/audit-operate'),
        meta: {
            title: '操作审计',
            requireAuth: true
        }
    }, {
        path: '/system-audit',
        name: '系统审计',
        component: () => import('@/views/audit/audit-system'),
        meta: {
            title: '系统审计',
            requireAuth: true
        }
    }, {
        path: '/sign-audit',
        name: '业务审计',
        component: () => import('@/views/audit/audit-sign'),
        meta: {
            title: '业务审计',
            requireAuth: true
        }
    }, {
        path: '/oam-log',
        name: '调试日志',
        component: () => import('@/views/oam/log/log-configure'),
        meta: {
            title: '调试日志',
            requireAuth: true
        }
    }, {
        path: '/log-configure',
        name: '日志配置',
        component: () => import('@/views/audit/log-configure'),
        meta: {
            title: '日志配置',
            requireAuth: true
        }
    }
];

export default {auditRouter}

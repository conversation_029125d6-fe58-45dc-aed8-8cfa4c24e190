<template>
    <el-dialog :title="addAppOpenTitle" :visible.sync="addAppOpen" width="720px" append-to-body :close-on-click-modal="false" @close="closeCaDia">
        <el-form ref="caAddForm" :model="caForm" :rules="caRules" label-width="100px">
            <el-form-item prop="name" label="CA名称：">
                <el-input v-model="caForm.name" clearable maxlength="255" show-word-limit size="small" placeholder="请输入CA名称"></el-input>
            </el-form-item>
            <el-form-item label="上传证书：" prop="certFile" class="upload_form_item">
                <el-upload
                        class="upload-demo"
                        :multiple='false'
                        :show-file-list="false"
                        accept='.cer'
                        :headers="headerObj"
                        :data="certFileData"
                        :before-upload="fileBeforeUpload"
                        :on-success="deviceKey"
                        action="/svs/business/ca/analysusCerts"
                        :auto-upload="true">
                    <el-button class="comBtn com_send_btn" size="small" type="primary">导入证书</el-button>
                </el-upload>
            </el-form-item>
        </el-form>
        <createTable
                :tableData="tableData"
                :tableHeader="tableDataHeader"
                :isPage="isPage"
                :pageAttributes="pageAttr"
                :selectionChange="handleSelectionChange"
                :current-change="ca_currentChange"
                :sizeChange="ca_sizeChange"
        >
        </createTable>
        <div slot="footer" class="dialog-footer">
            <el-button size="small" class="comBtn com_reset_btn" @click="closeCaDia">取 消</el-button>
            <el-button size="small" class="comBtn com_send_btn" type="primary" @click="addSubmitForm">确 定</el-button>
        </div>
        <el-dialog
                width="550px"
                title="查看证书"
                :visible.sync="innerVisible"
                append-to-body>
            <el-descriptions :column="1" border>
                <el-descriptions-item label="签发者">{{certDetail.issuer}}</el-descriptions-item>
                <el-descriptions-item label="签发给">{{certDetail.dn}}</el-descriptions-item>
                <el-descriptions-item label="序列号">{{ certDetail.sn }}</el-descriptions-item>
                <el-descriptions-item label="有效期">{{certDetail.dateStr}}</el-descriptions-item>
                <el-descriptions-item label="证书类型">{{certDetail.keyDesc}}</el-descriptions-item>
                <el-descriptions-item label="哈希算法">{{certDetail.digestEncAlg}}</el-descriptions-item>
                <!--<el-descriptions-item label="证书指纹(SHA1)">{{certDetail.fingerprintSha1}}</el-descriptions-item>-->
                <!--<el-descriptions-item label="证书指纹(MD5)">{{certDetail.fingerprintMd5}}</el-descriptions-item>-->
            </el-descriptions>
        </el-dialog>
    </el-dialog>
</template>

<script>
    import caMg from "@/api/caMG"
    import {getStore, dateFormat} from "@/utils/util";

    export default {
        data() {
            let _this = this;
            return {
                headerObj: {
                    'token': 'Bearer ' + getStore('token')
                },
                addAppOpen: false,
                addAppOpenTitle: "证书链新增",
                innerVisible: false,
                certFileData: {},
                caForm: {
                    operation: "add",
                    name: "",
                    certs: "",
                    id: "",
                    delIds: "",
                    certFile: null,
                    caId: ''
                },
                tableData: [],
                tableDataHeader: [
                    {
                        label: "序号",
                        width: "50",
                        type: "index",
                        tableColumnAttributes: {
                            align: "center",
                            "header-align": "center"
                        }
                    }, {
                        label: "证书主题",
                        prop: "dn",
                        type: 'normal'
                    }, {
                        label: "序列号",
                        prop: "sn",
                        type: 'normal'
                    }, {
                        label: "颁发者主题",
                        prop: "issuer",
                        type: 'normal'
                    }, {
                        label: "起始时间",
                        prop: "startdate",
                        type: 'text_formatter',
                        formatter: function (value) {
                            return dateFormat("YYYY-mm-dd HH:MM:SS", new Date(value));
                        }
                    }, {
                        label: "终止时间",
                        prop: "enddate",
                        type: 'text_formatter',
                        formatter: function (value) {
                            return dateFormat("YYYY-mm-dd HH:MM:SS", new Date(value));
                        }
                    }, {
                        width: "180",
                        label: "操作",
                        type: "operation",
                        tag: [
                            {
                                name: "查看",
                                operType: "view",
                                tagType: "el-button",
                                attributes: {
                                    size: "mini",
                                    type: "text",
                                    icon: "el-icon-view"
                                },
                                isShow: function (row) {
                                    return _this.caForm.operation === 'edit' && !!row.id
                                },
                                callback: function (row) {
                                    _this.innerVisible = true;
                                    // caMg.queryCaById(row.id).then(res => _this.certDetail = res.data)
                                    caMg.queryCertChain(row.id).then(res => {
                                        _this.certDetail = res.data
                                    });
                                }
                            },
                            {
                                name: "下载",
                                operType: "download",
                                tagType: "el-button",
                                attributes: {
                                    size: "mini",
                                    type: "text",
                                    icon: "el-icon-download"
                                },
                                isShow: function (row) {
                                    return _this.caForm.operation === 'edit' && !!row.id
                                },
                                callback: function (row) {
                                    caMg.chainDownLoad(row.id).then(res => {
                                        let blob = new Blob([res], {
                                            type: 'application/force-download'
                                        });
                                        let fileName = Date.parse(new Date()) + '.cer';
                                        if (window.navigator.msSaveOrOpenBlob) {
                                            navigator.msSaveBlob(blob, fileName)
                                        } else {
                                            // console.log(3)
                                            var link = document.createElement('a');
                                            link.href = window.URL.createObjectURL(blob);
                                            link.download = fileName;
                                            link.click();
                                            //释放内存
                                            window.URL.revokeObjectURL(link.href)
                                        }
                                    });
                                }
                            },
                            {
                                name: "删除",
                                operType: "china_del",
                                tagType: "el-button",
                                attributes: {
                                    size: "mini",
                                    type: "text",
                                    icon: "el-icon-delete"
                                },
                                callback: function (row) {
                                    console.log(row);
                                    _this.$confirm('确定要删除吗?', '删除确认', {
                                        confirmButtonText: '确定',
                                        cancelButtonText: '取消',
                                        type: 'warning'
                                    }).then(() => {
                                        _this.tableData.splice(_this.tableData.indexOf(row), 1);
                                    });
                                    // if (row.id) {
                                    //     caMg.chainDelete(row.id).then(res => {
                                    //         // this.chainList();
                                    //         _this.$parent.chainList()
                                    //     });
                                    // } else {
                                    // _this.tableData.map(item => {
                                    //     while (_this.siteDataList.indexOf(item) !== -1) {
                                    //         _this.siteDataList.splice(this.siteDataList.indexOf(item), 1);
                                    //     }
                                    // });
                                    // }
                                    // this.deleteHandle(row);
                                }
                            }
                        ],
                    }
                ],
                queryParams: {
                    caId: ""
                },
                certDetail: {},
                isPage: false,
                pageAttr: {},
                caRules: {
                    name: [
                        {required: true, message: '请输入名称', trigger: 'blur'},
                        {
                            validator: function (rule, value, callback) {
                                caMg.caNameCheck(value, _this.caForm.id).then(res => {
                                    let data = res.data;
                                    if (data) {
                                        callback();
                                    } else {
                                        callback(new Error("名称重复"));
                                    }
                                });
                            }, trigger: 'blur'
                        }
                    ],
                    // certFile: [
                    //     {required: true, message: "请上传证书", trigger: "chang"}
                    // ],
                },
            }
        },
        methods: {
            addCaInitFun() {
                this.addAppOpen = true;
                this.tableData = [];
                this.addAppOpenTitle = "证书链新增";
                this.caForm.operation = "add"
            },
            editCaFun(row) {
                caMg.queryCaById(row.id).then(({code, data}) => {
                    // let dataObj = res.data;
                    this.caForm.id = data.id;
                    this.caForm.name = data.name;
                    this.addAppOpenTitle = "证书链修改";
                    this.addAppOpen = true;
                    this.caForm.operation = "edit";
                    this.queryParams.caId = row.id;
                    this.caForm.caId = row.id;
                    this.chainList()
                });
            },
            chainList() {
                this.tableData = [];
                caMg.chainList(this.queryParams).then(res => {
                    this.tableData = res.data;
                    // this.isPage = res.row > 0;
                    // this.pageAttr.total = res.row;
                });
            },
            fileBeforeUpload(file) {
                console.log(file);
                let index = file.name.lastIndexOf(".");
                let ext = file.name.substr(index + 1);
                if (ext !== 'cer') {
                    this.$message.warning('文件格式错误, 请上传正确格式!');
                    return false
                }
                this.certFileData.certFile = file;
            },
            deviceKey(response, file, fileList) {
                console.log(response, file, fileList);
                const {code, data, msg} = response;
                if (!code) {

                    // if (this.caForm.operation === "edit") {
                    // this.chainList();
                    this.tableData = this.tableData.concat(data);
                    // } else {
                    //     this.tableData = data;
                    // }
                    this.$message.success(msg || '上传成功!')
                } else {
                    this.$message.warning(msg || '上传失败!');
                }
            },
            addSubmitForm: function () {
                this.$refs["caAddForm"].validate((valid) => {
                    if (valid) {
                        if (this.tableData.length === 0) return this.$message.warning('请上传证书!');
                        // let asyncThen = {};
                        this.caForm.certs = this.tableData;
                        if (this.caForm.operation === "add") {
                            caMg.addApi(JSON.stringify(this.caForm)).then(res => {
                                if (res.code === 400) this.$message.warning(res.msg);
                                this.closeCaDia();
                                this.$emit("parentDataFun");
                            });
                        } else {
                            caMg.updateApi(JSON.stringify(this.caForm)).then(res => {
                                if (res.code === 400) this.$message.warning(res.msg);
                                this.closeCaDia();
                                this.$emit("parentDataFun");
                            });
                        }
                        // asyncThen
                    }
                })
            },
            handleSelectionChange(val) {
                let delA = "";
                let length = val.length;
                val.forEach((item, index) => {
                    delA += item.id + ",";
                });
                if (length > 0) {
                    delA = delA.substr(0, delA.length - 1);
                }
                this.ca.form.delIds = delA;
            },
            closeCaDia() {
                this.addAppOpen = false;
                Object.assign(this.$data.caForm, this.$options.data().caForm);
                this.$refs['caAddForm'].clearValidate();
            },
            ca_currentChange(val) {
                this.ca.queryParams.pageNo = val;
                this.refresh();
            },
            ca_sizeChange(val) {
                this.ca.queryParams.pageSize = val;
                this.refresh();
            },
        }
    }
</script>

<style scoped lang="less">
    /deep/ .el-descriptions-item__label.is-bordered-label {
        width: 130px;
    }
    /deep/.el-input__count {
        /*height: 32px;*/
        .el-input__count-inner {
            /*height: 100%;*/
            padding: 7px;
            background-color: #f0f0f0;
        }
    }
</style>

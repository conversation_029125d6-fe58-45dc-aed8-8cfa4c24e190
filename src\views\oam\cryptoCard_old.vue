<template>
    <div class="container">
        <!--<el-card>-->
        <!--<el-button class="comBtn com_send_btn" size="mini" type="primary" @click="resetPinCard">重置密码卡</el-button>-->
        <!--<el-button class="comBtn com_send_btn" :disabled="cardStatus !== '出厂状态'" size="mini" @click="createKeyManage">创建密钥主管</el-button>-->
        <!--{{ cardStatus }}-->
        <!--</el-card>-->

        <!--<el-card style="margin-top: 10px">-->
        <!--<el-descriptions class="margin-top" title="设备基本信息" :column="1" border>-->
        <!--<el-descriptions-item label="设备厂商">{{cryptoData.devVendor}}</el-descriptions-item>-->
        <!--<el-descriptions-item label="设备编号">{{cryptoData.devSerial}}</el-descriptions-item>-->
        <!--<el-descriptions-item label="设备型号">{{cryptoData.devProduct}}</el-descriptions-item>-->
        <!--<el-descriptions-item label="版本号">{{cryptoData.devVersion}}</el-descriptions-item>-->
        <!--<el-descriptions-item label="设备状态">{{cryptoData.status}}</el-descriptions-item>-->
        <!--</el-descriptions>-->


        <!--&lt;!&ndash;<el-descriptions class="margin-top" title="设备基本信息" :column="1" border>&ndash;&gt;-->
        <!--&lt;!&ndash;<el-descriptions-item label="设备厂商">{{cryptoData.issuerName}}</el-descriptions-item>&ndash;&gt;-->
        <!--&lt;!&ndash;<el-descriptions-item label="设备版本">{{cryptoData.deviceVersion}}</el-descriptions-item>&ndash;&gt;-->
        <!--&lt;!&ndash;<el-descriptions-item label="LIB版本">{{cryptoData.libVersion}}</el-descriptions-item>&ndash;&gt;-->
        <!--&lt;!&ndash;<el-descriptions-item label="MCU版本">{{cryptoData.mcuVersion}}</el-descriptions-item>&ndash;&gt;-->
        <!--&lt;!&ndash;<el-descriptions-item label="FPGA版本">{{cryptoData.fpgaVersion}}</el-descriptions-item>&ndash;&gt;-->
        <!--&lt;!&ndash;<el-descriptions-item label="Driver版本">{{cryptoData.driverVersion}}</el-descriptions-item>&ndash;&gt;-->
        <!--&lt;!&ndash;<el-descriptions-item label="登录状态">{{cardStatus}}</el-descriptions-item>&ndash;&gt;-->
        <!--&lt;!&ndash;</el-descriptions>&ndash;&gt;-->
        <!--</el-card>-->

        <el-dialog title="生成主管用户" :visible.sync="managerVisible" width="35%" :before-close="closeManagerView"
            :close-on-click-modal="false" :close-on-press-escape="false">
            <ul class="add_con">
                <li class="add_item" v-for="item in addArr" :key="item.id">
                    <h4>{{ item.name }}{{ item.id }}</h4>
                    <div class="el-icon-user-solid"></div>
                </li>
                <li style="text-align: center" v-if="addArr.length < 3">
                    <el-button class="add_btn" @click="addItemFun">＋</el-button>
                    <p style="font-size: 14px">请创建密码主管{{ addArr.length + 1 }}</p>
                </li>
            </ul>
            <div slot="footer" class="dialog-footer">
                <!--<el-button class="comBtn com_reset_btn" size="small" type="success" @click="closeManagerView()"></el-button>-->
                <el-button class="comBtn com_send_btn" size="small" type="success"
                    @click="closeManagerView()">完成</el-button>
                <!--<el-button size="small" type="primary" :loading="buttionLoading" class="title" @click="submitAddManageForm('ruleForm')">保存</el-button>-->
            </div>
        </el-dialog>
        <!-- 添加密码主管 弹窗 -->
        <el-dialog title="添加密码主管" :visible.sync="addMangeShow" width="500px" @click="closeDialog"
            :close-on-click-modal="false">
            <el-form :model="devKeyForm" :rules="rules" ref="mangeForm" label-width="100px" class="demo-ruleForm">
                <el-form-item label="选择UKey：" prop="serial" key="serial" style="text-align: left">
                    <el-select v-model="devKeyForm.serial" placeholder="请选择" size="small" @change="changeSerialHandle">
                        <span v-for="item in initUKeyList" :key="item.serial">
                            <el-option :disabled="item.chosen === 1" :label="item.serial" :value="item.serial">
                            </el-option>
                        </span>
                    </el-select>
                </el-form-item>
                <el-form-item label="口令：" prop="pin">
                    <el-input v-model="devKeyForm.pin" clearable size="small" type="password"
                        placeholder="**********"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button class="comBtn com_reset_btn" size="small" @click="closeDialog">取消</el-button>
                <el-button class="comBtn com_send_btn" size="small" type="primary" :loading="buttionLoading"
                    @click="submitAddManageForm('ruleForm')">保存</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
// import oamMG from "@/api/oamMG";
// import initGm from "@/api/initMG";
// import systemMG from "@/api/systemMG";

export default {
    name: "cryptoCard",
    data() {

        const validatePasswd = (rule, value, callback) => {
            let vPasswd = /^[*]{8,16}$/;
            if (value === '') {
                callback(new Error('请输入密码'));
            } else {
                if (vPasswd.test(value)) {
                    callback(new Error('密码长度8-16位'));
                } else {
                    callback();
                }
            }
        };

        return {
            addArr: [], // 密码主管
            initUKeyList: [],
            active: 0,
            cardStatus: '', // 密码卡状态
            addMangeShow: false,
            devKeyForm: {
                type: 0,
                ukeyIndex: 1,
                serial: '',
                pin: '',
            },
            buttionLoading: false,
            managerVisible: false, // 密钥主管
            cryptoData: {
                issuerName: '',
                deviceVersion: '',
                libVersion: '',
                mcuVersion: '',
                fpgaVersion: '',
                driverVersion: '',
                loginStatus: '',
            },
            rules: {
                usertype: [{ required: true, message: "请选择管理员类型", trigger: ['blur', 'change'] }],
                pucPassword: [{ required: true, message: "请输入加密卡密码", trigger: 'blur' }],
                pin: [
                    { required: true, message: "请输入密码", trigger: 'blur' },
                    { validator: validatePasswd, trigger: 'blur' }
                ],
            },
        }
    },
    methods: {
        // 获取设备基本信息
        // getDeviceInfo() {
        //     oamMG.deviceInfo().then(({code, data, msg}) => {
        //         console.log(code, data, msg);
        //         if (code == 0) {
        //             this.cryptoData.devVendor = data.devVendor;
        //             this.cryptoData.devSerial = data.devSerial;
        //             this.cryptoData.devProduct = data.devProduct;
        //             this.cryptoData.devVersion = data.devVersion;
        //             this.cryptoData.status = data.status;
        //         } else {
        //             this.$message.warning(msg)
        //         }
        //     })
        // },
        /**
         * 重置密码卡
         * */
        resetPinCard() {
            this.$confirm('确定重置密码卡? 重置后将恢复至出厂状态。', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'error'
            }).then(() => {
                this.$http.initGm.resetCard().then(res => {
                    console.log(res);
                    // this.getDeviceStatus()
                    if (res.code === 0) this.$message.success("重置成功!");
                    this.$store.dispatch("getCardStatusFun")
                })
            }).catch(() => {
            })
        },
        /**
         * 获取设备状态(卡状态)
         * */
        getDeviceStatus() {
            this.$http.systemMG.devStatusApi().then(({ code, data }) => {
                this.cardStatus = data;
            })
        },
        /**
         * 创建密钥主管
         * */
        createKeyManage() {
            this.managerVisible = true;
            this.viewAdminStatus('add')
        },
        /**
         * **************************************** 添加密码主管开始 ********************************************
         * 创建密码主管
         * 判断当前卡的状态;
         * 调用两个接口 1. 查看卡状态; 2. 状态符合继续操作, 否则 调用重置接口清除历史数据 跳转初始化第一步;
         * */
        saveDevManage() {
            this.viewAdminStatus('next')
        },
        // 查看密码主管初始化状态 managerStatus === 3 直接跳转到下一步
        viewAdminStatus(str) {
            this.$http.initGm.initGetState().then(({ code, data, msg }) => {
                console.log(code, data, msg);
                const managerStatus = data;
                if (!code) {
                    if (data === -1) {
                        this.active = 1;
                        this.$message.warning(msg);
                        return
                    }
                    if (managerStatus === 0 && str !== 'add') return this.$message.info('请创建3位密码主管!');
                    if (1 < managerStatus < 3 && str === 'next') {
                        this.$message.info('创建密码主管数量不足, 请创建密码主管!')
                    } else if (managerStatus === 3) {
                        this.active = 3;
                        this.addArr = [];
                        // this.$store.dispatch("getCardStatusFun")
                    }

                    if (str !== 'next') {
                        this.addArr = [];
                        for (let i = 0; i < managerStatus; i++) {
                            this.addArr.push({ id: i + 1, name: "密码主管", isShow: true });
                        }
                    }
                    this.devKeyForm.ukeyIndex = managerStatus + 1;
                } else {
                    this.$message.warning(msg)
                }
            });
        },
        // 添加密码主管
        addItemFun() {
            this.$http.initGm.initUKeyList().then(({ code, msg, data }) => {
                console.log(code, data, msg);
                if (code === 0 && data.length !== 0) {
                    this.initUKeyList = data;
                    this.addMangeShow = true;
                    this.$nextTick(() => {
                        this.$refs["mangeForm"].clearValidate();
                    });
                    this.viewAdminStatus('add');
                } else {
                    this.$message.warning('UKey未插入, 请先插入UKey!')
                }
            });
        },
        changeSerialHandle() {
            this.devKeyForm.pin = '';
            // this.devKeyForm.confirmPassword = '';
            this.$refs["mangeForm"].clearValidate();
        },
        // 提交密码主管
        submitAddManageForm() {
            this.$refs["mangeForm"].validate((valid) => {
                if (valid) {
                    this.buttionLoading = true;
                    this.$http.initGm.initAddManage(JSON.stringify(this.devKeyForm)).then(({ code }) => {
                        if (!code) {
                            this.buttionLoading = false;
                            this.devKeyForm.ukeyIndex++;
                            this.viewAdminStatus('');
                            this.closeDialog('')
                        } else {
                            this.buttionLoading = false
                        }
                    }).catch(() => {
                        this.buttionLoading = false
                    });
                }
            })
        },
        closeDialog() {
            this.addMangeShow = false;
            this.devKeyForm.serial = '';
            this.devKeyForm.pin = '';
            this.$refs["mangeForm"].clearValidate();
        },
        /**
         * 关闭生成密钥主管
         * */
        closeManagerView() {
            this.managerVisible = false;
            this.$store.dispatch("getCardStatusFun")
        }
    },
    created() {
        // this.getDeviceInfo();
        // this.getDeviceStatus();
        // this.getDeviceInfo();
    }
}
</script>

<style lang="less" scoped>
/deep/ .el-descriptions-item__label.is-bordered-label {
    width: 160px;
}

.add_con {
    padding-left: 0;
    display: flex;
    list-style: none;
    align-items: center;
    justify-content: center;

    .add_item {
        padding: 10px;
        margin: 0 10px;
        /*border: 1px dashed ;*/
        border-radius: 5px;
        box-shadow: 0 0 5px 2px #eee;

        .el-icon-user-solid {
            font-size: 70px;
        }
    }

    .add_btn {
        width: 50px;
        height: 50px;
        padding: 0;
        border-radius: 50%;

        span {
            font-size: 40px;
        }
    }
}
</style>

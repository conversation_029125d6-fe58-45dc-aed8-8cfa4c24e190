<template>
    <div class="login-wrap" :style="bgObj">
        <div class="login_con">
            <div class="login_tit">{{ sysName }}</div>
            <div style="margin-top: 40px">
                <el-form label-width="90px" :model="ruleForm" :rules="rules" ref="ruleForm">
                    <el-form-item label="账户" prop="username" size="mini">
                        <el-input class="user" v-model="ruleForm.username" style="width: 90%" placeholder="管理员账户"></el-input>
                    </el-form-item>

                    <el-form-item label="密码" prop="password" size="mini">
                        <el-input type="password" v-model="ruleForm.password" style="width: 90%" placeholder="管理员密码"
                                  @keyup.enter.native="login"></el-input>
                    </el-form-item>
                    <br/>
                    <el-button id="login" @click="login">登录</el-button>
                </el-form>
            </div>
        </div>
    </div>
</template>

<script>
    // import userMG from "@/api/userMG";
    // import customGM from "@/api/customMG";
    import {setStore, doSM3} from '../utils/util';

    export default {
        name: "userLogin",
        data() {
            return {
                sysName: '签名验签管理系统',
                bgObj: {},
                ruleForm: {
                    username: '',
                    password: ''
                },
                keys: [],
                //rules前端验证
                rules: {
                    username: [{required: true, message: '请输入账号', trigger: 'blur'}],
                    password: [{required: true, message: '请输入密码', trigger: 'blur'}],
                },
            }
        },
        created() {
            this.getCurrentConfig();
            this.$store.dispatch("getCurrConfigFun")
        },
        methods: {
            getCurrentConfig() {
                this.$http.customGM.currentConfig().then(({code, data, msg}) => {
                    console.log(code, data, msg);
                    this.sysName = document.title = data.productName || '签名验签管理系统';
                    this.bgObj = {'background': 'url(' + data.backgroundLogin + ')','backgroundRepeat':'no-repeat','backgroundSize':'100% 100%'};
                    setStore("customStyle", data)
                })
            },
            login() {
                this.$refs["ruleForm"].validate((valid) => {
                    if (valid) {
                        var opt = {
                            "userName": this.ruleForm.username,
                            "userPwd": doSM3(this.ruleForm.password)
                        };
                        this.$http.userMG.login(opt).then(res => {
                            if (res.code == 0) {
                                setStore("token", res.data.token);
                                setStore("userName", res.data.userName);
                                setStore("menus", res.data.menus);
                                setStore("roleId", res.data.roleId);
                                this.$router.push({path: '/home'});
                                this.$message({
                                    message: '登录成功',
                                    type: 'success'
                                })
                            } else {
                                this.$message.error(res.msg)
                            }
                        })
                    }
                });
            }
        }
    }
</script>

<style lang="less" scoped>
    .login-wrap {
        box-sizing: border-box;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-image: url(../assets/img/log.jpg);
        /* background-color: #112346; */
        background-repeat: no-repeat;
        background-size: 100% 100%;

        .login_con {
            width: 450px;
            background: #ffffff;
            margin: auto;
            padding-bottom: 30px;

            .login_tit {
                height: 50px;
                line-height: 50px;
                color: #fff;
                font-size: 20px;
                text-align: center;
                background: linear-gradient(to right, #3e62ad, #1e50a2);
                border-bottom-right-radius: 20px;
                border-bottom-left-radius: 20px;
                /*margin-top: -20px*/
            }
        }
        #login {
            position: relative;
            left: 50%;
            transform: translateX(-50%);
            width: 85%;
            /*margin-left: -3%;*/
            color: #fff;
            font-size: 18px;
            border: 0 none;
            border-radius: 10px;
            background: linear-gradient(to right,#3e62ad, #1e50a2);
        }
    }

    .el-row {
        margin-bottom: 20px;
        margin-left: 30px;
    }

</style>

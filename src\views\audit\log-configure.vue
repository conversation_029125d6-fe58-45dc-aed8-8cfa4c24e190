<template>

    <el-card class="box-card" shadow="always" style="padding-bottom: 10px;">
        <el-row :gutter="15">
            <el-col :span="12">
                <div class="config_content">
                    <div class="con_tit">SYSLOG日志上传设置</div>
                    <el-form :model="setUploadLogCfg" :rules="rules" ref="setUploadLogCfg" label-width="130px" class="demo-ruleForm" style="margin-top: 10px">
                        <el-form-item label="日志上传：">
                            <el-switch v-model="logUploadStatus"></el-switch>
                        </el-form-item>
                        <el-form-item label="服务器地址：" prop="url">
                            <el-input size="small" v-model="setUploadLogCfg.url" style="width: 215px"></el-input>
                        </el-form-item>
                        <div style="display: flex" >
                            <el-form-item label="上传模式：">
                                <el-select v-model="selModel" placeholder="请选择" size="small" @change="changeModel">
                                    <el-option
                                            v-for="item in uploadModel"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value">
                                    </el-option>
                                </el-select>
                                <!--<el-input size="small" v-if="selModel === 2" v-model.number="setUploadLogCfg.batch" @blur="blurEvent" style="width: 120px"></el-input>-->
                            </el-form-item>
                            <el-form-item label-width="8px" v-if="selModel === 2" prop="batch" key="batch">
                                <el-input size="small" v-model="setUploadLogCfg.batch" style="width: 180px"></el-input>
                            </el-form-item>
                        </div>
                        <el-form-item label="日志数据类型：">
                            <el-select v-model="setUploadLogCfg.logType" placeholder="请选择" size="small">
                                <el-option
                                        v-for="item in logType"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button class="comBtn com_send_btn" size="small" type="primary" @click="logUploadSetting">保存</el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </el-col>
            <el-col :span="12">
                <div class="config_content">
                    <div class="con_tit">日志归档配置</div>
                    <el-form :model="setArchiveCfg" :rules="rules" ref="setArchiveCfg" label-width="130px" class="demo-ruleForm"
                             style="margin-top: 10px">
                        <el-form-item label="自动归档：">
                            <el-switch v-model="archiveState"></el-switch>
                        </el-form-item>
                        <el-row>
                            <el-col :span="11">
                                <el-form-item label="最大阈值(M)：" prop="hightCapacity">
                                    <el-input size="small" v-model.number="setArchiveCfg.hightCapacity" maxlength="15" placeholder="请输入最大阈值(M)"></el-input>
                                </el-form-item>
                                <el-form-item label="服务器地址：" prop="url">
                                    <el-input size="small" v-model="setArchiveCfg.url" placeholder="请输入服务器地址"></el-input>
                                </el-form-item>
                                <el-form-item label="账号：" prop="user">
                                    <el-input size="small" v-model="setArchiveCfg.user" placeholder="请输入账号"></el-input>
                                </el-form-item>
                                <!--onkeyup="if(this.value.length===1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"-->
                                <!--onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'0')}else{this.value=this.value.replace(/\D/g,'')}"-->
                                <el-form-item label="保存周期(月)：" prop="during">
                                    <el-input size="small" v-model="setArchiveCfg.during" placeholder="请输入1-36之间整数" maxlength="2"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="11">
                                <el-form-item label="最小阈值(M)：" prop="lowCapacity">
                                    <el-input size="small" v-model.number="setArchiveCfg.lowCapacity" maxlength="15" placeholder="请输入最小阈值(M)"></el-input>
                                </el-form-item>
                                <el-form-item label="端口：" prop="port">
                                    <el-input size="small" v-model="setArchiveCfg.port" maxlength="10" placeholder="请输入端口"></el-input>
                                </el-form-item>
                                <el-form-item label="密码：" prop="password">
                                    <el-input size="small" type="password" v-model="setArchiveCfg.password" placeholder="请输入密码"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-form-item style="margin-left: 24%">
                            <el-button class="comBtn com_send_btn" size="small" type="primary" @click="setArchiveConfigure()">保存</el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </el-col>
        </el-row>


        <el-row :gutter="15" style="margin-top: 10px">
            <el-col :span="12">
                <div class="config_content" style="min-height: 240px">
                    <div class="con_tit">日志签名验签设置</div>
                    <el-form ref="setLogSign" label-width="130px" class="demo-ruleForm" style="margin-top: 10px">
                        <el-form-item label="日志签名验签：">
                            <el-switch v-model="signStatus"></el-switch>
                        </el-form-item>
                        <el-form-item>
                            <el-button class="comBtn com_send_btn" size="small" type="primary" @click="setSignStatus">保存</el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </el-col>
            <el-col :span="12">
                <div class="config_content" style="min-height: 240px;">
                    <div class="con_tit">日志清理配置</div>
                  <el-form :model="setCleanupCfg" :rules="cleanUpRules" ref="setCleanupCfg" label-width="130px" class="demo-ruleForm"
                           style="margin-top: 10px">
                    <el-form-item label="自动清理：">
                      <el-switch v-model="setCleanupCfg.isEnabled"></el-switch>
                    </el-form-item>
                    <el-row>
                      <el-col :span="11">
                        <el-form-item label="最大阈值(M)：" prop="max">
                          <el-input size="small" v-model.number="setCleanupCfg.max" maxlength="15" placeholder="请输入最大阈值(M)"></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="11">
                        <el-form-item label="最小阈值(M)：" prop="min">
                          <el-input size="small" v-model.number="setCleanupCfg.min" maxlength="15" placeholder="请输入最小阈值(M)"></el-input>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-form-item style="margin-left: 24%">
                      <el-button class="comBtn com_send_btn" size="small" type="primary" @click="setCleanupConfigure()">保存</el-button>
                    </el-form-item>
                  </el-form>
                </div>
            </el-col>
        </el-row>
    </el-card>
</template>

<script>
    import logConfMG from "@/api/logConfMG"
    // import {getversion} from '@/api/about'

    export default {
        name: "log-configure",
        data() {
            const validatorHight = (rule, value, callback) => {
                let reg = /^\+?[1-9]\d*$/;
                if (!reg.test(value)) return callback(new Error('格式错误, 请输入正整数!'));
                if (this.setArchiveCfg.lowCapacity !== '') {
                    if (value <= this.setArchiveCfg.lowCapacity) {
                        callback("最大阈值不能低于或等于最小阈值");
                    } else {
                        callback();
                    }
                } else {
                    callback();
                }
            };
            const validatorLow = (rule, value, callback) => {
                let reg = /^\+?[1-9]\d*$/;
                if (!reg.test(value)) return callback(new Error('格式错误, 请输入正整数!'));
                if (this.setArchiveCfg.hightCapacity !== '') {
                    console.log(value, this.setArchiveCfg.hightCapacity);
                    if (value >= this.setArchiveCfg.hightCapacity) {
                        callback("最小阈值不能大于或等于最大阈值");
                    } else {
                        callback();
                    }
                } else {
                    callback();
                }
            };
            const validatorIp = (rule, value, callback) => {
                if (value !== "") {
                    let ip = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
                    if (!ip.test(value)) {
                        callback(new Error('请输入正确的IP!'))
                    } else {
                        callback()
                    }
                } else {
                    callback();
                }
            };
            const validatePort = (rule, value, callback) => {
                let reg = /^\+?[1-9]\d*$/;
                if (!reg.test(value)) return callback(new Error('格式错误, 请输入正整数!'));
                if (value < 0 || value > 65535) {
                    callback(new Error('端口在0-65535之间！'));
                } else {
                    callback();
                }
            };
            const validatorDuring = (rule, value, callback) => {
                let reg = /^\+?[1-9]\d*$/;
                if (value === '') return callback();
                if (+value < 1 || +value > 36) return callback(new Error('保存周期在1-36(月)之间!'));
                if (!reg.test(value)) return callback(new Error('格式错误, 请输入正整数!'));
                callback()
            };
            const checkBatch = (rule, value, callback) => {
                let reg = /^\+?[1-9]\d*$/;
                if (value === '') return callback();
                if (value < 1 || value > 100) return callback(new Error('固定条数在1-100条之间!'));
                if (!reg.test(value)) return callback(new Error('格式错误, 请输入正整数!'));
                callback()
            };
            return {
                signStatus: false, // 日志上传状态

                logUploadStatus: false, // 日志上传状态
                logUpload: {}, // 日志上传
                archiveState: false, // 自动归档
                logArchive: {}, // 日志归档
                logClean: {}, // 日志清除
                setUploadLogCfg: {
                    switching: false,
                    url: '',
                    batch: '', // 条数上传
                    logType: 0,
                },
                selModel: 1,
                uploadModel: [
                    {value: 1, label: '实时上传'},
                    {value: 2, label: '固定条数触发'}
                ],
                logType: [
                    {value: 0, label: '全部日志'},
                    {value: 1, label: '管理日志'},
                    {value: 2, label: '业务日志'}
                ],
                setArchiveCfg: {
                    hightCapacity: '',
                    lowCapacity: '',
                    url: '',
                    retentionLevel: '',
                    user: '',
                    password: '',
                    port: '',
                    during: 6,
                },
                setCleanupCfg: {
                    isEnabled: false,
                    enabled: 0,
                    max: 80,
                    min: 20
                },
                pageForm: {
                    pageNum: 1,
                    pageSize: 10
                },
                rules: {
                    batch: [
                        {required: true, message: '日志上传固定条数在1-100条之间', trigger: 'blur'},
                        {validator: checkBatch, trigger: 'blur'}
                    ],
                    switching: [
                        {required: true, message: '请选择自动归档', trigger: 'change'}
                    ],
                    url: [
                        {required: true, message: '请输入服务器地址', trigger: 'blur'},
                        {validator: validatorIp, trigger: "blur"},
                    ],
                    hightCapacity: [
                        {required: true, message: '请输入最大阈值', trigger: 'blur'},
                        {validator: validatorHight, trigger: 'blur'}
                    ],
                    during: [
                        {required: true, message: '请输入保存周期', trigger: 'blur'},
                        {validator: validatorDuring, trigger: 'blur'}
                    ],
                    lowCapacity: [
                        {required: true, message: '请输入最小阈值', trigger: 'blur'},
                        {validator: validatorLow, trigger: 'blur'}
                    ],
                    user: [
                        {required: true, message: '请输入账号', trigger: 'blur'},
                    ],
                    port: [
                        {required: true, message: '请输入端口', trigger: 'blur'},
                        {validator: validatePort, trigger: "blur"},
                    ],
                    password: [
                        {required: true, message: '请输入密码', trigger: 'blur'},
                    ]
                },
                cleanUpRules: {
                    enabled: [
                        {required: true, message: '请选择日志清除', trigger: 'change'}
                    ],
                    max: [
                        {required: true, message: '请输入最大阈值', trigger: 'blur'},
                        {validator: validatorHight, trigger: 'blur'}
                    ],
                    min: [
                        {required: true, message: '请输入最小阈值', trigger: 'blur'},
                        {validator: validatorLow, trigger: 'blur'}
                    ]
                }
            }
        },
        created() {
            this.$http.about.getAbilityApi({}).then(({ code, data, msg }) => {
                if (code == 0) {
                    const name = JSON.parse(data.data).product_code;
                    if(name === 'chsm'){
                        this.logType = [
                            {value: 0, label: '全部日志'},
                            {value: 1, label: '管理日志'}
                        ];
                    }
                }
            })
            this.$http.about.getversion();
            this.queryLogConfigure();
            this.getSignStatus();
            this.getCleanupConfigure();
        },
        methods: {
            // blurEvent(event) {
            //     let val = +event.target.value;
            //     if (val < 0 || val > 100) {
            //         this.$refs.setUploadLogCfg.validateField('batch')
            //     } else {
            //         this.$nextTick(() => {
            //             this.$refs['setUploadLogCfg'].clearValidate(['batch']);
            //         })
            //     }
            // },
            changeModel(val) {
                this.setUploadLogCfg.batch = '';
                if (val === 1) {
                    this.$nextTick(() => {
                        this.$refs['setUploadLogCfg'].clearValidate(['batch']);
                    })
                }
            },

            // 获取日志签名验签状态
            getSignStatus() {
                logConfMG.logSign().then(({ code, data, msg }) => {
                    // if (code !== 0) return this.$message.warning(msg);
                    this.signStatus = data.audit.sign === 1
                })
            },
            // 设置日志签名验签
            setSignStatus() {
                let p = {
                    sign: this.signStatus ? 1 : 0,
                    mark: 1
                };
                logConfMG.setLogSign(JSON.stringify(p)).then(({ code, data, msg }) => {
                    console.log(code, data, msg);
                    if (code !== 0 && code !== 400) return this.$message.warning(msg);
                    this.$message.success(msg || '设置成功!');
                    this.getSignStatus()
                })
            },

            getCleanupConfigure() {
                logConfMG.cleanupConfigure().then(({ code, data, msg }) => {
                  // if (code !== 0) return this.$message.warning(msg);
                  data.isEnabled = data.enabled === 1;
                  this.setCleanupCfg = data;
                })
            },

            //查询日志配置
            queryLogConfigure() {
                logConfMG.logConfigure(JSON.stringify(this.pageForm)).then(res => {
                    if (res.code == 0) {
                        let data = res.data;
                        if (data.list.length !== 0) {
                            data.list.map(item => {
                                if (item.ctg_type === 3) this.logUpload = item;
                                if (item.ctg_type === 1) this.logArchive = item;
                                if (item.ctg_type === 2) this.logClean = item
                            });
                            this.logUploadStatus = this.logUpload.status === 1;
                            let subscribers = JSON.parse(this.logUpload.info).subscribers[0];
                            this.setUploadLogCfg.url = subscribers.url;
                            if (typeof (subscribers.batch) === 'undefined' || typeof (subscribers.logType) === 'undefined') {
                                this.selModel = 1;
                                this.setUploadLogCfg.logType = 0;
                            } else {
                                // subscribers.batch === 0 ?
                                this.selModel = subscribers.batch === 0 ? 1 : 2;
                                this.setUploadLogCfg.batch = subscribers.batch === 0 ? '' : subscribers.batch;
                                this.setUploadLogCfg.logType = subscribers.logType;
                            }

                            // 日志归档
                            this.archiveState = this.logArchive.status === 1;
                            this.setArchiveCfg.hightCapacity = JSON.parse(this.logArchive.info).maxSize === -1 ? '' : JSON.parse(this.logArchive.info).maxSize;
                            this.setArchiveCfg.lowCapacity = JSON.parse(this.logArchive.info).minSize === -1 ? '' : JSON.parse(this.logArchive.info).minSize;
                            let tempObj = JSON.parse(this.logArchive.info).subscribers[1];

                            this.setArchiveCfg.url = tempObj.url;
                            this.setArchiveCfg.port = tempObj.port = tempObj.port === -1 ? '' : tempObj.port;
                            this.setArchiveCfg.user = tempObj.user;
                            this.setArchiveCfg.password = tempObj.password;
                            if (tempObj.during) this.setArchiveCfg.during = tempObj.during == -1 ? 0 : tempObj.during / 60 / 60 / 24 / 30;
                        }
                    } else {
                        if (res.code !== 400) this.$message.warning(res.msg);
                    }
                })
            },
            //设置日志上传
            logUploadSetting() {
                this.$refs['setUploadLogCfg'].validate((valid) => {
                    if (valid) {
                        this.setUploadLogCfg.batch = this.selModel === 1 ? 0 : this.setUploadLogCfg.batch;
                        // 处理数据
                        this.logUpload.status = this.logUploadStatus ? 1 : 0;
                        // this.logUpload.batch = this.setUploadLogCfg.batch;
                        // this.logUpload.logType = this.setUploadLogCfg.logType;
                        let arr = JSON.parse(this.logUpload.info).subscribers;
                        for (let i = 0; i < arr.length; i++) {
                            arr[i].url = this.setUploadLogCfg.url;
                            arr[i].batch = this.setUploadLogCfg.batch;
                            arr[i].logType = this.setUploadLogCfg.logType;
                        }
                        let info = {};
                        info.subscribers = arr;
                        this.logUpload.info = JSON.stringify(info);
                        this.updateConfigFun(this.logUpload, 1)
                    }
                })
            },
            //日志归档配置
            setArchiveConfigure() {
                this.$refs['setArchiveCfg'].validate((valid) => {
                    if (valid) {
                        // console.log(this.archiveState);
                        this.logArchive.status = this.archiveState ? 1 : 0;
                        // console.log(this.logArchive.status);
                        // console.log(this.logArchive.info);
                        let arr = JSON.parse(this.logArchive.info).subscribers;
                        for (let i = 0; i < arr.length; i++) {
                            if (arr[i].type === 'ftp') {
                                arr[i].url = this.setArchiveCfg.url;
                                arr[i].port = this.setArchiveCfg.port;
                                arr[i].user = this.setArchiveCfg.user;
                                arr[i].password = this.setArchiveCfg.password;
                                arr[i].during = this.setArchiveCfg.during * 30 * 24 * 60 * 60;
                            }
                        }

                        let info = JSON.parse(this.logArchive.info);
                        info.subscribers = arr;
                        info.maxSize = this.setArchiveCfg.hightCapacity;
                        info.minSize = this.setArchiveCfg.lowCapacity;
                        this.logArchive.info = JSON.stringify(info);
                        this.updateConfigFun(this.logArchive, 0);
                        this.logCleanFun()
                    } else {
                        console.log('error submit!!');
                        return false;
                    }
                });
            },
            setCleanupConfigure () {
                this.$refs['setCleanupCfg'].validate((valid) => {
                    if (valid) {
                        this.setCleanupCfg.enabled = this.setCleanupCfg.isEnabled ? 1 : 0;
                        logConfMG.updateCleanupConfigure(JSON.stringify(this.setCleanupCfg)).then(res => {
                            if (res.code == 0) {
                                this.$message.success(res.msg);
                                this.getCleanupConfigure();
                            } else {
                                this.$message.warning(res.msg);
                            }
                        })
                    }
                });
            },
            logCleanFun() {
                // logClean
                this.logClean.status = this.archiveState ? 1 : 0;
                let arr = JSON.parse(this.logClean.info).subscribers;
                for (let i = 0; i < arr.length; i++) {
                    arr[i].during = this.setArchiveCfg.during * 30 * 24 * 60 * 60;
                }
                let info = JSON.parse(this.logClean.info);
                info.subscribers = arr;
                info.maxSize = this.setArchiveCfg.hightCapacity;
                info.minSize = this.setArchiveCfg.lowCapacity;
                this.logClean.info = JSON.stringify(info);
                this.updateConfigFun(this.logClean, 1)
            },
            updateConfigFun(param, num) {
                logConfMG.updateConfigure(JSON.stringify(param)).then(res => {
                    if (!res.code) {
                        if (num === 1) {
                            this.$message.success(res.msg);
                            this.queryLogConfigure()
                        }
                    } else {
                        if (res.code !== 400) this.$message.warning(res.msg);
                    }
                })

            },

        }
    }
</script>

<style lang="less" scoped>
    .config_content {
        border: 1px solid #ccc;
        min-height: 450px;
    }
    .con_tit {
        border-bottom: 1px solid #ccc;
        padding: 10px;
    }
</style>

import {reqheaders, req, reqParams, noAuthreq, reqParamNo<PERSON>son, uploadReq, reqCommon} from './axiosFun';
import API from "@/api/apiuri"

let whiteListApi = API.whiteListApi;
export default {
  list(param) {
    return reqParam<PERSON>o<PERSON><PERSON>("POST", whiteListApi.list, param);
  },
  del(id) {
    return reqParam<PERSON>o<PERSON><PERSON>("GET", whiteListApi.del + "/" + id, {});
  },
  delBatch(ids) {
    return reqParamNo<PERSON>son("POST", whiteListApi.batchDel, {ids: ids});
  },
  queryById(id) {
    return reqParamNo<PERSON><PERSON>("GET", whiteListApi.queryById + "/" + id, {});
  },
  edit(param){
    return reqParamNo<PERSON><PERSON>("POST", whiteListApi.edit , param);
  },
  add(param){
    return reqParamNo<PERSON><PERSON>("POST", whiteListApi.add, param);
  }

}





{"name": "sign-vue", "version": "1.0.0", "description": "A Vue.js project", "author": "zhangyanbin <<EMAIL>>", "private": true, "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "build-bundle": "vue-cli-service build --mode production --target lib --name TESTVUE ./src/index.js", "build-bundle-test": "cross-env VUE_APP_PROJECTNAME=cbp npm run build-bundle", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.19.0", "browserslist": "^4.23.1", "caniuse-lite": "^1.0.30001636", "core-js": "^2.6.9", "crypto-js": "^4.2.0", "echarts": "^4.6.0", "element-ui": "^2.15.12", "jquery": "^3.6.3", "jsencrypt": "^3.0.0-rc.1", "keymaster": "^1.6.2", "lodash": "^4.17.21", "moment": "^2.24.0", "pako": "^2.1.0", "sockjs-client": "^1.4.0", "stompjs": "^2.3.3", "videojs-flash": "^2.2.1", "view-design": "^4.1.3", "vue": "^2.6.10", "vue-clipboard2": "^0.3.3", "vue-fragment": "^1.6.0", "vue-router": "^3.0.3", "vue-ueditor-wrap": "^2.4.1", "vue-video-player": "^5.0.2", "vuejs-storage": "^3.0.1", "vuex": "^3.0.1", "xterm": "^4.18.0", "xterm-addon-fit": "^0.5.0"}, "devDependencies": {"@babel/register": "^7.6.0", "@vue/cli-plugin-babel": "^3.11.0", "@vue/cli-plugin-eslint": "^3.11.0", "@vue/cli-plugin-unit-jest": "^3.11.0", "@vue/cli-service": "^3.11.0", "@vue/eslint-config-standard": "^4.0.0", "@vue/test-utils": "1.0.0-beta.29", "babel-core": "7.0.0-bridge.0", "babel-eslint": "^10.0.1", "babel-jest": "^23.6.0", "babel-plugin-import": "^1.13.0", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.0.0", "filemanager-webpack-plugin": "^2.0.5", "less": "^3.10.3", "less-loader": "^5.0.0", "mockjs": "^1.0.1-beta3", "stylus": "^0.54.5", "stylus-loader": "^3.0.2", "svg-sprite-loader": "^6.0.11", "vue-template-compiler": "^2.6.10"}}
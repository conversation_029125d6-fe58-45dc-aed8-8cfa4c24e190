<template>
    <div :style="{ height: '160px',}">
        <p :title="systemName">产品名称：{{systemName}}</p>
        <p :title="productSerial">产品编号：{{productSerial}}</p>
        <p :title="productModel">型号版本：{{productModel}}</p>
        <p :title="version">版本号：{{version}}</p>
        <p :title="runDay">运行时间：{{runDay}}</p>
    </div>
</template>

<script>
    // import systemMG from "@/api/systemMG";

    export default {
        name: "essential",
        data() {
            return {
                productSerial: "",
                productModel: "",
                runDay: '',
                systemName: '',
                version: '',
            }
        },
        created() {

            this.$http.systemMG.getProjectInfo().then((res) => {
                const code = res.code;
                if (res.code == "0") {
                    this.productSerial = res.data.productSerial;
                    this.runDay = res.data.runDay;
                    this.systemName = res.data.systemName;
                    this.version = res.data.version;
                    this.productModel = res.data.productModel;
                }
            })

        },
    }
</script>

<style lang="less" scoped>
    p {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
</style>

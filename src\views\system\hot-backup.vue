<template>
    <div>
        <el-card class="box-card" shadow="always" style="padding-bottom: 10px">
            <!-- 搜索筛选 -->
            <el-form :model="formInline" :rules="rules" ref="formInline" class="demo-ruleForm" label-width="150px">
                <div>
                    <el-row>
                        <h3 class="title_back">双机热备</h3>
                        <el-form-item label="启用状态:" label-width="100px" style="margin-block-start: 1em;margin-block-end: 1em;" disabled>
                            <el-switch v-model="formInline.status" active-color="#13ce66" inactive-color="#ff4949" disabled></el-switch>
                        </el-form-item>
                    </el-row>
                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="虚拟地址(VIP):">
                                <el-input size="small" v-model="formInline.ip" readonly></el-input>
                            </el-form-item>
                            <el-form-item label="主机地址:">
                                <el-input size="small" v-model="formInline.masterIp" readonly></el-input>
                            </el-form-item>
                            <el-form-item label="备机地址:">
                                <el-input size="small" v-model="formInline.backupIp" readonly></el-input>
                            </el-form-item>
                            <!--              <el-form-item >-->
                            <!--                <el-button class="comBtn com_send_btn" size="small" type="primary" @click="addGroupSave">保存IP配置</el-button>-->
                            <!--              </el-form-item>-->
                        </el-col>
                    </el-row>
                    <el-row>
                        <h3 class="title_back">状态监控</h3>
                    </el-row>
                    <el-row>
                        <el-col :span="8">
                            <el-form-item label="keepalived状态:">
                                <el-switch disabled
                                           v-model="datastatus"
                                           active-color="#13ce66"
                                           inactive-color="#ff4949">
                                </el-switch>
                                正常
                            </el-form-item>
                            <el-form-item label="设备状态:">
                                <el-switch disabled
                                           v-model="masterStatus"
                                           active-color="#13ce66"
                                           inactive-color="#ff4949">
                                </el-switch>
                                主机
                                <el-switch disabled
                                           v-model="backupStatus"
                                           active-color="#13ce66"
                                           inactive-color="#ff4949">
                                </el-switch>
                                备机
                            </el-form-item>
                            <el-form-item label="虚拟IP地址所在设备:">
                                <el-switch disabled
                                           v-model="zhuequipment"
                                           active-color="#13ce66"
                                           inactive-color="#ff4949">
                                </el-switch>
                                主机
                                <el-switch disabled
                                           v-model="beiequipment"
                                           active-color="#13ce66"
                                           inactive-color="#ff4949">
                                </el-switch>
                                备机
                            </el-form-item>

                            <el-form-item label="数据同步状态:">
                                <el-switch disabled
                                           v-model="backupSlaveStatus"
                                           active-color="#13ce66"
                                           inactive-color="#ff4949">
                                </el-switch>
                                主->备
                                <el-switch disabled
                                           v-model="masterSlaveStatus"
                                           active-color="#13ce66"
                                           inactive-color="#ff4949">
                                </el-switch>
                                备->主
                            </el-form-item>
                        </el-col>

                    </el-row>
                    <el-form-item label="手动同步">
                        <el-button class="comBtn com_send_btn" size="small" type="primary" @click="manualSave" :loading="loading" :disabled="formInline.status != 1">同步</el-button>
                    </el-form-item>

                </div>

            </el-form>
        </el-card>
    </div>
</template>

<script>
    // import systemMG from "@/api/systemMG";
    export default {
        name: "hot-backup",
        data() {
            const validatorIp = (rule, value, callback) => {
                if (value !== "") {
                    let ip = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
                    if (!ip.test(value)) {
                        callback(new Error('请输入正确的IP!'))
                    } else {
                        callback()
                    }
                } else {
                    callback();
                }
            };
            return {
                datastatus: false,
                backupStatus: false,
                masterStatus: false,
                zhuequipment: false,
                masterSlaveStatus: false,
                backupSlaveStatus: false,
                beiequipment: false,
                tongbu: false, // 无效赋值
                loading: false, //是显示加载
                // rules 表单验证
                rules: {
                    ip: [
                        {required: true, message: '请输入虚拟地址', trigger: 'blur'},
                        {validator: validatorIp, trigger: 'blur', required: true}
                    ],
                    masterIp: [
                        {required: true, message: '请输入主机地址', trigger: 'blur'},
                        {validator: validatorIp, trigger: 'blur', required: true}
                    ],
                    backupIp: [
                        {required: true, message: '请输入备机地址', trigger: 'blur'},
                        {validator: validatorIp, trigger: 'blur', required: true}
                    ]
                },
                formInline: {
                    ip: '',
                    masterIp: '',
                    backupIp: '',
                    status: false
                },
                id: "",
            }
        },
        methods: {
            queryBackupGroup() {
                this.$http.systemMG.queryBackupGroup().then((res) => {
                    const code = res.code;
                    if (code == 0) {
                        this.id = res.data.id;
                        this.formInline.ip = res.data.ip;
                        this.formInline.masterIp = res.data.masterIp;
                        this.formInline.backupIp = res.data.backupIp;
                        this.formInline.status = res.data.status == 1 ? true : false;
                        if (res.data.status == 1) {
                            this.tongbu = true;
                        } else {
                            this.tongbu = false;
                        }
                    } else {
                        // this.$message.error(res.msg);
                    }
                })
            },
            addGroupSave() {
                if (this.formInline.ip != "" && this.formInline.masterIp != '' && this.formInline.backupIp != '') {
                    if (this.formInline.ip == this.formInline.masterIp || this.formInline.backupIp == this.formInline.masterIp || this.formInline.ip == this.formInline.backupIp) {
                        this.$message.error("地址一致，无法进行高可用");
                        return false
                    }
                }

                this.$refs['formInline'].validate((valid) => {
                    if (valid) {
                        if (this.formInline.status == true) {
                            this.formInline.status = 0
                        } else {
                            this.formInline.status = 1
                        }


                        this.$http.systemMG.addBackupGroup(this.formInline).then((res) => {
                            if (res.code == 0) {
                                this.$message.success(res.msg);
                                this.getAvailabilityqueryStatus();
                            } else {
                                // this.$message.error(res.msg);
                            }
                            this.queryBackupGroup();
                        });
                    }
                });

            },
            manualSave() {
                if (this.formInline.masterIp != '' && this.formInline.backupIp != '') {
                    this.loading = true;
                    this.$http.systemMG.manualBackupSave({id: this.id}).then(res => {
                        if (res.code == 0) {
                            this.$message.success(res.msg);
                            this.loading = false;
                        } else {
                            // this.$message.error(res.msg);
                            this.loading = false;
                        }
                    }).catch(() => {
                        this.loading = false;
                    })
                } else {
                    this.$message.error("请输入主机地址或备机地址");
                }
            },
            startGroupSave() {
                if (this.formInline.masterIp != '' && this.formInline.backupIp != '' && this.formInline.ip != '') {
                    if (this.formInline.status == true) {
                        var status = 1;
                    } else {
                        var status = 0;
                    }
                    var opt = {
                        "status": status,
                        "id": this.id
                    };
                    this.$http.systemMG.startBackupGroup(opt).then(res => {
                        if (res.code == 0) {
                            this.$message.success(res.msg);
                        } else {
                            // this.$message.error(res.msg);
                        }
                        this.queryBackupGroup();
                        this.getAvailabilityqueryStatus();
                    })
                }
            },
            getAvailabilityqueryStatus() {
                this.$http.systemMG.queryBackupStatus().then(res => {
                    if (res.code == "0") {
                        if (res.data.status == 0) {
                            this.datastatus = res.data.status == 0 ? true : false;
                            if (res.data.status == 0) {
                                this.masterStatus = res.data.masterStatus == 1 ? false : true;
                                this.backupStatus = res.data.backupStatus == 1 ? false : true;
                                this.masterSlaveStatus = res.data.masterSlaveStatus == 1 ? false : true;
                                this.backupSlaveStatus = res.data.backupSlaveStatus == 1 ? false : true;
                                if (res.data.equipment == "0") {
                                    this.zhuequipment = true;
                                } else if (res.data.equipment == 1) {
                                    this.beiequipment = true;
                                } else {
                                    this.zhuequipment = false;
                                    this.beiequipment = false;
                                }
                            }
                        } else {

                        }
                    } else {
                        // this.$message.error(res.msg);
                    }
                })
            },
        },
        created() {
            this.queryBackupGroup();
            this.getAvailabilityqueryStatus();
        }
    }
</script>

<style lang="less" scoped>
    .el-button {
        float: left;
    }

    .title_back {
        float: left;
    }

    #p {
        float: left;
    }
</style>

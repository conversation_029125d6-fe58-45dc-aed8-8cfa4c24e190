<template>
  <div style="display: flex; justify-content: space-between;">
    <div style="border: 1px solid #ccc; width: calc((100% - 40px) / 2)">
      <div style="border-bottom: 1px solid #ccc;">
        <!--<p>日志上传设置</p>-->
        <p style="margin-left: 20px;">SYSLOG日志上传设置 </p>
      </div>
      <el-form :model="setUploadLogCfg" :rules="rules" ref="setUploadLogCfg" label-width="130px" class="demo-ruleForm"
               style="margin-top: 10px">
        <el-form-item label="日志上传：">
          <el-switch v-model="logUploadStatus"></el-switch>
        </el-form-item>
        <el-form-item label="服务器地址：">
          <el-input size="small" v-model="setUploadLogCfg.url" style="width: 215px"></el-input>
        </el-form-item>
        <el-form-item label="上传模式：" prop="batch">
          <el-select v-model="selModel" placeholder="请选择" size="small" @change="changeModel">
            <el-option
              v-for="item in uploadModel"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <el-input size="small" v-if="selModel === 2" v-model.number="setUploadLogCfg.batch"
                    @blur="blurEvent"
                    onkeyup="if(this.value.length===1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"
                    onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'0')}else{this.value=this.value.replace(/\D/g,'')}"
                    style="width: 120px"></el-input>
        </el-form-item>
        <el-form-item label="日志数据类型：">
          <el-select v-model="setUploadLogCfg.logType" placeholder="请选择" size="small">
            <el-option
              v-for="item in logType"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button class="comBtn com_send_btn" size="small" type="primary" @click="logUploadSetting">保存</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div style="border: 1px solid #ccc; width: calc((100% - 40px) / 2)">
      <div style="border-bottom: 1px solid #ccc;">
        <p style="margin-left: 20px;">日志归档配置</p>
      </div>
      <el-form :model="setArchiveCfg" :rules="rules" ref="setArchiveCfg" label-width="130px" class="demo-ruleForm"
               style="margin-top: 10px">
        <el-form-item label="自动归档：">
          <el-switch v-model="archiveState"></el-switch>
        </el-form-item>
        <el-row>
          <el-col :span="11">
            <el-form-item label="最大阈值(M)：" prop="hightCapacity">
              <el-input size="small" v-model.number="setArchiveCfg.hightCapacity"
                        onkeyup="if(this.value.length===1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"
                        onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'0')}else{this.value=this.value.replace(/\D/g,'')}"></el-input>
            </el-form-item>
            <el-form-item label="服务器地址：" prop="url">
              <el-input size="small" v-model="setArchiveCfg.url"></el-input>
            </el-form-item>
            <el-form-item label="账号：" prop="user">
              <el-input size="small" v-model="setArchiveCfg.user"></el-input>
            </el-form-item>
            <el-form-item label="保存周期(月)：" prop="during">
              <el-input size="small" v-model="setArchiveCfg.during" placeholder="单位为月"
                        onkeyup="if(this.value.length===1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"
                        onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'0')}else{this.value=this.value.replace(/\D/g,'')}"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="最小阈值(M)：" prop="lowCapacity">
              <el-input size="small" v-model.number="setArchiveCfg.lowCapacity"
                        onkeyup="if(this.value.length===1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"
                        onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'0')}else{this.value=this.value.replace(/\D/g,'')}"></el-input>
            </el-form-item>
            <el-form-item label="端口：" prop="port">
              <el-input size="small" v-model="setArchiveCfg.port"
                        onkeyup="if(this.value.length===1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"
                        onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'0')}else{this.value=this.value.replace(/\D/g,'')}"></el-input>
            </el-form-item>
            <el-form-item label="密码：" prop="password">
              <el-input size="small" type="password" v-model="setArchiveCfg.password"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item style="margin-left: 24%">
          <el-button class="comBtn com_send_btn" size="small" type="primary" @click="setArchiveConfigure()">保存
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
// import cluster from "@/api/cluster"

export default {
  name: "node-log-configure",
  data() {
    const validatorHight = (rule, value, callback) => {
      if (this.setArchiveCfg.lowCapacity !== '') {
        if (value <= this.setArchiveCfg.lowCapacity) {
          callback("最大阈值不能低于或等于最小阈值");
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    const validatorLow = (rule, value, callback) => {
      if (this.setArchiveCfg.hightCapacity !== '') {
        console.log(value, this.setArchiveCfg.hightCapacity);
        if (value >= this.setArchiveCfg.hightCapacity) {
          callback("最小阈值不能大于或等于最大阈值");
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    const validatorIp = (rule, value, callback) => {
      if (value !== "") {
        let ip = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
        if (!ip.test(value)) {
          callback(new Error('请输入正确的IP!'))
        } else {
          callback()
        }
      } else {
        callback();
      }
    };
    const validatePort = (rule, value, callback) => {
      if (value < 0 || value > 65535) {
        callback(new Error('端口在0-55535之间！'));
      } else {
        callback();
      }
    };
    return {
      nodeId: undefined,
      logUploadStatus: false, // 日志上传状态
      logUpload: {}, // 日志上传
      archiveState: false, // 自动归档
      logArchive: {}, // 日志归档
      logClean: {}, // 日志清除
      setUploadLogCfg: {
        switching: false,
        url: '',
        batch: '', // 条数上传
        logType: 0,
      },
      selModel: 1,
      setArchiveCfg: {
        hightCapacity: '',
        lowCapacity: '',
        url: '',
        retentionLevel: '',
        user: '',
        password: '',
        port: '',
        during: '',
      },
      pageForm: {
        pageNum: 1,
        pageSize: 10
      },
      rules: {
        batch: [
          {message: '日志上传固定条数在1-100条之间', trigger: 'blur'}
        ],
        switching: [
          {required: true, message: '请选择自动归档', trigger: 'change'}
        ],
        url: [
          {required: true, message: '请输入服务器地址', trigger: 'blur'},
          {validator: validatorIp, trigger: "blur"},
        ],
        hightCapacity: [
          {required: true, message: '请输入最大阈值', trigger: 'blur'},
          {validator: validatorHight, trigger: 'blur'}
        ],
        during: [
          {required: true, message: '请输入保存周期', trigger: 'blur'},
        ],
        lowCapacity: [
          {required: true, message: '请输入最小阈值', trigger: 'blur'},
          {validator: validatorLow, trigger: 'blur'}
        ],
        user: [
          {required: true, message: '请输入账号', trigger: 'blur'},
        ],
        port: [
          {required: true, message: '请输入端口', trigger: 'blur'},
          {validator: validatePort, trigger: "blur"},
        ],
        password: [
          {required: true, message: '请输入密码', trigger: 'blur'},
        ]
      },
      uploadModel: [
        {value: 1, label: '实时上传'},
        {value: 2, label: '固定条数触发'}
      ],
      logType: [
        {value: 0, label: '全部日志'},
        {value: 1, label: '管理日志'},
        {value: 2, label: '业务日志'}
      ]
    }
  },
  created() {},
  methods: {
    blurEvent(event) {
      let val = +event.target.value;
      if (val < 0 || val > 100) {
        this.$refs.setUploadLogCfg.validateField('batch')
      } else {
        this.$nextTick(() => {
          this.$refs['setUploadLogCfg'].clearValidate(['batch']);
        })
      }
    },
    changeModel(val) {
      this.setUploadLogCfg.batch = '';
      if (val === 1) {
        this.$nextTick(() => {
          this.$refs['setUploadLogCfg'].clearValidate(['batch']);
        })
      }
    },
    //清空页面数据
    cleanPageData() {
      let _this = this;
      _this.$refs['setUploadLogCfg'].clearValidate(['batch']);
      _this.$nextTick(() => {
        this.$refs['setUploadLogCfg'].clearValidate(['batch']);
      })
      _this.$nextTick(function () {
        _this.$refs['setArchiveCfg'].clearValidate();
      })
      Object.assign(_this.$data, _this.$options.data.call(_this));
    },
    //查询日志配置
    queryLogConfigure(id) {
      this.nodeId = id;
      this.pageForm.nodeId = id;
      this.$http.cluster.getLogConfig(this.pageForm).then(res => {
        if (res.code == 0) {
          let data = res.data;
          if (data.list.length !== 0) {
            data.list.map(item => {
              if (item.ctg_type === 3) this.logUpload = item;
              if (item.ctg_type === 1) this.logArchive = item;
              if (item.ctg_type === 2) this.logClean = item
            });
            this.logUploadStatus = this.logUpload.status === 1;
            let subscribers = JSON.parse(this.logUpload.info).subscribers[0];
            this.setUploadLogCfg.url = subscribers.url;
            if (typeof (subscribers.batch) === 'undefined' || typeof (subscribers.logType) === 'undefined') {
              this.selModel = 1;
              this.setUploadLogCfg.logType = 0;
            } else {
              // subscribers.batch === 0 ?
              this.selModel = subscribers.batch === 0 ? 1 : 2;
              this.setUploadLogCfg.batch = subscribers.batch === 0 ? '' : subscribers.batch;
              this.setUploadLogCfg.logType = subscribers.logType;
            }

            // 日志归档
            this.archiveState = this.logArchive.status === 1;
            this.setArchiveCfg.hightCapacity = JSON.parse(this.logArchive.info).maxSize === -1 ? '' : JSON.parse(this.logArchive.info).maxSize;
            this.setArchiveCfg.lowCapacity = JSON.parse(this.logArchive.info).minSize === -1 ? '' : JSON.parse(this.logArchive.info).minSize;
            let tempObj = JSON.parse(this.logArchive.info).subscribers[1];

            this.setArchiveCfg.url = tempObj.url;
            this.setArchiveCfg.port = tempObj.port = tempObj.port === -1 ? '' : tempObj.port;
            this.setArchiveCfg.user = tempObj.user;
            this.setArchiveCfg.password = tempObj.password;
            if (tempObj.during) this.setArchiveCfg.during = tempObj.during == -1 ? 0 : tempObj.during / 60 / 60 / 24 / 30;
          }
        } else {
          // this.$message.warning(res.msg);
        }
      })
    },
    //设置日志上传
    logUploadSetting() {
      if (this.setUploadLogCfg.url === '') {
        this.$message.error('请输入服务器IP地址');
        return false
      } else {
        let ip = /^((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}$/;
        if (!ip.test(this.setUploadLogCfg.url)) {
          this.$message.error('请输入正确的IP地址');
          return false
        }
        if (this.selModel === 2 && this.setUploadLogCfg.batch <= 0) {
          this.$message.info('日志上传条数须大于0');
          return
        }
        this.setUploadLogCfg.batch = this.selModel === 1 ? 0 : this.setUploadLogCfg.batch;
        // 处理数据
        this.logUpload.status = this.logUploadStatus ? 1 : 0;
        let arr = JSON.parse(this.logUpload.info).subscribers;
        for (let i = 0; i < arr.length; i++) {
          arr[i].url = this.setUploadLogCfg.url;
          arr[i].batch = this.setUploadLogCfg.batch;
          arr[i].logType = this.setUploadLogCfg.logType;
        }
        let info = {};
        info.subscribers = arr;
        this.logUpload.info = JSON.stringify(info);
        this.updateConfigFun(this.logUpload, 1)
      }
    },
    //日志归档配置
    setArchiveConfigure() {
      this.$refs['setArchiveCfg'].validate((valid) => {
        if (valid) {
          this.logArchive.status = this.archiveState ? 1 : 0;
          let arr = JSON.parse(this.logArchive.info).subscribers;
          for (let i = 0; i < arr.length; i++) {
            if (arr[i].type === 'ftp') {
              arr[i].url = this.setArchiveCfg.url;
              arr[i].port = this.setArchiveCfg.port;
              arr[i].user = this.setArchiveCfg.user;
              arr[i].password = this.setArchiveCfg.password;
              arr[i].during = this.setArchiveCfg.during * 30 * 24 * 60 * 60;
            }
          }

          let info = JSON.parse(this.logArchive.info);
          info.subscribers = arr;
          info.maxSize = this.setArchiveCfg.hightCapacity;
          info.minSize = this.setArchiveCfg.lowCapacity;
          this.logArchive.info = JSON.stringify(info);
          this.updateConfigFun(this.logArchive, 0);
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    updateConfigFun(param, num) {
      let _this = this;
      param.nodeId = _this.nodeId;
      this.$http.cluster.setLogConfig(param).then(res => {
        if (!res.code) {
          _this.$message.success(res.msg);
          _this.queryLogConfigure(_this.nodeId)
        } else {
          // _this.$message.warning(res.msg);
        }
      })
    }
  }
}
</script>

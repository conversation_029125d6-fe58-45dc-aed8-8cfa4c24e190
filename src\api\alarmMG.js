import API from "@/api/apiuri";
import {reqheaders, req, reqParams, noAuthreq, reqParamNoJson} from './axiosFun';

let alarmInfoApi = API.alarmInfoApi;

export default {
  setAlarmInfoConfigure(param) {
    return req("post", alarmInfoApi.setAlarmInfo, param);
  },
  getAlarmInfoConfigure() {
    return req("get", alarmInfoApi.getAlarmInfo);
  },
  setAlarmStatus(param) {
    return req("post", alarmInfoApi.setAlarmStatus, param);
  },
  setNoticeType(param) {
    return req("post", alarmInfoApi.setNoticeType, param);
  },
  configure() {
    return req("get", alarmInfoApi.configure);
  },
  setbussinessAlarm(param) {
    return req("post", alarmInfoApi.bussinessAlarm, param);
  },
  getbussinessAlarm() {
    return req("get", alarmInfoApi.getbussinessAlarm);
  },
  changeAlarmStatus(param) {
    return reqParams("put", alarmInfoApi.changeAlarmStatus, param);
  },
  getAlarmStatus() {
    return req("get", alarmInfoApi.alarmStatus);
  },
  setEmailAccount(param) {
    return req("post", alarmInfoApi.setEmailAccount, param);
  },
  setEmailServer(param) {
    return req("post", alarmInfoApi.setEmailServer, param);
  },
  getEmailInfo() {
    return req("get", alarmInfoApi.getEmailInfo);
  },
  editNoticeModel(param) {
    return req("put", alarmInfoApi.editNoticeModel, param);
  },
  pageNoticeModel(param) {
    return reqParams("get", alarmInfoApi.pageNoticeModel, param);
  },
  pageAlarmRecord(param) {
    return reqParams("get", alarmInfoApi.pageAlarmRecord, param)
  },
  getAlarmType() {
    return req("get", alarmInfoApi.getAlarmType);
  },
  confirmAlarmRecord(param) {
    return req("post", alarmInfoApi.confirmAlarmRecord, param)
  }
}

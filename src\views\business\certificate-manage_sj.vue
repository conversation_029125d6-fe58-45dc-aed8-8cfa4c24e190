<template>
    <div class="container">
        <el-card v-show="showSearch" class="box-card" shadow="always" style="margin-bottom: 10px">
            <el-form :inline="true" :show-message="false" label-width="75px" class="user-search comForm">
                <el-form-item label="证书ID：">
                    <el-input size="small" v-model="queryParams.ids" clearable style="width: 180px"></el-input>
                </el-form-item>
                <el-form-item label="证书类型：">
                    <!--<el-input size="small" v-model="queryParams.dn" clearable style="width: 180px"></el-input>-->
                    <el-select v-model="queryParams.updateResult" placeholder="请选择升级结果" clearable size="small">
                        <el-option  label="全部" value=""></el-option>
                    </el-select>
                </el-form-item>
                <!--<el-form-item label="绑定应用：">-->
                <!--<el-select size="small" v-model="queryParams.bindingApp" style="width: 180px">-->
                <!--<el-option :value="-1" label="全部">全部</el-option>-->
                <!--<el-option :value="0" label="未绑定">未绑定</el-option>-->
                <!--<el-option :value="1" label="绑定">绑定</el-option>-->
                <!--</el-select>-->
                <!--</el-form-item>-->
                <!--<el-form-item label="失效时间：">-->
                    <!--<el-date-picker-->
                            <!--v-model="startTimes" clearable-->
                            <!--@clear="clearHandle"-->
                            <!--type="datetimerange"-->
                            <!--size="small"-->
                            <!--:unlink-panels="true"-->
                            <!--align="right"-->
                            <!--format="yyyy-MM-dd HH:mm:ss"-->
                            <!--value-format="yyyy-MM-dd HH:mm:ss"-->
                            <!--range-separator="至"-->
                            <!--start-placeholder="开始日期"-->
                            <!--end-placeholder="结束日期">-->
                    <!--</el-date-picker>-->
                <!--</el-form-item>-->
                <el-form-item label="绑定应用：">
                    <el-select v-model="queryParams.updateResult" placeholder="请选择升级结果" clearable size="small">
                        <el-option  label="全部" value=""></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item style="background-color: transparent;">
                    <el-button class="comBtn com_send_btn" size="small" icon="el-icon-search" @click="refreshCert">搜索</el-button>
                    <el-button class="comBtn com_reset_btn" size="small" icon="el-icon-refresh" @click="resetHandle">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card class="box-card" shadow="always" style="padding-bottom: 10px">
            <!-- 操作方法 -->
            <div style="margin-bottom: 10px">
                <el-button class="comBtn com_send_btn" size="mini" type="success" @click="addCertHandle">新增</el-button>
                <!--<el-button class="comBtn com_send_btn" size="mini" type="success" @click="importCertHandle">证书导入</el-button>-->
                <el-button class="comBtn com_del_btn" size="mini" type="danger" @click="bathDeleteCert">删除</el-button>
                <Strategy :policyType="2"></Strategy>
                <div style="float: right">
                    <el-button-group>
                        <el-button size="mini" icon="el-icon-search" @click="showAndSearch"></el-button>
                        <el-button size="mini" icon="el-icon-refresh-left" @click="refreshCert"></el-button>
                    </el-button-group>
                </div>
            </div>
            <!--:pageAttributes="pageAttr"-->
            <createTable
                    :tableData="tableData"
                    :tableHeader="tableDataHeader"
                    :isPage="isPage"
                    :pageAttributes="{ total: total, currentPage: queryParams.pageNum, pageSize: queryParams.pageSize }"
                    :selectionChange="handleSelectionChange"
                    :current-change="cert_currentChange"
                    :sizeChange="cert_sizeChange"
            >
            </createTable>
        </el-card>

        <el-dialog :title="addCertForm.optionType === 'add'?'证书新增':'更新证书'" :visible.sync="addAppOpen" width="600px" append-to-body :close-on-click-modal="false" @closed="resetAddCertForm">
            <el-form ref="addCertForm" :model="addCertForm" :rules="rules" label-width="120px" size="medium">
                <el-form-item label="证书来源：" key="certSource">
                    <el-select v-model="addCertForm.certSource" placeholder="请选择">
                        <el-option label="P10 请求" :value="3"></el-option>
                        <el-option label="本地产生" :value="1"></el-option>
                    </el-select>
                </el-form-item>

                <!-- v-if="supportedRsa||item.type == 'SM2'" -->
                <el-form-item label="证书类型：" prop="keyDesc"  key="keyDesc">
                    <el-select v-model="addCertForm.keyDesc" placeholder="请选择" @change="keyChange" :disabled="addCertForm.optionType === 'updateCert'">
                        <el-option v-for="(item, index) in keyDescData" :key="index" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="哈希算法：" prop="digestEncAlg" key="digestEncAlg">
                    <el-select v-model="addCertForm.digestEncAlg" placeholder="请选择" :disabled="addCertForm.optionType === 'updateCert'">
                        <el-option v-for="(item,index) in digestEncAlgOption" :key="index" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="证书DN：" prop="dn" key="dn">
                    <el-input placeholder="CN=XXXXX,C=CN" v-model="addCertForm.dn" :disabled="addCertForm.optionType === 'updateCert'" clearable></el-input>
                </el-form-item>
                <el-form-item label="申请类型：" prop="doubleCert" v-if="addCertForm.keyDesc === 'SM2'" key="doubleCert">
                    <el-radio :label="1" v-model="addCertForm.doubleCert" :disabled="addCertForm.optionType === 'updateCert'" @change="changeUsage">双证</el-radio>
                    <el-radio :label="0" v-model="addCertForm.doubleCert" :disabled="addCertForm.optionType === 'updateCert'" @change="changeUsage">单证</el-radio>
                </el-form-item>
                <!--<el-form-item label="证书用途：" prop="Usage" v-if="addCertForm.keyDesc !== 'SM2'" key="Usage">-->
                <!--<el-checkbox-group :disabled="addCertForm.optionType === 'updateCert'" v-model="addCertForm.Usage" :max="1">-->
                <!--<el-checkbox :label="0">签名</el-checkbox>-->
                <!--<el-checkbox :label="3">加密</el-checkbox>-->
                <!--</el-checkbox-group>-->
                <!--</el-form-item>-->
                <el-form-item label="证书用途：" prop="Usage" key="Usage">
                    <el-checkbox-group :disabled="addCertForm.optionType === 'updateCert'" v-model="addCertForm.Usage">
                        <el-checkbox :disabled="addCertForm.keyDesc === 'SM2'" :label="0">签名</el-checkbox>
                        <el-checkbox :disabled="addCertForm.keyDesc === 'SM2'" v-if="addCertForm.doubleCert === 1 || addCertForm.keyDesc !== 'SM2'" :label="3">加密</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="扩展密钥用途：" prop="" key="expandedKey">
                    <el-checkbox-group :disabled="addCertForm.optionType === 'updateCert'" v-model="addCertForm.Usage">
                        <el-checkbox :label="108">时间戳</el-checkbox>
                        <!--<el-checkbox :disabled="addCertForm.keyDesc === 'SM2'" v-if="addCertForm.doubleCert === 1" :label="3">加密</el-checkbox>-->
                    </el-checkbox-group>
                </el-form-item>
            </el-form>

            <div slot="footer" class="dialog-footer">
                <el-button size="small" class="comBtn com_reset_btn" @click="resetAddCertForm">取 消</el-button>
                <el-button size="small" class="comBtn com_send_btn" type="primary" :loading="addCertLoading" @click="submitForm()">确 定</el-button>
            </div>
        </el-dialog>
        <!-- 证书导入 -->
        <cert-import-dia ref="certImportDia" @parentData="parentData"></cert-import-dia>
        <!--证书信息-->
        <view-cert-dia ref="viewCertDia"></view-cert-dia>
        <!-- 证书信息编辑 -->
        <edit-cert-dialog ref="editCertDialog"></edit-cert-dialog>
        <!--应用绑定-->
        <bind-app-dialog ref="bindAppDialog"></bind-app-dialog>
        <!--历史证书-->
        <history-cert-list-dia ref="historyCertListDia"></history-cert-list-dia>
    </div>
</template>

<script>
    import bussMG from "@/api/bussMG";
    import Template from "@/components/template";
    // import keyMG from "@/api/keyMG";
    import {dateFormat, getStore} from "@/utils/util";
    import {usageFilterFun} from '@/utils/filters'
    // import {Message} from "element-ui";
    import Strategy from "@/components/strategy/index";
    // import extKeyMG from "@/api/extKeyMG";
    import certImportDia from './components/certImportDia_sj'
    import historyCertListDia from './components/historyCertListDia'
    import bindAppDialog from './components/bindAppDialog'
    import editCertDialog from './components/editCertDialog'
    import viewCertDia from './components/viewCertDia'


    export default {
        name: "certificate-manage",
        components: {Template, Strategy, certImportDia, historyCertListDia, bindAppDialog, editCertDialog, viewCertDia},
        data() {
            return {
                // notUsedKeyCount: 0, // 无效
                supportedRsa: true,
                authHeader: {token: getStore('token')},
                addAppOpen: false, // 新增
                showSearch: true, // 是否显示查询
                tableData: [
                    {index: 1, id: 2, appName: 'TEST', is: '是', time1: '2022-08-26', time2: '2042-08-21', certType: '应用实体证书'},
                    {index: 1, id: 1, appName: 'TEST', is: '是', time1: '2022-08-26', time2: '2042-08-21', certType: '用户证书'}
                ], // 列表
                tableDataHeader: [], // 表头数据

                uploadForm: new FormData(),
                pageAttr: {},
                isPage: true,
                answer: false,
                // 查询参数
                queryParams: {
                    updateResult: '',


                    ids: "",
                    dn: "",
                    endBetween1: '',
                    endBetween2: '',
                    bindingApp: -1,
                    pageNo: 1,
                    pageSize: 10,
                },
                startTimes: [],
                certEntityInfo: {}, // 证书详情
                delArray: "",
                keyDescData: [
                    {label: "SM2", value: "SM2", type: "SM2"},
                    {label: "RSA/1024", value: "RSA1024", type: "RSA"},
                    {label: "RSA/2048", value: "RSA2048", type: "RSA"},
                    {label: "RSA/4096", value: "RSA4096", type: "RSA"}
                ],
                digestEncAlgData: [
                    {label: "SM3", value: "SM3", type: "SM2"},
                    {label: "SHA1", value: "SHA1", type: "RSA"},
                    {label: "SHA256", value: "SHA256", type: "RSA"},
                    {label: "SHA512", value: "SHA512", type: "RSA"}
                ],
                // keyloading: false,
                digestEncAlgOption: [],
                addCertLoading: false,
                sign: 0,
                enc: 3,
                addCertForm: {
                    certSource: 3, // 证书来源
                    keyDesc: 'SM2', // 证书类型
                    digestEncAlg: 'SM3', // 哈希算法
                    dn: '',
                    // double: 1, // 申请类型
                    doubleCert: 1, // 单双证 1 -> 双证   0 -> 单证
                    Usage: [0, 3], // 证书用途  0 --> 签名  3 --> 加密
                    id: '', // 更新ID
                    optionType: "add", // add --> 证书注册   updateCert --> 证书更新
                    keyLength: "",
                },
                total: 0,
                rules: {
                    dn: [
                        {required: true, message: '请输入申请证书DN', trigger: 'blur'},
                        {max: 200, message: '不能超过200个字符!', trigger: 'blur'},
                        {
                            validator: function (rule, value, callback) {
                                if (value.indexOf("，") !== -1) {
                                    callback(new Error("dn不能包含中文逗号"));
                                } else if (value.indexOf(", ") !== -1) {
                                    callback(new Error("逗号后不能有空格"))
                                } else {
                                    bussMG.verifyDN(value).then(res => {
                                        const code = res.code;
                                        if (code === 0) {
                                            callback();
                                        } else {
                                            callback(res.msg);
                                        }
                                    })
                                }
                            }, trigger: 'blur'
                        }
                    ],
                    keyIndex: [
                        {required: true, message: "请选择密钥索引", trigger: "blur"}
                    ],
                    Usage: [
                        {required: true, message: "请选择证书用途", trigger: "change"},
                        {
                            validator: function (rule, value, callback) {
                                if (value.indexOf(0) === -1 && value.indexOf(3) === -1) callback(new Error("请选择证书用途！"));
                                callback();
                            }, trigger: 'blur'
                        }
                    ],
                    fileSize: [
                        {required: true, message: "请上传证书", trigger: "chang"}
                    ]
                }
            }
        },
        methods: {
            clearHandle() {
                this.startTimes = [];
                this.refreshCert()
            },
            // 搜索
            refreshCert: function () {
                this.tableData = [];
                this.loadData();
            },
            // 重置
            resetHandle() {
                this.startTimes = [];
                if (this.startTimes.length === 0) {
                    this.queryParams.endBetween1 = '';
                    this.queryParams.endBetween2 = ''
                }
                Object.assign(this.queryParams, this.$options.data().queryParams);
                this.loadData();
            },
            showAndSearch() {
                this.showSearch = !this.showSearch;
            },
            loadData() {
                if (this.startTimes.length !== 0) {
                    this.queryParams.endBetween1 = this.startTimes[0];
                    this.queryParams.endBetween2 = this.startTimes[1];
                }
                bussMG.certList(this.queryParams).then(res => {
                    console.log(console);
                    console.table(res.data);
                    // this.tableData = res.data;
                    this.isPage = res.row > 0;
                    this.total = res.row;
                });
            },
            handleSelectionChange(val) {
                let delA = "";
                let length = val.length;
                val.forEach((item, index) => {
                    delA += item.id + ",";
                });
                if (length > 0) {
                    delA = delA.substr(0, delA.length - 1);
                }
                this.delArray = delA;
            },
            // 分页操作
            cert_currentChange: function (val) {
                this.queryParams.pageNo = val;
                this.loadData()
            },
            cert_sizeChange: function (val) {
                this.queryParams.pageSize = val;
                this.loadData()
            },
            // 证书导入
            importCertHandle() {
                this.$refs.certImportDia.initCertImportFun('', 'add')
            },
            parentData() {
                this.loadData();
            },
            // 新增证书
            addCertHandle() {
                this.addAppOpen = true;
                this.addCertForm.optionType = 'add';
                this.addCertForm.certSource = 3;
                this.keyChange('SM2');
            },
            // 选择证书类型
            changeUsage(val) {
                console.log(val);
                this.addCertForm.Usage = val === 1 ? [0, 3] : [0]
            },
            // 选择证书类型
            keyChange(value) {
                console.log(value);
                let currentValue = value;
                let keyLength = 0;
                this.addCertForm.keyDesc = value;
                if (currentValue === "SM2") {
                    this.addCertForm.digestEncAlg = "SM3";
                    if (this.addCertForm.optionType === 'add') {
                        this.addCertForm.doubleCert = 1;
                        this.addCertForm.Usage = [0, 3];
                    }
                    keyLength = 0;
                } else {
                    this.addCertForm.doubleCert = 0;
                    if (this.addCertForm.optionType === 'add') {
                        this.addCertForm.digestEncAlg = "SHA256";
                        this.addCertForm.Usage = [0];
                    }
                    keyLength = currentValue.substr(3);
                }
                this.digestEncAlgOption = this.digestEncAlgData.filter(item => {
                    return currentValue.indexOf(item.type) > -1
                });
                this.addCertForm.keyLength = keyLength;
            },
            // 新增证书 提交
            submitForm() {
                let _this = this;
                this.$refs["addCertForm"].validate((valid) => {
                    if (valid) {
                        for (let i in this.addCertForm) {
                            if (this.uploadForm.has(i)) {
                                this.uploadForm.delete(i);
                            }
                            this.uploadForm.append(i, this.addCertForm[i]);
                        }
                        _this.addCertLoading = true;
                        bussMG.regCert(this.uploadForm).then(res => {
                            let code = res.code;
                            _this.addCertLoading = false;
                            if (code === 0) {
                                _this.addAppOpen = false;
                                if (_this.addCertForm.certSource === 3 && (_this.addCertForm.optionType === 'add' || _this.addCertForm.optionType === 'updateCert')) {
                                    let blob = new Blob([res.data], {type: 'text/plain;charset=utf-8'});
                                    let fileName = Date.parse(new Date()) + '.csr';
                                    if (window.navigator.msSaveOrOpenBlob) {
                                        navigator.msSaveBlob(blob, fileName)
                                    } else {
                                        var link = document.createElement('a');
                                        link.href = window.URL.createObjectURL(blob);
                                        link.download = fileName;
                                        link.click();
                                        //释放内存
                                        window.URL.revokeObjectURL(link.href);
                                    }
                                }
                                _this.resetAddCertForm();
                                _this.refreshCert();
                            } else if (code === 400) {
                                this.$message.warning(res.msg)
                            }
                        }).catch(err => {
                            console.log(err);
                            this.addCertLoading = false;
                            this.$refs["addCertForm"].clearValidate();
                        });
                    }
                });
            },
            resetAddCertForm() {
                this.$nextTick(() => {
                    this.$refs["addCertForm"].clearValidate();
                    Object.assign(this.addCertForm, this.$options.data().addCertForm);
                });
                // this.$refs["addCertForm"].resetFields();
                // this.keyChange('SM2');
                this.addAppOpen = false
            },
            certChange(file) {
                let lastIndex = file.name.lastIndexOf(".");
                let certExt = file.name.substring(lastIndex + 1);
                if (file.size === 0) {
                    this.$message.error('选择文件大小不能为0！');
                    this.addCertForm.fileList = [];
                    this.addCertForm.fileSize = "";
                    return false
                } else if (certExt !== "cer" && certExt !== "crt") {
                    this.$message.error('请上传公钥证书格式！');
                    this.addCertForm.fileList = [];
                    this.addCertForm.fileSize = "";
                    return false
                } else {
                    this.addCertForm.fileList.push(file);
                    this.addCertForm.fileSize = 1;
                    this.$refs["addCertForm"].validateField('fileList');
                    this.uploadForm.append('certFile', file.raw);
                }
            },
            // bathDeleteCert() {
            //     let _this = this;
            //     this.$confirm('确定删除证书吗?', '删除确认', {
            //         confirmButtonText: '确定',
            //         cancelButtonText: '取消',
            //         type: 'warning'
            //     }).then(() => {
            //         if (this.delArray == "") {
            //             this.$alert("请选择删除项!", "信息提示", {
            //                 cancelButtonText: '取消',
            //                 type: 'warning'
            //             });
            //             return;
            //         }
            //         bussMG.bathDeleteCert(this.delArray).then(res => {
            //             _this.loadData()
            //         })
            //     });
            // },
            deleteById(id) {
                this.$confirm('确定删除证书吗?', '删除确认', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    bussMG.bathDeleteCert(id).then(() => {
                        this.loadData()
                    });
                });
            },
            // 证书详情
            queryById(id) {
                this.$refs.viewCertDia.initViewCertFun(id)
            },
            // 下载
            downloadCert(id) {
                bussMG.downloadCert(id).then(response => {
                    let blob = new Blob([response], {
                        type: 'application/force-download'
                    });
                    let fileName = Date.parse(new Date()) + '.cer';
                    if (window.navigator.msSaveOrOpenBlob) {
                        navigator.msSaveBlob(blob, fileName)
                    } else {
                        let link = document.createElement('a');
                        link.href = window.URL.createObjectURL(blob);
                        link.download = fileName;
                        link.click();
                        //释放内存
                        window.URL.revokeObjectURL(link.href)
                    }
                }).catch(err => {
                    console.log(err)
                })
            },
        },
        created() {
            let _this = this;
            this.digestEncAlgOption = this.digestEncAlgData.filter(item => {
                return item.type === "SM2";
            });
            // this.tableDataHeader = [
            //     {
            //         label: "证书ID",
            //         type: "normal",
            //         prop: "id",
            //         width: "70",
            //     }, {
            //         label: "证书主题",
            //         type: "normal",
            //         prop: "dn",
            //         width: '440',
            //     }, {
            //         label: "序列号",
            //         type: "normal",
            //         prop: "sn",
            //         width: "160",
            //     }, {
            //         label: "生效时间",
            //         type: "text_formatter",
            //         width: "120",
            //         prop: "startTime",
            //         formatter(value) {
            //             return value ? dateFormat("YYYY-mm-dd HH:MM:SS", new Date(value)) : '-';
            //         }
            //     }, {
            //         label: "失效时间",
            //         type: "text_formatter",
            //         width: "120",
            //         prop: "endTime",
            //         formatter(value) {
            //             return value ? dateFormat("YYYY-mm-dd HH:MM:SS", new Date(value)) : '-';
            //         }
            //     }, {
            //         label: "证书类型",
            //         type: "text_formatter",
            //         prop: "keyDesc",
            //         width: '90',
            //         formatter(value) {
            //             return value === 'SM2' ? "SM2" : "RSA";
            //         }
            //     }, {
            //         label: "证书来源",
            //         prop: "certSource",
            //         type: "text_formatter",
            //         width: "90",
            //         formatter(value) {
            //             return value === 1 ? "本地产生" : value === 3 ? "P10 请求" : "证书上传";
            //         }
            //     }, {
            //         label: "绑定应用",
            //         type: "text_formatter",
            //         // prop: "bindingApp",
            //         prop: "appName",
            //         width: "90",
            //         formatter(value) {
            //             return value ? value : "-";
            //         }
            //     }, {
            //         label: "用途",
            //         type: "text_formatter",
            //         prop: "usage",
            //         width: "90",
            //         formatter(value) {
            //             return usageFilterFun(value);
            //         }
            //     }, {
            //         label: "密钥索引",
            //         width: "80",
            //         type: "text_formatter",
            //         prop: "keyIndex",
            //         tableColumnAttributes: {
            //             align: "center",
            //             "header-align": "center"
            //         },
            //         formatter(value) {
            //             return value === "" || value === -1 ? '-' : value;
            //         }
            //     },
            //     // 是否双证和双证id是doubleCert 0是单证1是双证   doubleCertId是双证则有id  不是则为null
            //     {
            //         label: "双证ID",
            //         type: "text_formatter",
            //         prop: "doubleCert",
            //         width: "70",
            //         formatter(value, row) {
            //             return value === 0 ? "-" : row.doubleCertId;
            //         }
            //     }, {
            //         label: "操作",
            //         type: "operation",
            //         width: "210",
            //         fixed: "right",
            //         tag: [
            //             {
            //                 name: "修改",
            //                 operType: "update",
            //                 tagType: "el-button",
            //                 attributes: {
            //                     size: "mini",
            //                     type: "text",
            //                     icon: "el-icon-edit"
            //                 },
            //                 // isShow(row) {
            //                 //     return row.certSource !== 2;
            //                 // },
            //                 callback: function (row) {
            //                     _this.$refs.editCertDialog.initEditCertFun(row);
            //                 }
            //             }, {
            //                 name: "删除",
            //                 operType: "del",
            //                 tagType: "el-button",
            //                 attributes: {
            //                     size: "mini",
            //                     type: "text",
            //                     icon: "el-icon-edit"
            //                 },
            //                 callback: function (row) {
            //                     if (row.bindingApp === 1) {
            //                         _this.$alert("绑定应用的证书不能删除，如果要删除请先解绑。", "信息提示", {
            //                             cancelButtonText: '取消',
            //                             type: 'warning'
            //                         });
            //                     } else {
            //                         _this.deleteById(row.id);
            //                     }
            //                 }
            //             },
            //             // {
            //             //     name: "证书响应",
            //             //     operType: "response",
            //             //     tagType: "el-button",
            //             //     attributes: {
            //             //         size: "mini",
            //             //         type: "text",
            //             //         icon: "el-icon-refresh-right"
            //             //     },
            //             //     isShow(row) {
            //             //         return row.certResStatus === 2;
            //             //         // if (row.certResStatus == 2) return true;
            //             //         // else return false;
            //             //     },
            //             //     callback: function (row) {
            //             //         _this.answer = true;
            //             //         bussMG.queryById(row.id).then(res => {
            //             //             _this.addCertForm = Object.assign(_this.addCertForm, res.data);
            //             //             _this.keyChange(row.keyDesc);
            //             //             _this.addCertForm.keyIndex = res.data.keyIndex;
            //             //             _this.addCertForm.certType = res.data.certType;
            //             //             // _this.addCertForm.cerSource = 3;
            //             //             _this.addCertForm.optionType = 'add';
            //             //             _this.addCertForm.reqType = "responseCert";
            //             //             _this.addAppOpen = true;
            //             //         });
            //             //     }
            //             // },
            //             {
            //                 operType: "config",
            //                 tagType: "el-dropdown",
            //                 // isShow(row) {
            //                 //     return row.certResStatus !== 2;
            //                 //     // if (row.certSource != 3) return true;
            //                 //     // else if (row.certResStatus == 3) return true;
            //                 //     // else return false;
            //                 // },
            //                 attributes: {},
            //                 on: {
            //                     "command": (command, row) => {
            //                         let _this = this;
            //                         // let c = arguments;
            //                         let id = row.id;
            //                         if (command === "queryCert") {
            //                             _this.queryById(id);
            //                         } else if (command === "download") {
            //                             // 下载
            //                             _this.downloadCert(id);
            //                         } else if (command === "bindCert") {
            //                             // 应用绑定
            //                             _this.$refs.bindAppDialog.initBindAppFun(row);
            //                         } else if (command === "updateCert") {
            //                             // _this.resetAddCertForm();
            //                             _this.addCertForm.id = row.id;
            //                             // let bindingApp = row.bindingApp;
            //                             // if (bindingApp == 1) {
            //                             //     Message.info("更新证书请先解绑应用！");
            //                             //     return;
            //                             // }
            //                             // 根据证书来源判断弹窗
            //                             console.log(row);
            //                             // if (row.certSource === 3 || row.certSource === 1) {
            //                             if (row.certSource === 2) {
            //                                 _this.$refs.certImportDia.initCertImportFun(row.id, "updateCert")
            //                             } else {
            //                                 _this.addCertForm.optionType = "updateCert";
            //                                 _this.addCertForm.keyDesc = row.keyDesc;
            //                                 _this.addCertForm.digestEncAlg = row.digestEncAlg;
            //                                 _this.addCertForm.certSource = row.certSource;
            //                                 _this.addCertForm.dn = row.dn;
            //                                 _this.addCertForm.doubleCert = row.doubleCert;
            //                                 // _this.addCertForm.Usage =
            //                                 // if (row.keyDesc !== 'SM2') _this.addCertForm.Usage = JSON.parse(row.usage);
            //                                 _this.addCertForm.Usage = JSON.parse(row.usage);
            //                                 _this.addAppOpen = true;
            //                             }
            //                             // 更新证书
            //
            //                             if (row.certSource != 2) {
            //                                 _this.$nextTick(() => {
            //                                     _this.keyChange(row.keyDesc);
            //                                 });
            //                             }
            //                         } else if (command === "unbindCert") {
            //                             // 解绑应用
            //                             this.$confirm('确定要解绑吗?', '解绑确认', {
            //                                 confirmButtonText: '确定',
            //                                 cancelButtonText: '取消',
            //                                 type: 'warning'
            //                             }).then(() => {
            //                                 bussMG.unbindCert(row.id).then(res => {
            //                                     _this.refreshCert();
            //                                 });
            //                             });
            //                         } else if (command === "queryHistoryCert") {
            //                             // 查询历史证书列表
            //                             _this.$refs.historyCertListDia.initHistoryCertFun(id)
            //                         }
            //                     }
            //                 },
            //                 children: [
            //                     {
            //                         name: "配置证书",
            //                         tagType: "el-button",
            //                         attributes: {
            //                             size: "mini",
            //                             type: "text",
            //                             icon: "el-icon-setting"
            //                         },
            //                         // isShow(row) {
            //                         //     return row.certResStatus !== 2;
            //                         //     // if (row.certResStatus == 2) return true;
            //                         //     // else return false;
            //                         // },
            //                     },
            //                     {
            //                         tagType: "el-dropdown-menu",
            //                         attributes: {
            //                             slot: "dropdown"
            //                         },
            //                         children: [
            //                             // {
            //                             //     name: "应用绑定",
            //                             //     tagType: "el-dropdown-item",
            //                             //     attributes: {
            //                             //         command: "bindCert"
            //                             //     },
            //                             //     isShow(row) {
            //                             //         let certSource = row.certSource;
            //                             //         let certResStatus = row.certResStatus || 0;
            //                             //         if (row.appId || (certSource == 2) || (certSource == 3 && (certResStatus == 0 || certResStatus == 1))) return false;
            //                             //         else return true;
            //                             //     }
            //                             // }, {
            //                             //     name: "应用解绑",
            //                             //     tagType: "el-dropdown-item",
            //                             //     attributes: {
            //                             //         command: "unbindCert"
            //                             //     },
            //                             //     isShow(row) {
            //                             //         if (row.appId) return true;
            //                             //         else return false;
            //                             //     }
            //                             // },
            //                             {
            //                                 name: "查看证书",
            //                                 tagType: "el-dropdown-item",
            //                                 attributes: {
            //                                     command: "queryCert"
            //                                 }
            //                             }, {
            //                                 name: "更新证书",
            //                                 tagType: "el-dropdown-item",
            //                                 attributes: {
            //                                     command: "updateCert"
            //                                 }
            //                             }, {
            //                                 name: "下载证书",
            //                                 tagType: "el-dropdown-item",
            //                                 attributes: {
            //                                     command: "download"
            //                                 },
            //                                 isShow(row) {
            //                                     return row.certResStatus === 3 || row.certResStatus === '' || row.certResStatus === null;
            //                                 },
            //                             }, {
            //                                 name: "查看历史证书",
            //                                 tagType: "el-dropdown-item",
            //                                 attributes: {
            //                                     command: "queryHistoryCert"
            //                                 }
            //                             }
            //                         ]
            //                     }
            //                 ]
            //             }
            //         ]
            //     }
            // ];
            this.tableDataHeader = [
                {
                    label: "密钥索引",
                    type: "normal",
                    prop: "index",
                    width: "90",
                }, {
                    label: "证书ID",
                    type: "normal",
                    prop: "id",
                    width: '100',
                },{
                    label: "绑定应用名",
                    type: "normal",
                    width: "220",
                    prop: "appName",
                    // formatter(value) {
                    //     return value ? dateFormat("YYYY-mm-dd HH:MM:SS", new Date(value)) : '-';
                    // }
                }, {
                    label: "失效时间",
                    type: "text_formatter",
                    width: "120",
                    prop: "endTime",
                    formatter(value) {
                        return value ? dateFormat("YYYY-mm-dd HH:MM:SS", new Date(value)) : '-';
                    }
                }, {
                    label: "证书类型",
                    type: "text_formatter",
                    prop: "keyDesc",
                    width: '90',
                    formatter(value) {
                        return value === 'SM2' ? "SM2" : "RSA";
                    }
                }, {
                    label: "证书来源",
                    prop: "certSource",
                    type: "text_formatter",
                    width: "90",
                    formatter(value) {
                        return value === 1 ? "本地产生" : value === 3 ? "P10 请求" : "证书上传";
                    }
                }, {
                    label: "绑定应用",
                    type: "text_formatter",
                    // prop: "bindingApp",
                    prop: "appName",
                    width: "290",
                    formatter(value) {
                        return value ? value : "-";
                    }
                }, {
                    label: "绑定应用",
                    type: "normal",
                    prop: "is",
                    // width: "190",
                    // formatter(value) {
                    //     return usageFilterFun(value);
                    // }
                }, {
                    label: "生效时间",
                    type: "normal",
                    prop: "time1",
                    tableColumnAttributes: {
                        align: "center",
                        "header-align": "center"
                    },
                    // formatter(value) {
                    //     return value === "" || value === -1 ? '-' : value;
                    // }
                },{
                    label: "到期时间",
                    // width: "80",
                    type: "normal",
                    prop: "time2",
                    tableColumnAttributes: {
                        align: "center",
                        "header-align": "center"
                    },
                    // formatter(value) {
                    //     return value === "" || value === -1 ? '-' : value;
                    // }
                },
                {
                    label: "证书类型",
                    type: "normal",
                    prop: "certType",
                    // formatter(value) {
                    //     return usageFilterFun(value);
                    // }
                },
                // 是否双证和双证id是doubleCert 0是单证1是双证   doubleCertId是双证则有id  不是则为null
                // {
                //     label: "双证ID",
                //     type: "text_formatter",
                //     prop: "doubleCert",
                //     width: "70",
                //     formatter(value, row) {
                //         return value === 0 ? "-" : row.doubleCertId;
                //     }
                // },
                {
                    label: "操作",
                    type: "operation",
                    width: "210",
                    fixed: "right",
                    tag: [
                        {
                            name: "修改",
                            operType: "update",
                            tagType: "el-button",
                            attributes: {
                                size: "mini",
                                type: "text",
                                icon: "el-icon-edit"
                            },
                            // isShow(row) {
                            //     return row.certSource !== 2;
                            // },
                            callback: function (row) {
                                _this.$refs.editCertDialog.initEditCertFun(row);
                            }
                        }, {
                            name: "删除",
                            operType: "del",
                            tagType: "el-button",
                            attributes: {
                                size: "mini",
                                type: "text",
                                icon: "el-icon-edit"
                            },
                            callback: function (row) {
                                if (row.bindingApp === 1) {
                                    _this.$alert("绑定应用的证书不能删除，如果要删除请先解绑。", "信息提示", {
                                        cancelButtonText: '取消',
                                        type: 'warning'
                                    });
                                } else {
                                    _this.deleteById(row.id);
                                }
                            }
                        },
                        // {
                        //     name: "证书响应",
                        //     operType: "response",
                        //     tagType: "el-button",
                        //     attributes: {
                        //         size: "mini",
                        //         type: "text",
                        //         icon: "el-icon-refresh-right"
                        //     },
                        //     isShow(row) {
                        //         return row.certResStatus === 2;
                        //         // if (row.certResStatus == 2) return true;
                        //         // else return false;
                        //     },
                        //     callback: function (row) {
                        //         _this.answer = true;
                        //         bussMG.queryById(row.id).then(res => {
                        //             _this.addCertForm = Object.assign(_this.addCertForm, res.data);
                        //             _this.keyChange(row.keyDesc);
                        //             _this.addCertForm.keyIndex = res.data.keyIndex;
                        //             _this.addCertForm.certType = res.data.certType;
                        //             // _this.addCertForm.cerSource = 3;
                        //             _this.addCertForm.optionType = 'add';
                        //             _this.addCertForm.reqType = "responseCert";
                        //             _this.addAppOpen = true;
                        //         });
                        //     }
                        // },
                        {
                            operType: "config",
                            tagType: "el-dropdown",
                            // isShow(row) {
                            //     return row.certResStatus !== 2;
                            //     // if (row.certSource != 3) return true;
                            //     // else if (row.certResStatus == 3) return true;
                            //     // else return false;
                            // },
                            attributes: {},
                            on: {
                                "command": (command, row) => {
                                    let _this = this;
                                    // let c = arguments;
                                    let id = row.id;
                                    if (command === "queryCert") {
                                        _this.queryById(id);
                                    } else if (command === "download") {
                                        // 下载
                                        _this.downloadCert(id);
                                    } else if (command === "bindCert") {
                                        // 应用绑定
                                        _this.$refs.bindAppDialog.initBindAppFun(row);
                                    } else if (command === "updateCert") {
                                        // _this.resetAddCertForm();
                                        _this.addCertForm.id = row.id;
                                        // let bindingApp = row.bindingApp;
                                        // if (bindingApp == 1) {
                                        //     Message.info("更新证书请先解绑应用！");
                                        //     return;
                                        // }
                                        // 根据证书来源判断弹窗
                                        console.log(row);
                                        // if (row.certSource === 3 || row.certSource === 1) {
                                        if (row.certSource === 2) {
                                            _this.$refs.certImportDia.initCertImportFun(row.id, "updateCert")
                                        } else {
                                            _this.addCertForm.optionType = "updateCert";
                                            _this.addCertForm.keyDesc = row.keyDesc;
                                            _this.addCertForm.digestEncAlg = row.digestEncAlg;
                                            _this.addCertForm.certSource = row.certSource;
                                            _this.addCertForm.dn = row.dn;
                                            _this.addCertForm.doubleCert = row.doubleCert;
                                            // _this.addCertForm.Usage =
                                            // if (row.keyDesc !== 'SM2') _this.addCertForm.Usage = JSON.parse(row.usage);
                                            _this.addCertForm.Usage = JSON.parse(row.usage);
                                            _this.addAppOpen = true;
                                        }
                                        // 更新证书

                                        if (row.certSource != 2) {
                                            _this.$nextTick(() => {
                                                _this.keyChange(row.keyDesc);
                                            });
                                        }
                                    } else if (command === "unbindCert") {
                                        // 解绑应用
                                        this.$confirm('确定要解绑吗?', '解绑确认', {
                                            confirmButtonText: '确定',
                                            cancelButtonText: '取消',
                                            type: 'warning'
                                        }).then(() => {
                                            bussMG.unbindCert(row.id).then(res => {
                                                _this.refreshCert();
                                            });
                                        });
                                    } else if (command === "queryHistoryCert") {
                                        // 查询历史证书列表
                                        _this.$refs.historyCertListDia.initHistoryCertFun(id)
                                    }
                                }
                            },
                            children: [
                                {
                                    name: "配置证书",
                                    tagType: "el-button",
                                    attributes: {
                                        size: "mini",
                                        type: "text",
                                        icon: "el-icon-setting"
                                    },
                                    // isShow(row) {
                                    //     return row.certResStatus !== 2;
                                    //     // if (row.certResStatus == 2) return true;
                                    //     // else return false;
                                    // },
                                },
                                {
                                    tagType: "el-dropdown-menu",
                                    attributes: {
                                        slot: "dropdown"
                                    },
                                    children: [
                                        // {
                                        //     name: "应用绑定",
                                        //     tagType: "el-dropdown-item",
                                        //     attributes: {
                                        //         command: "bindCert"
                                        //     },
                                        //     isShow(row) {
                                        //         let certSource = row.certSource;
                                        //         let certResStatus = row.certResStatus || 0;
                                        //         if (row.appId || (certSource == 2) || (certSource == 3 && (certResStatus == 0 || certResStatus == 1))) return false;
                                        //         else return true;
                                        //     }
                                        // }, {
                                        //     name: "应用解绑",
                                        //     tagType: "el-dropdown-item",
                                        //     attributes: {
                                        //         command: "unbindCert"
                                        //     },
                                        //     isShow(row) {
                                        //         if (row.appId) return true;
                                        //         else return false;
                                        //     }
                                        // },
                                        {
                                            name: "查看证书",
                                            tagType: "el-dropdown-item",
                                            attributes: {
                                                command: "queryCert"
                                            }
                                        }, {
                                            name: "更新证书",
                                            tagType: "el-dropdown-item",
                                            attributes: {
                                                command: "updateCert"
                                            }
                                        }, {
                                            name: "下载证书",
                                            tagType: "el-dropdown-item",
                                            attributes: {
                                                command: "download"
                                            },
                                            isShow(row) {
                                                return row.certResStatus === 3 || row.certResStatus === '' || row.certResStatus === null;
                                            },
                                        }, {
                                            name: "查看历史证书",
                                            tagType: "el-dropdown-item",
                                            attributes: {
                                                command: "queryHistoryCert"
                                            }
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ];
            this.loadData();
            // this.getNotUsedKey();
        }
    }
</script>
<style lang="less" scoped>
    .el-divider--horizontal {
        margin: 8px 0;
        background: 0 0;
        border-top: 1px dashed #e8eaec;
    }

    .user-search .el-form-item {
        margin-bottom: 0;
        /*margin-top: 10px;*/
    }

    /deep/ .el-descriptions-item__label.is-bordered-label {
        width: 130px;
    }
</style>

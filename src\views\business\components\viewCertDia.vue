<template>
    <div>
        <el-dialog title="证书信息" :visible.sync="queryCertInfo" width="600px" append-to-body :close-on-click-modal="false">
            <el-descriptions class="margin-top" :column="1" border>
                <el-descriptions-item label="签发者">{{certEntityInfo.issuer}}</el-descriptions-item>
                <el-descriptions-item label="签发给">{{certEntityInfo.dn}}</el-descriptions-item>
                <el-descriptions-item label="序列号">{{ certEntityInfo.sn }}</el-descriptions-item>
                <el-descriptions-item label="有效期">{{certEntityInfo.dateStr}}</el-descriptions-item>
                <el-descriptions-item label="证书类型">{{certEntityInfo.keyDesc}}</el-descriptions-item>
                <el-descriptions-item label="哈希算法">{{certEntityInfo.digestEncAlg}}</el-descriptions-item>
            </el-descriptions>
            <div slot="footer" class="dialog-footer">
                <el-button size="small" class="comBtn com_send_btn" type="primary" @click="queryCertInfo=false">确 认</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    import bussMG from "@/api/bussMG";

    export default {
        data() {
            return {
                queryCertInfo: false,
                certEntityInfo: {}
            }
        },
        methods: {
            initViewCertFun(id) {
                this.queryCertInfo = true;
                bussMG.queryById(id).then(res => this.certEntityInfo = res.data)
            }
        }
    }
</script>

<style scoped>

</style>

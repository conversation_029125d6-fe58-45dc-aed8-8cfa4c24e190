<template>
  <el-dialog
    :title="title"
    :visible.sync="addVXLANShow"
    width="550px"
    append-to-body
    :destroy-on-close="true"
    :close-on-click-modal="false"
    @close="closeAddAppDia"
  >
    <el-form
      ref="addVXLANFrom"
      :model="addVXLANFrom"
      :rules="rules"
      label-width="170px"
      size="small"
    >
      <el-form-item label="连接名：" prop="vxlanName">
        <el-input
          v-model="addVXLANFrom.vxlanName"
          placeholder="请输入连接名"
          clearable
        />
      </el-form-item>
      <el-form-item label="VNI：" prop="vni">
        <el-input
          v-model="addVXLANFrom.vni"
          placeholder="请输入VNI"
          clearable
        />
      </el-form-item>
      <el-form-item label="VTEP通信端囗：" prop="vtepPort">
        <el-input
          v-model="addVXLANFrom.vtepPort"
          placeholder="请输入VTEP通信端囗"
          clearable
        />
      </el-form-item>
      <el-form-item label="当前节点VTEP地址：" prop="localVtepIp">
        <el-input
          v-model="addVXLANFrom.localVtepIp"
          placeholder="请输入当前节点VTEP地址"
          clearable
        />
      </el-form-item>
      <el-form-item label="对端VTEP地址：" prop="remoteVtepIp">
        <el-input
          v-model="addVXLANFrom.remoteVtepIp"
          placeholder="请输入对端VTEP地址"
          clearable
        />
      </el-form-item>
      <el-form-item label="当前VXLAN网络IP：" prop="vxlanIp">
        <el-input
          v-model="addVXLANFrom.vxlanIp"
          placeholder="请输入当前VXLAN网络IP"
          clearable
        />
      </el-form-item>
      <el-form-item label="当前VXLAN子网掩码：" prop="localVtepMask">
        <el-input
          v-model="addVXLANFrom.localVtepMask"
          placeholder="请输入当前VXLAN子网掩码"
          clearable
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button
        class="comBtn com_reset_btn"
        size="small"
        @click="closeAddAppDia"
        >取 消</el-button
      >
      <el-button
        class="comBtn com_send_btn"
        size="small"
        :loading="submitLoading"
        @click="submitForm"
        >确 定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
export default {
  data() {
    const validatorIp = (rule, value, callback) => {
      if (value !== "") {
        let ip =
          /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
        if (!ip.test(value)) {
          callback(new Error("请输入正确的IP!"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    const validatorName = (rule, value, callback) => {
      if (value !== "") {
        let ip = /^[a-zA-Z0-9]\w{3,14}$/;
        if (!ip.test(value)) {
          callback(
            new Error('格式错误, 4-15个字符(可包含"_", 但不能以"_"开头)!')
          );
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    const validatorMask = (rule, value, callback) => {
      if (value !== "") {
        let mask =
          /^(254|252|248|240|224|192|128|0)\.0\.0\.0|255\.(254|252|248|240|224|192|128|0)\.0\.0|255\.255\.(254|252|248|240|224|192|128|0)\.0|255\.255\.255\.(254|252|248|240|224|192|128|0)$/;
        if (!mask.test(value)) {
          callback(new Error("请输入正确的VXLAN子网掩码！"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    return {
      addVXLANShow: false,
      title: "新增",
      keyIndexArr: [],
      isEdit: false,
      addVXLANFrom: {
        vxlanName: "",
        vni: "",
        vtepPort: "",
        localVtepIp: "",
        remoteVtepIp: "",
        vxlanIp: "",
        localVtepMask: "",
      },
      submitLoading: false,
      rules: {
        vxlanName: [
          { required: true, message: "连接名不能为空", trigger: "blur" },
          { validator: validatorName, trigger: "blur" },
        ],
        vni: [
          { required: true, message: "VNI不能为空", trigger: "blur" },
          {
            pattern: /^(?:[1-9]\d{0,6}|0)$|^16777215$/,
            message: "只能输入0-16777215之间的正整数",
            trigger: "blur",
          },
        ],
        vtepPort: [
          { required: true, message: "VTEP通信端囗不能为空", trigger: "blur" },
          {
            pattern:
              /^(?:[1-9]\d{3}|[1-3]\d{4}|4[0-8]\d{3}|490\d{2}|491[0-4]\d|4915[0-1])$/,
            message: "只能输入1024-49151之间的正整数",
            trigger: "blur",
          },
        ],
        localVtepIp: [
          {
            required: true,
            message: "当前节点VTEP地址不能为空",
            trigger: "blur",
          },
          { validator: validatorIp, trigger: "blur" },
        ],
        remoteVtepIp: [
          { required: true, message: "对端VTEP地址不能为空", trigger: "blur" },
          { validator: validatorIp, trigger: "blur" },
        ],
        vxlanIp: [
          {
            required: true,
            message: "当前VXLAN网络IP不能为空",
            trigger: "blur",
          },
          { validator: validatorIp, trigger: "blur" },
        ],
        localVtepMask: [
          {
            required: true,
            message: "当前VXLAN子网掩码不能为空",
            trigger: "blur",
          },
          { validator: validatorMask, trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    addVXLANInit(data, type) {
      this.addVXLANShow = true;
      if (type === "edit") {
        this.isEdit = true;
        this.title = "编辑VXLAN配置";
        this.addVXLANFrom.localVtepMask = data.vxlanMask;
        Object.assign(this.addVXLANFrom, data);
      } else {
        this.title = "新增VXLAN配置";
        this.$nextTick(() => {
          this.$refs["addVXLANFrom"].resetFields();
        });
      }
    },
    submitForm() {
      this.$refs["addVXLANFrom"].validate((valid) => {
        if (!valid) return;
        this.submitLoading = true;
        if (this.isEdit) {
          this.$http.systemMG
            .editVXLAN(JSON.stringify(this.addVXLANFrom))
            .then(({ code, data, msg }) => {
              this.submitLoading = false;
              if (code === 0) {
                this.$message.success(msg);
                this.$emit("parentHandle");
                this.closeAddAppDia();
              }
            });
        } else {
          this.$http.systemMG
            .addVXLAN(JSON.stringify(this.addVXLANFrom))
            .then(({ code, data, msg }) => {
              this.submitLoading = false;
              if (code === 0) {
                this.$message.success(msg);
                this.$emit("parentHandle");
                this.closeAddAppDia();
              }
            });
        }
      });
    },
    closeAddAppDia() {
      this.addVXLANShow = false;
      this.addVXLANFrom = {
        vxlanName: "",
        vni: "",
        vtepPort: "",
        localVtepIp: "",
        remoteVtepIp: "",
        vxlanIp: "",
        localVtepMask: "",
      };
    },
  },
};
</script>

<style scoped></style>

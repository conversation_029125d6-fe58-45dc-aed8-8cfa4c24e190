<template>
  <div>
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span style="font-size: 18; font-weight: 600;">配置文件设置</span>
      </div>
      <el-form label-width="160px" ref="configForm" :model="configForm" size="small">
        <el-form-item label="服务密码机：" prop="hsm">
          <el-select v-model="configForm.hsm" placeholder="请选择" size="small">
            <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="协同签名服务器：" prop="cds">
          <el-select v-model="configForm.cds" placeholder="请选择" size="small">
            <el-option v-for="item in levelOptions" v-if="item.value!='sdf'" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="签名验签服务器：" prop="svs">
          <el-select v-model="configForm.svs" placeholder="请选择" size="small">
            <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="时间戳服务器：" prop="tsa">
          <el-select v-model="configForm.tsa" placeholder="请选择" size="small">
            <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button class="comBtn com_send_btn" size="small" @click="save">设置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
export default {
  name: "manage-log",
  data() {
    return {
        configForm: {
            hsm: '',
            cds: '',
            svs: '',
            tsa: ''
        },
        levelOptions: [
          { label: "hct", value: "hct" },
          { label: "soft", value: "soft" },
          { label: "sdf", value: "sdf" }
        ],
    }
  },
  methods: {
    searchConfig() {
      this.$http.oamMG.configGet().then(res => {
        if (res.code === 0) {
          this.configForm = res.data;
        }
      })
    },
    save() {
        this.$http.oamMG.configEdit(JSON.stringify(this.configForm)).then(({code, msg}) => {
        if (code == 0) {
          this.$message.success(msg);
        }
      })
    }
  },
  mounted() {
    this.searchConfig();
  }
}
</script>
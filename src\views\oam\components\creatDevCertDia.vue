<template>
    <el-dialog title="生成设备证书申请" :visible.sync="show" width="35%" :before-close="closeCertApply"
        :close-on-click-modal="false">
        <el-form label-width="140px" ref="certForm" :model="certForm" :rules="rules" style="padding-right: 40px">
            <el-form-item label="证书算法：">
                <el-input size="small" value="国密SM2算法" readOnly="true" />
            </el-form-item>
            <!--<el-form-item label="国密证书类型：" prop="certType">-->
            <!--<el-select v-model="certForm.certType" placeholder="请选择" style="width: 100%" size="small">-->
            <!--<el-option v-for="item in certTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>-->
            <!--</el-select>-->
            <!--</el-form-item>-->
            <el-form-item label="CA：">
                <el-input size="small" value="国密签名CA" readOnly="true" />
            </el-form-item>
            <el-form-item label="国家/C：" prop="countryCode">
                <el-input size="small" value="CN" readOnly="true" />
            </el-form-item>
            <el-form-item label="省份/ST：" prop="province">
                <el-input size="small" v-model="certForm.province" placeholder="请输入省份" />
            </el-form-item>
            <el-form-item label="城市/L：" prop="city">
                <el-input size="small" v-model="certForm.city" placeholder="请输入城市" />
            </el-form-item>
            <el-form-item label="组织/O：" prop="organization">
                <el-input size="small" v-model="certForm.organization" placeholder="请输入组织" />
            </el-form-item>
            <el-form-item label="部门/OU：" prop="organizationalUnit">
                <el-input size="small" v-model="certForm.organizationalUnit" placeholder="请输入部门" />
            </el-form-item>
            <el-form-item label="主题名称/CN：" prop="commonName">
                <el-input size="small" v-model="certForm.commonName" placeholder="请输入主题名称" />
            </el-form-item>
            <el-form-item label="用户邮箱：" prop="sendMail">
                <el-input size="small" v-model="certForm.sendMail" placeholder="请输入用户邮箱" />
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button class="comBtn com_reset_btn" size="small" @click="closeCertApply">取消</el-button>
            <el-button class="comBtn com_send_btn" size="small" :loading="loginLoading" type="primary"
                @click="requestCert('certForm')">申请证书</el-button>
        </div>
    </el-dialog>
</template>

<script>
import oamMG from "@/api/oamMG";
export default {
    data() {
        return {
            paramsTit: '生成设备证书申请',
            show: false,
            certAlgArr: [
                { label: '国密SM2算法', value: 0 }
            ],
            // certTypeOptions: [
            //     {label: '签名证书', value: 0},
            //     {label: '加密证书', value: 1}
            // ],
            selCaArr: [
                { label: '国家签名CA', value: 0 }
            ],
            selCountryArr: [
                { label: 'CN', value: 0 }
            ],
            certForm: {
                certType: 0,
                countryCode: 'CN',
                province: '',
                city: '',
                organization: '',
                organizationalUnit: '',
                commonName: '',
                sendMail: ''
            },
            loginLoading: false,
            rules: {
                commonName: [
                    { required: true, message: '请输入主题名称', trigger: 'blur' }
                ],
                // certType: [
                //     {required: true, message: '请输入主题名称', trigger: 'change'}
                // ]
            }
        }
    },
    methods: {
        initDialog() {
            this.show = true;
        },
        // 申请证书
        requestCert(name) {
            let _this = this;
            this.$refs[name].validate(valid => {
                if (valid) {
                    this.loginLoading = true;
                    this.$http.oamMG.generateDeviceCertApply(this.certForm).then(res => {
                        let reader = new FileReader();
                        reader.readAsText(res, 'utf-8');
                        reader.onload = function (e) {
                            try {
                                const { code, msg } = JSON.parse(e.target.result);
                                _this.$message({
                                    message: msg || 'Error',
                                    type: 'error',
                                    // 如果需要，可以打开注释或根据实际需求进行设置
                                    // duration: 5 * 1000
                                });
                                _this.loginLoading = false;
                            } catch (err) {
                                let blob = new Blob([res], {
                                    type: 'application/force-download'
                                });
                                let fileName = _this.certForm.commonName + '.pem';
                                if (window.navigator.msSaveOrOpenBlob) {
                                    navigator.msSaveBlob(blob, fileName)
                                } else {
                                    let link = document.createElement('a');
                                    link.href = window.URL.createObjectURL(blob);
                                    link.download = fileName;
                                    link.click();
                                    //释放内存
                                    window.URL.revokeObjectURL(link.href)
                                }
                                _this.closeCertApply();
                                _this.show = false;
                                _this.loginLoading = false;
                            }
                        }
                    })
                }
            })
        },
        closeCertApply() {
            this.show = false;
            this.$refs["certForm"].resetFields();
            Object.assign(this.$data.certForm, this.$options.data().certForm);
        },
    }
}
</script>

<style lang="less" scoped>
/deep/ .el-form-item {
    /*width: 90%;*/
    margin-bottom: 18px;
}

/deep/ .el-dialog {
    margin-top: 8vh !important;
}
</style>

// var default_dn = "/CN=www.shudun.com/O=Shudun/OU=IT/ST=BJ/L=BJ/C=CN/emailAddress=<EMAIL>";
// var default_userPIN = "111111";
// var default_cert = "";
// var default_oriData = "1234567890";

// window.onload = function() {
// 	document.getElementById('clear_ori').onclick = function() {
// 		document.getElementById('textarea_send').value = "";
// 	};
// 	document.getElementById('clear_ret').onclick = function() {
// 		document.getElementById('textarea_receive').value = "";
// 	};
// 	document.getElementById('clear_dev').onclick = function() {
// 		document.getElementById('textarea_devinfo').value = "";
// 	};
// }



const Base64 = {
    _keyStr: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",
    encode: function (e) {
        let t = "";
        let n, r, i, s, o, u, a;
        let f = 0;
        e = Base64._utf8_encode(e);
        while (f < e.length) {
            n = e.charCodeAt(f++);
            r = e.charCodeAt(f++);
            i = e.charCodeAt(f++);
            s = n >> 2;
            o = (n & 3) << 4 | r >> 4;
            u = (r & 15) << 2 | i >> 6;
            a = i & 63;
            if (isNaN(r)) {
                u = a = 64
            } else if (isNaN(i)) {
                a = 64
            }
            t = t + this._keyStr.charAt(s) + this._keyStr.charAt(o) + this._keyStr.charAt(u) + this._keyStr.charAt(a)
        }
        return t
    },
    decode: function (e) {
        let t = "";
        let n, r, i;
        let s, o, u, a;
        let f = 0;
        e = e.replace(/[^A-Za-z0-9+/=]/g, "");
        while (f < e.length) {
            s = this._keyStr.indexOf(e.charAt(f++));
            o = this._keyStr.indexOf(e.charAt(f++));
            u = this._keyStr.indexOf(e.charAt(f++));
            a = this._keyStr.indexOf(e.charAt(f++));
            n = s << 2 | o >> 4;
            r = (o & 15) << 4 | u >> 2;
            i = (u & 3) << 6 | a;
            t = t + String.fromCharCode(n);
            if (u != 64) {
                t = t + String.fromCharCode(r)
            }
            if (a != 64) {
                t = t + String.fromCharCode(i)
            }
        }
        t = Base64._utf8_decode(t);
        return t
    },
    _utf8_encode: function (e) {
        e = e.replace(/rn/g, "n");
        var t = "";
        for (var n = 0; n < e.length; n++) {
            var r = e.charCodeAt(n);
            if (r < 128) {
                t += String.fromCharCode(r)
            } else if (r > 127 && r < 2048) {
                t += String.fromCharCode(r >> 6 | 192);
                t += String.fromCharCode(r & 63 | 128)
            } else {
                t += String.fromCharCode(r >> 12 | 224);
                t += String.fromCharCode(r >> 6 & 63 | 128);
                t += String.fromCharCode(r & 63 | 128)
            }
        }
        return t
    },
    _utf8_decode: function (e) {
        let t = "";
        let n = 0;
        let r = 0;
        let c1 = 0;
        let c2 = 0;
        while (n < e.length) {
            r = e.charCodeAt(n);
            if (r < 128) {
                t += String.fromCharCode(r);
                n++
            } else if (r > 191 && r < 224) {
                c2 = e.charCodeAt(n + 1);
                t += String.fromCharCode((r & 31) << 6 | c2 & 63);
                n += 2
            } else {
                c2 = e.charCodeAt(n + 1);
                let c3 = e.charCodeAt(n + 2);
                t += String.fromCharCode((r & 15) << 12 | (c2 & 63) << 6 | c3 & 63);
                n += 3
            }
        }
        return t
    }
};

var show = function () {
    var pin = document.getElementById("input_pin");
    if (pin.getAttribute("type") == "password") {
        pin.type = "";
    } else {
        pin.type = "password";
    }
};

const ajax = function (type, url, reqbody, callback) {
    const xhr = new XMLHttpRequest();
    xhr.onreadystatechange = function () {
        if (xhr.readyState === XMLHttpRequest.DONE) {
            if (xhr.status === 200) {
                callback(xhr.responseText);
            } else {
                // alert("服务异常");
                const err = {
                    "errCode": 400
                };
                console.log(err);
                callback(JSON.stringify(err));
            }
        }
    };
    if (type === 'POST') {
        xhr.open(type, 'http://127.0.0.1:27015' + url, true);
        xhr.setRequestHeader('Content-type', 'text/plain');
        xhr.send(reqbody);
    }
    if (type === 'GET') {
        if (!reqbody) {
            xhr.open(type, 'http://127.0.0.1:27015' + url, true);
        } else {
            xhr.open(type, 'http://127.0.0.1:27015' + url + "?" + encodeURI(reqbody), true);
        }
        xhr.send();
    }
};

function testGetKeyState(callback) {
    ajax('GET', '/getUkeyState', null, function (data) {
        let recvJson = JSON.parse(data);
        // objtext=recvJson
        callback(recvJson)
    });
}


const testGetKeyInfo = function (callback) {
    // var str = "";
    let sendJson = {};
    let reqbody = JSON.stringify(sendJson);
    console.log("/getUkeyInfo send:" + reqbody);
    ajax('GET', '/getUkeyInfo', reqbody, function (data) {

        let recvJson = JSON.parse(data);
        callback(recvJson)
        // console.log("/getUkeyInfo receive:" + JSON.stringify(recvJson));
        // str += ("version:" + recvJson.version + "\n");
        // str += ("manufacturer:" + recvJson.manufacturer + "\n");
        // str += ("issuer:" + recvJson.issuer + "\n");
        // str += ("label:" + recvJson.label + "\n");
        // str += ("serialNumber:" + recvJson.serialNumber + "\n");
        // str += ("hwVersion:" + recvJson.hwVersion + "\n");
        // str += ("firmwareVersion:" + recvJson.firmwareVersion + "\n");
        // str += ("algSymCap:" + recvJson.algSymCap + "\n");
        // str += ("algAsymCap:" + recvJson.algAsymCap + "\n");
        // str += ("algHashCap:" + recvJson.algHashCap + "\n");
        // str += ("devAuthAlgId:" + recvJson.devAuthAlgId + "\n");
        // str += ("totalSpace:" + recvJson.totalSpace + "\n");
        // str += ("freeSpace:" + recvJson.freeSpace + "\n");
        // str += ("maxECCBufferSize:" + recvJson.maxECCBufferSize + "\n");
        // str += ("maxBufferSize:" + recvJson.maxBufferSize + "\n");
        // str += ("reserved:" + recvJson.reserved + "\n");
        // document.getElementById("input_errcode").value = recvJson.errCode;
        // document.getElementById("textarea_devinfo").value = str;
    });
};

const testInitKey = function () {
    let pinNode = document.getElementById('input_pin');
    if (pinNode.value.length == 0) {
        alert('pin不能为空');
        return;
    }
    let sendJson = {
        "userPIN": pinNode.value
    };
    let reqbody = JSON.stringify(sendJson);
    console.log("/initKey send:" + reqbody);
    ajax('POST', '/initKey', reqbody, function (data) {
        let recvJson = JSON.parse(data);
        console.log("/initKey receive:" + JSON.stringify(recvJson));
        document.getElementById("input_errcode").value = recvJson.errCode;
    });
};

const testGetPublicKey = function () {
    let pinNode = document.getElementById('input_pin');
    if (pinNode.value.length == 0) {
        alert('pin不能为空');
        return;
    }
    let sendJson = {
        "userPIN": pinNode.value,
    };
    let reqbody = JSON.stringify(sendJson);
    console.log("/createKeyPair send:" + reqbody);
    ajax('POST', '/createKeyPair', reqbody, function (data) {
        let recvJson = JSON.parse(data);
        console.log("/createKeyPair receive:" + JSON.stringify(recvJson));
        document.getElementById("textarea_receive").value = recvJson.b64PublicKey;
        document.getElementById("input_errcode").value = recvJson.errCode;
    });
};

var testGetCSR = function (pinNode, dnNode, callback) {
    if (pinNode.length == 0 || dnNode.length == 0) {
        alert('pin和DN值不能为空');
        return;
    }
    var sendJson = {
        "userPIN": pinNode,
        "dn": dnNode
    };
    var reqbody = JSON.stringify(sendJson);
    console.log("/createCSR send:" + reqbody);
    ajax('POST', '/createCSR', reqbody, function (data) {
        var recvJson = JSON.parse(data);
        console.log("/createCSR receive:" + JSON.stringify(recvJson));
        callback(recvJson);
        // document.getElementById("textarea_receive").value = recvJson.b64CertCSR;
        // document.getElementById("input_errcode").value = recvJson.errCode;
    });
};

var testImportCert = function (pinNode, certNode, callback) {
    if (pinNode.length == 0 || certNode.length == 0) {
        alert('pin和证书数据不能为空');
        return;
    }
    var sendJson = {
        "userPIN": pinNode,
        "b64CertData": certNode
    };
    var reqbody = JSON.stringify(sendJson);
    console.log("/importCert send:" + reqbody);
    ajax('POST', '/importCert', reqbody, function (data) {
        var recvJson = JSON.parse(data);
        callback(recvJson);
        // console.log("/importCert receive:" + JSON.stringify(recvJson));
        // document.getElementById("input_errcode").value = recvJson.errCode;
    });
};

function testExportCert(callback) {
    ajax('GET', '/exportCert', null, function (data) {
        /*var recvJson = JSON.parse(data);
        console.log("/exportCert receive:" + JSON.stringify(recvJson));
        document.getElementById('textarea_receive').value = recvJson.b64CertData;
        document.getElementById("input_errcode").value = recvJson.errCode;*/
        const parse = JSON.parse(data);
        callback(parse)
    });
}

var testGetContainerList = function () {
    ajax('GET', '/getContainerList', null, function (data) {
        var recvJson = JSON.parse(data);
        console.log("/getContainerList receive:" + JSON.stringify(recvJson));
        document.getElementById('textarea_receive').value = recvJson.containerList;
        document.getElementById("input_errcode").value = recvJson.errCode;
    });
};

function testBase64Encode(index) {
    var oriDataNode = document.getElementById('textarea_send');
    // var b64Node = document.getElementById('textarea_receive');
    // b64Node.value = Base64.encode(oriDataNode.value);
    // console.log(Base64.encode(oriDataNode.value))
    return Base64.encode(oriDataNode.value)
}

var testBase64Decode = function () {
    var oriDataNode = document.getElementById('textarea_send');
    var b64Node = document.getElementById('textarea_receive');
    b64Node.value = Base64.decode(oriDataNode.value);
};


function testSignData(pass, yuan, callback) {
    // var pinNode = document.getElementById('input_pin');
    // var oriDataNode = document.getElementById('textarea_send');
    if (pass.length == 0 || yuan.length == 0) {
        alert('签名原文和pin不能为空');
        return;
    }
    var sendJson = {
        "userPIN": pass,
        "oriData": yuan
    };
    var reqbody = JSON.stringify(sendJson);

    ajax('POST', '/signData', reqbody, function (data) {
        var recvJson = JSON.parse(data);
        callback(recvJson)

    });
}

var testVerifyData = function () {
    var oriDataNode = document.getElementById('textarea_send');
    var signDataNode = document.getElementById('textarea_receive');
    if (oriDataNode.value.length == 0 || signDataNode.value.length == 0) {
        alert('签名原文和签名值不能为空');
        return;
    }
    var sendJson = {
        "oriData": oriDataNode.value,
        "b64SignData": signDataNode.value
    };
    var reqbody = JSON.stringify(sendJson);
    console.log("/verifyData send:" + reqbody);
    ajax('POST', '/verifyData', reqbody, function (data) {
        var recvJson = JSON.parse(data);
        console.log("/verifyData receive:" + JSON.stringify(recvJson));
        document.getElementById("input_errcode").value = recvJson.errCode;
    });
}

var testGetFileList = function () {
    ajax('GET', '/getFileList', null, function (data) {
        console.log(data);
        var recvJson = JSON.parse(data);
        console.log("/getFileList receive:" + JSON.stringify((recvJson)));
        document.getElementById("textarea_read").value = recvJson.fileList;
        document.getElementById("input_errcode").value = recvJson.errCode;
    })
}
var filetext;
var pintext

function testCreateFile(index, callback) {
    var now = new Date();
    var year = now.getFullYear(); //得到年份
    var month = now.getMonth() + 1;//得到月份
    var date = now.getDate();//得到日期
    var hour = now.getHours();//得到小时
    var minu = now.getMinutes();//得到分钟
    var sec = now.getSeconds();//得到秒
    var fileName = index.filename + year + month + date + hour + minu + sec;
    var pinNode = index.password;
    filetext = fileName
    pintext = pinNode
    var fileSize = index.size;
    var readright = index.user;
    var writeright = index.user;
    if (fileName == "" || pinNode == "" || fileSize == "") {
        alert('文件名、文件大小和pin不能为空');
        return;
    }
    if (readright !== "user" && readright !== "admin" && readright !== "anyone") {
        alert('读权限输入错误，请输入user、admin或者anyone');
        return;
    }
    if (writeright !== "user" && writeright !== "admin" && writeright !== "anyone") {
        alert('写权限输入错误，请输入user、admin或者anyone');
        return;
    }
    var sendJson = {
        "fileName": fileName,
        "userPIN": pinNode,
        "fileSize": fileSize,
        "readRight": readright,
        "writeRight": writeright
    };
    var reqbody = JSON.stringify(sendJson);
    ajax('POST', '/createFile', reqbody, function (data) {
        var recvJson = JSON.parse(data);
        callback(data)
        // var recvJson = JSON.parse(data);
    })
}

function testDeleteFile(index, callback) {
    var fileName = fileName
    var pinNode = pinNode;
    if (fileName == "" || pinNode == "") {
        alert('文件名和pin不能为空');
        return;
    }
    var sendJson = {
        "fileName": fileName,
        "userPIN": pinNode
    };
    var reqbody = JSON.stringify(sendJson);
    ajax('POST', '/deleteFile', reqbody, function (data) {
        callback(data)
    })
}

var testGetFileMsg = function () {
    var fileName = document.getElementById('input_filename');
    if (fileName.value.length == 0) {
        alert('文件名不能为空');
        return;
    }
    var sendJson = {
        "fileName": fileName.value
    };
    var reqbody = JSON.stringify(sendJson);
    console.log("/getFileInfo send:" + reqbody);
    ajax('GET', '/getFileInfo', reqbody, function (data) {
        var recvJson = JSON.parse(data);
        console.log("/getFileInfo receive:" + JSON.stringify((recvJson)));
        document.getElementById("input_filesize").value = recvJson.fileSize;
        document.getElementById("input_readright").value = recvJson.readRight;
        document.getElementById("input_writeright").value = recvJson.writeRight;
        document.getElementById("input_errcode").value = recvJson.errCode;
    })
}

function testReadFile(callback) {
    var sendJson = {
        "fileName": "key",
        "userPIN": '111111'
    };
    var reqbody = JSON.stringify(sendJson);
    ajax('GET', '/readFile', reqbody, function (data) {
        // var recvJson = JSON.parse(data);
        /*console.log("/readFile receive:" + JSON.stringify((recvJson)));
        document.getElementById("textarea_read").value = recvJson.outData;
        document.getElementById("input_errcode").value = recvJson.errCode;*/
        callback(data)
    })
}

function testWriteFile(index, data, callback) {
    var fileName = filetext;
    var pinNode = pintext;
    var filedata = data;
    if (fileName == "" || data == "" || pinNode == "") {
        alert('文件名、写入数据和pin不能为空');
        return;
    }
    var sendJson = {
        "fileName": fileName,
        "userPIN": pinNode,
        "data": JSON.stringify(filedata)
    };
    var reqbody = JSON.stringify(sendJson);
    ajax('POST', '/writeFile', reqbody, function (data) {
        callback(data)
    })
}

function testGetPINInfo(callback) {
  ajax('GET', '/getPinInfo', null, function(data) {
    var recvJson = JSON.parse(data);
    callback(recvJson)
    // console.log("/getUkeyState receive:" + JSON.stringify(recvJson));
    // document.getElementById("input_errcode").value = recvJson.errCode;
    // alert("PIN最大重试次数为："+recvJson.maxCount+",剩余重试次数为："+recvJson.remainCount);
  });
}

export {
    testGetKeyState, testBase64Encode, testSignData, testExportCert, testCreateFile, testDeleteFile,
    testWriteFile, testReadFile, testGetKeyInfo, testGetCSR, testImportCert, testGetPINInfo
}



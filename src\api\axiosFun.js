import request from "@/utils/request";
import axios from 'axios'
import {getStore} from '../utils/util'
// import apiuri from "@/api/apiuri";

const url = process.env.NODE_ENV === 'production' ? '' : '';

const convertJson = (param) => {
    if (typeof param == "object") {
        return JSON.stringify(param);
    } else {
        return param;
    }
};
const reqFrom = (method, url, params) => {
    return request({
        method: method,
        url: url,
        isHashToken: true,
        headers: {
            "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            'token': "Bearer " + getStore("token"),
            'Cache-Control': 'no-cache',
            'isToken': false,
        },
        data: params,
        withCredentials: true,
        traditional: true,
        transformRequest: [
            function (data) {

                return data
            }
        ]
    })
};

const reqFromL = (method, url, params) => {
    return axios({
        method: method,
        url: url,
        isHashToken: true,
        headers: {
            "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            'token': "Bearer " + getStore("token"),
            'Cache-Control': 'no-cache',
            'isToken': false,
        },
        data: params,
        withCredentials: true,
        traditional: true,
        transformRequest: [
            function (data) {

                return data
            }
        ]
    })
};
const get = (method, url, params) => {
    return axios({
        method: method,
        url: url,
        headers: {
            'Content-Type': 'application/json;charset=UTF-8',
            'Cache-Control': 'no-cache',
            'Authorization': "Bearer " + getStore("token"),
            'token': "Bearer " + getStore("token")
        },
        params,
        withCredentials: true,
        traditional: true,
        transformRequest: [
            function (data) {
                return data
            }
        ]
    }).then(res => res.data);
};
const reqGet = (method, url, params) => {
    return request({
        method: method,
        url: url,
        headers: {
            'Content-Type': 'application/json;charset=UTF-8',
            'Cache-Control': 'no-cache',
            'Authorization': "Bearer " + getStore("token"),
            'token': "Bearer " + getStore("token")
        },
        params,
        withCredentials: true,
        traditional: true,
        transformRequest: [
            function (data) {
                return data
            }
        ]
    }).then(res => res);
};
const reqPost = (method, url, params) => {
    return axios({
        method: method,
        url:url,
        headers: {
            'Content-Type': 'application/json;charset=UTF-8',
            'Cache-Control': 'no-cache',
            'Authorization':"Barer " +getStore("token"),
            'token': "Bearer " + getStore("token")
        },
        data: params,
        withCredentials : true,
        traditional: true,
        transformRequest: [
            function(data) {

                return data
            }
        ]
    }).then(res => res.data);
};

// 携带文件接口
const fileReq = (method, url, params) => {
    return axios({
        method: method,
        url: url,
        headers: {
            'Content-Type': 'application/json;charset=UTF-8',
            'token': "Bearer " + getStore("token"),
            'Cache-Control': 'no-cache'
        },
        withCredentials: true,
        data: params,
        traditional: true,
        transformRequest: [
            function (data) {
                return data
            }
        ]
    }).then(res => res.data);
};

// 登录请求方法
const noAuthreq = (method, url, params) => {

    return request({
        method: method,
        url: url,
        isHashToken: false,
        headers: {
            'Content-Type': 'application/json;charset=UTF-8',
            'Cache-Control': 'no-cache'
        },
        withCredentials: true,
        data: convertJson(params),
        traditional: true,
        transformRequest: [
            function (data) {

                return data
            }
        ]
    })
};
// 通用公用方法  post
const req = (method, url, params, errorAlert) => {
    return request({
        method: method,
        errorAlert: errorAlert,
        isHashToken: true,
        url: url,
        headers: {
            'Content-Type': 'application/json;charset=UTF-8',
            'Cache-Control': 'no-cache',
            'isToken': false,
            'token': "Bearer " + getStore("token")
        },
        data: convertJson(params),
        withCredentials: true,
        traditional: true,
        transformRequest: [
            function (data) {

                return data
            }
        ]
    })
};
const reqFormData = (method, url, params, errorAlert) => {
    return request({
        method: method,
        errorAlert: errorAlert,
        isHashToken: true,
        url: url,
        headers: {
            'Content-Type': 'application/json;charset=UTF-8',
            'Cache-Control': 'no-cache',
            'isToken': false,
            'token': "Bearer " + getStore("token")
        },
        data: params,
        withCredentials: true,
        traditional: true,
        transformRequest: [
            function (data) {

                return data
            }
        ]
    })
};


// 通用公用方法  post
const reqheaders = (method, url, params, _header, complete, errorAlert) => {
    const headers = Object.assign({
        'Content-Type': 'application/json;charset=UTF-8',
        'Cache-Control': 'no-cache',
        'isToken': false,
        'token': "Bearer " + getStore("token")
    }, _header);
    return request({
        method: method,
        isHashToken: true,
        complete: complete,
        errorAlert: errorAlert,
        url: url,
        headers: headers,
        data: params,
        withCredentials: true,
        traditional: true,
        transformRequest: [
            function (data) {
                return data
            }
        ]
    });
};


const reqParams = (method, url, params) => {
    return request({
        method: method,
        url: url,
        isHashToken: true,
        headers: {
            'token': "Bearer " + getStore("token")
        },
        params: params,
        withCredentials: true,
        traditional: true,
        transformRequest: [
            function (data) {

                return data
            }
        ]
    })
};
const reqfrom = (method, url, params) => {
    return request({
        method: method,
        url: url,
        isHashToken: true,
        headers: {
            "Content-Type": "multipart/form-data;text/plain",
            'token': "Bearer " + getStore("token")
        },
        data: params,
        withCredentials: true,
        traditional: true,
        transformRequest: [
            function (data) {

                return data
            }
        ]
    })
};
const reqFromPost = (method, url, params) => {
    return axios({
        method: method,
        url: url,
        isHashToken: true,
        headers: {
            "Content-Type": "multipart/form-data;text/plain",
            'token': "Bearer " + getStore("token")
        },
        data: params,
        withCredentials: true,
        traditional: true,
        transformRequest: [
            function (data) {

                return data
            }
        ]
    })
};


const reqCommon = (method, url, params, option) => {
    let defutOption = {
        method: method,
        url: url,
        withCredentials: true,
        traditional: true,
        isHashToken: true,
        headers: {
            'Content-Type': 'application/json;charset=UTF-8',
            'Cache-Control': 'no-cache',
            'token': "Bearer " + getStore("token")
        },
        data: convertJson(params),
        transformRequest: [
            function (data) {
                return data
            }
        ]
    };
    let targetOption = Object.assign(defutOption, option);
    return request(targetOption);
};
const reqCommonForm = (method, url, params, option) => {
    let defutOption = {
        method: method,
        url: url,
        withCredentials: true,
        traditional: true,
        isHashToken: true,
        headers: {
            'Cache-Control': 'no-cache',
            'token': "Bearer " + getStore("token")
        },
        data: params,
        transformRequest: [
            function (data) {
                return data
            }
        ]
    };
    let targetOption = Object.assign(defutOption, option);
    return request(targetOption);
};

const uploadReq = (method, url, params, option, complete) => {
    let defutOption = {
        method: method,
        url: url,
        withCredentials: true,
        traditional: true,
        isHashToken: true,
        complete: complete,
        headers: {
            'Content-Type': 'application/json;charset=UTF-8',
            'Cache-Control': 'no-cache',
            'token': "Bearer " + getStore("token")
        },
        data: params,
        transformRequest: [
            function (data) {
                return data
            }
        ]
    };
    let targetOption = Object.assign(defutOption, option);
    return request(targetOption);
};

const reqParamNoJson = (method, url, params, callBack, errorAlert) => {
    return reqheaders(
        method,
        url,
        params,
        {"Content-Type": "application/x-www-form-urlencoded;charset=utf-8"},
        callBack,
        errorAlert
    );
};


// const reqParamNoJsonConfig = (method, url, params, config) => {
//     return reqheaders(method, url, params, {"Content-Type": "application/x-www-form-urlencoded;charset=utf-8"}, callBack);
// };

const goReq = (method, url, params) => {
    return axios({
        method: method,
        url: url,
        headers: {
            'Content-Type': 'application/json;charset=UTF-8',
            'Cache-Control': 'no-cache',
            // 'Authorization':"Bearer " +getStore("token")
        },
        data: params,
        withCredentials: true,
        traditional: true,
        transformRequest: [
            function (data) {

                return data
            }
        ]
    }).then(res => res.data);
};

export {
    url, noAuthreq, reqheaders, get, reqGet, reqPost, reqFrom, reqFromL, reqFromPost,
    req, reqfrom, reqParams, fileReq, reqCommon, uploadReq, reqParamNoJson, goReq, reqFormData, reqCommonForm
}

export default {
    uploadReq
}

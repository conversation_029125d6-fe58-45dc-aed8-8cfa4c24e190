<template>
    <el-row class="parent">
        <el-col :span="12" class="div1 div_around">
            <h1 class="left-h1">账号信息</h1>
            <el-form label-width="120px" style="width: 80%" :model="emailInfo" :rules="rules" ref="emailAccount">
                <el-form-item label="Email地址：" prop="email">
                    <el-input size="small" v-model="emailInfo.email" maxlength="60" style="width: 200px"></el-input>
                </el-form-item>
                <el-form-item label="密码：" prop="password">
                    <el-input size="small" type="password" v-model="emailInfo.password" style="width: 200px"></el-input>
                </el-form-item>
                <el-form-item label="发信人名称：" prop="userName">
                    <el-input size="small" v-model="emailInfo.userName" style="width: 200px"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="small" class="comBtn com_send_btn" @click="saveAccount">保存</el-button>
                </el-form-item>
            </el-form>
        </el-col>
        <el-col :span="12" class="div2 div_around">
            <h1 class="left-h1">服务器信息</h1>
            <el-form :model="emailInfo" :rules="rules" ref="emailServer" label-width="130px" class="demo-ruleForm">
                <el-form-item label="邮箱类型：" prop="emailType">
                    <el-select v-model="emailInfo.emailType" placeholder="请选择" style="width: 100px" size="small">
                        <el-option
                                v-for="item in options"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="账号服务器：" prop="accountServer">
                    <el-input size="small" v-model="emailInfo.accountServer" style="width: 200px"></el-input>
                </el-form-item>
                <div style="display: flex;flex-wrap: wrap">
                    <el-form-item label="收件服务器：" prop="receivedServer">
                        <el-input size="small" v-model="emailInfo.receivedServer"
                                  onkeyup="value=value.replace(/[\u4e00-\u9fa5]/ig,'')"
                                  style="width: 200px"></el-input>
                        <!--<el-checkbox v-model="emailInfo.receivedSsl" @change="ssl">SSL</el-checkbox>-->
                        <!--<el-checkbox v-model="emailInfo.recSll" @change="receivedSsl">SSL</el-checkbox>-->
                        <!--<span>端口：</span><el-input type="text" v-model="emailInfo.receivedPort"-->
                        <!--onkeyup="this.value=this.value.replace(/\D/g,'')" onafterpaste="this.value=this.value.replace(/\D/g,'')"-->
                        <!--style="width: 60px;height:32px;border: 1px solid #DCDFE6"></el-input>-->
                    </el-form-item>
                    <el-form-item label="端口：" prop="receivedPort" class="port_style">
                        <el-input type="text" size="small" v-model="emailInfo.receivedPort"
                                  onkeyup="this.value=this.value.replace(/\D/g,'')" onafterpaste="this.value=this.value.replace(/\D/g,'')"></el-input>
                    </el-form-item>
                </div>

                <div style="display: flex;flex-wrap: wrap">
                    <el-form-item label="发件服务器：" prop="sendServer">
                        <el-input size="small" v-model="emailInfo.sendServer"
                                  onkeyup="value=value.replace(/[\u4e00-\u9fa5]/ig,'')"
                                  style="width: 200px"></el-input>
                        <!--<el-checkbox v-model="emailInfo.sendSll" @change="sendSsl">SSL</el-checkbox>-->
                        <!--<span>端口：</span><input type="text" v-model="emailInfo.sendPort"-->
                        <!--onkeyup="this.value=this.value.replace(/\D/g,'')" onafterpaste="this.value=this.value.replace(/\D/g,'')"-->
                        <!--style="width: 60px;height:32px;border: 1px solid #DCDFE6">-->
                    </el-form-item>
                    <el-form-item label="端口：" prop="sendPort" class="port_style">
                        <el-input type="text" size="small" v-model="emailInfo.sendPort"
                                  onkeyup="this.value=this.value.replace(/\D/g,'')" onafterpaste="this.value=this.value.replace(/\D/g,'')"></el-input>
                    </el-form-item>
                </div>
                <el-form-item>
                    <el-button class="comBtn com_send_btn" size="small" type="primary" @click="saveServer">保存</el-button>
                </el-form-item>
            </el-form>
        </el-col>
    </el-row>
</template>

<script>
    import alarmMG from "@/api/alarmMG";
    import { elValidatePort, isIpHostUrl } from "@/utils/myValidate";
    import { encrypt, decrypt } from "@/utils/cryptoJs";

    export default {
        name: "email-alarm",
        data() {
            const validateEmail = (rules, value, callback) => {
                let email = /^(\w+\.?)*\w+@(?:\w+\.)\w+$/
                if (value == null) {
                    callback()
                }
                if (value != '') {
                    if (!email.test(value)) {
                        callback(new Error('请输入正确的邮箱!'))
                    } else {
                        callback()
                    }
                } else {
                    callback()
                }
            }
            return {
                url: '',
                emailInfo: {
                    email: '',
                    password: '',
                    userName: '',
                    emailType: 'IMAP',
                    accountServer: '',
                    receivedServer: '',
                    receivedPort: null,
                    sendServer: '',
                    sendPort: null
                },
                options: [
                    {value: 'IMAP', label: 'IMAP'},
                    {value: 'POP3', label: 'POP3'}
                ],
                rules: {
                    email: [
                        {required: true, message: '请输入Email地址', trigger: 'blur'},
                        {validator: validateEmail, trigger: 'blur'}
                    ],
                    password: [
                        {required: true, message: '请输入密码', trigger: 'blur'},
                    ],
                    userName: [
                        {required: true, message: '请输入发信人名称', trigger: 'blur'},
                        {min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur'}
                    ],
                    accountServer: [
                        {required: true, message: '请输入账号服务器', trigger: 'blur'},
                        {validator: isIpHostUrl, trigger: 'blur'}
                    ],
                    emailType: [
                        {required: true, message: '请选择邮箱类型', trigger: 'blur'}
                    ],
                    receivedServer: [
                        {required: true, message: '请输入收件服务器', trigger: 'blur'},
                        {validator: isIpHostUrl, trigger: 'blur'}
                    ],
                    receivedPort: [
                        {required: true, message: '请输入端口', trigger: 'blur'},
                        {validator: elValidatePort, trigger: 'blur'}
                    ],
                    sendServer: [
                        {required: true, message: '请输入发件服务器', trigger: 'blur'},
                        {validator: isIpHostUrl, trigger: 'blur'}
                    ],
                    sendPort: [
                        {required: true, message: '请输入端口', trigger: 'blur'},
                        {validator: elValidatePort, trigger: 'blur'}
                    ],
                },
            }
        },
        methods: {
            saveAccount() {
                this.$refs["emailAccount"].validate((valid) => {
                    if (valid) {
                        let account = {
                            email: this.emailInfo.email,
                            // password: this.emailInfo.password,
                            password: encrypt(this.emailInfo.password),
                            userName: this.emailInfo.userName
                        }
                        alarmMG.setEmailAccount(account).then(res => {
                            let code = res.code;
                            if (code == 0) {
                                this.$message.success("设置成功");
                                this.getEmailInfo();
                            }
                        })
                    }
                })
            },
            saveServer() {
                this.$refs["emailServer"].validate((valid) => {
                    if (valid) {
                        let server = {
                            emailType: this.emailInfo.emailType,
                            accountServer: this.emailInfo.accountServer,
                            receivedServer: this.emailInfo.receivedServer,
                            receivedPort: this.emailInfo.receivedPort,
                            sendServer: this.emailInfo.sendServer,
                            sendPort: this.emailInfo.sendPort
                        }
                        alarmMG.setEmailServer(server).then(res => {
                            let code = res.code;
                            if (code == 0) {
                                this.$message.success("设置成功");
                                this.getEmailInfo();
                            }
                        })
                    }
                })
            },
            getEmailInfo() {
                this.$refs["emailAccount"].clearValidate();
                this.$refs["emailServer"].clearValidate();
                alarmMG.getEmailInfo().then(res => {
                    let code = res.code;
                    if (code == 0) {
                        let data = res.data;
                        if (data != null) {
                            this.emailInfo.email = data.email;
                            if (data.password != null) {
                              this.emailInfo.password = decrypt(data.password);
                            } else {
                              this.emailInfo.password = null;
                            }
                            this.emailInfo.userName = data.userName;
                            this.emailInfo.emailType = data.emailType;
                            this.emailInfo.accountServer = data.accountServer;
                            this.emailInfo.receivedServer = data.receivedServer;
                            this.emailInfo.receivedPort = data.receivedPort;
                            this.emailInfo.sendServer = data.sendServer;
                            this.emailInfo.sendPort = data.sendPort;
                        }

                    }
                })
            }
        }
    }
</script>

<style lang="less" scoped>
    .port_style {
        width: 150px;

        /deep/ .el-form-item__label {
            width: 60px !important;
            padding: 0;
        }

        /deep/ .el-form-item__content {
            margin-left: 60px !important;

            .el-input__inner {
                padding: 0 10px;
            }
        }
    }

    /*.parent {*/
        /*width: 100%;*/
        /*display: flex;*/
        /*justify-content: space-between;*/
    /*}*/

    .div1, .div2 {
        /*width: 48%;*/
        height: 400px;
        overflow: auto;
        /*display: table-cell;*/
    }

    .div_around {
        border: 1px solid #cccccc
    }

    .left-h1 {
        margin-left: 5%;
    }
</style>

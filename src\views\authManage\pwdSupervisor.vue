<!-- 密码主管 zyft -->
<template>
    <el-card class="box-card" shadow="always">
        <create-table
                :tableData="tableData"
                :tableHeader="tableDataHeader"
                :isPage="false"
        ></create-table>
    </el-card>
</template>

<script>
    import createTable from "@/utils/createTable";
    // import authManage from "@/api/authManage";

    export default {
        // name: "administrator-manage",
        components: {createTable},
        data() {
            return {
                tableDataHeader: [
                    {label: '序号', type: "index", width: '60'},
                    {
                        label: '名称', prop: 'name', type: "text_formatter",
                        formatter: function (value, row) {
                            // if (row.index === 1) return "密码主管1";
                            // if (row.index === 2) return "密码主管2";
                            // if (row.index === 3) return "密码主管3";
                            return value
                        }
                    },
                    {
                        label: '角色', prop: 'role', type: "text_formatter",
                        formatter: function (value, row) {
                            return value
                            // if (row.role === 0) return "密码主管";
                        }
                    },
                    {label: '描述', prop: 'desc', type: "text_formatter",
                        formatter: function (value, row) {
                            return "通过登录任意两个密码主管，登录到管理态";
                        }
                    },
                ],
                tableData: [],
            }
        },
        methods: {
            // 获取管理员列表
            pageUser() {
                this.tableData = [];
                this.$http.authManage.supervisorList().then((res) => {
                    let code = res.code;
                    if (code == 0) {
                        this.tableData = res.data;
                        this.total = res.row;
                    } else {
                        this.$message({
                            message: res.msg,
                            type: 'error'
                        });
                    }
                })
            }
        },
        created() {
            this.pageUser();
        }
    }
</script>


<style lang="less" scoped>

</style>

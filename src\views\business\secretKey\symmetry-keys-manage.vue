<!-- 非对称密钥 -->
<template>
    <div class="container">
        <el-card v-show="showSearch" class="box-card" shadow="always">
            <el-form :inline="true" :show-message="false" label-width="60" class="user-search comForm">
                <el-form-item label="索引号：">
                    <el-input size="small" v-model="queryParam.keyIndex" oninput="value=value.replace(/[^\d]/g,'')" clearable></el-input>
                </el-form-item>
                <el-form-item label="算法：">
                    <el-select v-model="queryParam.algorithm" placeholder="请选择" size="small">
                        <el-option v-for="item in symmetryOptions" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item style="background-color: transparent;">
                    <el-button class="comBtn com_send_btn" size="mini" type="primary" icon="el-icon-search" @click="refreshKeys">搜索</el-button>
                    <el-button class="comBtn com_reset_btn" size="mini" type="primary" icon="el-icon-refresh" @click="resetQueryParam">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <div v-show="showSearch" style="padding: 5px"></div>
        <el-card class="box-card" shadow="always" style="padding-bottom: 10px">
            <!-- 操作方法 -->
            <el-form label-width="100px">
                <el-row>
                    <el-col :span="14" style="text-align: left">
                        <el-button class="comBtn com_send_btn" size="mini" type="success" @click="openAdd">新增</el-button>
                        <el-button class="comBtn com_send_btn" size="mini" type="success" @click="openBatch">批量新增</el-button>
                    </el-col>
                    <el-col :span="10">
                        <div style="text-align: right">
                            <el-button-group>
                                <el-button size="mini" icon="el-icon-search" @click="showAndSearch"></el-button>
                                <el-button size="mini" icon="el-icon-refresh-left" @click="refreshKeys"></el-button>
                            </el-button-group>
                        </div>
                    </el-col>
                </el-row>
            </el-form>

            <div id="keyTable" style="padding-top: 10px">
                <createTable
                        :tableData="tableData"
                        :tableHeader="tableHeader"
                        :isPage="isPage"
                        :pageAttributes="{total: total, currentPage: queryParam.pageNo, pageSize: queryParam.pageSize}"
                        :current-change="currentChange"
                        :sizeChange="sizeChange"
                >
                </createTable>
            </div>

            <!-- 密钥生成 -->
            <el-dialog title="新增" :visible.sync="addVisible" width="500px" @close="$refs.addSymmetry.resetFields()">
                <el-form label-width="150px" ref="addSymmetry" :model="symmetry" :rules="rules">
                    <el-form-item label="加密算法:" prop="algorithm">
                        <el-select v-model="symmetry.algorithm" placeholder="请选择" size="small">
                            <el-option v-for="item in symmetryOptions" :key="item.value" :label="item.label" :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="索引号:" prop="keyIndex">
                        <el-select
                          v-model="symmetry.keyIndex"
                          v-lazy-loading="loadingUnIndex"
                          placeholder="请选择"
                          size="small">
                            <el-option v-for="item in unUsedIndex" :key="item" :label="item" :value="item"></el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button class="comBtn com_reset_btn" @click="closeAdd">取 消</el-button>
                    <el-button class="comBtn com_send_btn" @click="addSymmetryKey">确 定</el-button>
                </div>
            </el-dialog>

            <!-- 密钥批量生成 -->
            <el-dialog title="批量新增" :visible.sync="batchVisible" width="500px" @close="$refs.symmetry.resetFields()">
                <el-form label-width="150px" ref="symmetry" :model="symmetry" :rules="rules">
                    <el-form-item label="加密算法:" prop="algorithm">
                        <el-select v-model="symmetry.algorithm" placeholder="请选择" size="small" style="width: 240px">
                            <el-option v-for="item in symmetryOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>

                    <el-row style="display: flex">
                        <!--<el-col :span="12">-->
                        <el-form-item label="索引号:" prop="startIndex" class="index_style">
                            <el-input size="small" v-model="symmetry.startIndex" style="width: 110px"></el-input>
                        </el-form-item>
                        <!--~-->
                        <!--</el-col>-->
                        <el-col :span="1" style="text-align: center;height: 40px;line-height: 40px">~</el-col>
                        <!--<el-col :span="11">-->
                        <el-form-item prop="endIndex" label-width="0px" class="index_style">
                            <el-input size="small" v-model="symmetry.endIndex" style="width: 110px"></el-input>
                        </el-form-item>
                        <!--</el-col>-->
                    </el-row>


                    <el-form-item label="是否覆盖已有密钥:">
                        <el-radio v-model="symmetry.cover" :label='true'>覆盖</el-radio>
                        <el-radio v-model="symmetry.cover" :label='false'>不覆盖</el-radio>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button class="comBtn com_reset_btn" @click="closeBatch">取 消</el-button>
                    <el-button class="comBtn com_send_btn" @click="batchAddSymmetryKey">确 定</el-button>
                </div>
            </el-dialog>


        </el-card>

    </div>
</template>

<script>
    // import symmetryMG from "@/api/symmetryMG";
    export default {
        name: "symmetry-keys-manage",
        data() {
            let _this = this;
            const validateStartIndex = (rule, value, callback) => {
                if (!value.replace(/\D|^0/g,'')) {
                    callback(new Error("起始索引只能输入数字"))
                }
                if (+value == 0) {
                    callback(new Error("起始索引不能为0"))
                }
                if (+value > +_this.symMaxCount && +_this.symMaxCount >= 10) {
                    callback(new Error("起始索引不能大于" + "\n" + _this.symMaxCount));
                } else if (+value > +_this.symMaxCount && _this.symMaxCount < 10) {
                    callback(new Error("起始索引不能大于" + _this.symMaxCount));
                } else {
                    callback();
                }
            };
            const validateEndIndex = (rule, value, callback) => {
                if (!value.replace(/\D|^0/g,'')) {
                    callback(new Error("终止索引只能输入数字"))
                }
                if (+value == 0) {
                    callback(new Error("终止索引不能为0"))
                }
                if (+value > +_this.symMaxCount && +_this.symMaxCount < 10) {
                    callback(new Error("终止索引不能大于" + _this.symMaxCount));
                }
                if (+value > +_this.symMaxCount && +_this.symMaxCount >= 10) {
                    callback(new Error("终止索引不能大于" + "\n" + _this.symMaxCount));
                }

                if (+value < +_this.symmetry.startIndex) {
                    callback(new Error("终止索引不能小于起始索引！"));
                } else {
                    callback();
                }
            };
            return {
                showSearch: true,
                isPage: true,
                total: 0,
                tableData: [],
                symMaxCount: 0,
                addVisible: false,
                batchVisible: false,
                symmetryOptions: [
                    {label: "SM1", value: "SM1"},
                    {label: "SM4", value: "SM4"},
                    {label: "AES128", value: "AES128"},
                    {label: "AES192", value: "AES192"},
                    {label: "AES256", value: "AES256"},
                    {label: "3DES", value: "3DES"}
                ],
                unUsedIndex: [],
                queryParam: {
                    pageNo: 1,
                    pageSize: 10,
                    keyIndex: undefined,
                    algorithm: undefined,
                },
                unIndexPage: 1,
                symmetry: {
                    algorithm: "SM1",
                    keyIndex: undefined,
                    startIndex: undefined,
                    endIndex: undefined,
                    cover: false
                },
                rules: {
                    startIndex: [
                        {required: true, message: '起始索引不能为空！', trigger: 'blur'},
                        {validator: validateStartIndex, trigger: 'blur'}
                    ],
                    endIndex: [
                        {required: true, message: '终止索引不能为空！', trigger: 'blur'},
                        {validator: validateEndIndex, trigger: 'blur'}
                    ],
                    keyIndex: [
                        {required: true, message: '密钥索引不能为空！', trigger: 'blur'}
                    ],

                },
                tableHeader: [
                    {
                        type: "index",
                        label: "序号",
                    }, {
                        type: "normal",
                        label: "索引号",
                        prop: "keyIndex",
                    }, {
                        type: "normal",
                        label: "算法",
                        prop: "keyAlgorithm"
                    }, {
                        type: "normal",
                        label: "密钥长度",
                        prop: "keyLength",
                    }, {
                        type: "time",
                        label: "创建时间",
                        prop: "createTime",
                    }, {
                        type: "operation",
                        label: "操作",
                        width: "150",
                        tag: [{
                            name: "清空密钥",
                            operType: "update",
                            tagType: "el-button",
                            attributes: {
                                size: "mini",
                                type: "text",
                                icon: "el-icon-delete"
                            },
                            isShow(row) {
                                if (row.keyContent != null) return true
                                else return false;
                            },
                            callback: function (row) {
                                _this.clearSymmetryKey(row.id);
                            }
                        }]
                    }
                ]
            }
        },
        methods: {
            loadingUnIndex() {
              this.unIndexPage++;
              this.$http.symmetryMG.getUnUsedIndex(this.unIndexPage).then(res => {
                if (res.code == 0) {
                  this.unUsedIndex.push(...res.data)
                } else {
                  this.unIndexPage--;
                }
              })
            },
            showAndSearch() {
                this.showSearch = !this.showSearch;
            },
            resetQueryParam() {
                this.queryParam.pageNo = 1;
                this.queryParam.pageSize = 10;
                this.queryParam.keyIndex = undefined;
                this.queryParam.algorithm = undefined;
                this.refreshKeys();
            },
            refreshKeys() {
                let _this = this;
                this.$http.symmetryMG.pageSymmetryKey(this.queryParam).then(res => {
                    _this.tableData = res.data;
                    _this.total = res.row;
                })
            },
            currentChange(val) {
                this.queryParam.pageNo = val;
                this.refreshKeys();
            },
            sizeChange(val) {
                this.queryParam.pageSize = val;
                this.refreshKeys();
            },
            openAdd() {
                this.addVisible = true;
            },
            openBatch() {
                this.batchVisible = true;
            },
            closeAdd() {
                this.addVisible = false;
                this.resetSymmetry();
            },
            closeBatch() {
                this.batchVisible = false;
                this.resetSymmetry();
            },
            resetSymmetry() {
                this.symmetry.keyIndex = undefined;
                this.symmetry.startIndex = undefined;
                this.symmetry.endIndex = undefined;
                this.symmetry.cover = false;
                this.symmetry.algorithm = "SM1";
            },
            getSymmetryKeysMaxCount() {
                this.$http.symmetryMG.getSymmetryKeysMaxCount().then(res => {
                    this.symMaxCount = res.data;
                })
            },
            getUnUsedIndex() {
                this.unUsedIndex = [];
                this.unIndexPage = 1;
                this.$http.symmetryMG.getUnUsedIndex(this.unIndexPage).then(res => {
                    this.unUsedIndex = res.data;
                })
            },
            addSymmetryKey() {
                this.$refs["addSymmetry"].validate((valid) => {
                    if (valid) {
                        this.$http.symmetryMG.addSymmetryKey(this.symmetry).then(res => {
                            let code = res.code;
                            if (code == 0) {
                                this.$message.success("新增密钥成功");
                                this.resetQueryParam();
                                this.refreshKeys();
                                this.getUnUsedIndex();
                                this.closeAdd();
                            } else {
                                this.$message.warning(res.msg)
                            }
                        })
                    }
                })
            },
            batchAddSymmetryKey() {
                this.$refs["symmetry"].validate((valid) => {
                    if (valid) {
                        this.$http.symmetryMG.batchAddSymmetryKey(this.symmetry).then(res => {
                            let code = res.code;
                            if (code === 0) {
                                this.$message.success("新增成功");
                                this.resetQueryParam();
                                this.getUnUsedIndex();
                                this.closeBatch();
                            } else {
                                this.$message.warning(res.msg);
                            }
                        })
                    }
                })
            },
            clearSymmetryKey(id) {
                this.$confirm('确定要清空密钥?', '确定', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.symmetryMG.clearSymmetryKey(id).then(res => {
                        let code = res.code;
                        if (code === 0) {
                            this.$message.success("清空密钥成功")
                            this.refreshKeys();
                            this.getUnUsedIndex();
                        } else {
                            this.$message.warning(res.msg);
                        }
                    })
                });
            }
        }
    }
</script>

<style lang="less" scoped>
    .index_style /deep/ .el-form-item__error {
        white-space: pre-line;
        top: 70%;
    }
    .user-search .el-form-item {
        margin-bottom: 0;
        /*margin-top: 10px;*/
    }
</style>

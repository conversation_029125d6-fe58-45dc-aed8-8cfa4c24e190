<template>
  <el-tabs type="border-card" v-model="activeName" @tab-click="handleClick">
    <el-tab-pane label="非对称密钥管理" name="first">
      <asymmetric-keys-manage ref="asymmetric"/>
    </el-tab-pane>

    <el-tab-pane label="对称密钥管理" name="second">
      <symmetry-keys-manage ref="symmetry"/>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
//非对称密钥管理
import AsymmetricKeysManage from "@/views/business/secretKey/asymmetric-keys-manage";
//对称密钥
import SymmetryKeysManage from "@/views/business/secretKey/symmetry-keys-manage";

export default {
  name: "keys-manage",
  components: {SymmetryKeysManage, AsymmetricKeysManage},
  data() {
    return{
      activeName: "first",
    }
  },
  methods: {
    handleClick(tab, event) {
      if (tab.name === "first") {
        this.$refs.asymmetric.renewData();
      } else if (tab.name === "second") {
        this.$refs.symmetry.getSymmetryKeysMaxCount();
        this.$refs.symmetry.refreshKeys();
        this.$refs.symmetry.getUnUsedIndex();
      }
    }
  },
}
</script>

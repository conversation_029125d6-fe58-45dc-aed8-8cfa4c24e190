/**
 * 运维管理 路由
 * */
const OAMRouter = [
    {
        path: '/systemUpgrade',
        name: '升级管理',
        component: () => import('@/views/oam/systemUpgrade'),
        meta: {
            title: '升级管理',
            requireAuth: true
        }
    }, {
        path: '/self-checkout',
        name: '系统检测',
        component: () => import('@/views/system/self-checkout'),
        meta: {
            title: '系统检测',
            requireAuth: true
        }
    }, {
        path: '/custom',
        name: '个性化定制',
        component: () => import('@/views/custom'),
        meta: {
            title: '个性化定制',
            requireAuth: true
        }
    },
    {
        path: '/tcpdump',
        name: 'packet',
        component: () => import('@/views/pcap/packet.vue'),
        meta: {
            title: '抓包工具',
            requireAuth: true
        }
    },
    {
        path: '/hard-engine',
        name: 'hard-engine',
        component: () => import('@/views/oam/hardEngine.vue'),
        meta: {
            title: '算力信息',
            requireAuth: true
        }
    },
    {
        path: '/hsmconfig',
        name: 'packet',
        component: () => import('@/views/oam/hsmconfig.vue'),
        meta: {
            title: '密码机配置',
            requireAuth: true
        }
    },
    {
        path: '/ssh-manage',
        name: 'ssh',
        component: () => import('@/views/ssh/sshIndex.vue'),
        meta: {
            title: 'SSH管理',
            requireAuth: true
        }
    },
    {
      path: '/firewalld-manage',
      name: 'ssh',
      component: () => import('@/views/firewalld/firewalld.vue'),
      meta: {
        title: '防火墙管理',
        requireAuth: true
      }
    },
    // {
    //     path: '/config',
    //     name: '字典表配置',
    //     component: () => import('@/views/oam/config.vue'),
    //     meta: {
    //         requireAuth: true
    //     }
    // },
];

export default {OAMRouter}

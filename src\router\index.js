import Vue from 'vue'
import Router from 'vue-router'
import login from "@/views/login";
import userLogin from "@/views/userLogin";
import home from "@/views/home";
import index from "@/views/index";

import businessRouter from './businessRouter';
import systemRouter from './systemRouter';
import monitorRouter from './monitorRouter';
import auditRouter from './auditRouter';
import OAMRouter from './OAMRouter';
console.log(businessRouter.businessRouter);

//初始化
import init from "@/views/init/init";
//DNS配置
import dnsConfigure from "@/views/system/dns-configure";
//签名验签配置
// import signConfigure from "@/views/business/sign-configure";
import signConfigureOld from "@/views/business/sign-configure_old";
//签名验签测试
import signTest from "@/views/business/sign-test";
//设备代管理
import deviceAgent from "@/views/oam/deviceAgent";
import menuManager from "@/views/oam/menuManage";
import roleManager from "@/views/oam/roleManager";

Vue.use(Router);

export default new Router({
    routes: [
        {
            path: '/',
            name: 'login',
            component: login,
            hidden: true,
            meta: {
                requireAuth: false
            }
        }, {
            path: '/initukey',
            name: '时间戳备份查看',
            component: () => import('@/views/initUKey'),
            meta: {
                requireAuth: false
            }
        }, {
            path: '/root/login',
            name: 'userLogin',
            component: userLogin,
            hidden: true,
            meta: {
                requireAuth: false
            }
        }, {
            path: '/init',
            name: 'init',
            component: init,
            hidden: true,
            meta: {
                requireAuth: false
            }
        }, {
            path: '/index',
            name: '首页',
            component: index,
            iconCls: 'el-icon-tickets',
            children: [
                ...businessRouter.businessRouter, // 业务管理
                ...systemRouter.systemRouter, // 系统管理
                ...monitorRouter.monitorRouter, // 监控管理
                ...auditRouter.auditRouter, // 审计管理
                ...OAMRouter.OAMRouter, // 审计管理
                {
                    path: '/home',
                    name: '首页',
                    component: home,
                    meta: {
                        requireAuth: true
                    }
                }, {
                    path: '/dns-configure',
                    name: 'DNS配置',
                    component: dnsConfigure,
                    meta: {
                        requireAuth: true
                    }
                }, {
                    path: '/sign-configure-old',
                    name: '签名验签配置',
                    component: signConfigureOld,
                    meta: {
                        requireAuth: true
                    }
                }, {
                    path: '/sign-test',
                    name: '签名验签测试',
                    component: signTest,
                    meta: {
                        requireAuth: true
                    }
                }, {
                    path: '/cardManage',
                    name: '密码卡管理',
                    component: () => import('@/views/oam/cryptoCard_old'),
                    meta: {
                        requireAuth: true
                    }
                }, {
                    path: '/deviceAgent',
                    name: '设备代管理',
                    component: deviceAgent,
                    meta: {
                        requireAuth: true
                    }
                }, {
                    path: '/menuManager',
                    name: '菜单管理',
                    component: menuManager,
                    meta: {
                        requireAuth: true
                    }
                }, {
                    path: '/roleManager',
                    name: '权限管理',
                    component: roleManager,
                    meta: {
                        requireAuth: true
                    }
                },
                {
                    path: '/config',
                    name: '字典表配置',
                    component: () => import('@/views/oam/config.vue'),
                    meta: {
                        requireAuth: true
                    }
                },
            ]
        }
    ]
})

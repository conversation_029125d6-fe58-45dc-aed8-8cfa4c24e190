import { fileReq, req, goReq, reqfrom } from './axiosFun';
import apiuri from "@/api/apiuri";

const licenseApi = apiuri.licenseApi;
const systemApi = apiuri.systemApi;

// function getLicenseUrl() {
//     let protocol = window.location.protocol; /* 获取协议 */
//     // let host = window.location.host.split(":")[0];
//     let host = process.env.NODE_ENV === 'production' ? window.location.host.split(":")[0] : '**********';
//     let dic = {
//         "http:": "http://",
//         "https:": "https://",
//         "HTTP:": "http://",
//         "HTTPS:": "https://"
//     };
//     // return dic[protocol] + "**********" + ':10245';
//     // return dic[protocol] + host + ':10245';
//     return dic[protocol] + host;
// }

// const licenseUrl = getLicenseUrl();
const licenseUrl = '/licenseServer';
// const licenseUrl = process.env.NODE_ENV === 'production' ? '/licenseServer' : '/svs/licenseServer';
// 获取 License 信息
const getLicenseApi = (params) => {
    // return req("post", licenseUrl + licenseApi.getLicense, params, false)
    return goReq("post", licenseUrl + licenseApi.getLicense, params, false)
};

const getAbilityApi = (params) => {
    return goReq("post", licenseUrl + licenseApi.getAbility, params, false)
};


// 提交 License
const setLicenseApi = (params) => {
    return reqfrom("post", licenseApi.setLicense, params)
};

const uploadLicenseApi = (params) => {
    return reqfrom("post", licenseApi.setLicense, params)
};

// 获取version
const getversion = () => {
    return req("post", systemApi.getversion);
};

// 获取 license param
const getParams = () => {
    return goReq("post", licenseUrl + licenseApi.getLicenseParam, {}, false)
};


export default {
    getAbilityApi,
    getLicenseApi,
    uploadLicenseApi,
    getversion,
    getParams,
    setLicenseApi,
}
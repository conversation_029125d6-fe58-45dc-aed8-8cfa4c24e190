<template>
    <div>
        <el-card class="box-card" shadow="always" style="padding-bottom: 10px">
            <div style="margin-bottom: 10px">
                <el-button class="comBtn com_send_btn" size="mini" :loading="refreshLoading" @click="querySslCertList">刷新</el-button>
                <el-button class="comBtn com_reset_btn" size="mini" @click="rebootNginxService">重启服务</el-button>
            </div>
            <!-- 列表 -->
            <h5 style="padding: 0;margin-bottom: 0">RSA SSL 证书</h5>
            <el-divider></el-divider>
            <create-table :tableData="rasTableData" :tableHeader="tableDataHeader"></create-table>

            <h5 style="padding: 0;margin-bottom: 0">SM2 SSL 证书</h5>
            <el-divider></el-divider>
            <create-table :tableData="sm2TableData" :tableHeader="tableDataHeader"></create-table>

            <!-- 系统注册证书更新 -->
            <el-dialog title="系统注册证书更新" :visible.sync="systemVisible" width="25%" :before-close='closeSystemRegister' :close-on-click-modal="false">
                <span style="color: red">* 重置证书后，需要手动重启服务后生效！</span>
                <el-form label-width="100px" ref="certificateFrom" :model="certificateFrom" :rules="certRules" style="margin-top: 15px">
                    <el-form-item label="证书DN：" prop="dn">
                        <el-input size="small" v-model="certificateFrom.dn" placeholder="DN格式：CN=XXXXX,C=XX"></el-input>
                    </el-form-item>
                    <el-form-item label="证书类型：" prop="certAlgo">
                        <el-select v-model="certificateFrom.certAlgo" filterable placeholder="请选择" size="small" style="width: 100%">
                            <el-option v-for="item in keyOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="哈希算法：" prop="algName">
                        <el-select v-model="certificateFrom.algName" placeholder="请选择" size="small" style="width: 100%">
                            <el-option v-for="item in algorithmOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                </el-form>

                <div slot="footer" class="dialog-footer">
                    <el-button class="comBtn com_reset_btn" size="small" @click='closeSystemRegister'>取消</el-button>
                    <el-button class="comBtn com_send_btn" size="small" type="primary" @click="uploadSslCert">保存</el-button>
                </div>
            </el-dialog>

            <!-- 编辑界面 查看证书 title="SSL证书" -->
            <el-dialog title="SSL证书" :visible.sync="searchSslVisible" width="35%" @click='closeSslCert' :close-on-click-modal="false">
                <el-descriptions class="margin-top" :column="1" border>
                    <el-descriptions-item label="签发者">{{editForm.issuer}}</el-descriptions-item>
                    <el-descriptions-item label="签发给">{{editForm.dn}}</el-descriptions-item>
                    <el-descriptions-item label="序列号">{{ editForm.serialNumber }}</el-descriptions-item>
                    <el-descriptions-item label="有效期">{{editForm.endDate}}</el-descriptions-item>
                    <el-descriptions-item label="证书类型">{{editForm.secretKey}}</el-descriptions-item>
                    <el-descriptions-item label="哈希算法">{{editForm.digestEncAlg}}</el-descriptions-item>
                    <el-descriptions-item label="证书指纹(SHA1)">{{editForm.fingerprintSha1}}</el-descriptions-item>
                    <el-descriptions-item label="证书指纹(MD5)">{{editForm.fingerprintMd5}}</el-descriptions-item>
                </el-descriptions>
                <div slot="footer" class="dialog-footer">
                    <el-button class="comBtn com_send_btn" size="small" @click='closeSslCert'>关 闭</el-button>
                </div>
            </el-dialog>

            <!-- 证书上传 -->
            <el-dialog title="证书上传" :visible.sync="uploadVisible" width="500px" :before-close='closeUploadView' :close-on-click-modal="false">
                <span style="color: red">* 上传证书后，需要手动重启服务后生效！</span>
                <el-form label-width="100px" ref="certificateFrom" :model="certificateFrom" :rules="certRules" style="margin-top: 15px">
                    <el-form-item v-if="certificateFrom.cerSource == '2'" label="PIN码：" prop="pin">
                        <el-input type="password" size="small" v-model="certificateFrom.pin" placeholder="请输入PIN码"></el-input>
                    </el-form-item>

                    <el-form-item v-if="false" label="别名：" prop="alias">
                        <el-input size="small" v-model="certificateFrom.alias" placeholder="请输别名，可为空"></el-input>
                    </el-form-item>
                    <!--<el-form-item label="请上传站点证书：" v-if="certificateFrom.cerSource == '2'" prop="base64Text">-->
                    <el-form-item label="站点证书：" v-if="certificateFrom.cerSource == '2'" prop="base64Text">
                        <el-input v-show="false" v-model="certificateFrom.base64Text"></el-input>
                        <el-upload
                                class="upload-demo"
                                action="#"
                                :on-remove="handleRemove"
                                :on-change="handleChange"
                                :limit="1"
                                :file-list="certificateFrom.fileList"
                                :auto-upload="false">
                            <el-button class="comBtn com_send_btn" size="small" type="primary">点击上传</el-button>
                        </el-upload>
                    </el-form-item>
                </el-form>

                <div slot="footer" class="dialog-footer">
                    <el-button class="comBtn com_reset_btn" size="small" @click='closeUploadView'>取消</el-button>
                    <el-button class="comBtn com_send_btn" size="small" @click="uploadSslCert" :loading="loading">保存</el-button>
                </div>
            </el-dialog>

            <!-- 证书请求 -->
            <el-dialog title="证书请求" :visible.sync="askVisible" width="25%" :before-close="closeAsk" :close-on-click-modal="false">
                <el-form label-width="100px" ref="askFrom" :model="certificateFrom" :rules="certRules">
                    <el-form-item label="证书DN：" prop="dn">
                        <el-input size="small" v-model="certificateFrom.dn" placeholder="DN格式：CN=XXXXX,C=XX"></el-input>
                    </el-form-item>
                    <el-form-item label="证书类型：" prop="certAlgo">
                        <el-select v-model="certificateFrom.certAlgo" filterable placeholder="请选择" size="small" style="width: 200px">
                            <el-option v-for="item in keyOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="哈希算法：" prop="algName">
                        <el-select v-model="certificateFrom.algName" placeholder="请选择" size="small" style="width: 200px">
                            <el-option v-for="item in algorithmOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                </el-form>

                <div slot="footer" class="dialog-footer">
                    <el-button class="comBtn com_reset_btn" size="small" @click='closeAsk'>取消</el-button>
                    <el-button class="comBtn com_send_btn" size="small" @click="createCsr">保存</el-button>
                </div>
            </el-dialog>

            <!-- 证书应答 -->
            <el-dialog title="证书应答" :visible.sync="answerVisible" width="25%" :before-close='closeAnswer' :close-on-click-modal="false">
                <span style="color: red">* 应答证书后，需要手动重启服务后生效！</span>
                <el-form label-width="100px" ref="answerForm" :model="certificateFrom" :rules="certRules" style="margin-top: 15px">
                    <el-form-item label="设备证书" prop="certFile" v-if="isShowId">
                        <el-upload
                                class="upload-demo"
                                action="#"
                                :on-remove="certUploadRemove"
                                :on-change="certUploadChange"
                                :limit="1"
                                :file-list="certificateFrom.fileList"
                                :auto-upload="false">
                            <el-button class="comBtn com_send_btn" size="small" type="primary">点击上传</el-button>
                        </el-upload>
                    </el-form-item>
                    <!-- SM2 -->
                    <div v-else>
                        <el-form-item label="签名证书" prop="certFile">
                            <el-upload
                                    class="upload-demo"
                                    action="#"
                                    accept='.cer, .crt, .pem'
                                    :on-remove="signCertRemove"
                                    :on-change="signCertChange"
                                    :limit="1"
                                    :file-list="certificateFrom.signCertArr"
                                    :auto-upload="false">
                                <el-button class="comBtn com_send_btn" size="small" type="primary">点击上传</el-button>
                            </el-upload>
                        </el-form-item>
                        <el-form-item label="加密证书" prop="encCertFile">
                            <el-upload
                                    class="upload-demo"
                                    action="#"
                                    accept='.cer, .crt, .pem'
                                    :on-remove="encCertRemove"
                                    :on-change="encCertChange"
                                    :limit="1"
                                    :file-list="certificateFrom.encCertArr"
                                    :auto-upload="false">
                                <el-button class="comBtn com_send_btn" size="small" type="primary">点击上传</el-button>
                            </el-upload>
                        </el-form-item>
                        <el-form-item label="加密密钥" prop="encPrivateFile">
                            <el-upload
                                    class="upload-demo"
                                    action="#"
                                    accept='.p7b, .pem'
                                    :on-remove="encKeyRemove"
                                    :on-change="encKeyChange"
                                    :limit="1"
                                    :file-list="certificateFrom.encKeyArr"
                                    :auto-upload="false">
                                <el-button class="comBtn com_send_btn" size="small" type="primary">点击上传</el-button>
                            </el-upload>
                        </el-form-item>
                    </div>
                    <!--<el-form-item label="PIN:" prop="pin">-->
                    <!--<el-input size="small" v-model="certificateFrom.pin" type="password"-->
                    <!--placeholder="请输入正确的证书保护密码"></el-input>-->
                    <!--</el-form-item>-->
                </el-form>

                <div slot="footer" class="dialog-footer">
                    <el-button class="comBtn com_reset_btn" size="small" @click='closeAnswer'>取消</el-button>
                    <el-button class="comBtn com_send_btn" size="small" @click="answerSubmit">保存</el-button>
                </div>
            </el-dialog>
        </el-card>
    </div>
</template>

<script>
    // import systemMG from "@/api/systemMG";
    // import initMG from "@/api/initMG";
    import {getExtendName} from "@/utils/util";

    export default {
        name: "certificate",
        components: {},

        data() {
            const validatorDN = (rule, value, callback) => {
                var chineseExp = RegExp(/^[\u0000-\u007f]+$/);
                if (value !== "") {
                    if (!chineseExp.test(value)) {
                        callback(new Error("DN不能包含中文及中文字符"));
                    }
                    if (value.indexOf("，") != -1) {
                        callback(new Error("dn不能包含中文逗号"));
                    } else if (value.indexOf(", ") != -1) {
                        callback(new Error("逗号后不能有空格"));
                    } else {
                        this.verifyDN(value, callback);
                    }
                } else {
                    callback();
                }
            };
            let _this = this;
            return {
                tableDataHeader: [
                    {
                        label: '证书类型',
                        prop: 'certType',
                        type: "text_formatter",
                        // width: '150',
                        formatter: function (value, row) {
                            if (value == 1) return "证书申请";
                            else if (value == 2) return "证书上传";
                            else if (value == 3) return "证书请求";
                        }
                    },
                    {label: '颁发给', prop: 'dn', type: "normal"},
                    {label: '颁发者', prop: 'issuer', type: "normal"},
                    {label: '截止日期', prop: 'endDate', type: "time"},
                    {
                        label: '是否启用',
                        prop: 'isEnable',
                        type: "switch",
                        callback: function (row, opts, event) {
                            _this.changeEnable(row);
                        }
                    },
                    {
                        label: "操作",
                        prop: "1",
                        type: "operation",
                        width: "180",
                        tag: [
                            {
                                name: "查看",
                                operType: "get",
                                tagType: "el-button",
                                attributes: {
                                    size: "mini",
                                    type: "text",
                                    icon: "el-icon-search"
                                },
                                callback: function (row, opts, event) {
                                    _this.searchSslCert(row);
                                }
                            },
                            /*{
                              name: "下载",
                              operType: "put",
                              tagType: "el-button",
                              attributes: {
                                size: "mini",
                                type: "text",
                                icon: "el-icon-edit"
                              },
                              isShow: function(row) {
                                const certType = row.certType;
                                if (certType != 1) {
                                  return false;
                                }
                              },
                              callback: function (row, opts, event) {
                                _this.downloadCert(row);
                              }
                            },*/
                            {
                                name: "重置",
                                operType: "put",
                                tagType: "el-button",
                                attributes: {
                                    size: "mini",
                                    type: "text",
                                    icon: "el-icon-edit"
                                },
                                isShow: function (row) {
                                    const certType = row.certType;
                                    if (certType != 1) {
                                        return false;
                                    }
                                },
                                callback: function (row, opts, event) {
                                    _this.systemRegisterView(row);
                                }
                            },
                            {
                                name: "上传",
                                operType: "put",
                                tagType: "el-button",
                                attributes: {
                                    size: "mini",
                                    type: "text",
                                    icon: "el-icon-edit"
                                },
                                isShow: function (row) {
                                    const certType = row.certType;
                                    if (certType != 2) {
                                        return false;
                                    }
                                },
                                callback: function (row, opts, event) {
                                    _this.uploadView(row);
                                }
                            },
                            {
                                name: "配置",
                                operType: "config",
                                tagType: "el-dropdown",
                                children: [
                                    {
                                        name: "配置",
                                        tagType: "el-button",
                                        attributes: {
                                            size: "mini",
                                            type: "text",
                                            icon: "el-icon-setting"
                                        }
                                    },
                                    {
                                        tagType: "el-dropdown-menu", attributes: {slot: "dropdown"},
                                        children: [
                                            {
                                                name: "证书请求",
                                                tagType: "el-dropdown-item",
                                                attributes: {},
                                                isShow: function (row) {
                                                    // return row.dn != null && row.dn !== '';
                                                    if (row.dn != null && row.dn != '') {
                                                        return false;
                                                    } else {
                                                        return true;
                                                    }
                                                },
                                                callback: function (row) {
                                                    _this.askView(row);
                                                }
                                            },
                                            {
                                                name: "证书应答",
                                                tagType: "el-dropdown-item",
                                                attributes: {},
                                                isShow: function (row) {
                                                    // return row.issuer != null && row.issuer != '';
                                                    if (row.issuer != null && row.issuer != '') {
                                                        return false;
                                                    } else {
                                                        return true;
                                                    }
                                                },
                                                callback: function (row) {
                                                    _this.answerView(row);
                                                }
                                            },
                                            {
                                                name: "清空证书",
                                                tagType: "el-dropdown-item",
                                                attributes: {},
                                                isShow: function (row) {
                                                    if (row.dn == null || row.dn == '') {
                                                        return false;
                                                    } else {
                                                        return true;
                                                    }
                                                },
                                                callback: function (row) {
                                                    _this.clearSslCert(row);
                                                }
                                            },
                                        ]
                                    }
                                ],
                                isShow: function (row) {
                                    const certType = row.certType;
                                    if (certType != 3) {
                                        return false;
                                    }
                                },
                            }
                        ]
                    }
                ],
                keyOptions: [
                    {value: 'RSA2048', label: 'RSA2048'},
                    {value: 'RSA4096', label: 'RSA4096'}
                ],
                isShowId: true,
                // 签名算法
                algorithmOptions: [
                    {value: 'SHA256', label: 'SHA256'}
                ],
                loading: false,
                refreshLoading: false,
                rasTableData: [],
                sm2TableData: [],
                // multipleSelection: [], // 无效变量
                searchSslVisible: false,  //是否显示编辑界面
                uploadVisible: false,   //更新证书
                askVisible: false,   //证书请求
                answerVisible: false,  // 证书应答
                systemVisible: false,  // 系统注册页面
                certificateFrom: {
                    dn: '',
                    algName: 'SHA256',
                    cerSource: '',
                    certAlgo: 'RSA2048',
                    // fileBuffer: '',
                    pin: '',
                    alias: '',
                    base64Text: '', //设备证书
                    fileList: [],

                    certFile: null,
                    signCertArr: [],
                    encCertFile: null,
                    encCertArr: [],
                    encPrivateFile: null,
                    encKeyArr: [],
                },
                editForm: {
                    id: '',
                    purpose: '',
                    password: '',
                    dn: '',
                    issuer: '',
                    alias: '',
                    endDate: '',
                    isEnable: '',
                    certType: '',
                    certAlg: '',
                    requestStatus: '',
                    digestEncAlg: '',
                    serialNumber: '',
                    secretKey: '',
                    fingerprintSha1: '',
                    fingerprintMd5: ''
                },
                certRules: {
                    dn: [
                        {required: true, message: '请输入证书DN', trigger: 'blur'},
                        {validator: validatorDN, trigger: 'blur'}
                    ],
                    base64Text: [{required: true, message: "请上传证书", trigger: "blur"}],
                    pin: [{required: true, message: "请输入PIN码", trigger: "blur"}],
                    certFile: [{required: true, message: "请上传证书", trigger: "change"}],
                    encCertFile: [{required: true, message: "请上传证书", trigger: "change"}],
                    encPrivateFile: [{required: true, message: "请上传证书", trigger: "change"}],
                }
            }
        },
        created() {
            this.querySslCertList();
        },
        methods: {
            changeEnable(row) {
                const dn = row.dn;
                let currentEnable = row.isEnable;
                const sslId = row.id;
                if (dn == null || dn == '') return this.$message.error("该类型证书未注册！");
                this.$confirm('确定启用选择证书吗? 启用证书后需要手动重启服务！', '启用确认', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    if (!currentEnable) {
                        this.$http.systemMG.enableSslCert(sslId).then((res) => {
                            const code = res.code;
                            if (code == 0) {
                                this.$message.success("设置证书成功！重启服务后生效！");
                                this.querySslCertList();
                            } else {
                                // this.$message.warning(res.msg);
                            }
                        });
                    } else {
                        this.$message.warning("证书必须启用一个，请选择需要启用的证书，启用成功后会关闭之前的证书！");
                    }
                });
            },
            // 获取列表
            querySslCertList() {
                this.rasTableData = [];
                this.sm2TableData = [];
                this.refreshLoading = true;
                this.$http.systemMG.quertSslCertList().then(({code, data, msg}) => {
                    if (code == '0') {
                        // 隐藏国密证书
                        data.map(item => {
                            if (item.id <= 3) this.rasTableData.push(item);
                            if (item.id > 3 && item.id <= 6) this.sm2TableData.push(item)
                        })
                    } else {
                        // this.$message.error(msg);
                    }
                    this.refreshLoading = false;
                }).catch(() => {
                    this.refreshLoading = false;
                })
            },
            verifyDN(dn, callback) {
                this.$http.systemMG.verifyDN(dn).then(({code, msg}) => {
                    code === 0 ? callback() : callback(msg)
                })
            },
            // 重启服务
            rebootNginxService() {
                this.$confirm('确定重启Nginx服务吗?', '重启确认', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.systemMG.reStartNginx().then(({code, msg}) => {
                        // code === 0 ? this.$message.success("重启服务成功！") : this.$message.error(msg)
                        if(code === 0) this.$message.success("重启服务成功！")
                    })
                });
            },
            // 设备证书 选择
            certUploadChange(file) {
                const iscert = "application/x-x509-ca-cert";
                if (!iscert) {
                    this.$message.error('上传文件只能是x509格式!');
                    return;
                }
                let index = file.name.lastIndexOf(".");
                let ext = file.name.substr(index + 1);
                if (ext === 'cer') {
                    this.certificateFrom.certFile = file.raw;
                    this.certificateFrom.encCertFile = file.raw;
                    this.certificateFrom.encPrivateFile = file.raw;
                    this.$nextTick(() => {
                        this.$refs.answerForm.validateField('certFile');
                    })
                } else {
                    this.$message.warning('文件格式错误, 请上传正确格式!')
                }
            },
            // 设备证书 移除
            certUploadRemove() {
                this.certificateFrom.base64Text = null;
                this.certificateFrom.certFile = null;
            },
            // 签名证书
            signCertChange(file) {
                let index = file.name.lastIndexOf(".");
                let ext = file.name.substr(index + 1);
                if (ext === 'cer' || ext === 'crt' || ext === 'pem') {
                    this.certificateFrom.certFile = file.raw;
                    this.$nextTick(() => {
                        this.$refs.answerForm.validateField('certFile');
                    })
                } else {
                    this.$message.warning('文件格式错误, 请上传正确格式!')
                }
            },
            signCertRemove() {
                this.certificateFrom.certFile = null;
                this.certificateFrom.signCertArr = [];
            },
            // 加密证书
            encCertChange(file) {
                let index = file.name.lastIndexOf(".");
                let ext = file.name.substr(index + 1);
                if (ext === 'cer' || ext === 'crt' || ext === 'pem') {
                    this.certificateFrom.encCertFile = file.raw;
                    this.$nextTick(() => {
                        this.$refs.answerForm.validateField('encCertFile');
                    })
                } else {
                    this.$message.warning('文件格式错误, 请上传正确格式!')
                }
            },
            encCertRemove() {
                this.certificateFrom.encCertFile = null;
                this.certificateFrom.encCertArr = [];
            },
            // 加密密钥
            encKeyChange(file) {
                let index = file.name.lastIndexOf(".");
                let ext = file.name.substr(index + 1);
                if (ext === 'p7b' || ext === 'pem') {
                    this.certificateFrom.encPrivateFile = file.raw;
                    this.$nextTick(() => {
                        this.$refs.answerForm.validateField('encPrivateFile');
                    })
                } else {
                    this.$message.warning('文件格式错误, 请上传正确格式!')
                }
            },
            encKeyRemove() {
                this.certificateFrom.encPrivateFile = null;
                this.certificateFrom.encKeyArr = [];
            },

            // 提交证书应答
            answerSubmit() {
                this.$refs["answerForm"].validate((valid) => {
                    if (valid) {
                        let params = new FormData();
                        Object.keys(this.certificateFrom).forEach(key => {
                            params.append(key, this.certificateFrom[key])
                        });
                        this.$http.systemMG.uploadSslCert(params).then((res) => {
                            if (res.code == 0) {
                                this.querySslCertList();
                                this.clearCertificate('answerForm');
                                this.answerVisible = false;
                                this.$message.success("证书应答成功，请重启服务！");
                            } else {
                                // this.$message.error(res.msg);
                            }
                        })
                    }
                })
            },
            // 证书应答 取消清除
            closeAnswer() {
                this.clearCertificate('answerForm');
                this.answerVisible = false;
            },

            // 站点证书
            handleChange(file) {
                const isPkcs12 = (file.raw.type === 'application/x-pkcs12');
                if (!isPkcs12) {
                    this.$message.error('上传文件只能是PKCS12格式!');
                    return;
                }
                let exName = getExtendName(file.name);
                if (exName != "p12" && exName != "pfx") {
                    this.$message.error('请上传.p12证书！');
                    this.certificateFrom.fileList = [];
                    return;
                }
                let _this = this;
                var reader = new FileReader();
                reader.readAsDataURL(file.raw);
                reader.onload = function (e) {
                    console.log(this.result); //图片的base64数据
                    _this.certificateFrom.base64Text = this.result.split(",")[1];
                }
            },
            // 移除
            handleRemove() {
                this.certificateFrom.base64Text = '';
            },
            // 证书应答
            answerView(row) {
                if (row.dn == null || row.dn == '') {
                    this.$message.warning("尚未进行证书请求，请先请求证书！");
                    return;
                }
                this.isShowId = row.id <= 3;
                this.changeSelectOpt(row.id);
                this.certificateFrom.cerSource = row.certType;
                this.answerVisible = true;
            },
            systemRegisterView(row) {
                this.changeSelectOpt(row.id);
                this.certificateFrom.cerSource = row.certType;
                this.systemVisible = true;
            },
            changeSelectOpt(id) {
                if (id > 3) {
                    this.keyOptions = [{value: 'SM2', label: 'SM2'}];
                    this.algorithmOptions = [{value: 'SM3', label: 'SM3'}];
                    this.certificateFrom.certAlgo = 'SM2';
                    this.certificateFrom.algName = 'SM3';
                } else {
                    this.keyOptions = [
                        {value: 'RSA2048', label: 'RSA2048'},
                        {value: 'RSA4096', label: 'RSA4096'}
                    ];
                    this.algorithmOptions = [{value: 'SHA256', label: 'SHA256'}];
                    this.certificateFrom.certAlgo = 'RSA2048';
                    this.certificateFrom.algName = 'SHA256';
                }
            },
            closeSystemRegister() {
                this.clearCertificate('certificateFrom');
                this.systemVisible = false;
                this.$refs["certificateFrom"].clearValidate();
            },
            askView(row) {
                this.changeSelectOpt(row.id);
                this.certificateFrom.cerSource = row.certType;
                this.askVisible = true;
            },
            closeAsk() {
                this.clearCertificate('askFrom');
                this.askVisible = false;
                this.$refs["askFrom"].clearValidate();
            },
            createCsr() {
                this.$refs["askFrom"].validate((valid) => {
                    if (valid) {
                        this.$http.initMG.createCsr(this.certificateFrom).then((res) => {
                            this.closeAsk();
                            this.querySslCertList();

                            let blob = new Blob([res], {
                                type: 'application/force-download'
                            });
                            let fileName = Date.parse(new Date()) + '.csr';
                            if (window.navigator.msSaveOrOpenBlob) {
                                navigator.msSaveBlob(blob, fileName)
                            } else {
                                var link = document.createElement('a');
                                link.href = window.URL.createObjectURL(blob);
                                link.download = fileName;
                                link.click();
                                //释放内存
                                window.URL.revokeObjectURL(link.href)
                            }
                        })
                    }
                })
            },

            uploadView(row) {
                this.certificateFrom.cerSource = row.certType;
                this.uploadVisible = true;
            },
            closeUploadView() {
                this.clearCertificate('certificateFrom');
                this.$refs["certificateFrom"].clearValidate();
                this.uploadVisible = false;
            },
            uploadSslCert() {
                this.$refs["certificateFrom"].validate((valid) => {
                    if (valid) {
                        this.loading = true;
                        let params = new FormData();
                        Object.keys(this.certificateFrom).forEach(key => {
                            params.append(key, this.certificateFrom[key])
                        });
                        params.delete('certFile');
                        params.delete('encCertFile');
                        params.delete('encPrivateFile');
                        this.$http.systemMG.uploadSslCert(params).then((res) => {
                                const code = res.code;
                                if (code == '0') {
                                    this.$message.success("更新设备证书成功，请重启服务！");
                                    if (this.certificateFrom.cerSource == 1) {
                                        this.closeSystemRegister();
                                    } else {
                                        this.closeUploadView();
                                    }
                                    this.querySslCertList();
                                } else {
                                    // this.$message.error(res.msg);
                                }
                            },
                            error => {
                                this.closeUploadView();
                            });
                        this.loading = false;
                    }
                })
            },
            clearSslCert(row) {
                const isEnable = row.isEnable;
                if (isEnable) {
                    this.$message.error("启用的证书无法清除！");
                    return;
                }
                this.$confirm('确定要清除设备证书吗?', '删除确认', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.systemMG.clearSslCert(row.id).then((res) => {
                        const code = res.code;
                        if (code == 0) {
                            this.$message.success("清除设备证书成功！");
                            this.querySslCertList();
                        } else {
                            // this.$message.error(res.msg)
                        }
                    })
                })
            },
            // 查看证书 详情
            closeSslCert() {
                this.searchSslVisible = false;
            },
            searchSslCert(row) {
                let dn = row.dn;
                if (!dn) {
                    this.$message.info("证书不存在！");
                    return;
                }
                Object.keys(this.editForm).forEach(key => {
                    this.editForm[key] = row[key]
                });
                this.searchSslVisible = true;
            },
            clearCertificate(name) {
                this.$refs[name].clearValidate();
                Object.assign(this.$data.certificateFrom, this.$options.data().certificateFrom)
            }
        },
    }
</script>
<style lang="less" scoped>
    /deep/ .el-divider--horizontal {
        margin: 5px 0 10px;
    }

    /deep/ .el-descriptions-item__label.is-bordered-label {
        width: 130px;
    }

    .admin-button {
        margin-top: 20px;
        margin-bottom: 20px;
        float: left;
    }
</style>

/**
 * 业务管理 路由
 * */
const businessRouter = [
    {
        path: '/keys-manage',
        name: '密钥管理',
        component: () => import('@/views/business/secretKey/keys-manage'),
        meta: {
            title: '密钥管理',
            requireAuth: true
        }
    }, {
        path: '/SM9Keys-manage',
        name: 'SM9密钥管理',
        component: () => import('@/views/business/secretKey/SM9-key-manage'),
        meta: {
            title: 'SM9密钥管理',
            requireAuth: true
        }
    }, {
        path: '/app-manage',
        name: '应用管理',
        component: () => import('@/views/business/app-manage'),
        meta: {
            title: '应用管理',
            requireAuth: true
        }
    }, {
        path: '/white-list',
        name: '白名单管理',
        component: () => import('@/views/business/white-list'),
        meta: {
            title: '白名单管理',
            requireAuth: true
        }
    }, {
        path: '/sign-configure',
        name: '业务端口配置',
        component: () => import('@/views/business/sign-configure'),
        meta: {
            title: '业务端口配置',
            requireAuth: true
        }
    },

    // 证书管理
    {
        path: '/certificate-manage',
        name: '证书管理',
        component: () => import('@/views/business/certificate-manage'),
        meta: {
            title: '证书管理',
            requireAuth: true
        }
    }, {
        path: '/certificate-chain-manage',
        name: '证书链管理',
        component: () => import('@/views/business/certificate-chain-manage'),
        meta: {
            title: '证书链管理',
            requireAuth: true
        }
    },
    // 签名验签管理
    {
        path: '/service-checkout',
        name: '服务检测',
        component: () => import('@/views/system/service-checkout'),
        meta: {
            title: '服务检测',
            requireAuth: true
        }
    }, {
        path: '/self-service',
        name: '自测历史',
        component: () => import('@/views/system/self-checkout-list'),
        meta: {
            title: '自测历史',
            requireAuth: true
        }
    },

    

    // 时间戳管理
    {
        path: '/time_certificate',
        name: '证书绑定',
        component: () => import('@/views/business/time_certificate'),
        meta: {
            title: '证书绑定',
            requireAuth: true
        }
    }, {
        path: '/timestamp-manage',
        name: '时间戳检索',
        component: () => import('@/views/business/timestampManage'),
        meta: {
            title: '时间戳检索',
            requireAuth: true
        }
    }, {
        path: '/timestamp-check',
        name: '时间戳备份查看',
        component: () => import('@/views/business/timestamp-check'),
        meta: {
            title: '时间戳备份查看',
            requireAuth: false
        }
    },

    // {
    //     path: '/sign-configure',
    //     name: '删除历史',
    //     component: () => import('@/views/business/sign-configure'),
    //     meta: {
    //         title: '删除历史',
    //         requireAuth: true
    //     }
    // }
];

export default {businessRouter}

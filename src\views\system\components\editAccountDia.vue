<template>
    <el-dialog title="编辑管理员" :visible.sync="editVisible" width="500px" :closed="closeEdit" :close-on-click-modal="false">
        <el-form label-width="100px" ref="editAccountForm" :model="editAccountForm" id="example_form" :rules="rules">
            <el-form-item label="姓名：" prop="realName">
                <el-input size="small" v-model="editAccountForm.realName" placeholder="请输入姓名"/>
            </el-form-item>
            <el-form-item label="手机号：" prop="phone">
                <el-input size="small" v-model="editAccountForm.phone" placeholder="请输入手机号"/>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button class="comBtn com_reset_btn" size="small" @click="closeEdit">取消</el-button>
            <el-button class="comBtn com_send_btn" size="small" type="primary" @click="updateUser">保存</el-button>
        </div>
    </el-dialog>
</template>

<script>
    // import userMG from "@/api/userMG";
    export default {
        name: "editAccountDia",
        data() {
            const validatorName = (rule, value, callback) => {
                if (value !== "") {
                    let mask = RegExp(/[(\ )(\~)(\!)(\@)(\#)(\$)(\%)(\^)(\&)(\*)(\()(\))(\-)(\+)(\=)(\[)(\])(\{)(\})(\|)(\\)(\;)(\:)(\')(\")(\，)(\。)(\/)(\<)(\>)(\?)(\)]+/);
                    if (mask.test(value)) {
                        callback(new Error("不能含有特殊字符！"));
                    } else {
                        callback();
                    }
                } else {
                    callback();
                }
            };
            const iphone1 = (rule, value, callback) => {
                const iphone1 = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
                if (value == null) {
                    value = ''
                }
                if (value !== '') {
                    if (!iphone1.test(value)) {
                        callback("请输入正确的手机号");
                        return false
                    } else {
                        callback();
                    }
                } else {
                    callback();
                }
            };
            return {
                editVisible: false,
                editAccountForm: {
                    id: '',
                    dn: '',
                    realName: '',
                    phone: '',
                },
                rules: {
                    realName: [
                        {message: '请输入姓名', trigger: 'blur'},
                        {min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur'},
                        {validator: validatorName, trigger: 'blur'}
                    ],
                    phone: [
                        {message: '请输入正确的手机号', trigger: 'blur'},
                        {validator: iphone1, trigger: 'blur',}
                    ]
                }
            }
        },
        methods: {
            initEditAccountFun(row) {
                this.editAccountForm.id = row.id;
                this.dn = row.dn;
                this.editAccountForm.realName = row.realName;
                this.editAccountForm.phone = row.phone;
                this.editVisible = true;
            },
            // 修改管理员
            updateUser() {
                this.$refs["editAccountForm"].validate((valid) => {
                    if (valid) {
                        this.$confirm('确定要修改管理员信息吗?', '编辑确认', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }).then(() => {
                            this.$http.userMG.updateUser(JSON.stringify(this.editAccountForm)).then((res) => {
                                let code = res.code;
                                if (code === 0) {
                                    this.$message.success('修改成功');
                                    this.$emit('parentHandle');
                                    this.closeEdit();
                                } else {
                                    this.$message.warning(res.msg)
                                }
                            })
                        })
                    }
                })
            },
            // 关闭编辑
            closeEdit() {
                this.editVisible = false;
                this.$nextTick(() => {
                    this.$refs["editAccountForm"].resetFields();
                });
            }
        }
    }
</script>

<style scoped>

</style>

<template>
  <div>
    <!-- 告警通知 -->
    <!-- <div class="div_around"> -->
    <!-- <div class="div_bottom">
        <p style="margin-left: 1%">告警通知</p>
      </div> -->
    <!-- <el-row>
      <el-form label-width="110px" style="margin-top: 10px">
        <el-form-item label="是否开启告警:">
          <el-switch
            v-model="openSwitch"
            active-color="#13ce66"
            inactive-color="#ff4949"
            :active-value="1"
            :inactive-value="0"
            @change="changeAlarmSwitch"
          ></el-switch>
        </el-form-item>
      </el-form>
    </el-row> -->
    <!-- </div> -->

    <!-- <div class="demarcation" /> -->

    <!-- 告警阈值 -->
    <el-row :gutter="15">
      <el-col :span="!virtual?10:15">
        <div class="div_around">
          <div class="div_bottom">
            <p style="margin-left: 2%">设置告警阈值</p>
          </div>
          <!-- oninput="value=value.replace(/[^\d]/g,'')" -->
          <el-row>
            <el-form
              label-width="110px"
              style="margin: 2% 6% 0 0"
              :rules="rules"
              ref="alarmForm"
              :model="alarmForm"
            >
              <el-row :gutter="setGutter">
                <el-col :span="12">
                  <el-form-item label="CPU(%)" prop="cpu.threshold">
                    <el-input
                      size="small"
                      v-model.number="alarmForm.cpu.threshold"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="持续时长(分)" prop="cpu.duration">
                    <el-input
                      size="small"
                      v-model="alarmForm.cpu.duration"
                      placeholder="(1-60)分钟"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="setGutter">
                <el-col :span="12">
                  <el-form-item label="内存(%)" prop="memory.threshold">
                    <el-input
                      size="small"
                      v-model="alarmForm.memory.threshold"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="持续时长(分)" prop="memory.duration">
                    <el-input
                      size="small"
                      v-model="alarmForm.memory.duration"
                      placeholder="(1-60)分钟"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="setGutter">
                <el-col :span="12">
                  <el-form-item label="磁盘(%)" prop="disk.threshold">
                    <el-input size="small" v-model="alarmForm.disk.threshold" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="持续时长(分)" prop="disk.duration">
                    <el-input
                      size="small"
                      v-model="alarmForm.disk.duration"
                      placeholder="(1-60)分钟"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <!--          <el-row>-->
              <!--            <el-col :span="8">-->
              <!--              <el-form-item label="磁盘使用上限(%)"  prop="diskUpper.threshold">-->
              <!--                <el-input size="small" v-model="alarmForm.diskUpper.threshold"-->
              <!--                          oninput = "value=value.replace(/[^\d]/g,'')"/>-->
              <!--              </el-form-item>-->
              <!--            </el-col>-->
              <!--            <el-col :span="8">-->
              <!--              <el-form-item label="持续时长(分)" prop="diskUpper.duration">-->
              <!--                <el-input size="small" v-model="alarmForm.diskUpper.duration" placeholder="(0-60)分钟"-->
              <!--                          oninput = "value=value.replace(/[^\d]/g,'')"/>-->
              <!--              </el-form-item>-->
              <!--            </el-col>-->
              <!--          </el-row>-->
              <el-row>
                <el-form-item>
                  <el-button
                    class="comBtn com_send_btn"
                    size="small"
                    @click="setAlarm"
                    >保存</el-button
                  >
                </el-form-item>
              </el-row>
            </el-form>
          </el-row>
        </div>
      </el-col>
      <el-col :span="!virtual?14:1" v-if="!virtual">
        <div class="div_around">
          <div class="div_bottom">
            <p style="margin-left: 2%">业务告警阈值</p>
          </div>
          <el-row>
            <el-form
              style="margin: 2% 3% 0 0"
              :rules="serviceRules"
              ref="serviceAlarmForm"
              :model="serviceAlarmForm"
            >
              <el-row :gutter="setGutter">
                <el-col :span="13">
                  <el-form-item
                    label="签名(失败次数)"
                    :label-width="serviceLeftWith"
                    prop="sign.threshold"
                  >
                    <el-input
                      size="small"
                      v-model.number="serviceAlarmForm.sign.threshold"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="11">
                  <el-form-item
                    label="持续时长(分)"
                    :label-width="serviceRightWith"
                    prop="sign.duration"
                  >
                    <el-input
                      size="small"
                      v-model="serviceAlarmForm.sign.duration"
                      placeholder="(1-60)分钟"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="setGutter">
                <el-col :span="13">
                  <el-form-item
                    label="验签(失败次数)"
                    :label-width="serviceLeftWith"
                    prop="verify.threshold"
                  >
                    <el-input
                      size="small"
                      v-model="serviceAlarmForm.verify.threshold"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="11">
                  <el-form-item
                    label="持续时长(分)"
                    :label-width="serviceRightWith"
                    prop="verify.duration"
                  >
                    <el-input
                      size="small"
                      v-model="serviceAlarmForm.verify.duration"
                      placeholder="(1-60)分钟"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="setGutter">
                <el-col :span="13">
                  <el-form-item
                    label="数字信封封装(失败次数)"
                    :label-width="serviceLeftWith"
                    prop="envelopeEncode.threshold"
                  >
                    <el-input
                      size="small"
                      v-model="serviceAlarmForm.envelopeEncode.threshold"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="11">
                  <el-form-item
                    label="持续时长(分)"
                    :label-width="serviceRightWith"
                    prop="envelopeEncode.duration"
                  >
                    <el-input
                      size="small"
                      v-model="serviceAlarmForm.envelopeEncode.duration"
                      placeholder="(1-60)分钟"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="setGutter">
                <el-col :span="13">
                  <el-form-item
                    label="数字信封解封(失败次数)"
                    :label-width="serviceLeftWith"
                    prop="envelopeDecode.threshold"
                  >
                    <el-input
                      size="small"
                      v-model.number="serviceAlarmForm.envelopeDecode.threshold"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="11">
                  <el-form-item
                    label="持续时长(分)"
                    :label-width="serviceRightWith"
                    prop="envelopeDecode.duration"
                  >
                    <el-input
                      size="small"
                      v-model="serviceAlarmForm.envelopeDecode.duration"
                      placeholder="(1-60)分钟"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="setGutter">
                <el-col :span="13">
                  <el-form-item
                    label="对称加解密(失败次数)"
                    :label-width="serviceLeftWith"
                    prop="symmetry.threshold"
                  >
                    <el-input
                      size="small"
                      v-model="serviceAlarmForm.symmetry.threshold"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="11">
                  <el-form-item
                    label="持续时长(分)"
                    :label-width="serviceRightWith"
                    prop="symmetry.duration"
                  >
                    <el-input
                      size="small"
                      v-model="serviceAlarmForm.symmetry.duration"
                      placeholder="(1-60)分钟"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="setGutter">
                <el-col :span="13">
                  <el-form-item
                    label="非对称加密(失败次数)"
                    :label-width="serviceLeftWith"
                    prop="asymEncode.threshold"
                  >
                    <el-input
                      size="small"
                      v-model="serviceAlarmForm.asymEncode.threshold"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="11">
                  <el-form-item
                    label="持续时长(分)"
                    :label-width="serviceRightWith"
                    prop="asymEncode.duration"
                  >
                    <el-input
                      size="small"
                      v-model="serviceAlarmForm.asymEncode.duration"
                      placeholder="(1-60)分钟"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="setGutter">
                <el-col :span="13">
                  <el-form-item
                    label="非对称解密(失败次数)"
                    :label-width="serviceLeftWith"
                    prop="asymDecode.threshold"
                  >
                    <el-input
                      size="small"
                      v-model.number="serviceAlarmForm.asymDecode.threshold"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="11">
                  <el-form-item
                    label="持续时长(分)"
                    :label-width="serviceRightWith"
                    prop="asymDecode.duration"
                  >
                    <el-input
                      size="small"
                      v-model="serviceAlarmForm.asymDecode.duration"
                      placeholder="(1-60)分钟"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="setGutter">
                <el-col :span="13">
                  <el-form-item
                    label="数字摘要(失败次数)"
                    :label-width="serviceLeftWith"
                    prop="hash.threshold"
                  >
                    <el-input
                      size="small"
                      v-model="serviceAlarmForm.hash.threshold"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="11">
                  <el-form-item
                    label="持续时长(分)"
                    :label-width="serviceRightWith"
                    prop="hash.duration"
                  >
                    <el-input
                      size="small"
                      v-model="serviceAlarmForm.hash.duration"
                      placeholder="(1-60)分钟"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="setGutter">
                <el-col :span="13">
                  <el-form-item
                    label="数字MAC(失败次数)"
                    :label-width="serviceLeftWith"
                    prop="mac.threshold"
                  >
                    <el-input
                      size="small"
                      v-model="serviceAlarmForm.mac.threshold"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="11">
                  <el-form-item
                    label="持续时长(分)"
                    :label-width="serviceRightWith"
                    prop="mac.duration"
                  >
                    <el-input
                      size="small"
                      v-model="serviceAlarmForm.mac.duration"
                      placeholder="(1-60)分钟"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-form-item :label-width="serviceLeftWith">
                  <el-button
                    class="comBtn com_send_btn"
                    size="small"
                    @click="setserviceAlarm"
                    >保存</el-button
                  >
                </el-form-item>
              </el-row>
            </el-form>
          </el-row>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import alarmMG from "@/api/alarmMG";

export default {
  name: "host-alarm",
  data() {
    const checkThreshold = (rule, value, callback) => {
      console.log("value" + value);
      let reg = /^\+?[1-9]\d*$/;
      if (!reg.test(value))
        return callback(new Error("格式错误, 请输入正整数!"));
      if (value < 1 || value > 100) {
        callback(new Error("阈值在 1~100 之间"));
      } else {
        callback();
      }
    };
    const checkDuration = (rule, value, callback) => {
      let reg = /^\+?[1-9]\d*$/;
      if (!reg.test(value))
        return callback(new Error("格式错误, 请输入正整数!"));
      if (value < 1 || value > 60) {
        callback(new Error("持续时长在 1~60 分钟！"));
      } else {
        callback();
      }
    };
    const numRule = (rule, value, callback) => {
      let reg = /^\+?[1-9]\d*$/;
      if (!reg.test(value) && value) {
        return callback(new Error("格式错误, 请输入正整数!"));
      } else {
        callback();
      }
    };
    return {
      virtual: true,
      serviceLeftWith: "180px",
      serviceRightWith: "110px",
      setGutter: 15,
      openSwitch: 0,
      serviceAlarmForm: {
        sign: {
          threshold: null,
          duration: null,
        },
        verify: {
          threshold: null,
          duration: null,
        },
        envelopeEncode: {
          threshold: null,
          duration: null,
        },
        envelopeDecode: {
          threshold: null,
          duration: null,
        },
        symmetry: {
          threshold: null,
          duration: null,
        },
        asymEncode: {
          threshold: null,
          duration: null,
        },
        asymDecode: {
          threshold: null,
          duration: null,
        },
        hash: {
          threshold: null,
          duration: null,
        },
        mac: {
          threshold: null,
          duration: null,
        },
      },
      alarmForm: {
        cpu: {
          threshold: null,
          duration: null,
        },
        memory: {
          threshold: null,
          duration: null,
        },
        disk: {
          threshold: null,
          duration: null,
        },
        diskUpper: {
          threshold: null,
          duration: null,
        },
      },
      serviceRules: {
        sign: {
          threshold: [
            {
              required: false,
              message: "请输入签名(失败次数)阈值",
              trigger: "blur",
            },
            { validator: numRule, trigger: "blur" },
            {
              validator: (rule, value, callback) => {
                let val = this.serviceAlarmForm.sign.duration;
                if (val && !value) {
                  return callback(new Error("请输入签名(失败次数)阈值"));
                } else {
                  callback();
                }
              },
              trigger: "blur",
            },
          ],
          duration: [
            { required: false, message: "请输入持续时长", trigger: "blur" },
            { validator: numRule, trigger: "blur" },
            {
              validator: (rule, value, callback) => {
                let val = this.serviceAlarmForm.sign.threshold;
                if (val && !value) {
                  return callback(new Error("请输入持续时长"));
                } else {
                  callback();
                }
              },
              trigger: "blur",
            },
          ],
        },
        verify: {
          threshold: [
            {
              required: false,
              message: "请输入验签(失败次数)阈值",
              trigger: "blur",
            },
            { validator: numRule, trigger: "blur" },
            {
              validator: (rule, value, callback) => {
                let val = this.serviceAlarmForm.verify.duration;
                if (val && !value) {
                  return callback(new Error("请输入验签(失败次数)阈值"));
                } else {
                  callback();
                }
              },
              trigger: "blur",
            },
          ],
          duration: [
            { required: false, message: "请输入持续时长", trigger: "blur" },
            { validator: numRule, trigger: "blur" },
            {
              validator: (rule, value, callback) => {
                let val = this.serviceAlarmForm.verify.threshold;
                if (val && !value) {
                  return callback(new Error("请输入持续时长"));
                } else {
                  callback();
                }
              },
              trigger: "blur",
            },
          ],
        },
        envelopeEncode: {
          threshold: [
            {
              required: false,
              message: "请输入数字信封封装(失败次数)阈值",
              trigger: "blur",
            },
            { validator: numRule, trigger: "blur" },
            {
              validator: (rule, value, callback) => {
                let val = this.serviceAlarmForm.envelopeEncode.duration;
                if (val && !value) {
                  return callback(new Error("请输入数字信封封装(失败次数)阈值"));
                } else {
                  callback();
                }
              },
              trigger: "blur",
            },
          ],
          duration: [
            { required: false, message: "请输入持续时长", trigger: "blur" },
            { validator: numRule, trigger: "blur" },
            {
              validator: (rule, value, callback) => {
                let val = this.serviceAlarmForm.envelopeEncode.threshold;
                if (val && !value) {
                  return callback(new Error("请输入持续时长"));
                } else {
                  callback();
                }
              },
              trigger: "blur",
            },
          ],
        },
        envelopeDecode: {
          threshold: [
            {
              required: false,
              message: "请输入数字信封解封(失败次数)阈值",
              trigger: "blur",
            },
            { validator: numRule, trigger: "blur" },
            {
              validator: (rule, value, callback) => {
                let val = this.serviceAlarmForm.envelopeDecode.duration;
                if (val && !value) {
                  return callback(new Error("请输入数字信封解封(失败次数)阈值"));
                } else {
                  callback();
                }
              },
              trigger: "blur",
            },
          ],
          duration: [
            { required: false, message: "请输入持续时长", trigger: "blur" },
            { validator: numRule, trigger: "blur" },
            {
              validator: (rule, value, callback) => {
                let val = this.serviceAlarmForm.envelopeDecode.threshold;
                if (val && !value) {
                  return callback(new Error("请输入持续时长"));
                } else {
                  callback();
                }
              },
              trigger: "blur",
            },
          ],
        },
        symmetry: {
          threshold: [
            {
              required: false,
              message: "请输入对称加解密(失败次数)阈值",
              trigger: "blur",
            },
            { validator: numRule, trigger: "blur" },
            {
              validator: (rule, value, callback) => {
                let val = this.serviceAlarmForm.symmetry.duration;
                if (val && !value) {
                  return callback(new Error("请输入对称加解密(失败次数)阈值"));
                } else {
                  callback();
                }
              },
              trigger: "blur",
            },
          ],
          duration: [
            { required: false, message: "请输入持续时长", trigger: "blur" },
            { validator: numRule, trigger: "blur" },
            {
              validator: (rule, value, callback) => {
                let val = this.serviceAlarmForm.symmetry.threshold;
                if (val && !value) {
                  return callback(new Error("请输入持续时长"));
                } else {
                  callback();
                }
              },
              trigger: "blur",
            },
          ],
        },
        asymEncode: {
          threshold: [
            {
              required: false,
              message: "请输入非对称加密(失败次数)阈值",
              trigger: "blur",
            },
            { validator: numRule, trigger: "blur" },
            {
              validator: (rule, value, callback) => {
                let val = this.serviceAlarmForm.asymEncode.duration;
                if (val && !value) {
                  return callback(new Error("请输入非对称加密(失败次数)阈值"));
                } else {
                  callback();
                }
              },
              trigger: "blur",
            },
          ],
          duration: [
            { required: false, message: "请输入持续时长", trigger: "blur" },
            { validator: numRule, trigger: "blur" },
            {
              validator: (rule, value, callback) => {
                let val = this.serviceAlarmForm.asymEncode.threshold;
                if (val && !value) {
                  return callback(new Error("请输入持续时长"));
                } else {
                  callback();
                }
              },
              trigger: "blur",
            },
          ],
        },
        asymDecode: {
          threshold: [
            {
              required: false,
              message: "请输入非对称解密(失败次数)阈值",
              trigger: "blur",
            },
            { validator: numRule, trigger: "blur" },
            {
              validator: (rule, value, callback) => {
                let val = this.serviceAlarmForm.asymDecode.duration;
                if (val && !value) {
                  return callback(new Error("请输入非对称解密(失败次数)阈值"));
                } else {
                  callback();
                }
              },
              trigger: "blur",
            },
          ],
          duration: [
            { required: false, message: "请输入持续时长", trigger: "blur" },
            { validator: numRule, trigger: "blur" },
            {
              validator: (rule, value, callback) => {
                let val = this.serviceAlarmForm.asymDecode.threshold;
                if (val && !value) {
                  return callback(new Error("请输入持续时长"));
                } else {
                  callback();
                }
              },
              trigger: "blur",
            },
          ],
        },
        hash: {
          threshold: [
            {
              required: false,
              message: "请输入数字摘要(失败次数)阈值",
              trigger: "blur",
            },
            { validator: numRule, trigger: "blur" },
            {
              validator: (rule, value, callback) => {
                let val = this.serviceAlarmForm.hash.duration;
                if (val && !value) {
                  return callback(new Error("请输入数字摘要(失败次数)阈值"));
                } else {
                  callback();
                }
              },
              trigger: "blur",
            },
          ],
          duration: [
            { required: false, message: "请输入持续时长", trigger: "blur" },
            { validator: numRule, trigger: "blur" },
            {
              validator: (rule, value, callback) => {
                let val = this.serviceAlarmForm.hash.threshold;
                if (val && !value) {
                  return callback(new Error("请输入持续时长"));
                } else {
                  callback();
                }
              },
              trigger: "blur",
            },
          ],
        },
        mac: {
          threshold: [
            {
              required: false,
              message: "请输入数字MAC(失败次数)阈值",
              trigger: "blur",
            },
            { validator: numRule, trigger: "blur" },
            {
              validator: (rule, value, callback) => {
                let val = this.serviceAlarmForm.mac.duration;
                if (val && !value) {
                  return callback(new Error("请输入数字MAC(失败次数)阈值"));
                } else {
                  callback();
                }
              },
              trigger: "blur",
            },
          ],
          duration: [
            { required: false, message: "请输入持续时长", trigger: "blur" },
            { validator: numRule, trigger: "blur" },
            {
              validator: (rule, value, callback) => {
                let val = this.serviceAlarmForm.mac.threshold;
                if (val && !value) {
                  return callback(new Error("请输入持续时长"));
                } else {
                  callback();
                }
              },
              trigger: "blur",
            },
          ],
        },
      },
      rules: {
        cpu: {
          threshold: [
            { required: true, message: "请输入告警阈值", trigger: "blur" },
            { validator: checkThreshold, trigger: "blur" },
          ],
          duration: [
            { required: true, message: "请输入持续时长", trigger: "blur" },
            { validator: checkDuration, trigger: "blur" },
          ],
        },
        memory: {
          threshold: [
            { required: true, message: "请输入告警阈值", trigger: "blur" },
            { validator: checkThreshold, trigger: "blur" },
          ],
          duration: [
            { required: true, message: "请输入持续时长", trigger: "blur" },
            { validator: checkDuration, trigger: "blur" },
          ],
        },
        disk: {
          threshold: [
            { required: true, message: "请输入告警阈值", trigger: "blur" },
            { validator: checkThreshold, trigger: "blur" },
          ],
          duration: [
            { required: true, message: "请输入持续时长", trigger: "blur" },
            { validator: checkDuration, trigger: "blur" },
          ],
        },
        diskUpper: {
          threshold: [
            { required: true, message: "请输入告警阈值", trigger: "blur" },
            { validator: checkThreshold, trigger: "blur" },
          ],
          duration: [
            { required: true, message: "请输入持续时长", trigger: "blur" },
            { validator: checkDuration, trigger: "blur" },
          ],
        },
      },
    };
  },
  methods: {
    //设置告警配置
    setAlarm() {
      this.$refs["alarmForm"].validate((valid) => {
        if (valid) {
          alarmMG.setAlarmInfoConfigure(this.alarmForm).then((res) => {
            let code = res.code;
            if (code == 0) {
              this.$message.success("设置成功");
            }
          });
        }
      });
    },
    setserviceAlarm() {
      this.$refs["serviceAlarmForm"].validate((valid) => {
        if (valid) {
          alarmMG.setbussinessAlarm(this.serviceAlarmForm).then((res) => {
            let code = res.code;
            if (code == 0) {
              this.$message.success("设置成功");
            }
          });
        }
      });
    },
    //获取告警配置 cpu memory disk diskUpper ...
    getAlarmInfo() {
      alarmMG
        .getAlarmInfoConfigure()
        .then((res) => {
          if (res.code === 0 && res.data) {
            const keysToAssign = ["cpu", "memory", "disk"]; // 需要赋值的key列表
            keysToAssign.forEach((key) => {
              if (res.data[key]) {
                this.alarmForm[key].threshold = res.data[key].threshold;
                this.alarmForm[key].duration = res.data[key].duration;
              }
            });
          } else {
            console.error("获取告警信息失败:", res);
          }
        })
        .catch((error) => {
          console.error("请求告警信息出错:", error);
        });
    },
    getServiceAlarmInfo() {
      alarmMG
        .getbussinessAlarm()
        .then((res) => {
          if (res.code === 0 && res.data) {
            this.virtual = res.data.virtual;
            const keysToAssign = [
              "sign",
              "verify",
              "envelopeEncode",
              "envelopeDecode",
              "symmetry",
              "asymEncode",
              "asymDecode",
              "hash",
              "mac",
            ]; // 需要赋值的key列表
            keysToAssign.forEach((key) => {
              if (res.data[key]) {
                this.serviceAlarmForm[key].threshold = res.data[key].threshold;
                this.serviceAlarmForm[key].duration = res.data[key].duration;
              }
            });
          } else {
            console.error("获取业务告警信息失败:", res);
          }
        })
        .catch((error) => {
          console.error("请求业务告警信息出错:", error);
        });
    },
    //修改告警状态
    // changeAlarmSwitch() {
    //   let status = {
    //     status: this.openSwitch,
    //   };
    //   alarmMG.changeAlarmStatus(status).then((res) => {
    //     let code = res.code;
    //     if (code == 0) {
    //       this.$message.success("设置成功！");
    //     } else {
    //       //   this.$message.warning(res.msg);
    //       this.openSwitch = !this.openSwitch;
    //     }
    //     // this.getAlarmStatus();
    //   });
    // },
    // getAlarmStatus() {
    //   alarmMG.getAlarmStatus().then((res) => {
    //     let code = res.code;
    //     if (code == 0) {
    //       this.openSwitch = res.data;
    //     }
    //   });
    // },
  },
  mounted() {
    this.getAlarmInfo();
    this.getServiceAlarmInfo();
    // this.getAlarmStatus();
  },
};
</script>

<style scoped>
.div_around {
  border: 1px solid #cccccc;
}

.div_bottom {
  border-bottom: 1px solid #cccccc;
}

.demarcation {
  height: 20px;
}
</style>

<template>
  <!--为echarts准备一个具备大小的容器dom-->
  <div class="pie" :id="pie" :style="{ height: '310px' }" />
</template>
<script>

export default {
  name: "lineGraph",
  props: ["id", "getData"], // 满足一个页面有多个饼图，建议传入不同id
  data() {
    return {
      pie: this.id || "pie",
      charts: "",
      series1: [],
    };
  },
  // 实时监听父组件传过来的值，进而执行drawBar方法，重绘柱状图
  watch: {
    getData: {
      handler(value) {
        this.optseries(value);
      },
      deep: true,
    },
  },
  mounted() {
    this.optseries(this.getData);
  },
  methods: {
    optseries(getData) {
      var getDataList = getData.opinionDatay;
      var colorArray = [
        "#4D88FE",
        "#50CCCB",
        "#EE9201",
        "#29AAE3",
        "#B74AE5",
        "#0AAF9F",
        "#E89589",
        "#16A085",
        "#4A235A",
        "#C39BD3",
        "#F9E79F",
        "#BA4A00",
        "#ECF0F1",
        "#616A6B",
        "#EAF2F8",
        "#4A235A",
        "#3498DB",
      ];
      this.series1 = [];
      var count = 0;
      for (var p = 0; p < getDataList.length; p++) {
        count++;
        var item = {
          name: getDataList[p].name,
          type: "line",
          areaStyle: {
            opacity: 0.8,
            color: colorArray[p],
          },
          // realtimeSort: true,
          symbol: "circle", //折线点设置为实心点
          itemStyle: {
            normal: {
              color: colorArray[p], //折线点的颜色
            },
          },
          showAllSymbol: true,
          symbolSize: 4,
          smooth: true,
          lineStyle: {
            normal: {
              width: 1,
              // color: "#1E9FFF",
            },
            borderColor: "rgba(0,0,0,.4)",
          },
          data: getDataList[p].value,
        };
        this.series1.push(item);
        if (count === getDataList.length) {
          this.drawPie(this.getData);
        }
      }
    },
    drawPie(getData) {
      var charts = this.$echarts.init(document.getElementById(this.pie));
      var getDataName = getData.opinionDatax;
      charts.setOption(
        {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "line",
              textStyle: {
                color: "#fff",
              },
            },
            formatter: function (params) {
              // 自定义格式化内容
              var res = params[0].name + "<br/>";
              for (var i = 0, l = params.length; i < l; i++) {
                res +=
                  "<span style='display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:" +
                  params[i].color +
                  ";'></span>" +
                  params[i].seriesName +
                  " : " +
                  params[i].value +
                  getData.unit +
                  "<br/>";
              }
              return res;
            },
          },
          grid: {
            left: "3%",
            right: "4%",
            bottom: "3%",
            top: "6%",
            containLabel: true,
          },
          xAxis: {
            type: "category",
            boundaryGap: false,
            // inverse:true,
            axisLine: {
              show: false,
              lineStyle: {
                color: "#666",
                width: 1,
              },
            },
            axisLabel: {
              formatter: "{value}",
              textStyle: {
                color: "#666",
                padding: [10, 0, 0, 0],
                fontSize: 12,
              },
              interval: 300, // 只显示最大和最小坐标
              showMinLabel: true, // 显示最小刻度标签
              showMaxLabel: true, // 显示最大刻度标签
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
              alignWithLabel: true,
              interval: 1,
            },
            data: getDataName,
          },
          yAxis: {
            type: "value",
            // inverse: true,
            nameTextStyle: {
              color: "#666",
              fontSize: 12,
              padding: [0, 0, 0, 0],
              lineHeight: 40,
            },
            minInterval:1,
            min: 0,
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              formatter: "{value} " + getData.unit,
            },
          },
          // animationDuration: 0,//这里两个动画设置可以让图表更顺滑
          // animationEasing: 'cubicInOut',//这里两个动画设置可以让图表更顺滑
          animation: true,
          animationDuration: 0,
          animationEasing: "cubicInOut",
          series: this.series1,
        },
        true
      );
      window.addEventListener("resize", () => {
        setTimeout(() => {
          charts.resize();
        }, 500);
      });
    },
  },
};
</script>

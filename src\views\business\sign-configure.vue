<template>
    <div class="container">
        <el-card style="margin-bottom: 10px">
            <el-form label-width="75px" :inline="true" class="comForm">
                <el-form-item label="业务端口：" style="margin-bottom: 0">
                    <el-input size="small" v-model="queryParam.port" placeholder="请输入业务端口"></el-input>
                </el-form-item>
                <el-form-item style="margin-bottom: 0; background-color: transparent">
                    <el-button class="comBtn com_send_btn" size="small" icon="el-icon-search" @click="getPortListFun">搜 索</el-button>
                    <el-button class="comBtn com_reset_btn" size="small" icon="el-icon-refresh" @click="resetHandle">重 置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card style="padding-bottom: 10px">
            <el-button class="comBtn com_send_btn" size="mini" icon="el-icon-plus" @click="addPortHandle">新 增</el-button>
            <el-button class="comBtn com_send_btn" size="mini" icon="el-icon-refresh" @click="updateConHandle">更新配置</el-button>
            <!--<Strategy :policyType="3"></Strategy>-->
            <createTable
                    style="margin-top: 10px"
                    :tableData="tableData"
                    :tableHeader="tableDataHeader"
                    :isPage="isPage"
                    :pageAttributes="{total: total, currentPage: queryParam.pageNo, pageSize: queryParam.pageSize }"
                    :current-change="cert_currentChange"
                    :sizeChange="cert_sizeChange"
            >
            </createTable>
        </el-card>

        <!--配置信息-->
        <el-dialog :title="title === 'add' ? '端口新增' : '编辑端口'" :visible.sync="openWindow" width="600px" append-to-body :close-on-click-modal="false" @close="closeDiaHandle">
            <el-form ref="portForm" label-width="100px" :model="portForm" :rules="rules">
                <el-form-item label="业务端口" prop="port">
                    <el-input size="small" v-model="portForm.port" placeholder="请输入业务端口"/>
                </el-form-item>
                <el-form-item label="端口说明" prop="des">
                    <el-input size="small" maxlength="255" show-word-limit resize="none" v-model="portForm.des" placeholder="请输入端口说明" type="textarea" rows="5"/>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button size="small" class="comBtn com_reset_btn" @click="closeDiaHandle">取 消</el-button>
                <el-button size="small" class="comBtn com_send_btn" @click="submitHandle">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    // import svsServrMG from "@/api/svsServrMG";
    // import systemMG from "@/api/systemMG";
    import Strategy from "@/components/strategy"
    import {elValidatePort} from '@/utils/myValidate'

    export default {
        name: "svs-list",
        components: {Strategy},
        data() {
            return {
                tableData: [],
                tableDataHeader: [],
                openWindow: false,
                isPage: false,
                total: 0,
                title: 'add',
                // pageAttr: {},
                queryParam: {
                    port: '',
                    pageNo: 1,
                    pageSize: 10
                },
                portForm: {
                    id: '',
                    port: '',
                    des: ''
                },
                rules: {
                    port: [
                        {required: true, message: '请输入端口号', trigger: 'blur'},
                        {validator: elValidatePort, trigger: 'blur'}
                    ]
                },
            }
        },
        methods: {
            // 重置
            resetHandle() {
                Object.assign(this.queryParam, this.$options.data().queryParam);
                this.getPortListFun()
            },
            getPortListFun() {
                this.tableData = [];
                this.$http.svsServrMG.portListApi(this.queryParam).then(({code, data, msg, row}) => {
                    console.log(code, data, msg);
                    this.tableData = data;
                    this.total = row;
                    this.isPage = row > 0
                });
            },
            // 分页操作
            cert_currentChange: function (val) {
                this.queryParam.pageNo = val;
                this.getPortListFun()
            },
            cert_sizeChange: function (val) {
                this.queryParam.pageSize = val;
                this.getPortListFun()
            },
            // 新增端口
            addPortHandle() {
                this.openWindow = true;
                this.title = 'add'
            },
            // 更新配置
            updateConHandle() {
                this.$confirm('更新配置可能会导致网络短暂中断, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.svsServrMG.sendApi().then(({code, data, msg}) => {
                        // console.log(code, data, msg);
                        if (code === 0) {
                            this.$message.success(msg);
                            this.getPortListFun();
                        } else {
                            this.$message.warning(msg)
                        }
                    });
                }).catch(() => {

                })
            },
            // 提交
            submitHandle() {
                this.$refs["portForm"].validate((valid) => {
                    if (valid) {
                        if (this.title === 'add') {
                            this.$http.svsServrMG.addPortApi(JSON.stringify(this.portForm)).then(({code, data, msg}) => {
                                console.log(code, data, msg);
                                if (code === 0) {
                                    this.openWindow = false;
                                    this.getPortListFun();
                                } else {
                                    this.$message.warning(msg)
                                }
                            });
                        } else {
                            this.$http.svsServrMG.editPortApi(JSON.stringify(this.portForm)).then(({code, data, msg}) => {
                                console.log(code, data, msg);
                                if (code === 0) {
                                    this.openWindow = false;
                                    this.getPortListFun();
                                } else {
                                    this.$message.warning(msg)
                                }
                            });
                        }
                    }
                })
            },
            closeDiaHandle() {
                this.openWindow = false;
                Object.assign(this.portForm, this.$options.data().portForm);
                this.$refs.portForm.clearValidate();
            }
        },
        created() {
            let _this = this;
            this.getPortListFun();
            this.tableDataHeader = [
                {
                    type: "index",
                    label: "序号",
                    width: "100",
                }, {
                    type: "normal",
                    label: "业务端口",
                    prop: "port"
                }, {
                    type: "normal",
                    label: "说明",
                    prop: "des"
                }, {
                    type: "col_componet",
                    label: "是否生效",
                    width: "80",
                    // prop: "status",
                    // formatter(value) {
                    //     let success = `<el-button type="success"></el-button>`;
                    //     // return value === 0 ? '未生效' : '已生效';
                    //     return value === 0 ? '未生效' : success;
                    //
                    // },

                    componet: function (h, props) {
                        switch (props.row.status) {
                            case 0:
                                return [h("el-button", {
                                    attrs: {type: "info", circle: true, size: "mini", style: "vertical-align: middle;margin-bottom: 1px;"}
                                })];
                            case 1:
                                return [h("el-button", {
                                    attrs: {type: "success", circle: true, size: "mini", style: "vertical-align: middle;margin-bottom: 1px;"}
                                })];
                            case 2:
                                return [h("el-button", {
                                    attrs: {type: "danger", circle: true, size: "mini", style: "vertical-align: middle;margin-bottom: 1px;"}
                                })];
                        }
                    }
                }, {
                    type: "operation",
                    label: "操作",
                    width: "150",
                    tag: [
                        {
                            name: "修改",
                            operType: "update",
                            tagType: "el-button",
                            attributes: {
                                size: "small",
                                type: "text",
                                icon: "el-icon-edit"
                            },
                            callback: function (row) {
                                _this.portForm = JSON.parse(JSON.stringify(row));
                                _this.title = 'edit';
                                _this.openWindow = true;
                            }
                        }, {
                            name: "删除",
                            operType: "update",
                            tagType: "el-button",
                            attributes: {
                                size: "mini",
                                type: "text",
                                icon: "el-icon-delete"
                            },
                            isShow(row) {
                                return row.builtIn === 2;
                            },
                            callback: function (row) {
                                _this.$confirm('确定删除吗?', '删除确认', {
                                    confirmButtonText: '确定',
                                    cancelButtonText: '取消',
                                    type: 'warning'
                                }).then(() => {
                                    _this.$http.svsServrMG.delPortApi(row.id).then(res => {
                                        // _this.form = res.data;
                                        _this.getPortListFun();
                                    });
                                })
                            }
                        }
                    ]
                }
            ];
        }
    }

</script>

<style lang="less" scoped>
    .success_btn {
        margin-bottom: 20px;
    }
    /deep/ .el-textarea__inner {
        font-family: 'Avenir', Helvetica, Arial, sans-serif;
    }
</style>

<template>
    <el-dialog title="应用绑定管理" :visible.sync="bindAppOpen" width="800px" append-to-body :close-on-click-modal="false"
               @close="Object.assign(app.queryParams,$options.data().app.queryParams)">
        <el-form style="text-align: left;margin-top: -20px;margin-bottom: 10px" :inline="true">
            <el-form-item label="证书绑定类型：" size="mini">
                <el-select v-model="app.queryParams.type" style="width: 160px">
                    <el-option label="全部" :value="-1">全部</el-option>
                    <el-option label="绑定应用" :value="1">绑定应用</el-option>
                    <el-option label="未绑定应用" :value="0">未绑定应用</el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="应用名称 :" size="mini">
                <el-input placeholder="请输入应用名称" v-model="app.queryParams.appName" style="width: 160px" clearable></el-input>
            </el-form-item>
            <el-form-item size="mini">
                <!-- app.queryParams.pageNo = 1; app.queryParams.pageSize = 10; -->
                <el-button class="comBtn com_send_btn" size="small" type="primary" icon="el-icon-search" @click="refreshApp()">查询</el-button>
                <el-button class="comBtn com_reset_btn" size="small" type="primary" icon="el-icon-refresh" @click="resetAppBindForm">重置</el-button>
            </el-form-item>
        </el-form>
        <createTable
                :tableData="app.tableData"
                :tableHeader="app.tableDataHeader"
                :isPage="true"
                :pageAttributes="{total: app.pageAttr.total, currentPage: app.queryParams.pageNo}"
                :current-change="app_change"
                :sizeChange="app_sizeChange"
                :prev-click="prevAppPage"
                :next-click="nextAppPage"
        >
        </createTable>
        <div slot="footer" class="dialog-footer">
            <el-button size="small" class="comBtn com_reset_btn" @click="bindAppOpen=false">取 消</el-button>
            <el-button size="small" class="comBtn com_send_btn" type="primary" @click="bindSubmit">确 定</el-button>
        </div>
    </el-dialog>
</template>

<script>
    import bussMG from "@/api/bussMG";
    import appUtil from "@/api/appMG";
    import {Message} from "element-ui";

    export default {
        data() {
            return {
                bindAppOpen: false, // 应用绑定 弹窗
                // 应用绑定
                bindAppFrom: {
                    appId: "",
                    certId: ""
                },
                // 应用绑定
                app: {
                    tableData: [],
                    isPage: true,
                    tableDataHeader: [],
                    queryParams: {
                        pageNo: 1,
                        pageSize: 10,
                        appName: "",
                        certId: "",
                        // cerType: "",
                        type: 0
                    },
                    pageAttr: {total: 0},
                    appRef: "appTableRef"
                },
            }
        },
        methods: {
            initBindAppFun(row) {
                this.bindAppOpen = true;
                this.app.queryParams.certId = row.id;
                this.bindAppFrom.certId = row.id;
                this.bindAppFrom.appId = row.appId;
                this.loadAppData();
            },
            resetAppBindForm: function () {
                this.app.queryParams.type = 0;
                this.app.queryParams.appName = "";
                this.app.queryParams.pageNo = 1;
                this.app.queryParams.pageSize = 10;
                this.refreshApp();
            },
            // 应用绑定 提交
            bindSubmit: function () {
                let _this = this;
                if (this.bindAppFrom.appId === "" || this.bindAppFrom.appId == null) {
                    Message({
                        message: "请选择应用",
                        type: 'warning'
                    });
                    return;
                }
                bussMG.bindApp(this.bindAppFrom).then(() => {
                    _this.bindAppOpen = false;
                    this.$parent.refreshCert();
                    // _this.refreshCert();
                });
            },
            // 应用绑定
            async loadAppData() {
                let _this = this;
                _this.app.tableData = [];
                await appUtil.appPage(this.app.queryParams).then(res => {
                    _this.app.tableData = res.data;
                    _this.app.pageAttr.total = res.row;
                    // if (_this.row == 0) _this.app.isPage = false;
                    // else _this.app.isPage = true;
                });
            },
            // 应用绑定 分页
            app_change: function (val) {
                this.app.queryParams.pageNo = val;
                this.refreshApp();
            },
            app_sizeChange: function (val) {
                this.app.queryParams.pageSize = val;
                this.refreshApp();
            },
            prevAppPage: function (val) {
                this.app.queryParams.pageNo = val;
                this.refreshApp();
            },
            nextAppPage: function (val) {
                this.app.queryParams.pageNo = val;
                this.refreshApp();
            },
            refreshApp: function () {
                this.loadAppData()
            },
        },
        created() {
            let _this = this;
            // 应用绑定列表
            this.app.tableDataHeader = [
                {
                    label: "", type: "radio", prop: "id", width: "50", checkColumn: "checked", disableColumn: "disabled",
                    callback: function (row) {
                        _this.bindAppFrom.appId = row.id;
                    }
                },
                {label: "序号", type: "index", width: "50"},
                {label: "应用编码", prop: "appCode", type: "normal", width: "100"},
                {label: "应用名称", prop: "appName", type: "normal"},
                {label: "IP地址", prop: "appIp", type: "normal", width: "200"}
            ];
        }
    }
</script>

<style scoped>

</style>

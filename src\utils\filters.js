/**
 * DIGITAL_SIGNATURE(0, KeyUsage.digitalSignature),  //签名
 * NON_REPUDIATION(1, KeyUsage.nonRepudiation),      //防抵赖
 * KEY_ENCIPHERMENT(2, KeyUsage.keyEncipherment),    //密钥加密
 * DATA_ENCIPHERMENT(3, KeyUsage.dataEncipherment),  //加密
 * KEY_AGREEMENT(4, KeyUsage.keyAgreement),          //密钥协商
 * KEY_CERT_SIGN(5, KeyUsage.keyCertSign),           //证书签名
 * CRL_SIGN(6, KeyUsage.cRLSign),                    //CRL签名
 * ENCIPHER_ONLY(7, KeyUsage.encipherOnly),          //仅加密
 * DECIPHER_ONLY(8, KeyUsage.decipherOnly);          //仅解密
 *
 * SERVER_AUTH(101, KeyPurposeId.id_kp_serverAuth),           //服务器认证
 * CLIENT_AUTH(102, KeyPurposeId.id_kp_clientAuth),           //客户端认证
 * CODE_SIGNING(103, KeyPurposeId.id_kp_codeSigning),         //代码签名
 * EMAIL_PROTECTION(104, KeyPurposeId.id_kp_emailProtection), //邮件保护
 * IPSEC_ENDSYSTEM(105, KeyPurposeId.id_kp_ipsecEndSystem),   //IPSec终端
 * IPSEC_TUNNEL(106, KeyPurposeId.id_kp_ipsecTunnel),         //IPSec隧道
 * IPSEC_USER(107, KeyPurposeId.id_kp_ipsecUser),             //IPSec用户
 * TIME_STAMPING(108, KeyPurposeId.id_kp_timeStamping),       //时间戳
 * OCSP_SIGNING(109, KeyPurposeId.id_kp_OCSPSigning);         //OCSP签名
 * */
const allArr = [
    {val: 0, label: '签名'},
    {val: 1, label: '防抵赖'},
    {val: 2, label: '密钥加密'},
    {val: 3, label: '加密'},
    {val: 4, label: '密钥协商'},
    {val: 5, label: '证书签名'},
    {val: 6, label: 'CRL签名'},
    {val: 7, label: '仅加密'},
    {val: 8, label: '仅解密'},
    {val: 101, label: '服务器认证'},
    {val: 102, label: '客户端认证'},
    {val: 103, label: '代码签名'},
    {val: 104, label: '邮件保护'},
    {val: 105, label: 'IPSec终端'},
    {val: 106, label: 'IPSec隧道'},
    {val: 107, label: 'IPSec用户'},
    {val: 108, label: '时间戳'},
    {val: 109, label: 'OCSP签名'},
];
function usageFilter(value) {
    if (!value) return '-';
    let arr = JSON.parse(value);
    let str = '';
    allArr.forEach(item => {
        arr.forEach(i => {
            if (item.val === i) str += item.label + '/'
        })
    });
    str = str.substring(0, str.lastIndexOf('/'));
    return str
}

export function usageFilterFun(value) {
    if (!value) return '-';
    let arr = JSON.parse(value);
    let str = '';
    allArr.forEach(item => {
        arr.forEach(i => {
            if (item.val === i) str += item.label + '/'
        })
    });
    str = str.substring(0, str.lastIndexOf('/'));
    return str
}

export default function (Vue) {
    Vue.filter('usage-type-filter', usageFilter); // 设备类型
}
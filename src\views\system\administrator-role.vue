<template>
    <div>
        <el-card class="box-card" shadow="always" style="padding-bottom: 10px">
            <el-button class="comBtn com_add_btn" size="mini" type="primary" style="margin-bottom: 10px"
                @click="pageRoles">刷新</el-button>
            <!--<br/>-->
            <!-- 列表 -->
            <create-table :tableData="tableData" :tableHeader="tableDataHeader" :isPage="true"
                :pageAttributes="{ total: total }"></create-table>

            <!-- 角色管理 -->
            <el-dialog title="角色管理" :visible.sync="addOrEditVisible" width="500px" :before-close='closeDialog'>
                <el-form label-width="100px" ref="addOrEditForm" :model="addOrEditForm" :rules="rules">
                    <el-form-item label="角色名称：" prop="name">
                        <el-input size="small" v-model="addOrEditForm.name" placeholder="请输入角色名称" maxlength="20"
                            show-word-limit></el-input>
                    </el-form-item>
                    <el-form-item label="角色描述：" prop="desc">
                        <el-input size="small" v-model="addOrEditForm.desc" placeholder="请输入角色描述"></el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button class="comBtn com_reset_btn" size="small" @click='closeDialog'>取消</el-button>
                    <el-button class="comBtn com_send_btn" size="small" @click="saveRole">保存</el-button>
                </div>
            </el-dialog>


            <!-- 查看权限 -->
            <el-dialog title="查看权限" :visible.sync="searchVisible" width="35%" :before-close='closeSearch'
                :close-on-click-modal="false">
                <el-scrollbar style="height: 500px">
                    <el-tree :data="treeData" :props="defaultProps">
                        <!-- <span class="custom-tree-node" slot-scope="{ node, data }">
                            {{ data.advancedOps }}
                            <span v-if="data.advancedOps" class="tree_node_label">{{ node.label }}</span>
                        </span> -->
                    </el-tree>
                </el-scrollbar>
            </el-dialog>
        </el-card>
    </div>
</template>

<script>
// import userMG from "@/api/userMG";
import CreateTable from "@/utils/createTable";

export default {
    name: "administrator-role",
    comments: { CreateTable },
    data() {
        let _this = this;
        return {
            tableDataHeader: [
                { label: '序号', prop: 'id', type: "index", width: '150' },
                { label: '角色名称', prop: 'name', type: "normal" },
                {
                    label: "操作",
                    prop: "1",
                    type: "operation",
                    width: "300",
                    tag: [
                        {
                            name: "查看",
                            operType: "get",
                            tagType: "el-button",
                            attributes: {
                                size: "mini",
                                type: "text",
                                icon: "el-icon-search"
                            },
                            callback: function (row, opts, event) {
                                _this.searchRoleMenus(row);
                            }
                        },
                        {
                            name: "编辑",
                            operType: "put",
                            tagType: "el-button",
                            attributes: {
                                size: "mini",
                                type: "text",
                                icon: "el-icon-edit"
                            },
                            callback: function (row, opts, event) {
                                _this.edit(row);
                            }
                        },
                        {
                            name: "删除",
                            operType: "del",
                            tagType: "el-button",
                            attributes: {
                                size: "mini",
                                type: "text",
                                icon: "el-icon-delete"
                            },
                            isShow: function (row) {
                                return row.system;
                            },
                            callback: function (row, opts, event) {
                                _this.deleteRole(row);
                            }
                        }
                    ]
                }
            ],
            tableData: [],
            rules: {
                name: [{ required: true, max: 20, message: '请输入角色名称', trigger: 'blur' }]
            },
            addOrEditForm: {
                id: null,
                name: null,
                desc: null,
                system: null
            },
            addOrEditVisible: false,
            searchVisible: false,
            // 分页参数
            pageParam: {
                pageNo: 1,
                pageSize: 10
            },
            total: null,
            treeData: [],
            tableHeight: null,
            defaultProps: {
                children: 'menus',
                label: 'menuName'
            }
        }
    },
    created() {
        this.tableHeight = window.innerHeight - 300;
        this.pageRoles()
    },
    methods: {
        edit(row) {
            this.addOrEditForm.id = row.id;
            this.addOrEditForm.name = row.name;
            this.addOrEditForm.desc = row.desc;
            this.addOrEditForm.assign = row.assign;
            this.addOrEditForm.createtime = row.createtime;
            this.addOrEditVisible = true;
        },
        add() {
            this.cleanFrom();
            this.addOrEditVisible = true;
        },
        closeDialog() {
            this.addOrEditVisible = false;
            this.cleanFrom();
        },
        //分页显示管理员角色
        pageRoles() {
            this.tableData = [];
            this.$http.userMG.pageRoles(this.pageParam).then((res) => {
                let code = res.code;
                if (code == 0) {
                    this.tableData = res.data;
                    this.total = res.row;
                }
            })
        },
        //获取角色下所有的菜单
        searchRoleMenus(row) {
            let id = row.id;
            this.treeData = [];
            this.$http.userMG.listMenus(id).then((res) => {
                let code = res.code;
                if (code == 0) {
                    res.data.map(item => { if(!item.advancedOps) this.treeData.push(item) })
                    // this.treeData = res.data;
                    this.searchVisible = true;
                }
            })
        },
        closeSearch() {
            this.treeData = null;
            this.searchVisible = false;
        },
        saveRole() {
            let id = this.addOrEditForm.id;
            if (id == null)
                this.insertRole()
            else
                this.updateRole()
        },
        //添加管理员角色
        insertRole() {
            this.$confirm('确定添加角色信息吗?', '保存确认', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$http.userMG.insertRole(this.addOrEditForm).then((res) => {
                    let code = res.code;
                    if (code == 0) {
                        this.$message({
                            message: '添加角色成功',
                            type: 'success'
                        });
                        this.addOrEditVisible = false;
                        this.pageRoles();
                        this.cleanFrom();
                    }
                })
            })
        },
        //编辑管理员角色
        updateRole() {
            this.$confirm('确定编辑角色信息吗?', '编辑确认', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$refs["addOrEditForm"].validate((valid) => {
                    if (valid) {
                        this.$http.userMG.updateRole(this.addOrEditForm).then((res) => {
                            let code = res.code;
                            if (code == 0) {
                                this.$message({
                                    message: '编辑角色成功',
                                    type: 'success'
                                });
                                this.addOrEditVisible = false;
                                this.pageRoles();
                                this.cleanFrom();
                            }
                        })
                    }
                })
            })
        },
        //删除管理员角色
        deleteRole(row) {
            let id = row.id;
            this.$confirm('确定删除角色信息吗?', '删除确认', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$http.userMG.deleteRole(id).then((res) => {
                    let code = res.code;
                    if (code == 0) {
                        this.$message({
                            message: '删除角色成功',
                            type: 'success'
                        });
                        this.pageRoles()
                    }
                })
            })
        },
        cleanFrom() {
            this.addOrEditForm.id = null;
            this.addOrEditForm.name = null;
            this.addOrEditForm.desc = null;
            this.addOrEditForm.assign = null;
            this.addOrEditForm.createtime = null;
            this.$refs['addOrEditForm'].clearValidate();
        },
    }
}
</script>

<style lang="less" scoped>
/deep/ .el-input .el-input__count .el-input__count-inner {
    padding: 5px;
    background-color: #f0f0f0;
}

/deep/ .el-pagination__total {
    float: left;
}

.admin-button {
    margin-top: 20px;
    margin-bottom: 20px;
    float: left;
}
</style>

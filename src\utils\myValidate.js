export function ValidateIPaddress(ipaddress) {
  let re = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  return re.test(ipaddress);
}
export function ValidatePortNumber(port) {
  var re = /^([0-9]{1,4}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])$/;
  return re.test(port);
}
export function ValidateUsername(name) {
  var re = /^.{5,32}$/;
  return re.test(name);
}
export function ValidatePassword(pass) {
  let re = /^.{8,32}$/;
  let upper = /[A-Z]/.test(pass);
  let lower = /[a-z]/.test(pass);
  let number = /[0-9]/.test(pass);
  let symbol = /[^A-Za-z0-9]/.test(pass);
  return re.test(pass) && (upper + lower + number + symbol) >= 3;
}
export function ValidateNetMask(netmask) {
  var N = "(255|254|252|248|240|224|192|128|0+)";
  var restring = `^${N}\.0+\.0+\.0+|255\.${N}\.0+\.0+|255\.255\.${N}\.0+|255\.255\.255\.${N}$`;
  var re = new RegExp(restring);
  return re.test(netmask);
}
export function ValidateEmailAddress(emailaddress) {
  var re = /^[A-Za-z\d]+([-_.][A-Za-z\d]+)*@([A-Za-z\d]+[-.])+[A-Za-z\d]{2,4}$/;
  return re.test(emailaddress);
}
export function is_empty_string(value) {
  if (value == undefined || value == "") {
    return true;
  } else {
    return false;
  }
}
// http / https
export function isIpHostUrl (rule, value, callback) {
  // let pattern = new RegExp("^([hH][tT]{2}[pP]:\\/\\/|[hH][tT]{2}[pP][sS]:\\/\\/)(([A-Za-z0-9-~]+)\\.)+([A-Za-z0-9-~\\/])+$");
  // let pattern = new RegExp("^(http://|https://)[a-z0-9]+([-.]{1}[a-z0-9]+)*.[a-z]{2,5}(:[0-9]{1,5})?(/.*)?|^((http://|https://)?([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5]).){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(:d{0,5})?(/.*)?$");
  // let pattern = new RegExp("^(https?|http):\\/\\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\\.)*[a-zA-Z0-9-]+\\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\\/($|[a-zA-Z0-9.,?'\\\\+&%$#=~_-]+))*$");
  let pattern = new RegExp("^(http://|https://)[a-z0-9]+([-.]{1}[a-z0-9]+)*.[a-z]{2,5}(:[0-9]{1,5})?(/.*)?|^((http://|https://)?([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[0-9][0-9]?)(\\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\\.)*[a-zA-Z0-9-]+\\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2})))(:[0-9]+)*(\\/($|[a-zA-Z0-9.,?'\\\\+&%$#=~_-]+))*$");
  if (!pattern.test(value)) {
    return callback(new Error('输入的地址格式错误，请重新输入!'));
  }
  return callback();
}


// http / https
export function isIpHost (rule, value, callback) {
  // let pattern = new RegExp("^([hH][tT]{2}[pP]:\\/\\/|[hH][tT]{2}[pP][sS]:\\/\\/)(([A-Za-z0-9-~]+)\\.)+([A-Za-z0-9-~\\/])+$");
  // let pattern = new RegExp("^(http://|https://)[a-z0-9]+([-.]{1}[a-z0-9]+)*.[a-z]{2,5}(:[0-9]{1,5})?(/.*)?|^((http://|https://)?([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5]).){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(:d{0,5})?(/.*)?$");
  let pattern = new RegExp("^(https?|http):\\/\\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\\.)*[a-zA-Z0-9-]+\\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\\/($|[a-zA-Z0-9.,?'\\\\+&%$#=~_-]+))*$");
  if (!pattern.test(value)) {
    return callback(new Error('输入的地址不正确，请重新输入!'));
  }
  return callback();
}
// ldap / ldaps
export function isIpLdap (rule, value, callback) {
  // let pattern = new RegExp("^([hH][tT]{2}[pP]:\\/\\/|[hH][tT]{2}[pP][sS]:\\/\\/)(([A-Za-z0-9-~]+)\\.)+([A-Za-z0-9-~\\/])+$");
  // let pattern = new RegExp("^(http://|https://)[a-z0-9]+([-.]{1}[a-z0-9]+)*.[a-z]{2,5}(:[0-9]{1,5})?(/.*)?|^((http://|https://)?([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5]).){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(:d{0,5})?(/.*)?$");
  let pattern = new RegExp("^(ldaps?|ldap):\\/\\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$;-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\\.)*[a-zA-Z0-9-]+\\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\\/($|[a-zA-Z0-9.,?'\\\\+&%;$#=~_-]+))*$");
  if (!pattern.test(value)) {
    return callback(new Error('输入的地址不正确，请重新输入!'));
  }
  return callback();
}
export function elValidateIP(rule, value, callback) {
  console.log(rule);
  if (is_empty_string(value) || ValidateIPaddress(value)) {
    callback();
  } else {
    callback(new Error(`请输入设备IP地址`));
  }
}
export function isCheckIP(rule, value, callback) {
  console.log(rule);
  if (is_empty_string(value) || ValidateIPaddress(value)) {
    callback();
  } else {
    callback(new Error(`格式错误, 请重新输入!`));
  }
}
export function centerValidateIP(rule, value, callback) {
  console.log(rule);
  if (is_empty_string(value) || ValidateIPaddress(value)) {
    callback();
  } else {
    callback(new Error(`请输入中心IP地址`));
  }
}

// 检测端点地址
export function CheckSiteIp(rule, value, callback) {
  if (!value) {
    return callback();
  }
  let pattern = new RegExp("^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$");  // ^[\u4E00-\u9FA5A-Za-z0-9_]+$
  if (pattern.test(value)) {
    return callback();
  } else {
    return callback(new Error('请输入合法的IP地址'));
  }
}
// 检测数值区间
export const checkNumberRange = (rule, value, callback) => {
  console.log(value);
  if (!value) {
    return callback(new Error('输入不可以为空'));
  }
  if (3600 < value < 86400) {
    console.log(value);
    return callback();
  } else {
    return callback(new Error('最大文件存储空间不合法'));
  }
};

export function elValidatePort(rule, value, callback) {
  if (is_empty_string(value) || ValidatePortNumber(value)) {
    callback();
  } else {
    // callback(new Error("请输入端口号(0-65535)"));
    callback(new Error("格式错误"));
  }
}
export function elValidateUsername(rule, value, callback) {
  if (is_empty_string(value) || ValidateUsername(value)) {
    callback();
  } else {
    callback(new Error("用户名需在5到32位"));
  }
}

export function elValidatePassword(rule, value, callback) {
  if (is_empty_string(value) || ValidatePassword(value)) {
    callback();
  } else {
    callback(new Error("密码需在8到32位，由大小写数字符号至少三种组成"));
  }
}
export function elValidateNetMask(rule, value, callback) {
  if (is_empty_string(value) || ValidateNetMask(value)) {
    callback();
  } else {
    callback(new Error("请输入合法的子网掩码"));
  }
}

export function elValidateEmailAddress(rule, value, callback) {
  if (is_empty_string(value) || ValidateEmailAddress(value)) {
    callback();
  } else {
    callback(new Error("请输入合法的邮箱地址"));
  }
}
/**
 * 设备管理正则
 * */
/**
 * 是否包含空格
 * */
function checkTrim(rule, value, callback) {
  let checkTrim =/^[^\s]*$/;
  if (!checkTrim.test(value)) {
    return callback(new Error('输入的内容不能包含空格!'));
  }
}
/**
 * @description 验证用户名
 * */
export const checkUserName = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('输入不可以为空'));
  }
  checkTrim(rule, value, callback);
  let pattern = new RegExp("^[A-Za-z0-9\u4e00-\u9fa5]{2,16}$");  // ^[\u4E00-\u9FA5A-Za-z0-9_]+$
  if (pattern.test(value)) {
    return callback();
  }
  return callback(new Error('格式错误，请输入2-16字符，中文、字母、数字'));
};

/**
 * @description 验证策略名称 (和部门名称相同)
 * */
export const checkPolicyName = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('输入不可以为空'));
  }
  checkTrim(rule, value, callback);
  let pattern = new RegExp("^[A-Za-z0-9\u4e00-\u9fa5]{2,16}$");  // ^[\u4E00-\u9FA5A-Za-z0-9_]+$
  if (pattern.test(value)) {
    return callback();
  }
  return callback(new Error('格式错误，请输入2-16字符，中文、字母、数字'));
};

/**
 * @description 验证部门名称 (和策略名称相同)
 * */
export const checkDeptName = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('输入不可以为空'));
  }
  checkTrim(rule, value, callback);
  let pattern = new RegExp("^[A-Za-z0-9\u4e00-\u9fa5]{2,16}$");  // ^[\u4E00-\u9FA5A-Za-z0-9_]+$
  if (pattern.test(value)) {
    return callback();
  }
  return callback(new Error('格式错误，请输入2-16字符，中文、字母、数字'));
};

/**
 * @description 验证组织编号
 * */
export const checkName = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('输入不可以为空'));
  }
  checkTrim(rule, value, callback);
  // let pattern = new RegExp("^[A-Za-z0-9_\\\-]{2,16}$");  // ^[\u4E00-\u9FA5A-Za-z0-9_]+$
  let pattern = new RegExp("^[a-zA-Z0-9]?(([a-zA-Z0-9]+[_|-]?)*)[a-zA-Z0-9]+$");  // ^[\u4E00-\u9FA5A-Za-z0-9_]+$
  if (pattern.test(value)) {
    return callback();
  }
  // return callback(new Error('格式错误，请输入2-16字符，数字、字母以及"_""-"'));
  return callback(new Error('格式错误，只能以数字、字母开头或结尾'));
};

/**
 * @description 验证设备名称
 * */
export const checkDevName = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('输入不可以为空'));
  }
  checkTrim(rule, value, callback);
  // let pattern = new RegExp("^[A-Za-z0-9\u4e00-\u9fa5_\\\-]{2,16}$");  // ^[\u4E00-\u9FA5A-Za-z0-9_]+$
  let pattern = new RegExp("^[a-zA-Z0-9\u4e00-\u9fa5]?(([a-zA-Z0-9\u4e00-\u9fa5]+[_|-]?)*)[a-zA-Z0-9\u4e00-\u9fa5]+$");  // ^[\u4E00-\u9FA5A-Za-z0-9_]+$
  if (pattern.test(value)) {
    return callback();
  }
  return callback(new Error('格式错误，请输入2-16字符，中文、数字、字母以及"_""-"'));
};

/**
 * @description 验证 设备 物理位置
 * */
export const checkLocation = (rule, value, callback) => {
  if (!value) {
    return callback();
  }
  checkTrim(rule, value, callback);
  // let pattern = new RegExp("^[A-Za-z0-9\u4e00-\u9fa5_\\\-]{0,32}$");  // ^[\u4E00-\u9FA5A-Za-z0-9_]+$
  let pattern = new RegExp("^[a-zA-Z0-9\u4e00-\u9fa5]?(([a-zA-Z0-9\u4e00-\u9fa5]+[_|-]?)*)[a-zA-Z0-9\u4e00-\u9fa5]+$");  // ^[\u4E00-\u9FA5A-Za-z0-9_]+$
  if (pattern.test(value)) {
    return callback();
  }
  return callback(new Error('格式错误，最大32字符，中文、数字、字母以及"_""-"'));
};

/**
 * @description 验证联系方式
 * */
export const checkPhone = (rule, value, callback) => {
  if (!value) {
    return callback();
  }
  checkTrim(rule, value, callback);
  // let pattern = new RegExp("(010\\d{8})|(0[2-9]\\d{9})|(13\\d{9})|(14[57]\\d{8})|(15[0-35-9]\\d{8})|(18[0-35-9]\\d{8})");  // ^[\u4E00-\u9FA5A-Za-z0-9_]+$
  let pattern = new RegExp("^((0\\d{2,3}\\d{7,8})|(1[3456789]\\d{9}))$");  // ^[\u4E00-\u9FA5A-Za-z0-9_]+$
  if (pattern.test(value)) {
    return callback();
  }
  return callback(new Error('格式错误, 区号+座机号或手机号'));
};

/**
 * @description 验证厂商 （必填）
 * */
export const checkIssuer = (rule, value, callback) => {
  if (!value) {
    return callback();
  }
  checkTrim(rule, value, callback);
  let pattern = new RegExp("^[A-Za-z0-9\u4e00-\u9fa5]{2,50}$");  // ^[\u4E00-\u9FA5A-Za-z0-9_]+$
  if (pattern.test(value)) {
    return callback();
  }
  return callback(new Error('格式错误，请输入2-50字符，中文、数字、字母'));
};

//   checkUserName ^([1-9][0-9]*)+(\.[0-9]{1,2})?$
/**
 * @description 验证设备序列号
 * */
export const checkSerial = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('输入不可以为空'));
  }
  checkTrim(rule, value, callback);
  // let pattern = new RegExp("^[A-Za-z\u4e00-\u9fa5]{2,50}$");  // ^[\u4E00-\u9FA5A-Za-z0-9_]+$
  // let pattern = new RegExp("^(\\d{4}[0|1]\\d{1,2}[0|1|2|3]\\d{1,2})+([0-9]{3})+([0-9]{5})?$");  // ^[\u4E00-\u9FA5A-Za-z0-9_]+$
  let pattern = new RegExp("^((?:(?!0000)[0-9]{4}(?:(?:0[1-9]|1[0-2])(?:0[1-9]|1[0-9]|2[0-8])|(?:0[13-9]|1[0-2])(?:29|30|31)|(?:0[13578]|1[02])-31)|(?:[0-9]{2}(?:0[48]|[2468][048]|[13579][26])|(?:0[48]|[2468][048]|[13579][26])00)-02-29))+([0-9a-zA-Z]{3})+([0-9a-zA-Z]{5})$");  // ^[\u4E00-\u9FA5A-Za-z0-9_]+$
  if (pattern.test(value)) {
    return callback();
  }
  return callback(new Error('包含: 日期(8个字符)、批次号(3个字符)、流水号(5个字符)'));
};

// 设备编号 / 单位编号 （必填）
export const checkDevCode = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('输入不可以为空'));
  }
  checkTrim(rule, value, callback);
  // let pattern = new RegExp("^[A-Za-z0-9_\\-]{4,8}$");  // ^[\u4E00-\u9FA5A-Za-z0-9_]+$
  let pattern = new RegExp("^[a-zA-Z0-9]?(([a-zA-Z0-9]+[_|-]?)*)[a-zA-Z0-9]+$");  // ^[\u4E00-\u9FA5A-Za-z0-9_]+$
  if (pattern.test(value)) {
    return callback();
  }
  return callback(new Error('格式错误，请输入4-8字符, 字母、数字以及"_""-"!'));
};

// 设备型号
export const checkDevMode = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('输入不可以为空'));
  }
  checkTrim(rule, value, callback);
  // let pattern = new RegExp("^[A-Za-z0-9_\\-]{3,16}$");  // ^[\u4E00-\u9FA5A-Za-z0-9_]+$
  let pattern = new RegExp("^[a-zA-Z0-9]?(([a-zA-Z0-9]+[_|-]?)*)[a-zA-Z0-9]+$");  // ^[\u4E00-\u9FA5A-Za-z0-9_]+$
  if (pattern.test(value)) {
    return callback();
  }
  return callback(new Error('格式错误，请输入3-8字符, 字母、数字以及"_""-"!'));
};
// 设备ID
export const checkDevId = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('输入不可以为空'));
  }
  checkTrim(rule, value, callback);
  // let pattern = new RegExp("^[A-Za-z0-9_\\-]{3,16}$");  // ^[\u4E00-\u9FA5A-Za-z0-9_]+$
  let pattern = new RegExp("^[a-zA-Z0-9]?(([a-zA-Z0-9]+[_|-]?)*)[a-zA-Z0-9]+$");  // ^[\u4E00-\u9FA5A-Za-z0-9_]+$
  if (pattern.test(value)) {
    return callback();
  }
  return callback(new Error('格式错误，字母、数字以及"_""-"!'));
};

export function beautify_dn(dn) {
  if (dn.startsWith('/')) {
    dn = dn.substring(1);
  }
  return dn.replaceAll(/\/(\w+=)/g, ',$1');
}


export const checkNumber = (rule, value, callback) => {
    if (value === '') {
        return callback(new Error('输入不可以为空'));
    }
    let pattern = new RegExp("^\\d+$");
    if (pattern.test(value)) {
        return callback();
    }
    return callback(new Error('格式错误，只能输入非负正整数!'));
};

export const checkMaxMini = (rule, value, callback) => {
    if (!value) {
        return callback();
    }
    if (value > 2147483647) {
      return callback(new Error('连接超时不合法, 请重新输入'));
    }
    if (value < 5000) {
      return callback(new Error('连接超时不得小于5000, 请重新输入!'));
    }
    let pattern = new RegExp("^\\d+$");
    if (pattern.test(value)) {
        return callback();
    }
    return callback(new Error('格式错误，只能输入非负正整数!'));
};

export const checkNumF = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('输入不可以为空'));
  }
  let pattern = new RegExp("^(\\+)?\\d+(\\.\\d+)?$");
  if (pattern.test(value)) {
    return callback();
  }
  return callback(new Error('格式错误，只能输入非负正数或小数!'));
};
// 版本号
export const checkVersion = (rule, value, callback) => {
    if (!value) {
        return callback();
    }
    let pattern = new RegExp("^[a-zA-Z0-9]?(([a-zA-Z0-9]+[._|-]?)*)[a-zA-Z0-9]+$");
    if (pattern.test(value)) {
        return callback();
    }
    return callback(new Error('由数字、字母、-、_、.组成，长度为0-100位'));
};


/**
 * 密码
 * */
export const checkNewPwd = (rule, value, callback) => {
  let pass = /^(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])(?=.*[!@#$%^&*,\.])[0-9a-zA-Z!@#$%^&*,\\.]{8,16}$/;
  if (value === '') {
    callback(new Error('请输入密码'));
  } else {
    if (!pass.test(value)) {
      callback(new Error('密码格式为8-16位，包含数字、大小写母和特殊字符!'));
    } else if (this.pwdForm.confirmPwd !== '') {
      if (value !== this.pwdForm.confirmPwd) {
        callback(new Error('两次输入密码不一致!'));
      } else {
        callback();
      }
    }
    callback();
  }
};
export const checkLowPwd = (rule, value, callback) => {
  let pass = /^(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])[0-9a-zA-Z]{8,16}$/;
  if (value === '') {
    callback(new Error('请输入密码'));
  } else {
    if (!pass.test(value)) {
      callback(new Error('密码格式为8-16位，包含数字、大小写母!'));
    } else if (this.pwdForm.confirmPwd !== '') {
      if (value !== this.pwdForm.confirmPwd) {
        callback(new Error('两次输入密码不一致!'));
      } else {
        callback();
      }
    }
    callback();
  }
};


// Vue.mixin({
//   data: function () {
//     return {
//       global: {
//         form_label_width: "200px",
//       },
//       SD: SD,
//     };
//   },
//   methods: {
//     get_GroupName(key) {
//       var o = { SUPER: "超级管理员", SYSTEM: "系统管理员", SECURITY: "安全管理员", AUDIT: "审计管理员" };
//       return o[key] || key;
//     },
//     get_cc_state_desc: get_cc_state_desc,
//     beautify_dn: beautify_dn,
//   },
// });

export const Debounce = (fn, t) => {
  let delay = t || 500;
  let timer;
  // console.log(fn);
  // console.log(typeof fn);
  return function () {
    let args = arguments;
    if(timer){
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      timer = null;
      fn.apply(this, args);
    }, delay);
  }
};

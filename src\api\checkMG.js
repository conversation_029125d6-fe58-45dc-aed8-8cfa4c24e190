import {req, reqParams} from './axiosFun';
import Api from "@/api/apiuri";
const checkApi = Api.checkApi;
export default {
  random() {
    return req("post", checkApi.random);
  },
  generateSm2Certs() {
    return req("post", checkApi.generateSm2)
  },
  sm2Encryption(enc) {
    return req("post", checkApi.sm2Encryption, enc);
  },
  sm2Decryption(dec) {
    return req("post", checkApi.sm2Decryption, dec);
  },
  sm2Signature(sign) {
    return req("post", checkApi.sm2Signature, sign);
  },
  sm2Verify(verify) {
    return req("post", checkApi.sm2Verify, verify);
  },
  sm3Hash1(hash) {
    return req("post", checkApi.sm3Hash1, hash);
  },
  sm3Hash2(hash) {
    return req("post", checkApi.sm3Hash2, hash);
  },
  generateSm4Certs() {
    return req("post", checkApi.generateSm4)
  },
  sm4ECBEncryption(enc) {
    return req("post", checkApi.sm4ECBEncryption, enc);
  },
  sm4ECBDecryption(dec) {
    return req("post", checkApi.sm4ECBDecryption, dec);
  },
  sm4CBCEncryption(enc) {
    return req("post", checkApi.sm4CBCEncryption, enc);
  },
  sm4CBCDecryption(dec) {
    return req("post", checkApi.sm4CBCDecryption, dec);
  },
  sm4OFBEncryption(enc) {
    return req("post", checkApi.sm4OFBEncryption, enc);
  },
  sm4OFBDecryption(dec) {
    return req("post", checkApi.sm4OFBDecryption, dec);
  },
  sm4MAC(mac) {
    return req("post", checkApi.sm4MAC, mac);
  },
  checkPage(page) {
    return reqParams("get", checkApi.checkPage, page);
  }
}

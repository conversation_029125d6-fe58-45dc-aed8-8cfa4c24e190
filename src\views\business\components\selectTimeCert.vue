<template>

  <el-dialog title="选择证书" :visible.sync="selectCertOpen" width="800px" append-to-body :close-on-click-modal="false"
             @close="Object.assign(queryParams,$options.data().queryParams);Object.assign(bindParam,$options.data().bindParam);
Object.assign(tableData,$options.data().tableData)">
    <createTable
      v-if='showTable'
      :tableData="tableData"
      :tableHeader="tableDataHeader"
      :isPage="true"
      :pageAttributes="{total: pageAttr.total, currentPage: queryParams.pageNo}"
      :current-change="cert_change"
      :sizeChange="cert_sizeChange"
      :prev-click="prevCertPage"
      :next-click="nextCertPage"
    >
    </createTable>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" class="comBtn com_reset_btn" @click="selectCertOpen=false">取 消</el-button>
      <el-button size="small" class="comBtn com_send_btn" type="primary" @click="bindSubmit" :loading="loading">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import timeStampMG from "@/api/timeStampMG";

export default {
  data() {
    return {
      showTable: false,
      selectCertOpen: false, // 应用绑定 弹窗
      tableDataHeader: [],
      loading:false,
      tableData: [],
      queryParams: {
        certType: "",
        pageNo: 0,
        pageSize: 10,
        currentPage: 1
      },
      pageAttr: {
        total: 0
      },
      bindParam: {
        certId: 0,
        id: 0
      }
    }
  },
  methods: {
    cert_change(val) {
      this.queryParams.pageNo = val;
      this.listcert();

    },
    cert_sizeChange(val) {
      this.queryParams.pageSize = val;
      this.listcert();
    },
    prevCertPage(val) {
      this.queryParams.pageNo = val;
      this.listcert();
    },
    nextCertPage(val) {
      this.queryParams.pageNo = val;
      this.listcert();
    },
    bindSubmit() {
      let _this = this;
      if (this.bindParam.certId == 0) {
        this.$alert("请选择证书！", "信息提示", {
          cancelButtonText: '取消',
          type: 'warning'
        });
        return;
      }
      _this.loading = true;
      timeStampMG.bindCert(this.bindParam).then(rdata=>{
          if(rdata.code == 0){
            _this.$message.success("绑定成功")
            _this.selectCertOpen = false;
            _this.$parent.list();
          }else{
            _this.$message.error(rdata.message);
          }
        _this.loading = false;
      });

    },
    listcert(){
      let _this = this;
      timeStampMG.listCert(_this.queryParams).then((rdata) => {
        let code = rdata.code;
        if (code == 0) {
          _this.tableData = rdata.data;
          _this.pageAttr.total = rdata.row;
        } else {
          this.$message.error(rdata.msg);
        }
      });
    },
    loadSelectTimeCert(row) {
      this.reload();
      let _this = this;
      this.selectCertOpen = true;
      this.bindParam.id = row.id;
      if (row.certType == 1) {
        _this.queryParams.certType = "RSA";
      } else {
        _this.queryParams.certType = "SM2";
      }
      this.listcert();
    },
    reload () {
      this.showTable = false
      this.$nextTick(() => {
        this.showTable = true
      })
    }
  },
  created() {
    let _this = this;
    // 应用绑定列表
    this.tableDataHeader = [
      {
        label: " ", type: "radio", prop: "id", width: "50", checkColumn: "checked", disableColumn: "disabled",
        callback: function (row) {
          _this.bindParam.certId = row.id;
        }
      },
      {label: "证书ID", prop: "id", type: "normal", width: "100"},
      {label: "证书DN", prop: "dn", type: "normal", width: "200"},
      {label: "证书类型", prop: "keyDesc", type: "normal", width: "100"},
      {label: '生效时间', prop: 'startTime', type: "time",width: "200"},
      {label: '失效时间', prop: 'endTime', type: "time",width: "200"}
    ];
  }
}
</script>

<style scoped>

</style>

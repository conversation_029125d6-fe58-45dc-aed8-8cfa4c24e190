<template>
    <div class="login-wrap" :style="bgObj">
        <div class="login_content">
            <div class="login_title">{{ sysName }}</div>
            <!--<div class="login_form">-->
            <!--<el-form label-width="80px" ref="ruleForm1" :model="ruleForm" :rules="rules">-->
            <!--<el-form-item label="证书：" prop="label" size="small">-->
            <!--<el-select v-model="ruleForm.label" placeholder="请选择" size="small" required style="width: 90%">-->
            <!--<el-option v-for="item in keys" :key="item.realName" :label="item.realName" :value="item.realName"></el-option>-->
            <!--</el-select>-->
            <!--</el-form-item>-->
            <!--<el-form-item label="PIN码：" prop="pin" size="small">-->
            <!--<el-input size="small" v-model="ruleForm.pin" placeholder="请输入Ukey PIN码" type="password" style="width: 90%" @keyup.enter.native="ukeylogin('ruleForm1')"></el-input>-->
            <!--</el-form-item>-->
            <!--<el-form-item label="密码：" prop="userPwd" size="small" v-if="ruleForm.loginType === 2">-->
            <!--<el-input size="small" type="password" v-model="ruleForm.userPwd" style="width: 90%" placeholder="管理员密码" @keyup.enter.native="ukeylogin('ruleForm1')"></el-input>-->
            <!--</el-form-item>-->
            <!--</el-form>-->
            <!--<br>-->
            <!--<el-button class="login_btn" :loading="loading" @click="ukeylogin('ruleForm1')">登 录</el-button>-->
            <!--</div>-->
            <el-tabs type="card" v-model="activeName" class="login_tabs commonTabs" :before-leave="beforeHandle" ref="tabs">
                <el-tab-pane label="用户名/密码" name="first">
                    <el-form label-width="70px" :model="ruleForm" :rules="rules" ref="firstForm" class="login_form">
                        <el-form-item label="用户名：" prop="userName" size="small">
                            <el-input class="user User" id="User" v-model="ruleForm.userName" placeholder="请输入用户名"></el-input>
                        </el-form-item>
                        <el-form-item label="密码：" prop="userPwd" size="small" key="userPwd">
                            <el-input type="password" id="userPass" v-model="ruleForm.userPwd" placeholder="请输入密码" @keyup.enter.native="submitForm('firstForm')"></el-input>
                        </el-form-item>
                        <!--<button @click="BtRegister">注册</button>  v-on:click="check" -->
                    </el-form>
                    <el-button id="login" class="login_btn" :loading="loading" @click="submitForm('firstForm')">登 录</el-button>
                </el-tab-pane>

                <el-tab-pane label="数字证书" name="second">
                    <el-form label-width="65px" ref="secondForm" :model="ruleForm" :rules="rules" class="login_form">
                        <el-form-item label="证书：" prop="label" size="small">
                            <el-select v-model="ruleForm.label" placeholder="请选择" size="small" required style="width: 100%">
                                <el-option v-for="item in keys" :key="item.realName" :label="item.realName" :value="item.realName"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="口令：" prop="pin" size="small">
                            <el-input size="small" v-model="ruleForm.pin" placeholder="请输入Ukey PIN码" type="password" @keyup.enter.native="UKeyLogin('secondForm')"></el-input>
                        </el-form-item>
                    </el-form>
                    <el-button class="login_btn" :loading="loading" @click="UKeyLogin('secondForm')">登 录</el-button>
                </el-tab-pane>

                <el-tab-pane label="双因子登录" name="third">
                    <el-form label-width="75px" ref="ruleForm" :model="ruleForm" :rules="rules" class="login_form">
                        <el-form-item label="证书：" prop="label" size="small">
                            <el-select v-model="ruleForm.label" placeholder="请选择" size="small" required style="width: 100%">
                                <el-option v-for="item in keys" :key="item.realName" :label="item.realName" :value="item.realName"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="口令：" prop="pin" size="small">
                            <el-input size="small" v-model="ruleForm.pin" placeholder="请输入Ukey PIN码" type="password" @keyup.enter.native="UKeyLogin('ruleForm')"></el-input>
                        </el-form-item>
                        <el-form-item label="用户名：" prop="userName" size="small">
                            <el-input class="user User" v-model="ruleForm.userName" placeholder="请输入用户名"></el-input>
                        </el-form-item>
                        <el-form-item label="密码：" prop="userPwd" size="small">
                            <el-input size="small" type="password" v-model="ruleForm.userPwd" placeholder="请输入密码"></el-input>
                        </el-form-item>
                    </el-form>
                    <el-button class="login_btn" :loading="loading" @click="UKeyLogin('ruleForm')">登 录</el-button>
                </el-tab-pane>
            </el-tabs>
            <div style="margin: 15px; text-align: center">
                <a style="cursor: pointer; text-decoration: underline; color: #1e50a2;" @click="downloadPackage">下载证书应用环境安装程序</a>
            </div>
        </div>
    </div>
</template>
<script type="text/ecmascript-6">
    // import userMG from "@/api/userMG";
    // import systemMG from "@/api/systemMG";
    // import customGM from "@/api/customMG";
    import {CryptoJS} from "@/sm3/core"
    import {setStore, doSM3} from '../utils/util'
    import {downloadPackage} from '@/utils/exportExcel'
    import {testGetKeyState, testSignData, testExportCert, GET_KEY_STATE} from "../utils/Ukey";
    import {mapState} from 'vuex';

    export default {
        name: 'login',
        data() {
            return {
                sysName: '签名验签服务器',
                bgObj: {},
                activeName: 'first', //定义标签页
                // inputVal: '',
                //定义loading默认为false
                loading: false,
                // 记住密码
                // downVisible: false,
                // rememberpwd: false,
                interName: null,
                loginType: 1, // 登录类型
                ruleForm: {
                    // userName和password默认为空
                    label: '',
                    userName: '',
                    dn: '',
                    userPwd: '',
                    loginType: 1,
                    code: '',
                    random: '',
                    certData: '',
                    signData: '',
                    pin: '',
                    tokenAB: ""
                },
                server_random: "",
                keys: [],
                rules: {
                    pin: [{required: true, message: '请输入PIN码', trigger: 'blur'}],
                    userName: [{required: true, message: '请输入用户名', trigger: 'blur'}],
                    userPwd: [{required: true, message: '请输入密码', trigger: 'blur'}]
                },
            }
        },
        computed: {
            ...mapState({
                UKeyList: state => state.UKeyList
            }),
            // monitorCurrKey() {
            //     return this.downloadForm.ukeySn
            // }
        },
        watch: {
            /**
             * 1. 监听 UKeyList 数据变化
             *  a. 当数据为空时 当前所选KEY置空 证书密钥来源切换为"系统";
             *  b. 当前所选KEY拔出时, 当前所选KEY置空;
             * */
            UKeyList(newData, oldData) {
                // monitorUKeyList(newData, oldData) {
                console.log(newData, oldData);
                this.beforeHandle('second');
                if (newData.length === 0) {
                    this.ruleForm.label = '';
                    // this.addCertForm.certSource = 1;
                    return
                }

                // if (newData.indexOf(this.downloadForm.ukeySn) === -1) {
                //     this.ruleForm.label = '';
                //     // this.downloadForm.pin = ''
                // }
            },
            // monitorCurrKey(newVal, oldVal) {
            //     console.log(newVal, oldVal);
            //     if (newVal !== oldVal) this.downloadForm.pin = ''
            // }
        },
        // 创建完毕状态(里面是操作)
        created() {
            // var store = getStore("token");
            // if (store != null) {
            //     this.$router.push({path: '/home'});
            //     return
            // }
            //获取初始化状态，未初始化跳转初始化界面
            this.$http.userMG.statusInit().then(res => {
                if (res.code == "0") {
                    if (res.data == "0") {
                        this.BtRegister()
                    }
                } else {
                    this.$message.error(res.msg);
                }
            });
            this.currentStrategy();
            //获取随机数
            this.getRandomNumber();
            // this.readUkey();
            this.ukeyStatusPoll();
            this.getCurrentConfig();
        },
        // 里面的函数只有调用才会执行
        methods: {
            // tab切换 切换标签之前的钩子 检测UKey是否插入
            beforeHandle(activeName) {
                this.$refs["firstForm"].clearValidate();
                this.$refs["secondForm"].clearValidate();
                this.$refs["ruleForm"].clearValidate();
                this.ruleForm.userName = '';
                this.ruleForm.userPwd = '';
                // Object.assign(this.$data.ruleForm, this.$options.data().ruleForm);
                if (activeName === "second" || activeName === "third") {
                    // 检测到UKey 导出证书
                    testExportCert(({errCode, b64CertData}) => {
                        this.cert = b64CertData;
                        if (errCode === 0) {
                            // 分析检验证书
                            this.$http.userMG.analysisCert(JSON.stringify({certData: b64CertData})).then(({code, data, msg}) => {
                                console.log(code, data, msg);
                                if (code === 0) {
                                    this.keys = [data];
                                    this.ruleForm.label = data.cn;
                                    this.ruleForm.dn = data.dn;
                                }
                            })
                        }
                    })

                }
            },
            /* 获取自定义配置 */
            getCurrentConfig() {
                this.$store.dispatch("getCurrConfigFun").then( data => {
                    // console.log(data);
                    this.sysName = document.title = data.productName || '签名验签管理系统';
                    this.bgObj = {'background': 'url(' + data.backgroundLogin + ')', 'backgroundRepeat': 'no-repeat', 'backgroundSize': '100% 100%'}
                });
            },
            // 获取当前启用策略
            currentStrategy() {
                this.$http.userMG.queryEnableStrategy().then(({code, data, msg}) => {
                    if (code !== 0) return this.$message.warning(msg);
                    this.loginType = data.loginType.split(',').map(i => parseInt(i, 0));
                    // this.ruleForm.loginType = data.loginType.split(',').map(i => parseInt(i, 0));
                    // let loginType = [2];
                    this.$nextTick(function () {
                        let tabsElements = this.$refs.tabs.$children[0].$refs.tabs;
                        if (this.loginType[0] === 1) {
                            this.activeName = 'first'
                        } else if (this.loginType[0] === 2) {
                            this.activeName = 'second';
                            this.readUkey()
                        } else if (this.loginType[0] === 3) {
                            this.activeName = 'third';
                            this.readUkey()
                        }
                        if (this.loginType.indexOf(1) !== -1) {
                            tabsElements[0].style.display = 'inline-block';
                        }
                        if (this.loginType.indexOf(2) !== -1) {
                            tabsElements[1].style.display = 'inline-block';
                        }
                        if (this.loginType.indexOf(3) !== -1) {
                            tabsElements[2].style.display = 'inline-block';
                        }
                    });
                })
            },
            RANDOM_FUN(L) {
                return CryptoJS.enc.Hex.stringify(CryptoJS.lib.WordArray.random(L));
            },
            readUkey() {
                testExportCert((res) => {
                    if (res.errCode === 0) {
                        this.$http.userMG.analysisCert({certData: res.b64CertData}).then((res) => {
                            console.log(res);
                            const code1 = res.code;
                            if (code1 == 0) {
                                this.keys = [res.data];
                                this.ruleForm.label = res.data.cn;
                                this.ruleForm.dn = res.data.dn;
                            } else {
                                // this.$message.error(res.msg)
                            }
                        })
                    } else {
                        this.$message.error("Ukey 证书异常，请检查Ukey！")
                    }
                })
            },
            // 用户名/密码
            submitForm() {
                this.$refs["firstForm"].validate((valid) => {
                    if (valid) {
                        let opt = {
                            userName: this.ruleForm.userName,
                            userPwd: doSM3(this.ruleForm.userPwd)
                        };
                        this.$http.userMG.login(opt).then(res => {
                            if (res.code == 0) {
                                setStore("token", res.data.token);
                                setStore("userId", res.data.userId);
                                setStore("roleName", res.data.roleName);
                                setStore("userName", res.data.userName);
                                setStore("menus", res.data.menus);
                                setStore("roleId", res.data.roleId);
                                setStore("rePassword", res.data.rePassword);
                                this.$store.dispatch("SET_WATCH_TOKEN_FUN", res.data.token);
                                let routerFlag = JSON.stringify(res.data.menus).indexOf('/home') !== -1;
                                if (routerFlag) this.$router.push({path: '/home'});
                                if (res.data.menus[0].menuUrl !== '' && !routerFlag) this.$router.push({path: res.data.menus[0].menuUrl});
                                if (res.data.menus[0].menuUrl === '') this.$router.push({path: res.data.menus[0].menus[0].menuUrl});
                                // this.$router.push({path: '/home'});
                                this.$message({
                                    message: '登录成功',
                                    type: 'success'
                                })
                            } else {
                                // this.$message.error(res.msg)
                            }
                        })
                    }
                });
            },
            //获取随机数
            getRandomNumber() {
                this.$http.userMG.randomNumber().then((res) => {
                    const code = res.code;
                    if (code === 0) this.server_random = res.data;
                })
            },
            //Ukey登录
            UKeyLogin(name) {
                let _this = this;
                GET_KEY_STATE(() => {
                    this.$refs[name].validate(valid => {
                        if (!valid) return;
                        let pass = this.ruleForm.pin;
                        let rb = _this.RANDOM_FUN(16);
                        // _this.ruleForm.random = _this.server_random + "||" + rb;
                        _this.ruleForm.random = `${_this.server_random}||${rb}`;
                        let yuan = btoa(_this.ruleForm.random);
                        testSignData(pass, yuan, (res) => {
                            this.ruleForm.signData = res.b64SignData;
                            this.ruleForm.tokenAB = `${_this.ruleForm.random}||${_this.ruleForm.signData}`;
                            testExportCert((cert) => {
                                const code = cert.errCode;
                                if (code == '0') {
                                    let tempRuleFrom = JSON.stringify(_this.ruleForm);
                                    let obj = JSON.parse(tempRuleFrom);
                                    obj.loginType = this.activeName === 'second' ? 2 : 3;
                                    obj.certData = cert.b64CertData;
                                    delete obj.pin;
                                    if (obj.loginType === 3) obj.userPwd = doSM3(_this.ruleForm.userPwd);
                                    this.$http.userMG.loginCert(obj).then((res) => {
                                        if (res.code == '0') {
                                            setStore("token", res.data.token);
                                            setStore("roleId", res.data.roleId);
                                            setStore("userId", res.data.userId);
                                            setStore("roleName", res.data.roleName);
                                            setStore("userName", res.data.userName);
                                            setStore("menus", res.data.menus);
                                            setStore("rePassword", res.data.rePassword);
                                            setStore("drawKeyPoll", res.data.drawKeyPoll);
                                            this.$store.dispatch("SET_WATCH_TOKEN_FUN", res.data.token);
                                            let routerFlag = JSON.stringify(res.data.menus).indexOf('/home') !== -1;
                                            if (routerFlag) this.$router.push({path: '/home'});
                                            if (res.data.menus[0].menuUrl !== '' && !routerFlag) this.$router.push({path: res.data.menus[0].menuUrl});
                                            if (res.data.menus[0].menuUrl === '') this.$router.push({path: res.data.menus[0].menus[0].menuUrl});
                                            // this.$router.push({path: '/home'});
                                            this.$message.success("登录成功！");
                                            clearInterval(_this.interName);
                                        } else {
                                            // _this.$message.error(res.msg);
                                            _this.ruleForm.password = '';
                                        }
                                    })
                                } else {
                                    this.$message.error('导出证书异常，请检查UKey！');
                                }
                            })
                        })
                    })
                })
            },
            // 跳转初始化界面
            BtRegister() {
                this.$router.push('/init')
            },
            ukeyStatusPoll() {
                this.interName = setInterval(() => {
                    console.log(this.activeName);
                    if (this.activeName === 'first') return;
                    testGetKeyState((res) => {
                        if (res.state != "1") {
                            this.ruleForm.label = "";
                        } else {
                            if (this.ruleForm.label == null || this.ruleForm.label === "") {
                                this.readUkey();
                            }
                        }
                    })
                }, 5000);
            },

            downloadPackage() {
                // const url = process.env.NODE_ENV === 'production' ? '/svs' : '/svs';
                // downloadPackage("get", `${url}/download/uktool`)
                downloadPackage("get", `/svs/download/uktool`)
            }
        },
        beforeDestroy() {
            if (this.interName) {
                clearInterval(this.interName); //关闭
            }  //利用vue的生命周期函数
        }
    }
</script>
<style lang="less" scoped>

    .login-wrap {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        box-sizing: border-box;
        /*height: 50%;*/
        /*background-image: url(../assets/img/log.jpg);*/
        /* background-color: #112346; */
        background-repeat: no-repeat;
        background-size: 100% 100%;

        .login_content {
            width: 450px;
            margin: auto;
            background: #ffffff;

            .login_title {
                height: 50px;
                line-height: 50px;
                /*margin-bottom: 40px;*/
                font-size: 20px;
                color: #fff;
                text-align: center;
                background: linear-gradient(to right, #3e62ad, #1e50a2);
                border-bottom-right-radius: 20px;
                border-bottom-left-radius: 20px;
            }

            .login_tabs {
                margin: 20px 5px 0;
            }

            .login_btn {
                width: 90%;
                margin: 10px 22px 0;
                font-size: 18px;
                color: #ffffff;
                border: 0 none;
                border-radius: 10px;
                background: linear-gradient(to right, #3e62ad, #1e50a2);
            }
        }
    }

    .login_form {
        width: 360px;
        margin: 0 auto;

        /deep/ .el-form-item__label {
            padding: 0;
        }
    }
    /deep/ .el-tabs__nav {
        .el-tabs__item {
            display: none;
        }
    }
</style>

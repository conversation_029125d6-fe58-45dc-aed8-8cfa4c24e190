/**
* 头部菜单
*/
<template>
    <el-menu class="el-menu-demo top_header" mode="horizontal" collapse-transition text-color="#fff" active-text-color="#fff" @select="selectM">
        <button class="buttonimg" style="cursor: pointer" @click="toggle(collapsed)">
            <svg-icon class="showimg" :icon-class="collapsed?close:open"></svg-icon>
        </button>
        <!-- <svg-icon iconClass="hanyan" :stroke="'#4bc8c8'"></svg-icon> -->
        <el-submenu index="2" class="csubmenu" >
            <template slot="title"><span id="USER_NAME">{{ userName }}</span><span style="display: none" id="USER_ID">{{ userId }}</span></template>
            <el-menu-item @click="loginOut()" index="2-3">退出</el-menu-item>
        </el-submenu>
        <el-submenu index="1" class="csubmenu" v-if="isShowFun || isShow">
            <template slot="title">{{ '设备管理' }}</template>
            <!--<el-menu-item index="1-3" v-if="(cardStatus === '工作态' || cardStatus === '管理态') && cardState == 0" @click="cardLogout">-->
                <!--<svg-icon icon-class="logout" style="margin-right: 5px;width: 24px;vertical-align: middle"></svg-icon>-->
                <!--登 出-->
            <!--</el-menu-item>-->
            <el-menu-item index="1-3" v-if="isShowFun" @click="cardLogout">
                <svg-icon icon-class="logout" style="margin-right: 5px;width: 24px;vertical-align: middle"></svg-icon>
                登 出
            </el-menu-item>
            <el-menu-item index="1-1" v-if="isShow" @click="shutdown">
                <i class="el-icon-switch-button"></i>
                关 机
            </el-menu-item>
            <el-menu-item index="1-2" v-if="isShow" @click="reboot">
                <i class="el-icon-refresh-right"></i>
                重 启
            </el-menu-item>
        </el-submenu>

        <div style="float: right;line-height: 60px;">
            <!--<span style="color: darkorange">{{ $store.state.CURRENT_DEV_STATE }}&#45;&#45;&#45;&#45;{{ $store.state.IS_No_VISIT_MODE === 0 ? '有访态' : '无访态' }}</span>-->
            <el-button v-if="($store.state.CURRENT_DEV_STATE === '就绪状态' || $store.state.CURRENT_DEV_STATE === '主管鉴别中状态') && $store.state.IS_No_VISIT_MODE === 0" size="mini" class="shutdown" @click="deviceLoginHandle">设备登录</el-button>
            <!--<el-button size="mini" class="shutdown" @click="deviceLoginHandle">设备登录</el-button>-->
            <!--<el-button size="mini" class="shutdown" @click="loginCipherMachine">请登录到密码机</el-button>-->
            <!-- <el-button :disabled="$store.state.common.IS_KILL_STATUS" v-if="cardStatus === '工作态'" size="mini" class="shutdown" type="warning" @click="editPassword">修改密码</el-button> -->
            <!--<el-button v-if="isShow" size="mini" class="card_status" :type="btnType">{{ cardStatus }}</el-button>-->
        </div>
        <!--<el-button v-if="isShow" size="mini" class="shutdown" type="danger" @click="shutdown">关机</el-button>-->
        <!--<el-button v-if="isShow" size="mini" class="shutdown" type="warning" @click="reboot">重启</el-button>-->

        <!-- 登录到密码机 -->
        <el-dialog title="设备登录" :visible.sync="editFormVisible" width="500px" :close-on-click-modal="false" @close="closeCryptoView">
            <!--<span class="login_tip">请插入密码机UKEY，并输入口令！</span>-->
            <el-form label-width="120px" :model="loginForm" ref="loginForm" :rules="rules">
                <el-form-item label="">
                    <el-radio :disabled="$store.state.CURRENT_DEV_STATE === '主管鉴别中状态'" v-model="loginForm.pinType" :label="1" @change="changeRadio">密码主管</el-radio>
                    <el-radio :disabled="$store.state.CURRENT_DEV_STATE === '主管鉴别中状态'" v-model="loginForm.pinType" :label="2" @change="changeRadio">密码用户</el-radio>
                </el-form-item>
                <el-form-item label="选择UKey：" v-if="loginForm.pinType === 1" prop="serial" key="serial" style="text-align: left">
                    <el-select v-model="loginForm.serial" placeholder="请选择" size="small" @change="changeSerialHandle" style="width: 260px">
                        <span v-for="item in initUKeyList" :key="item.serial">
                            <!-- :disabled="item.chosen === 1" -->
                            <el-option
                                    :disabled="item.status === 1"
                                    :label="item.serial"
                                    :value="item.serial">
                            </el-option>
                        </span>
                    </el-select>
                </el-form-item>
                <el-form-item label="请输入口令：" prop="pin">
                    <el-input size="small" v-model="loginForm.pin" type="password" style="width: 260px"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button class="comBtn com_reset_btn" size="small" @click="closeCryptoView">取 消</el-button>
                <el-button class="comBtn com_send_btn" size="small" type="primary" @click="submitForm('loginForm')">登 录</el-button>
            </div>
        </el-dialog>
    </el-menu>
</template>
<script>
    import {getStore} from "@/utils/util";
    import $ from "jquery";
    import userMG from "@/api/userMG";
    // import authManage from "@/api/authManage";
    import systemMG from "@/api/systemMG";
    import initGm from "@/api/initMG";

    export default {
        name: 'navcon',
        data() {
            return {
                collapsed: true,
                isShow: false,
                open: 'menuOpen',
                close: 'menuClose',
                userName: '',
                userId: '',
                cardStatus: '',
                cardState: getStore('CARD_STATUS'),
                transition: false,
                editFormVisible: false,
                initUKeyList: [],
                loginForm: {
                    pinType: 1,
                    serial: '',
                    pin: '',
                },
                user: {},
                rules: {
                    pin: [{required: true, message: "请输入口令", trigger: 'blur'}],
                    serial: [{required: true, message: "请选择UKey", trigger: 'change'}],
                },
            }
        },
        // 创建完毕状态(里面是操作)
        created() {
            this.userName = getStore("userName");
            this.userId = getStore("userId");
            let roleId = getStore('roleId');
            // this.isShow = (roleId == 1) || (roleId == 0);
            this.isShow = roleId.includes('1');
            // this.getDeviceStatus();
            // this.$store.dispatch("getCardStatusFun").then(res => this.cardStatus = res)
            this.$store.dispatch("getCardStatusFun")
        },
        computed: {
            isShowFun() {
                let state = this.$store.state.CURRENT_DEV_STATE;
                return (state === '用户状态' || state === '主管状态' || state === '主管鉴别中状态') && this.$store.state.IS_No_VISIT_MODE === 0
            }
        },
        methods: {

            getUKeyListFun(type) {
                initGm.initUKeyListApi(type).then(({code, msg, data}) => {
                    console.log(code, data, msg);
                    if (code === 0 && data.length !== 0) {
                        this.initUKeyList = data;
                        if (type === 2) this.loginForm.serial = data[0].serial
                    } else if (code === 400) {
                        this.$message.warning(msg)
                    } else {
                        let str = type === 1 ? '密码主管' : '密码用户';
                        this.$message.warning(`请插入${str}UKey!`)
                    }
                });
            },
            changeRadio(val) {
                this.getUKeyListFun(val);
                this.loginForm.serial = '';
                this.loginForm.pin = '';
                this.$nextTick(() => {
                    this.$refs["loginForm"].clearValidate();
                })
            },
            changeSerialHandle() {
                this.loginForm.pin = '';
                this.$refs["loginForm"].clearValidate();
            },
            deviceLoginHandle() {
                this.getUKeyListFun(1);
                this.editFormVisible = true;
                this.$nextTick(() => {
                    this.$refs["loginForm"].clearValidate();
                })
            },
            // 登录到密码卡
            submitForm(name) {
                this.$refs[name].validate(valid => {
                    if (valid) {
                        this.loginLoading = true;
                        systemMG.devLogin(this.loginForm).then(res => {
                            let code = res.code;
                            if (code == 0) {
                                this.closeCryptoView();
                                // this.getCryptoCardStatus();
                                // this.getDeviceStatus();
                                this.$store.dispatch("getCardStatusFun");
                                systemMG.devStatusApi().then(({code, data, msg}) => {
                                    console.log(code, data, msg);
                                    if (!code) {
                                        // let tempObj = data[0];
                                        // console.log(tempObj);
                                        // console.log(code, data, msg);
                                        // if (this.loginForm.pinType === 0 && tempObj.loginStatus !== 4 && tempObj.loginStatus !== 3) {
                                        if (this.loginForm.pinType === 1 && data === '主管鉴别中状态') {
                                            this.$message.success('登录成功! 请插入第二个管理员UKey!');
                                            // let _this = this;
                                            // setTimeout(function(){
                                            //     _this.editFormVisible = true
                                            // }, 1000);
                                            // this.editFormVisible = true
                                        } else {
                                            this.$message.success("登录成功！");
                                        }
                                    } else {
                                        this.$message.warning(msg)
                                    }
                                });

                                // this.getStatusMenus();
                                this.loginLoading = false;
                            } else {
                                // this.$message.error("登录异常！")
                            }
                        })
                    }
                })
            },
            closeCryptoView() {
                this.editFormVisible = false;
                this.loginForm.pinType = 1;
                this.loginForm.pin = '';
                this.loginForm.serial = '';
                this.$refs["loginForm"].clearValidate();
            },
            // 密码卡退出
            cardLogout() {
                this.$confirm('确定要登出吗?', '登出确认', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    systemMG.devLoginout().then(res => {
                        let code = res.code;
                        if (code == 0) {
                            this.$message.success("退出成功！");
                            // this.getCryptoCardStatus();
                            // this.getDeviceStatus();
                            this.$store.dispatch("getCardStatusFun")
                            // this.getStatusMenus();
                        } else {
                            this.$message.error(res.msg);
                        }
                    })
                })
            },
            // 获取设备状态
            getDeviceStatus() {
                systemMG.devStatusApi().then(({code, data}) => {
                    this.cardStatus = data;
                })
            },
            // 根据卡状态获取菜单
            // getStatusMenus() {
            //     authManage.statusMenus(getStore('roleId')).then(({code, data, msg}) => {
            //         this.$store.dispatch("setAllMenu", data);
            //         setStore("menus", data);
            //         if (JSON.stringify(data).indexOf(this.$route.path) === -1) this.$router.push({path: '/home'});
            //         // this.$forceUpdate();
            //     })
            // },
            // 退出登录
            loginOut() {
                this.$confirm('退出登录, 是否继续?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    userMG.loginOut().then((res) => {
                        console.log(res);
                        const code = res.code;
                        if (code === 0) {
                            this.$store.commit('SET_IS_SHOW_MENU', false);
                            window.localStorage.clear();
                            this.$router.push("/");
                            clearInterval(getStore("interName"))
                        }
                    })
                })
            },
            shutdown() {
                this.$confirm('是否确认关闭服务器?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    systemMG.shutdownDevice().then((res) => {
                        const code = res.code;
                        if (code == 0) {
                            this.$message.success("操作成功！");
                        } else {
                            this.$message.error(res.msg)
                        }
                    })
                })
            },
            reboot() {
                this.$confirm('是否确认重启服务器?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    systemMG.rebootDevice().then((res) => {
                        const code = res.code;
                        if (code == 0) {
                            this.$message.success("操作成功！");
                        } else {
                            this.$message.error(res.msg)
                        }
                    })
                })
            },
            selectM() {
                this.$nextTick(() => {
                    $(".is-active").find(".el-submenu__title").css("border-bottom-color", "transparent");
                })
            },
            // 切换显示
            toggle(showtype) {
                this.collapsed = !showtype;
                this.$root.Bus.$emit('toggle', !showtype)
            },
        }
    }
</script>
<style lang="less">
    .el-menu--horizontal > .el-menu-item:not(.is-disabled):focus,
    .el-menu--horizontal > .el-menu-item:not(.is-disabled):hover,
    .el-menu--horizontal > .el-submenu .el-submenu__title:hover {
        background-color: transparent !important;
    }

    .el-menu--horizontal > .el-menu--popup {
        min-width: 120px;
    }

    .el-menu--horizontal .el-menu,
    .el-menu--horizontal .el-menu .el-menu-item,
    .el-menu--horizontal .el-menu .el-submenu__title {
        background-color: #022547 !important;
    }
</style>
<style scoped lang="less">
    .el-menu-vertical-demo:not(.el-menu--collapse) {
        border: none;
    }

    .top_header.el-menu.el-menu--horizontal {
        border-bottom: 0 none;
        background-image: linear-gradient(to left, #022547, #26447a); /* 自右向左 */
    }

    .csubmenu {
        float: right !important;
    }

    .buttonimg {
        height: 60px;
        background-color: transparent;
        border: none;
        text-align: center;
        float: left;
        width: 50px;
    }

    .buttonimg:hover {
        background-color: #ffffff;
        background-color: rgba(0, 0, 0, 0.2);
    }

    .showimg {
        width: 26px;
        height: 26px;
        position: absolute;
        top: 17px;
        left: 14px;
    }

    .showimg:active {
        border: none;
    }

    .shutdown {
        margin-top: 18px;
        margin-left: 10px;
        margin-right: 10px;
        border: none;
        text-align: center;
        float: right;
    }
</style>
<template>
    <el-dialog title="导入主密钥" :visible.sync="show" width="550px" append-to-body :close-on-click-modal="false" @close="closeDialog">
        <el-form label-width="110px" :model="importForm" :rules="rules" ref="importForm">
            <el-form-item label="密钥类型:" prop="keyType">
                <el-radio-group v-model="importForm.keyType" @change="changeKeyType">
                    <el-radio :label="1">签名</el-radio>
                    <el-radio :label="2">加密</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="上传文件:" prop="file" style="height: 220px" class="myUpload_form_item">
                <el-upload
                        class="upload-demo myUpload"
                        drag
                        action=""
                        accept=".zip"
                        :on-remove="handleRemove"
                        :on-change="handleChange"
                        :multiple='false'
                        :file-list="fileList"
                        :auto-upload="false">
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em><span class="el-upload__tip" slot="tip">请上传 .zip 文件</span></div>
                    <!--<div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>-->
                    <!--<div class="el-upload__tip" slot="tip">提示只能上传zip文件，且不超过5G</div>-->
                </el-upload>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button size="small" class="comBtn com_reset_btn" @click="closeDialog">取 消</el-button>
            <el-button size="small" class="comBtn com_send_btn" @click="importMasterKeyFun" :loading="loading">导 入</el-button>
        </div>
    </el-dialog>
</template>

<script>
    // import keyManage from "../../../api/symmetryMG";
    export default {
        data() {
            return {
                show: false,
                importForm: {
                    keyType: 1,
                    file: null
                },
                indexArr: [],
                fileList: [],
                rules: {
                    file: [{required: true, message: '请上传文件', trigger: 'change'}]
                },
                loading: false
            }
        },
        methods: {
            initImportMaster() {
                this.show = true;
                this.$nextTick(() => {
                    this.$refs["importForm"].clearValidate();
                })
            },
            changeKeyType() {
                this.$refs["importForm"].clearValidate();
            },
            handleChange(file) {
                this.fileList = [];
                let index = file.name.lastIndexOf(".");
                let ext = file.name.substr(index + 1);
                if (ext === 'zip') {
                    this.importForm.file = file.raw;
                    this.fileList.push(file);
                    this.$refs.importForm.validateField('file');
                } else {
                    this.$message.warning('文件格式错误, 请上传正确格式!')
                }
            },
            handleRemove(file, fileList) {
                this.importForm.file = null;
                this.fileList = fileList
            },
            importMasterKeyFun() {
                this.$refs["importForm"].validate((valid) => {
                    if (valid) {
                        // 调用后端接口
                        let p = new FormData();
                        p.append('file', this.importForm.file);
                        p.append('keyType', this.importForm.keyType);
                        this.$http.symmetryMG.importMasterKey(p).then(({code, data, msg}) => {
                            console.log(code, data, msg);
                            this.closeDialog();
                            if (code !== 0) return this.$message.warning(msg);
                            this.$message.success('导入成功!');
                            this.$emit('parentHandle')
                        });
                    }
                })
            },
            closeDialog() {
                this.show = false;
                this.fileList = [];
                this.$nextTick(() => {
                    this.$refs.importForm.resetFields();
                })
            }
        }
    }
</script>

<style lang="less" scoped>
    .myUpload_form_item {
        margin-bottom: 5px;
    }

    .myUpload{
        /deep/ .el-upload-list {
            height: 25px;
            display: inline-block;
            vertical-align: middle;
            margin-left: 10px;
            .el-upload-list__item {
                margin-top: 0;
                transition: none ;
            }
        }

        .el-upload__tip {
            display: block;
            height: 20px;
            line-height: 20px;
            color: #8b8b8b;
            margin-top: 0;
        }
    }
</style>

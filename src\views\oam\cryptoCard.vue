<template>
    <el-card>
        <div slot="header" class="clearfix">
            <span style="font-size: 16px;font-weight: 600;">基本信息</span>
            <el-button style="float: right; padding: 8px 15px" class="comBtn com_send_btn"
                @click="factoryResetHandle">恢复出厂设置</el-button>
        </div>
        <el-descriptions class="margin-top" :column="1" border>
            <el-descriptions-item label="设备厂商">{{ cryptoData.devVendor }}</el-descriptions-item>
            <el-descriptions-item label="设备编号">{{ cryptoData.devSerial }}</el-descriptions-item>
            <el-descriptions-item label="产品型号">{{ cryptoData.devProduct }}</el-descriptions-item>
            <el-descriptions-item label="产品版本">{{ cryptoData.devVersion }}</el-descriptions-item>
            <el-descriptions-item label="设备状态">{{ cryptoData.status }}</el-descriptions-item>
        </el-descriptions>
    </el-card>
</template>

<script>
// import oamMG from "../../api/oamMG";

export default {
    name: "cryptoCard",
    data() {
        return {
            cryptoData: {
                devVendor: '', // 设备厂商
                devSerial: '', // 设备编号
                devProduct: '', // 设备型号
                devVersion: '', // 版本号
                status: '', // 设备状态
            }
        }
    },
    methods: {
        // 获取设备基本信息
        getDeviceInfo() {
            this.$http.oamMG.deviceInfo().then(({ code, data, msg }) => {
                console.log(code, data, msg);
                if (code == 0) {
                    this.cryptoData.devVendor = data.devVendor;
                    this.cryptoData.devSerial = data.devSerial;
                    this.cryptoData.devProduct = data.devProduct;
                    this.cryptoData.devVersion = data.devVersion;
                    this.cryptoData.status = data.status;
                } else {
                    this.$message.warning(msg)
                }
            })
        },
        factoryResetHandle() {
            this.$confirm('确定要恢复出厂设置吗?', '恢复出厂设置', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$http.oamMG.factoryReset().then(({ code, data, msg }) => {
                    console.log(code, data, msg);
                    if (code === 0) {
                        // this.$http.userMG.loginOut().then((res) => {
                        //     console.log(res);
                        //     const code = res.code;
                        //     if (code === 0) {
                        //         this.$store.commit('SET_IS_SHOW_MENU', false);
                        //         window.localStorage.clear();
                        //         this.$router.push("/");
                        //         clearInterval(getStore("interName"))
                        //     }
                        // })
                        this.$message.warning(msg);
                    }
                })
            })
        }
    },
    created() {
        this.$http.userMG.statusInit().then((res) => {
            if (res.code === 0) {
                if (res.data == 0) {
                this.$router.push("/init");
                }
            }
        });
        this.getDeviceInfo();
    }
}
</script>

<style lang="less" scoped>
/deep/ .el-descriptions-item__label.is-bordered-label {
    width: 400px;
    text-align: center;
}

/deep/.el-descriptions-row .el-descriptions-item__content {
    text-align: center;
}

.init_but {
    width: 200px;
    height: 40px;
    margin-top: 20px;
    margin-bottom: 20px;
    margin-left: 200px;
}
</style>

<template>
  <div>
    <el-card>
      <div slot="header" class="clearfix">
        <span style="font-size: 16px; font-weight: 600">基本信息</span>
        <el-button
          style="float: right; padding: 8px 15px"
          class="comBtn com_send_btn"
          @click="factoryResetHandle"
          >恢复出厂设置</el-button
        >
      </div>
      <el-descriptions class="margin-top" :column="1" border>
        <el-descriptions-item label="设备厂商">{{
          cryptoData.devVendor
        }}</el-descriptions-item>
        <el-descriptions-item label="设备编号">{{
          cryptoData.devSerial
        }}</el-descriptions-item>
        <el-descriptions-item label="产品型号">{{
          cryptoData.devProduct
        }}</el-descriptions-item>
        <el-descriptions-item label="产品版本">{{
          cryptoData.devVersion
        }}</el-descriptions-item>
        <el-descriptions-item label="设备状态">{{
          cryptoData.status
        }}</el-descriptions-item>
      </el-descriptions>
    </el-card>
    <el-card class="crypto-table-card">
      <div slot="header" class="clearfix">
        <span style="font-size: 16px; font-weight: 600">对称算法</span>
      </div>
      <!-- 对称算法表格 -->
      <div class="crypto-table-section">
        <!-- <h3 class="table-title">对称算法</h3> -->
        <el-table
          :data="symmetricAlgorithms"
          border
          class="crypto-table"
          size="small"
        >
          <el-table-column
            prop="algorithm"
            label="算法"
            width="80"
            align="center"
          ></el-table-column>
          <el-table-column prop="ecb" label="ECB" width="60" align="center">
            <template slot-scope="scope">
              <span :class="scope.row.ecb ? 'support-yes' : 'support-no'">
                {{ scope.row.ecb ? "✓" : "✗" }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="cbc" label="CBC" width="60" align="center">
            <template slot-scope="scope">
              <span :class="scope.row.cbc ? 'support-yes' : 'support-no'">
                {{ scope.row.cbc ? "✓" : "✗" }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="ofb" label="OFB" width="60" align="center">
            <template slot-scope="scope">
              <span :class="scope.row.ofb ? 'support-yes' : 'support-no'">
                {{ scope.row.ofb ? "✓" : "✗" }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="cfb" label="CFB" width="60" align="center">
            <template slot-scope="scope">
              <span :class="scope.row.cfb ? 'support-yes' : 'support-no'">
                {{ scope.row.cfb ? "✓" : "✗" }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="ctr" label="CTR" width="60" align="center">
            <template slot-scope="scope">
              <span :class="scope.row.ctr ? 'support-yes' : 'support-no'">
                {{ scope.row.ctr ? "✓" : "✗" }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="gcm" label="GCM" width="60" align="center">
            <template slot-scope="scope">
              <span :class="scope.row.gcm ? 'support-yes' : 'support-no'">
                {{ scope.row.gcm ? "✓" : "✗" }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="ccm" label="CCM" width="60" align="center">
            <template slot-scope="scope">
              <span :class="scope.row.ccm ? 'support-yes' : 'support-no'">
                {{ scope.row.ccm ? "✓" : "✗" }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="xts" label="XTS" width="60" align="center">
            <template slot-scope="scope">
              <span :class="scope.row.xts ? 'support-yes' : 'support-no'">
                {{ scope.row.xts ? "✓" : "✗" }}
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
    <el-card class="crypto-table-card">
      <div slot="header" class="clearfix">
        <span style="font-size: 16px; font-weight: 600">对称算法</span>
      </div>
      <!-- 非对称算法表格 -->
      <div class="crypto-table-section">
        <!-- <h3 class="table-title">非对称算法</h3> -->
        <el-table
          :data="asymmetricAlgorithms"
          border
          class="crypto-table"
          size="small"
        >
          <el-table-column
            prop="algorithm"
            label="算法"
            width="120"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="supported"
            label="是否支持"
            width="100"
            align="center"
          >
            <template slot-scope="scope">
              <span :class="scope.row.supported ? 'support-yes' : 'support-no'">
                {{ scope.row.supported ? "✓" : "✗" }}
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
    <el-card class="crypto-table-card">
      <div slot="header" class="clearfix">
        <span style="font-size: 16px; font-weight: 600">对称算法</span>
      </div>
      <!-- 摘要算法表格 -->
      <div class="crypto-table-section">
        <h3 class="table-title">摘要算法</h3>
        <el-table
          :data="digestAlgorithms"
          border
          class="crypto-table"
          size="small"
        >
          <el-table-column
            prop="algorithm"
            label="算法"
            width="120"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="supported"
            label="是否支持"
            width="100"
            align="center"
          >
            <template slot-scope="scope">
              <span :class="scope.row.supported ? 'support-yes' : 'support-no'">
                {{ scope.row.supported ? "✓" : "✗" }}
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>
<script>
// import oamMG from "../../api/oamMG";

export default {
  name: "cryptoCard",
  data() {
    return {
      cryptoData: {
        devVendor: "", // 设备厂商
        devSerial: "", // 设备编号
        devProduct: "", // 设备型号
        devVersion: "", // 版本号
        status: "", // 设备状态
      },
      // 对称算法数据
      symmetricAlgorithms: [
        {
          algorithm: "SM1",
          ecb: true,
          cbc: true,
          ofb: true,
          cfb: true,
          ctr: true,
          gcm: true,
          ccm: true,
          xts: true,
        },
        {
          algorithm: "SM4",
          ecb: true,
          cbc: true,
          ofb: true,
          cfb: true,
          ctr: true,
          gcm: true,
          ccm: true,
          xts: true,
        },
        {
          algorithm: "SM7",
          ecb: true,
          cbc: true,
          ofb: true,
          cfb: true,
          ctr: true,
          gcm: true,
          ccm: true,
          xts: true,
        },
        {
          algorithm: "AES",
          ecb: true,
          cbc: true,
          ofb: true,
          cfb: true,
          ctr: true,
          gcm: true,
          ccm: true,
          xts: true,
        },
        {
          algorithm: "DES",
          ecb: true,
          cbc: true,
          ofb: true,
          cfb: true,
          ctr: true,
          gcm: true,
          ccm: true,
          xts: true,
        },
        {
          algorithm: "3DES",
          ecb: true,
          cbc: true,
          ofb: true,
          cfb: true,
          ctr: true,
          gcm: true,
          ccm: true,
          xts: true,
        },
        {
          algorithm: "Camellia",
          ecb: false,
          cbc: false,
          ofb: false,
          cfb: false,
          ctr: false,
          gcm: false,
          ccm: false,
          xts: false,
        },
        {
          algorithm: "BlowFish/TwoFish",
          ecb: false,
          cbc: false,
          ofb: false,
          cfb: false,
          ctr: false,
          gcm: false,
          ccm: false,
          xts: false,
        },
      ],
      // 非对称算法数据
      asymmetricAlgorithms: [
        { algorithm: "SM2", supported: true },
        { algorithm: "SM9", supported: true },
        { algorithm: "RSA1024", supported: true },
        { algorithm: "RSA2048", supported: true },
        { algorithm: "RSA3072", supported: true },
        { algorithm: "RSA4096", supported: true },
        { algorithm: "ECDSA", supported: true },
        { algorithm: "ECC", supported: true },
        { algorithm: "DSA", supported: false },
      ],
      // 摘要算法数据
      digestAlgorithms: [
        { algorithm: "SM3", supported: true },
        { algorithm: "MD5", supported: true },
        { algorithm: "SHA1", supported: true },
        { algorithm: "SHA256", supported: true },
        { algorithm: "SHA512", supported: true },
      ],
    };
  },
  methods: {
    // 获取设备基本信息
    getDeviceInfo() {
      this.$http.oamMG.deviceInfo().then(({ code, data, msg }) => {
        console.log(code, data, msg);
        if (code == 0) {
          this.cryptoData.devVendor = data.devVendor;
          this.cryptoData.devSerial = data.devSerial;
          this.cryptoData.devProduct = data.devProduct;
          this.cryptoData.devVersion = data.devVersion;
          this.cryptoData.status = data.status;
        } else {
          this.$message.warning(msg);
        }
      });
    },
    factoryResetHandle() {
      this.$confirm("确定要恢复出厂设置吗?", "恢复出厂设置", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$http.oamMG.factoryReset().then(({ code, data, msg }) => {
          console.log(code, data, msg);
          if (code === 0) {
            // this.$http.userMG.loginOut().then((res) => {
            //     console.log(res);
            //     const code = res.code;
            //     if (code === 0) {
            //         this.$store.commit('SET_IS_SHOW_MENU', false);
            //         window.localStorage.clear();
            //         this.$router.push("/");
            //         clearInterval(getStore("interName"))
            //     }
            // })
            this.$message.warning(msg);
          }
        });
      });
    },
  },
  created() {
    this.$http.userMG.statusInit().then((res) => {
      if (res.code === 0) {
        if (res.data == 0) {
          this.$router.push("/init");
        }
      }
    });
    this.getDeviceInfo();
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-descriptions-item__label.is-bordered-label {
  width: 400px;
  text-align: center;
}

/deep/.el-descriptions-row .el-descriptions-item__content {
  text-align: center;
}

.init_but {
  width: 200px;
  height: 40px;
  margin-top: 20px;
  margin-bottom: 20px;
  margin-left: 200px;
}

/* 加密算法表格样式 */
.crypto-table-card{
    margin-top: 10px;
}

.crypto-table-section {
  margin-bottom: 30px;
}

.table-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #333;
  padding-bottom: 5px;
}

.crypto-table {
  border: 2px solid #000 !important;
  width: 100%;
}

.crypto-table /deep/ .el-table__header-wrapper th {
  background-color: #f5f7fa;
  border: 1px solid #000 !important;
  font-weight: bold;
  text-align: center;
}

.crypto-table /deep/ .el-table__body-wrapper td {
  border: 1px solid #000 !important;
  text-align: center;
}

.crypto-table /deep/ .el-table__border-left-patch {
  border: 1px solid #000 !important;
}

.crypto-table /deep/ .el-table__border-bottom-patch {
  border: 1px solid #000 !important;
}

.crypto-table /deep/ .el-table::before {
  background-color: #000;
}

.crypto-table /deep/ .el-table::after {
  background-color: #000;
}

.support-yes {
  color: #67c23a;
  font-weight: bold;
  font-size: 16px;
}

.support-no {
  color: #f56c6c;
  font-weight: bold;
  font-size: 16px;
}
</style>

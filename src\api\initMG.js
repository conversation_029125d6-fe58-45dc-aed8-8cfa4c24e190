import API from './apiuri';
// import axios from "axios";
import {url, req, get, reqParams, noAuthreq, fileReq, reqFormData, reqCommon} from './axiosFun';

const initApi = API.initApi;

// 添加密钥主管
const initAddManage = (params) => { return req("post", url + `/init/keyManager`, params) };
// 获取状态
const initGetState = (params) => { return get("get", url + `/svs/init/keyManager`, params) };
// 获取密钥主管UKey列表
const initUKeyList = () => { return get("get", url + `/svs/init/uKeySerial`) };
// const initUKeyListApi = (type) => { return get("get", url + `/svs/init/uKeyLogin/${type}`) };
const initUKeyListApi = (type) => { return get("get", url + `/svs/init/uKeyLogin?type=` + type) };


const resetCard = () => { return req("post", url + `/business/svs/reset`) };


export default {
  initAddManage,
  initGetState,
  initUKeyList,
  initUKeyListApi,
  resetCard,
  //获取系统初始化状态
  initSSl(param) {
    return reqFormData("post", initApi.sslCert, param)
  },
  systemInitSSL(param) {
    return req("post", initApi.sslCert, param);
  },
  createCsr(param) {
    return reqCommon("post",initApi.generateCsr,param,{responseType: 'blob' });
  },
  isVSM() {
    return req("get", "/ics/vsm/vxlan/isVsm")
  },
};

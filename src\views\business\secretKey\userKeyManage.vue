<template>
    <div>
        <el-card>
            <el-form :inline="true" class="comForm">
                <el-form-item label="密钥索引：" style="margin-bottom: 0" size="small">
                    <el-input size="small" v-model="queryParams.index" clearable placeholder="请输入密钥索引"></el-input>
                </el-form-item>
                <el-form-item label="密钥类型：" style="margin-bottom: 0" size="small">
                    <el-select v-model="queryParams.keyType" size="small">
                        <el-option value="" label="全部"></el-option>
                        <el-option :value="1" label="签名密钥"></el-option>
                        <el-option :value="2" label="加密密钥"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item style="margin-bottom: 0;background-color: transparent" size="small">
                    <el-button class="comBtn com_send_btn" size="small" type="primary" icon="el-icon-search" @click="refreshHandle">搜索</el-button>
                    <el-button class="comBtn com_reset_btn" size="small" type="primary" icon="el-icon-refresh" @click="resetHandle">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card style="margin-top: 10px">
            <div style="margin-bottom: 10px">
                <el-button class="comBtn com_send_btn" size="small" type="primary" icon="el-icon-plus" @click="createUserKey">生成用户密钥</el-button>
                <Strategy :policyType="5" btnSize="small"></Strategy>
            </div>
            <createTable
                    :tableData="userKeyList"
                    :tableHeader="tableHeader"
                    :isPage="isPage"
                    :pageAttributes="{total: total, currentPage: queryParams.pageNo, pageSize: queryParams.pageSize}"
                    :current-change="currentChange"
                    :sizeChange="sizeChange"
            >
            </createTable>
        </el-card>
        <create-user-key-dia ref="createUserKeyDia" @parentHandle="getUserKeyList"></create-user-key-dia>
    </div>
</template>

<script>
    // import keyManage from "../../../api/symmetryMG";
    import createUserKeyDia from "./createUserKeyDia"
    import Strategy from "@/components/strategy/index";
    export default {
        name: 'userKeyManage',
        components: { createUserKeyDia, Strategy },
        data() {
            let _this = this;
            return {
                queryParams: {
                    index: '',
                    keyType: '',
                    pageNo: 1,
                    pageSize: 10,
                },
                userKeyList: [],
                indexArr: [],
                tableHeader: [
                    {type: "index", label: "序号"},
                    {type: "normal", label: "索引", prop: "id"},
                    {type: "normal", label: "主密钥索引", prop: "masterId"},
                    {type: "normal", label: "用户标识", prop: "userId"},
                    {type: "text_formatter", label: "密钥类型", prop: "keyType",
                        formatter(value) {
                            // return value === 1 ? "签名主密钥" : value === 2 ? "加密主密钥" : "-";
                            return value === 1 ? "签名密钥" : value === 2 ? "加密密钥" : "-";
                        }
                    },
                    {type: "time", label: "创建时间", prop: "createTime"},
                    {
                        type: "operation",
                        label: "操作",
                        width: "150",
                        tag: [
                            {
                                name: "删除",
                                operType: "del",
                                tagType: "el-button",
                                attributes: {
                                    size: "mini",
                                    type: "text",
                                    icon: "el-icon-delete"
                                },
                                callback: function (row) {
                                    _this.deleteUserKey(row.id);
                                }
                            }
                        ]
                    }
                ],
                isPage: true,
                total: 0,
            }
        },
        methods: {
            initUserKeyManage() {
                this.getUserKeyList();
                this.getMasterKeyIndex()
            },
            refreshHandle() {
                this.getUserKeyList()
            },
            resetHandle() {
                Object.assign(this.queryParams, this.$options.data().queryParams);
                this.getUserKeyList()
            },
            createUserKey() {
                this.$refs.createUserKeyDia.initCreateUserKey(this.indexArr)
            },
            // 获取列表
            getUserKeyList() {
                this.$http.symmetryMG.userKeyList(this.queryParams).then(({code, data, msg, row}) => {
                    console.log(code, data, msg, row);
                    if (code !== 0) return this.$message.warning(msg);
                    this.userKeyList = data;
                    this.total = row
                })
            },
            // 获取主密钥索引
            getMasterKeyIndex () {
                this.$http.symmetryMG.masterKeyIndex().then(res => {
                    console.log(res);
                    this.indexArr = res.data
                })
            },
            deleteUserKey(id) {
                this.$confirm('确定删除吗?', '删除确认', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.symmetryMG.delUserKey(id).then(({code, data, msg}) => {
                        console.log(code, data, msg);
                        if (code !== 0) return this.$message.warning(msg);
                        this.$message.success('删除成功!');
                        this.getUserKeyList()
                    })
                }).catch(() =>{})
            },
            // 分页操作
            currentChange(val) {
                this.queryParams.pageNo = val;
                this.getUserKeyList();
            },
            sizeChange(val) {
                this.queryParams.pageSize = val;
                this.getUserKeyList();
            }
        }
    }
</script>
<style>
    .el-tooltip__popper {
        max-width: 350px !important;
    }
</style>

<style lang="less" scoped>
    /deep/ .el-tooltip__popper {
        max-width: 300px !important;
    }
    /deep/ .el-tooltip__popper.is-dark {
        max-width: 500px!important;
    }
    .svg-icon {
        width: 12px;
        height: 12px;
    }
</style>

<template>
    <el-dialog title="导出主密钥" :visible.sync="show" width="550px" append-to-body :close-on-click-modal="false" @close="closeDialog">
        <el-form label-width="110px" :model="exportForm" :rules="rules" ref="exportForm">
            <el-form-item label="上传文件:" prop="file" style="height: 220px" class="myUpload_form_item">
                <el-upload
                        class="upload-demo myUpload"
                        drag
                        action=""
                        accept=".cer"
                        :on-remove="handleRemove"
                        :on-change="handleChange"
                        :multiple='false'
                        :file-list="fileList"
                        :auto-upload="false">
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em><span class="el-upload__tip" slot="tip">请上传 .cer 文件</span></div>
                    <!--<div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>-->
                    <!--<div class="el-upload__tip" slot="tip">提示只能上传zip文件，且不超过5G</div>-->
                </el-upload>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button size="small" class="comBtn com_reset_btn" @click="closeDialog">取 消</el-button>
            <el-button size="small" class="comBtn com_send_btn" @click="exportMasterKeyFun" :loading="loading">导 出</el-button>
        </div>
    </el-dialog>
</template>

<script>
    // import keyManage from "../../../api/symmetryMG";
    import { downloadPackage } from "@/utils/exportExcel"
    export default {
        data() {
            return {
                show: false,
                exportForm: {
                    ids: '',
                    file: null
                },
                indexArr: [],
                fileList: [],
                rules: {
                    file: [{required: true, message: '请上传文件', trigger: 'change'}]
                },
                loading: false
            }
        },
        methods: {
            initExportMaster(id) {
                this.show = true;
                this.exportForm.ids = id;
                this.$nextTick(() => {
                    this.$refs["exportForm"].clearValidate();
                })
            },
            changeKeyType() {
                this.$refs["exportForm"].clearValidate();
            },
            handleChange(file) {
                this.fileList = [];
                let index = file.name.lastIndexOf(".");
                let ext = file.name.substr(index + 1);
                if (ext === 'cer') {
                    this.exportForm.file = file.raw;
                    this.fileList.push(file);
                    this.$refs.exportForm.validateField('file');
                } else {
                    this.$message.warning('文件格式错误, 请上传正确格式!')
                }
            },
            handleRemove(file, fileList) {
                this.exportForm.file = null;
                this.fileList = fileList
            },
            exportMasterKeyFun() {
                this.$refs["exportForm"].validate((valid) => {
                    if (valid) {
                        // 调用后端接口
                        let p = new FormData();
                        p.append('file', this.exportForm.file);
                        p.append('ids', this.exportForm.ids);
                        downloadPackage('post', '/svs/sm9/master/export', p);
                        this.closeDialog()
                    }
                })
            },
            closeDialog() {
                this.show = false;
                this.fileList = [];
                this.$nextTick(() => {
                    this.$refs.exportForm.resetFields();
                })
            }
        }
    }
</script>

<style lang="less" scoped>
    .myUpload_form_item {
        margin-bottom: 5px;
    }

    .myUpload{
        /deep/ .el-upload-list {
            height: 25px;
            display: inline-block;
            vertical-align: middle;
            margin-left: 10px;
            .el-upload-list__item {
                margin-top: 0;
                transition: none ;
            }
        }

        .el-upload__tip {
            display: block;
            height: 20px;
            line-height: 20px;
            color: #8b8b8b;
            margin-top: 0;
        }
    }
</style>

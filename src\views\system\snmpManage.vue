<template>
    <div>
        <el-card class="box-card" shadow="always" style="margin-bottom: 10px">
            <template #header>
                <div class="card-title">SNMP V2管理</div>
            </template>
            <div style="display: flex;">
                <label style="margin-right: 10px;">启用状态：</label>
                <el-switch style="align-self: end;" v-model="snmpV2Enable" active-value="1" inactive-value="0" @change="handleSwitchChange"></el-switch>
            </div>
        </el-card>
        <el-card class="box-card" shadow="always">
            <template #header>
                <div class="card-title">SNMP V3管理</div>
            </template>
            <el-row>
                <el-button class="comBtn com_send_btn" size="mini" type="success" @click="add">新增</el-button>
                <el-button class="comBtn com_add_btn" size="mini" type="primary" @click="pageStrategy">刷新</el-button>
            </el-row>
            <br/>
            <!-- 列表 -->
            <create-table
                    :tableData="tableData"
                    :tableHeader="tableDataHeader"
                    :isPage="true"
                    :pageAttributes="{total: total, currentPage: pageParam.pageNo}"
                    :size-change="sizeChange"
                    :current-change="currentChange"
                    :prev-click="prevClick"
                    :next-click="nextClick"
            ></create-table>

            <!-- 新增 -->
            <el-dialog title="管理员策略管理" :visible.sync="addOrEditVisible" width="600px" :before-close="closeDialog"
                       :close-on-click-modal="false">
                <el-form label-width="200px" ref="editForm" :model="editForm" :rules="rules">
                    <el-form-item label="策略名称：" prop="name">
                        <el-input size="small" v-model="editForm.name" placeholder="请输入策略名称"></el-input>
                    </el-form-item>
                    <el-form-item label="密码修改周期(天)：" prop="passwordCycle">
                        <el-input size="small" v-model.number="editForm.passwordCycle" placeholder="0-99999天(0为不强制修改)" maxlength="8"></el-input>
                    </el-form-item>
                    <el-form-item label="密码强度：" prop="passwordStrength">
                        <el-col :span="10">
                            <el-select v-model="editForm.passwordStrength" filterable placeholder="请选择" size="small">
                                <el-option v-for="item in passwordOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </el-col>
                    </el-form-item>
                    <el-form-item label="登录方式：" prop="loginType">
                        <!--<el-radio-group v-model="editForm.loginType">-->
                            <!--<el-radio :label="1">证书登录</el-radio>-->
                            <!--<el-radio :label="2">双因子登录</el-radio>-->
                        <!--</el-radio-group>-->

                        <el-checkbox-group v-model="editForm.loginType" :min="1">
                            <el-checkbox :label="1">用户名/密码</el-checkbox>
                            <el-checkbox :label="2">数字证书</el-checkbox>
                            <el-checkbox :label="3">双因子登录</el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>
                    <el-form-item label="登录设置：账号锁定阈值：" prop="lockThreshold">
                        <el-input size="small" v-model.number="editForm.lockThreshold" placeholder="请输入账号锁定阈值(5-15)" maxlength="8"></el-input>
                    </el-form-item>
                    <el-form-item label="自动解锁时间(分钟)：" prop="unlockTime">
                        <el-input size="small" v-model.number="editForm.unlockTime" placeholder="请输入自动解锁时间(5-60)分钟" maxlength="8"></el-input>
                    </el-form-item>
                    <el-form-item label="超时限制(分钟)：" prop="sessionExpireTime">
                        <el-input size="small" v-model.number="editForm.sessionExpireTime" placeholder="请输入超时限制(1-999)分钟" maxlength="8"></el-input>
                    </el-form-item>
                    <el-form-item label="UKey拔出检测：" prop="drawKeyPoll">
                        <el-switch v-model="editForm.drawKeyPoll" active-color="#13ce66" inactive-color="#ff4949" :active-value="1" :inactive-value="0"></el-switch>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button class="comBtn com_reset_btn" size="small" @click='closeDialog'>取消</el-button>
                    <el-button class="comBtn com_send_btn" size="small" type="primary" @click="saveStrategy">保存</el-button>
                </div>
            </el-dialog>
        </el-card>
    </div>
</template>

<script>
    // import userMG from "@/api/userMG";
    import createTable from "@/utils/createTable";

    export default {
        name: "administrator-strategy",
        components: {createTable},
        data() {
            const validateCycle = (rule, value, callback) => {
                if (!Number.isInteger(value)) return callback(new Error('请输入数值'));
                if (value > 99999 || value < 0) {
                    callback(new Error("密码修改周期在 0-99999天(0为不强制修改)"));
                } else {
                    callback();
                }
            };
            const validateThreshold = (rule, value, callback) => {
                if (!Number.isInteger(value)) return callback(new Error('请输入数值'));
                if (value < 5 || value > 15) {
                    callback(new Error('阈值只能在5-15之间'));
                } else {
                    callback();
                }
            };
            const validateUnlock = (rule, value, callback) => {
                if (!Number.isInteger(value)) return callback(new Error('请输入数值'));
                if (value < 5 || value > 60) {
                    callback(new Error('自动解锁时间只能在5-60之间'));
                } else {
                    callback();
                }
            };
            const validateExpireTime = (rule, value, callback) => {
                if (!Number.isInteger(value)) return callback(new Error('请输入数值'));
                if (value < 1 || value > 999) {
                    callback(new Error("超时时间在1-999分钟之间"));
                } else {
                    callback();
                }
            };
            let _this = this;
            return {
                snmpV2Enable: 0,
                tableDataHeader: [
                    {label: '序号', prop: 'id', type: "index"},
                    {label: '用户名', prop: 'username', type: "normal"},
                    {
                        label: "操作",
                        prop: "1",
                        type: "operation",
                        width: "160",
                        tag: [
                            {
                                name: "编辑",
                                operType: "put",
                                tagType: "el-button",
                                attributes: {
                                    size: "mini",
                                    type: "text",
                                    icon: "el-icon-edit"
                                },
                                callback: function (row) {
                                    _this.edit(row);
                                }
                            }, {
                                name: "删除",
                                operType: "del",
                                tagType: "el-button",
                                attributes: {
                                    size: "mini",
                                    type: "text",
                                    icon: "el-icon-delete"
                                },
                                isShow: function (row) {
                                    return row.system;
                                },
                                callback: function (row) {
                                    _this.deleteStrategy(row);
                                }
                            }
                        ]
                    }
                ],
                tableData: [],
                passwordOptions: [
                    {value: 1, label: '高-(大小写、数字和特殊字符)'},
                    {value: 2, label: '中-(包含大小写和数字)'}
                ],
                addOrEditVisible: false,  //是否显示新增编辑界面
                editForm: {
                    id: null,
                    name: null,
                    passwordCycle: null,
                    passwordStrength: 1,
                    loginType: [1],
                    sessionExpireTime: null,
                    isEnable: null,
                    lockThreshold: null,
                    unlockTime: null,
                    loginMode: null,
                    ipPart: null,
                    drawKeyPoll: false
                },
                pageParam: {
                    pageNo: 1,
                    pageSize: 10
                },
                total: 1,
                rules: {
                    name: [{required: true, message: '请输入策略名称', trigger: 'blur'}],
                    passwordCycle: [
                        {required: true, message: '请输入密码修改周期', trigger: 'blur'},
                        {validator: validateCycle, trigger: 'blur'}
                    ],
                    passwordStrength: [{required: true, message: '请输入密码强度', trigger: 'blur'}],
                    lockThreshold: [
                        {required: true, message: '请输入账号锁定阈值', trigger: 'blur'},
                        {validator: validateThreshold, trigger: 'blur'}
                    ],
                    unlockTime: [
                        {required: true, message: '请输入自动解锁时间', trigger: 'blur'},
                        {validator: validateUnlock, trigger: 'blur'}
                    ],
                    sessionExpireTime: [
                        {required: true, message: '请输入超时限制', trigger: 'blur'},
                        {validator: validateExpireTime, trigger: 'blur'}
                    ]
                }
            }
        },
        created() {
            this.pageStrategy(this.pageSize, this.currentPage);
        },
        methods: {
            handleSwitchChange(val){
                console.log(val);
            },
            add() {
                this.addOrEditVisible = true
            },
            edit(row) {
                this.addOrEditVisible = true;
                this.setEditForm(row);
            },
            setEditForm(row) {
                console.log(row);
                this.editForm.id = row.id;
                this.editForm.name = row.name;
                this.editForm.passwordCycle = +row.passwordCycle;
                this.editForm.passwordStrength = row.passwordStrength;
                this.editForm.loginType = row.loginType.split(',').map(i => parseInt(i, 0));
                this.editForm.sessionExpireTime = +row.sessionExpireTime;
                this.editForm.isEnable = row.isEnable;
                this.editForm.lockThreshold = +row.lockThreshold;
                this.editForm.unlockTime = +row.unlockTime;
                this.editForm.loginMode = row.loginMode;
                this.editForm.ipPart = row.ipPart;
                // debugger
                this.editForm.drawKeyPoll = row.drawKeyPoll;
                this.editForm.system = row.system;
            },
            closeDialog() {
                this.addOrEditVisible = false;
                this.cleanFrom();
                this.$refs["editForm"].clearValidate();
            },
            //获取管理员策略列表
            listStrategy() {
                this.tableData = [];
                this.$http.userMG.listStrategy().then((res) => {
                    let code = res.code;
                    if (code == '0') {
                        this.tableData = res.data;
                        console.log(this.tableData)
                    }
                })
            },
            // 分页显示管理员策略
            pageStrategy() {
                this.tableData = [];
                this.$http.userMG.pageStrategy(this.pageParam).then((res) => {
                    this.tableData = res.data;
                    this.total = res.row;
                })
            },
            // 保存管理员策略
            saveStrategy() {
                this.$refs["editForm"].validate((valid) => {
                    if (valid) {
                        this.editForm.loginType = this.editForm.loginType.sort((a,b)=>{return a-b}).join(',');
                        if (this.editForm.id == null) {
                            this.editForm.system = null;
                            this.$http.userMG.insertStrategy(JSON.stringify(this.editForm)).then((res) => {
                                let code = res.code;
                                if (code == '0') {
                                    this.cleanFrom();
                                    this.pageStrategy();
                                    this.$message.success("添加管理员策略成功");
                                } else {
                                    this.addOrEditVisible = false;
                                    // this.$message.error(res.msg)
                                }
                            })
                        } else {
                            this.$http.userMG.updateStrategy(JSON.stringify(this.editForm)).then((res) => {
                                let code = res.code;
                                if (code == '0') {
                                    this.cleanFrom();
                                    this.pageStrategy();
                                    this.$message.success("编辑管理员策略成功");
                                } else {
                                    this.addOrEditVisible = false;
                                    // this.$message.error(res.msg)
                                }
                            })
                        }
                    }
                })
            },
            // 删除管理员策略
            deleteStrategy(row) {
                let id = row.id;
                this.$confirm('确定要删除吗?', '删除确认', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.userMG.deleteStrategy(id).then((res) => {
                        let code = res.code;
                        if (code == '0') {
                            this.$message.success('删除成功');
                            this.pageStrategy();
                        }
                    })
                })
            },
            // 改变当前策略状态
            changeEnable(row) {
                // 当前策略启用状态
                let currentEnable = row.isEnable;
                let id = row.id;
                if (currentEnable === 0) {
                    // 当前为未启用状态，改变后为启用
                    this.enableStrategy(id);
                } else {
                    this.$message.warning("管理员策略必须有一个启用，请开启需要的策略，当前策略会自动关闭！");
                }
            },
            // 开启策略
            enableStrategy(id) {
                this.$confirm('启用策略会关闭之前开启的策略，确认开启当前策略吗?', '启用确认', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.userMG.enableStrategy(id).then((res) => {
                        let code = res.code;
                        if (code === 0) {
                            this.$message.success('策略启用成功');
                            this.pageStrategy()
                        }
                    })
                })
            },
            // 关闭策略
            unEnableStrategy(id) {
                this.$confirm('您确定要关闭当前策略吗?', '关闭确认', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.userMG.unEnableStrategy(id).then((res) => {
                        let code = res.code;
                        if (code === 0) {
                            this.$message.success('策略关闭成功');
                            this.pageStrategy()
                        }
                    })
                })
            },
            // 每页显示条数改变
            sizeChange(res) {
                this.pageParam.pageSize = res;
                this.pageStrategy()
            },
            // 前往页
            currentChange(res) {
                this.pageParam.pageNo = res;
                this.pageStrategy()
            },
            // 上一页
            prevClick(res) {
                this.pageParam.pageNo = res;
                this.pageStrategy()
            },
            // 下一页
            nextClick(res) {
                this.pageParam.pageNo = res;
                this.pageStrategy()
            },
            cleanFrom() {
                this.editForm.id = null;
                this.editForm.name = null;
                this.editForm.passwordCycle = null;
                this.editForm.passwordStrength = 1;
                this.editForm.loginType = [1];
                this.editForm.sessionExpireTime = null;
                this.editForm.isEnable = null;
                this.editForm.lockThreshold = null;
                this.editForm.unlockTime = null;
                this.editForm.loginMode = null;
                this.editForm.ipPart = null;
                this.editForm.drawKeyPoll = 0;
                this.addOrEditVisible = false;
            }
        },
    }
</script>

<style lang="less" scoped>
    .admin-button {
        margin-top: 20px;
        margin-bottom: 20px;
        float: left;
    }

    /deep/ .el-pagination__total {
        float: left;
    }
    .el-radio-group {
        margin-top: 13px;
        float: left;
    }

    .el-select {
        width: 260px;
    }
</style>
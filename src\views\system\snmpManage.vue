<template>
    <div>
        <el-card class="box-card" shadow="always" style="margin-bottom: 10px">
            <template #header>
                <div class="card-title">SNMP V2管理</div>
            </template>
            <div style="display: flex;">
                <label style="margin-right: 10px;">启用状态：</label>
                <el-switch style="align-self: end;" v-model="snmpV2Enable" active-value="1" inactive-value="0" @change="handleSwitchChange"></el-switch>
            </div>
        </el-card>
        <el-card class="box-card" shadow="always">
            <template #header>
                <div class="card-title">SNMP V3管理</div>
            </template>
            <el-row>
                <el-button class="comBtn com_send_btn" size="mini" type="success" @click="add">新增</el-button>
                <el-button class="comBtn com_add_btn" size="mini" type="primary" @click="getV3Items">刷新</el-button>
            </el-row>
            <br/>
            <!-- 列表 -->
            <create-table
                    :tableData="tableData"
                    :tableHeader="tableDataHeader"
                    :isPage="false"
                    :pageAttributes="{total: total, currentPage: pageParam.pageNo}"
                    :size-change="sizeChange"
                    :current-change="currentChange"
                    :prev-click="prevClick"
                    :next-click="nextClick"
            ></create-table>

            <!-- 新增 -->
            <el-dialog title="管理员策略管理" :visible.sync="addOrEditVisible" width="600px" :before-close="closeDialog"
                       :close-on-click-modal="false">
                <el-form label-width="200px" ref="editForm" :model="editForm" :rules="rules">
                    <el-form-item label="用户名：" prop="uname">
                        <el-input size="small" v-model="editForm.uname" placeholder="请输入用户名"></el-input>
                    </el-form-item>
                    <el-form-item label="认证算法：" prop="authAlg">
                        <el-col :span="10">
                            <el-select v-model="editForm.authAlg" placeholder="请选择认证算法" size="small">
                                <el-option key="MD5" label="MD5" value="1"></el-option>
                                <el-option key="SHA" label="SHA" value="2"></el-option>
                                <el-option key="SHA-224" label="SHA-224" value="3"></el-option>
                                <el-option key="SHA-256" label="SHA-256" value="4"></el-option>
                                <el-option key="SHA-384" label="SHA-384" value="5"></el-option>
                                <el-option key="SHA-512" label="SHA-512" value="6"></el-option>
                            </el-select>
                        </el-col>
                    </el-form-item>
                    <el-form-item label="认证PIN：" prop="authPIN">
                        <el-input size="small" v-model="editForm.authPIN" placeholder="请输入认证PIN"></el-input>
                    </el-form-item>
                    <el-form-item label="加密算法：" prop="cryptoAlg">
                        <el-col :span="10">
                            <el-select v-model="editForm.cryptoAlg" placeholder="请选择加密算法" size="small">
                                <el-option key="DES" label="DES" value="1"></el-option>
                                <el-option key="AES" label="AES" value="2"></el-option>
                            </el-select>
                        </el-col>
                    </el-form-item>
                    <el-form-item label="加密密钥：" prop="cryptoKey">
                        <el-input size="small" v-model="editForm.cryptoKey" placeholder="请输入加密密钥"></el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button class="comBtn com_reset_btn" size="small" @click='closeDialog'>取消</el-button>
                    <el-button class="comBtn com_send_btn" size="small" type="primary" @click="saveV3">确定</el-button>
                </div>
            </el-dialog>
        </el-card>
    </div>
</template>

<script>
    // import userMG from "@/api/userMG";
    import createTable from "@/utils/createTable";

    export default {
        name: "snmpManage",
        components: {createTable},
        data() {
            let _this = this;
            return {
                snmpV2Enable: 0,
                tableDataHeader: [
                    {label: '序号', prop: 'id', type: "index"},
                    {label: '用户名', prop: 'username', type: "normal"},
                    {
                        label: "操作",
                        prop: "1",
                        type: "operation",
                        width: "160",
                        tag: [
                            {
                                name: "编辑",
                                operType: "put",
                                tagType: "el-button",
                                attributes: {
                                    size: "mini",
                                    type: "text",
                                    icon: "el-icon-edit"
                                },
                                callback: function (row) {
                                    _this.edit(row);
                                }
                            }, {
                                name: "删除",
                                operType: "del",
                                tagType: "el-button",
                                attributes: {
                                    size: "mini",
                                    type: "text",
                                    icon: "el-icon-delete"
                                },
                                isShow: function (row) {
                                    return row.system;
                                },
                                callback: function (row) {
                                    _this.deleteStrategy(row);
                                }
                            }
                        ]
                    }
                ],
                tableData: [],
                passwordOptions: [
                    {value: 1, label: '高-(大小写、数字和特殊字符)'},
                    {value: 2, label: '中-(包含大小写和数字)'}
                ],
                addOrEditVisible: false,  //是否显示新增编辑界面
                editForm: {
                    uname: '',
                    authAlg: null,
                    authPIN: '',
                    cryptoAlg: null,
                    cryptoKey: ''
                },
                pageParam: {
                    pageNo: 1,
                    pageSize: 10
                },
                total: 1,
                rules: {
                    uname: [
                        {required: true, message: '请输入用户名', trigger: 'blur'}
                    ],
                    authAlg: [
                        {required: true, message: '请选择认证算法', trigger: 'blur'}
                    ],  
                    authPIN: [
                        {required: true, message: '请输入认证PIN', trigger: 'blur'}
                    ],
                    cryptoAlg: [
                        {required: true, message: '请选择加密算法', trigger: 'blur'}
                    ],
                    cryptoKey: [
                        {required: true, message: '请输入加密密钥', trigger: 'blur'}
                    ]
                }
            }
        },
        created() {
            this.$http.userMG.getV2Config().then((res) => {
                if (res.code == 0) {
                    this.snmpV2Enable = res.data;
                }
            })
        },
        mounted() {
            this.getV3Items()
        },
        methods: {
            handleSwitchChange(val){
                if(val == 1){
                    this.$http.userMG.startSnmpV2().then((res) => {
                        if (res.code == 0) {
                            this.$message.success('启用成功！')
                        }
                    })
                }
                if(val == 0){
                    this.$http.userMG.stopSnmpV2().then((res) => {
                        if (res.code == 0) {
                            this.$message.success('禁用成功！')
                        }
                    })
                }
            },
            add() {
                this.addOrEditVisible = true
            },
            edit(row) {
                this.addOrEditVisible = true;
                this.setEditForm(row);
            },
            setEditForm(row) {
                console.log(row);
                this.editForm.id = row.id;
                this.editForm.name = row.name;
                this.editForm.passwordCycle = +row.passwordCycle;
                this.editForm.passwordStrength = row.passwordStrength;
                this.editForm.loginType = row.loginType.split(',').map(i => parseInt(i, 0));
                this.editForm.sessionExpireTime = +row.sessionExpireTime;
                this.editForm.isEnable = row.isEnable;
                this.editForm.lockThreshold = +row.lockThreshold;
                this.editForm.unlockTime = +row.unlockTime;
                this.editForm.loginMode = row.loginMode;
                this.editForm.ipPart = row.ipPart;
                // debugger
                this.editForm.drawKeyPoll = row.drawKeyPoll;
                this.editForm.system = row.system;
            },
            closeDialog() {
                this.addOrEditVisible = false;
                this.cleanFrom();
                this.$refs["editForm"].clearValidate();
            },
            //获取管理员策略列表
            listStrategy() {
                this.tableData = [];
                this.$http.userMG.listStrategy().then((res) => {
                    let code = res.code;
                    if (code == '0') {
                        this.tableData = res.data;
                        console.log(this.tableData)
                    }
                })
            },
            getV3Items() {
                this.$http.userMG.queryV3User({uname:"all"}).then((res) => {
                    console.log(res)
                    if(res.code == 0 && res.data != 'none'){

                    }
                    // this.tableData = res.data;
                })
            },
            // 保存管理员策略
            saveV3() {
                this.$refs["editForm"].validate((valid) => {
                    if (valid) {
                        
                    }
                })
            },
            // 删除管理员策略
            deleteStrategy(row) {
                let id = row.id;
                this.$confirm('确定要删除吗?', '删除确认', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.userMG.deleteStrategy(id).then((res) => {
                        let code = res.code;
                        if (code == '0') {
                            this.$message.success('删除成功');
                            this.getV3Items();
                        }
                    })
                })
            },
            // 改变当前策略状态
            changeEnable(row) {
                // 当前策略启用状态
                let currentEnable = row.isEnable;
                let id = row.id;
                if (currentEnable === 0) {
                    // 当前为未启用状态，改变后为启用
                    this.enableStrategy(id);
                } else {
                    this.$message.warning("管理员策略必须有一个启用，请开启需要的策略，当前策略会自动关闭！");
                }
            },
            // 开启策略
            enableStrategy(id) {
                this.$confirm('启用策略会关闭之前开启的策略，确认开启当前策略吗?', '启用确认', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.userMG.enableStrategy(id).then((res) => {
                        let code = res.code;
                        if (code === 0) {
                            this.$message.success('策略启用成功');
                            this.getV3Items()
                        }
                    })
                })
            },
            // 关闭策略
            unEnableStrategy(id) {
                this.$confirm('您确定要关闭当前策略吗?', '关闭确认', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.userMG.unEnableStrategy(id).then((res) => {
                        let code = res.code;
                        if (code === 0) {
                            this.$message.success('策略关闭成功');
                            this.getV3Items()
                        }
                    })
                })
            },
            // 每页显示条数改变
            sizeChange(res) {
                this.pageParam.pageSize = res;
                this.getV3Items()
            },
            // 前往页
            currentChange(res) {
                this.pageParam.pageNo = res;
                this.getV3Items()
            },
            // 上一页
            prevClick(res) {
                this.pageParam.pageNo = res;
                this.getV3Items()
            },
            // 下一页
            nextClick(res) {
                this.pageParam.pageNo = res;
                this.getV3Items()
            },
            cleanFrom() {
                this.editForm.id = null;
                this.editForm.name = null;
                this.editForm.passwordCycle = null;
                this.editForm.passwordStrength = 1;
                this.editForm.loginType = [1];
                this.editForm.sessionExpireTime = null;
                this.editForm.isEnable = null;
                this.editForm.lockThreshold = null;
                this.editForm.unlockTime = null;
                this.editForm.loginMode = null;
                this.editForm.ipPart = null;
                this.editForm.drawKeyPoll = 0;
                this.addOrEditVisible = false;
            }
        },
    }
</script>

<style lang="less" scoped>
    .admin-button {
        margin-top: 20px;
        margin-bottom: 20px;
        float: left;
    }

    /deep/ .el-pagination__total {
        float: left;
    }
    .el-radio-group {
        margin-top: 13px;
        float: left;
    }

    .el-select {
        width: 260px;
    }
</style>
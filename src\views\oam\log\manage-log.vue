<template>
  <div>
    <!-- 设置root级别 -->
    <el-card class="box-card" style="width: 70%;margin-bottom: 10px">
      <div slot="header" class="clearfix">
        <span style="font-size: 18; font-weight: 600;">设置root级别</span>
      </div>
      <el-form label-width="150px" style="margin-top: 10px">
        <el-form-item label="日志级别">
          <el-select v-model="rootLevel" placeholder="请选择" size="small">
            <el-option v-for="item in levelOptions" :key="item.value" :label="item.label"
              :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button class="comBtn com_send_btn" size="small" @click="setRootLevel">设置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <!-- logger列表 -->
    <el-card class="box-card" style="width: 70%;margin-bottom: 10px">
      <div slot="header" class="clearfix">
        <span style="font-size: 18; font-weight: 600;">logger列表</span>
        <el-button style="float: right;" class="comBtn com_send_btn" size="small" @click="openLoggerView">添加设置
        </el-button>
      </div>
      <create-table :tableData="tableData" :tableHeader="tableDataHeader"></create-table>
    </el-card>
    <!-- 下载日志 -->
    <el-card class="box-card" style="width: 70%;margin-bottom: 10px">
      <div slot="header" class="clearfix">
        <span style="font-size: 18; font-weight: 600;">下载日志</span>
      </div>
      <el-form label-width="160px" ref="downloadForm" :model="downloadForm" :rules="rules" size="small">
        <el-form-item label="日志级别：" prop="logLevel">
          <el-select v-model="downloadForm.logLevel" placeholder="请选择" size="small">
            <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="日志日期：" style="width: 50%" prop="searchTime">
          <el-date-picker v-model="downloadForm.searchTime" type="date" placeholder="选择日期" value-format="yyyy-MM-dd">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button class="comBtn com_send_btn" :loading="downLoading" size="small" @click="downloadLog">下载</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- logger配置添加 -->
    <el-dialog title="logger配置添加" :visible.sync="loggerVisible" width="35%" :before-close="closeLoggerView"
      :close-on-click-modal="false">
      <el-form label-width="150px" style="margin-top: 10px" ref="logger" :model="logger" :rules="rules">
        <el-form-item label="logger地址：" prop="loggerName">
          <el-input size="small" v-model="logger.loggerName"></el-input>
        </el-form-item>
        <el-form-item label="日志级别：" prop="logLevel">
          <el-select v-model="logger.logLevel" placeholder="请选择" size="small">
            <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button class="comBtn com_send_btn" size="small" @click="setLoggerLevel">设置</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
// import oamMG from "@/api/oamMG";

export default {
  name: "manage-log",
  data() {
    let _this = this;
    return {
      loggerVisible: false,
      tableData: [],
      downLoading: false,
      tableDataHeader: [
        { label: 'logger地址', prop: 'loggerName', type: "normal" },
        { label: 'log级别', prop: 'logLevel', type: "normal" },
        {
          label: "操作",
          type: "operation",
          width: "240",
          fixed: "right",
          tag: [
            {
              name: "删除",
              operType: "del",
              tagType: "el-button",
              attributes: {
                size: "mini",
                type: "text",
                icon: "el-icon-delete"
              },
              isShow(row, opts, event) {
                if (row.del) {
                  return true;
                } else {
                  return false;
                }
              },
              callback: function (row, opts, event) {
                _this.delLogger(row.loggerName);
              }
            }
          ]
        }
      ],
      levelOptions: [
        { label: "DEBUG", value: "debug" },
        { label: "INFO", value: "info" },
        { label: "WARN", value: "warn" },
        { label: "ERROR", value: "error" }
      ],
      rootLevel: "",
      logger: {
        loggerName: "",
        logLevel: "",
      },
      downloadForm: {
        searchTime: "",
        logLevel: ""
      },
      rules: {
        searchTime: [{ required: true, message: '请选择要下载日志的时间', trigger: 'blur' }],
        logLevel: [{ required: true, message: '请选择下载日志的级别', trigger: ['blur', 'change'] }],
        loggerName: [{ required: true, message: '请输入logger地址', trigger: 'blur' }]
      }

    }
  },
  methods: {
    setRootLevel() {
      this.$http.oamMG.chanageRootLevel(this.rootLevel).then(res => {
        let code = res.code;
        if (code == 0) {
          this.$message.success("设置成功！");
        } else {
          this.$message.error(res.msg);
        }
      })
    },
    delLogger(loggerName) {
      this.$http.oamMG.loggerDelete({ name: loggerName }).then(res => {
        let code = res.code;
        if (code == 0) {
          this.$message.success("设置成功！");
          this.searchLoggerList();
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    setLoggerLevel() {
      this.$refs["logger"].validate(valid => {
        if (valid) {
          this.$http.oamMG.loggerLevel(this.logger).then(res => {
            let code = res.code;
            if (code == 0) {
              this.$message.success("设置成功！");
              this.searchLoggerList();
              this.closeLoggerView();
            } else {
              this.$message.error(res.msg);
            }
          })
        }
      })
    },
    downloadLog() {

      this.$refs["downloadForm"].validate((valid) => {
        if (valid) {
          this.downLoading = true;
          this.$http.oamMG.downloadLog(this.downloadForm).then(res => {

            let blob = new Blob([res], {
              type: 'application/zip'
            });
            let fileName = this.downloadForm.searchTime + this.downloadForm.logLevel + ".zip";
            if (window.navigator.msSaveOrOpenBlob) {
              // console.log(2)
              navigator.msSaveBlob(blob, fileName)
            } else {
              // console.log(3)
              var link = document.createElement('a');
              link.href = window.URL.createObjectURL(blob);
              link.download = fileName;
              link.click();
              //释放内存
              window.URL.revokeObjectURL(link.href)
            }
            this.downLoading = false;
          },
            error => {
              this.$message.error("下载异常！");
              this.downLoading = false;
            })
        }
      })
    },
    searchRootLevel() {
      this.$http.oamMG.getRootLevel().then(res => {
        let code = res.code;
        if (code == 0) {
          this.rootLevel = res.data;
        } else {
          this.$message.error("获取异常！");
        }
      })
    },
    searchLoggerList() {
      this.$http.oamMG.getLoggerList().then(res => {
        let code = res.code;
        if (code == 0) {
          this.tableData = res.data;
        } else {
          this.$message.error("获取异常！");
        }
      })
    },
    openLoggerView() {
      this.loggerVisible = true;
      this.$nextTick(() => {
        this.$refs['logger'].clearValidate();
      })
    },
    closeLoggerView() {
      this.logger.loggerName = null;
      this.logger.logLevel = null;
      this.loggerVisible = false;
    }
  },
  mounted() {
    this.searchRootLevel();
    this.searchLoggerList();
  }
}
</script>

<style scoped></style>

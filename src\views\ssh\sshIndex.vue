<template>
    <div>
        <el-card style="margin-bottom: 10px;">
            <el-form ref="form" :model="formData" :rules="rules" inline size="small">
                <el-form-item label="主机：" prop="host">
                    <el-input v-model="formData.host"/>
                </el-form-item>
                <el-form-item label="端口号：" prop="port">
                    <el-input-number
                            v-model="formData.port"
                            :controls="false"
                            :min="1"
                            :max="65535"
                            class="text-left"
                            style="width: 100%;"/>
                </el-form-item>
                <el-form-item label="用户名：" prop="username">
                    <el-input v-model="formData.username"/>
                </el-form-item>
                <el-form-item label="密码：" prop="password">
                    <el-input v-model="formData.password" type="password" show-password style="width: calc(100% - 15px)"/>
                </el-form-item>
                <el-form-item label="">
                    <el-button type="primary" size="small" @click="onConnect">连 接</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card>
            <x-terminal ref="terminal" :connInfo="formData" style="height: 800px;"/>
        </el-card>
    </div>
</template>

<script>
import XTerminal from './components/xterm-terminal'
const defaultData = {
        // host: '**********',
        host: window.location.hostname,
        // host: '',
        port: 22,
        username: 'root',
        password: 'shudun@123'
    }
export default {
    name: 'ssh',
    components: {
        XTerminal,
    },
    data() {
        return {
            formData: JSON.parse(JSON.stringify(defaultData)),
            rules: {
                    name: [
                        {required: true, trigger: 'blur', message: '请填写名称'},
                        {min: 1, max: 20, trigger: 'blur', message: '长度在1 ~ 20个字符'}
                    ],
                    host: [
                        {required: true, trigger: 'blur', message: '请填写IP地址或域名'},
                        // {pattern: /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/, trigger: 'blur', message: '请填写正确的IP地址'}
                    ],
                    port: [
                        {required: true, trigger: 'blur', message: '请填写端口号'},
                    ],
                    username: [
                        {required: true, trigger: 'blur', message: '请填写登录用户名'},
                    ],
                    password: [
                        {required: true, trigger: 'blur', message: '请填写登录密码'},
                    ]
                }
        }
    },
    methods: {
        onConnect() {
            this.$refs.terminal.loadSocket()
        }
    }
}
</script>

<style lang="less" scoped>
/deep/ .el-card__body {
    padding: 10px;
}
</style>
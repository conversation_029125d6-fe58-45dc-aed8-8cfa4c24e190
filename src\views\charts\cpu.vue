<template>
    <div id="cpu" style="width:100%;height:308px"></div>
</template>

<script>
    export default {
        name: 'cpu',
        data() {
            return {
                switchStatusData: "",
                xtime: [0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00]
            }
        },
        props: ['cpu',],
        mounted() {
            this.switchStatusData = this.cpu;
            // this.xtime=this.time
            this.drawLine();
        },
        methods: {
            drawLine() {
                // 基于准备好的dom，初始化echarts实例
                let cpu = this.$echarts.init(document.getElementById('cpu'));
                // 绘制图表
                cpu.setOption({
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross',
                            label: {
                                backgroundColor: '#6a7985'
                            }
                        }
                    },
                    legend: {
                        data: ['CPU',]
                    },

                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: [
                        {
                            type: 'category',
                            boundaryGap: false,
                            // data: []
                        }
                    ],
                    yAxis: [
                        /*{
                          type: 'value',
                          axisLabel: {
                            show: true,
                            interval: 'auto',
                            formatter: '{value} %'
                          },
                          show: true
                        }*/
                        { // 纵轴标尺固定
                            type: 'value',
                            scale: true,
                            max: 100,
                            min: 0,
                            splitNumber: 10,
                            boundaryGap: [0.2, 0.2],
                            axisLabel: {
                                show: true,
                                interval: 'auto',
                                formatter: '{value} %'
                            },
                        }
                    ],
                    series: [
                        {
                            name: 'CPU使用',
                            type: 'line',
                            stack: '总量',
                            itemStyle: {
                                color: '#1890ff',
                                // borderColor: "#355293",
                                // borderWidth: 4,
                            },
                            data: this.switchStatusData
                        },
                    ]
                });
                cpu.resize();
                window.addEventListener("resize", () => {
                    cpu.resize()
                }, false);
            }
        },
        watch: {
            cpu: function (newVal, oldVal) { //不能用箭头函数
                this.switchStatusData = newVal;
                this.drawLine();
            },
            time: function (newVal, oldVal) { //不能用箭头函数
                this.xtime = newVal;
                this.drawLine();
            }
        },
    }
</script>

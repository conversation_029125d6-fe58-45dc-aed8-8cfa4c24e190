<template>
  <div>
    <el-card class="box-card" shadow="always" style="padding-bottom: 10px">
      <el-button class="comBtn com_send_btn" size="mini" type="primary" style="margin-bottom: 10px"
        @click="pageRoles">刷新</el-button>
      <!--<br/>-->
      <!-- 列表 -->
      <create-table :tableData="tableData" :tableHeader="tableDataHeader" :isPage="true"
        :pageAttributes="{ total: total }"></create-table>

      <!-- 角色管理 -->
      <el-dialog title="角色管理" :visible.sync="addOrEditVisible" width="35%" :before-close='closeDialog'>
        <el-form label-width="150px" ref="addOrEditForm" :model="addOrEditForm" :rules="rules">
          <el-form-item label="角色名称：" prop="name">
            <el-input size="small" v-model="addOrEditForm.name" placeholder="请输入角色名称"></el-input>
          </el-form-item>
          <el-form-item label="角色描述：" prop="desc">
            <el-input size="small" v-model="addOrEditForm.desc" placeholder="请输入角色描述"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button class="comBtn com_reset_btn" size="small" @click='closeDialog'>取消</el-button>
          <el-button class="comBtn com_send_btn" size="small" @click="saveRole">保存</el-button>
        </div>
      </el-dialog>


      <!-- 查看权限 -->
      <el-dialog title="查看权限" :visible.sync="searchVisible" width="35%" :before-close='closeSearch'
        :close-on-click-modal="false">
        <el-scrollbar style="height: 500px">
          <el-tree :data="treeData" show-checkbox node-key="id" ref="tree" :default-checked-keys="defaultChecked"
            :props="defaultProps"></el-tree>
        </el-scrollbar>

        <div slot="footer" class="dialog-footer">
          <el-button class="comBtn com_reset_btn" size="small" @click='closeSearch'>取消</el-button>
          <el-button class="comBtn com_send_btn" size="small" @click="saveRoleMenu">保存</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
// import userMG from "@/api/userMG";
// import menuMG from "@/api/menuMG";
import CreateTable from "@/utils/createTable";

export default {
  name: "administrator-role",
  comments: { CreateTable },
  data() {
    let _this = this;
    return {
      tableDataHeader: [
        { label: '序号', prop: 'id', type: "index", width: '150' },
        { label: '角色名称', prop: 'name', type: "normal" },
        {
          label: "操作",
          prop: "1",
          type: "operation",
          width: "300",
          tag: [
            {
              name: "权限",
              operType: "get",
              tagType: "el-button",
              attributes: {
                size: "mini",
                type: "text",
                icon: "el-icon-lock"
              },
              callback: function (row, opts, event) {
                _this.getCheckedMenuIds(row);
              }
            },
            {
              name: "编辑",
              operType: "put",
              tagType: "el-button",
              attributes: {
                size: "mini",
                type: "text",
                icon: "el-icon-edit"
              },
              callback: function (row, opts, event) {
                _this.edit(row);
              }
            },
            {
              name: "删除",
              operType: "del",
              tagType: "el-button",
              attributes: {
                size: "mini",
                type: "text",
                icon: "el-icon-delete"
              },
              isShow: function (row) {
                return row.system;
              },
              callback: function (row, opts, event) {
                _this.deleteRole(row);
              }
            }
          ]
        }
      ],
      tableData: [],
      rules: {
        name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }]
      },
      addOrEditForm: {
        id: null,
        name: null,
        desc: null,
        system: null
      },
      editId: undefined,
      addOrEditVisible: false,
      searchVisible: false,
      // 分页参数
      pageParam: {
        pageNo: 1,
        pageSize: 10
      },
      total: null,
      treeData: [],
      defaultChecked: [],
      tableHeight: null,
      defaultProps: {
        children: 'menus',
        label: 'menuName'
      }
    }
  },
  created() {
    this.tableHeight = window.innerHeight - 300
    this.pageRoles()
    this.allMenuTree();
  },
  methods: {
    edit(row) {
      this.addOrEditForm.id = row.id;
      this.addOrEditForm.name = row.name;
      this.addOrEditForm.desc = row.desc;
      this.addOrEditForm.assign = row.assign;
      this.addOrEditForm.createtime = row.createtime;

      this.addOrEditVisible = true;
    },
    add() {
      this.cleanFrom();
      this.addOrEditVisible = true;
    },
    closeDialog() {
      this.addOrEditVisible = false;
      this.cleanFrom();
    },
    //分页显示管理员角色
    pageRoles() {
      this.tableData = [];
      this.$http.userMG.pageRoles(this.pageParam).then((res) => {
        let code = res.code;
        if (code == 0) {
          this.tableData = res.data;
          this.total = res.row;
        }
      })
    },
    allMenuTree() {
      this.$http.menuMG.menuTree().then(res => {
        if (res.code === 0) {
          this.treeData = res.data;
        }
      })
    },
    getCheckedMenuIds(row) {
      this.$http.menuMG.getMenuIdsByRoleId(row.id).then(res => {
        if (res.code === 0) {
          this.editId = row.id;
          this.defaultChecked = res.data;
          this.searchVisible = true;
        }
      })
    },
    //获取角色下所有的菜单
    // searchRoleMenus(row) {
    //   let id = row.id;
    //   userMG.listMenus(id).then((res) => {
    //     let code = res.code;
    //     if (code == 0) {
    //       this.treeData = res.data;
    //       this.searchVisible = true;
    //     }
    //   })
    // },
    closeSearch() {
      this.defaultChecked = [];
      this.editId = undefined;
      this.$refs.tree.setCheckedKeys([]);
      this.searchVisible = false;
    },
    saveRole() {
      let id = this.addOrEditForm.id;
      if (id == null)
        this.insertRole()
      else
        this.updateRole()
    },
    saveRoleMenu() {
      let opt = {
        id: this.editId,
        menuIds: this.$refs.tree.getCheckedKeys()
      }
      this.$http.userMG.setRoleMenu(opt).then(res => {
        let code = res.code;
        if (code === 0) {
          this.$message.success(res.msg || "设置角色菜单成功");
          this.closeSearch();
        } else {
          this.$message.warning(res.msg || "设置角色菜单失败")
        }
      })
    },
    //添加管理员角色
    insertRole() {
      this.$confirm('确定添加角色信息吗?', '保存确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.userMG.insertRole(this.addOrEditForm).then((res) => {
          let code = res.code;
          if (code == 0) {
            this.$message({
              message: '添加角色成功',
              type: 'success'
            });
            this.addOrEditVisible = false;
            this.pageRoles();
            this.cleanFrom();
          } else {
            this.$message({
              message: res.msg,
              type: 'error'
            })
          }
        })
      })
    },
    //编辑管理员角色
    updateRole() {
      this.$confirm('确定编辑角色信息吗?', '编辑确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.userMG.updateRole(this.addOrEditForm).then((res) => {
          let code = res.code;
          if (code == 0) {
            this.$message({
              message: '编辑角色成功',
              type: 'success'
            });
            this.addOrEditVisible = false;
            this.pageRoles();
            this.cleanFrom();
          } else {
            this.$message({
              message: res.msg,
              type: 'error'
            })
          }
        })
      })
    },
    //删除管理员角色
    deleteRole(row) {
      let id = row.id;
      this.$confirm('确定删除角色信息吗?', '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.userMG.deleteRole(id).then((res) => {
          let code = res.code;
          if (code == 0) {
            this.$message({
              message: '删除角色成功',
              type: 'success'
            });
            this.pageRoles()
          } else {
            this.$message({
              message: res.msg,
              type: 'error'
            })
          }
        })
      })
    },
    cleanFrom() {
      this.addOrEditForm.id = null
      this.addOrEditForm.name = null
      this.addOrEditForm.desc = null
      this.addOrEditForm.assign = null
      this.addOrEditForm.createtime = null
    },
  }
}
</script>

<style scoped>
.admin-button {
  margin-top: 20px;
  margin-bottom: 20px;
  float: left;
}
</style>

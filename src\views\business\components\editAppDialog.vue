<template>
    <el-dialog title="修改应用" :visible.sync="editOpen" width="550px" append-to-body :close-on-click-modal="false" @close="closeEditDia">
        <el-form ref="editForm" :model="editForm" :rules="rules" label-width="100px" size="medium">
            <el-form-item label="应用编号：" prop="appCode">
                <el-input size="small" v-model="editForm.appCode" placeholder="2~10个字符，可使用字母、数字" style="width: 100%" clearable/>
            </el-form-item>
            <el-form-item label="应用名称：" prop="appName">
                <el-input size="small" v-model="editForm.appName" placeholder="2~30个字符，可使用汉字、字母、数字、下划线" clearable/>
            </el-form-item>
            <el-form-item label="IP地址：" prop="appIp">
                <el-input size="small" v-model="editForm.appIp" clearable/>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button class="comBtn com_reset_btn" size="small" @click="editOpen=false">取 消</el-button>
            <el-button class="comBtn com_send_btn" size="small" @click="editSubmitForm">确 定</el-button>
        </div>
    </el-dialog>
</template>

<script>
    import appUtil from "@/api/appMG";
    import appMG from "@/api/appMG";
    export default {
        data() {
            let appNameVal = (rule, value, callback) => {
                if (!value) {
                    return callback(new Error('应用名称不能为空！'));
                }
                let length = value.length;
                if (length < 2 || length > 30) {
                    return callback(new Error('长度在 2 ~ 30 个字符！'));
                }
                let patt = /^[a-zA-Z0-9_\u4e00-\u9fa5]+$/;
                if (!patt.test(value)) {
                    return callback(new Error('可使用汉字、字母、数字、下划线！'));
                }
                let exitsAppName = async function () {
                    await appMG.checkAppName(value, this.editForm.id).then(res => {
                        if (!res.data) {
                            callback(new Error('应用名称已存在！'));
                        } else {
                            callback();
                        }
                    });
                };
                exitsAppName();
            };
            return {
                editOpen: false,
                editForm: {
                    appCode: '',
                    appName: '',
                    appIp: '',
                },
                rules: {
                    appName: [
                        {required: true, validator: appNameVal, trigger: 'blur'}
                    ],
                    appCode: [
                        {required: true, message: "应用编码不能为空", trigger: "blur"},
                        {min: 2, max: 10, message: '长度在 2 ~ 10 个字符', trigger: 'blur'},
                        {pattern: /^[a-zA-Z0-9_]+$/, message: '只能使用字母和数字', trigger: 'blur'},
                        {
                            validator: function (rule, value, callback) {
                                let isSucces = true;
                                let exitsAppCode = async function () {
                                    await appMG.checkCode(value, this.editForm.id).then(res => {
                                        if (!res.data) {
                                            callback(new Error('应用编码已存在！'));
                                        } else {
                                            callback();
                                        }
                                    });
                                };
                                exitsAppCode();
                            }, trigger: 'blur'
                        }
                    ],
                    appIp: [
                        {
                            validator: function (rule, value, callback) {
                                if (value && value != "") {
                                    let reg = /^(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}$/;
                                    if (!reg.test(value)) {
                                        return callback(new Error('请输入正确的IP地址！'));
                                    }
                                }
                                callback();
                            }, trigger: 'blur'
                        }
                    ]
                }
            }
        },
        methods: {
            // 初始化
            editAppInitFun(row) {
                this.editForm = {...row};
                this.editOpen = true;
            },
            // 编辑提交
            editSubmitForm() {
                this.$refs["editForm"].validate((valid) => {
                    if (valid) {
                        appUtil.update(this.editForm).then(res => {
                            console.log(res);
                            this.editOpen = false;
                            this.$parent.refreshApp();
                            // this.refreshApp();
                        });
                    } else {
                        return false;
                    }
                });
            },
            closeEditDia() {
                Object.assign(this.editForm, this.$options.data().editForm);
                this.$refs['editForm'].clearValidate();
                this.editOpen = false;
            }
        }
    }
</script>

<style scoped>

</style>

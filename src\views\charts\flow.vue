<template>
  <div>
    <div id="flow" style="width:100%;height:310px"></div>
  </div>

</template>

<script>
  export default {
    name: 'flow',
    data () {
      return {
        switchStatusData: {
          readNum: [],
          writeNum: []
        },


      }
    },
    props: ['flow',"time"],
    mounted(){
      this.switchStatusData=this.flow;

      // this.drawLine();
    },
    methods: {
      drawLine(){
        // 基于准备好的dom，初始化echarts实例
        let flow = this.$echarts.init(document.getElementById('flow'))
        // 绘制图表
        flow.setOption({

          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              label: {
                backgroundColor: '#6a7985'
              }
            }
          },
          legend: {
            data: ['接收速率', '发送速率',]
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: [
            {
              type: 'category',
              boundaryGap: false,

            }
          ],
          yAxis: [
            {
              type: 'value',
              axisLabel: {
                show: true,
                interval: 'auto',
                formatter: '{value} K'
              },
              show: true
            }
          ],
          series: [
            {
              name: '接收速率',
              type: 'line',
              itemStyle: {
                color: '#3aa1ff',
                // borderColor: "#355293",
                // borderWidth: 4,
              },
              data:this.switchStatusData.readNum
            },
            {
              name: '发送速率',
              type: 'line',
              itemStyle: {
                color: '#36CBcb',
                // borderColor: "#355293",
                // borderWidth: 4,
              },
              data: this.switchStatusData.writeNum
            },



          ]
        });
        flow.resize();
        window.addEventListener("resize", () => {
          flow.resize()
        }, false);
      }

    },
    watch:{
      flow: function (newVal ,oldVal){ //不能用箭头函数
        if (newVal != null) {
          this.switchStatusData = newVal;
        }
        this.drawLine();
      },
      time: function (newVal ,oldVal){ //不能用箭头函数
        if (newVal != null) {
          this.switchStatusData = newVal;
        }
        this.drawLine();
      }
    },
  }
</script>

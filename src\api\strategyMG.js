import { req, get, reqGet, reqParamNoJson } from './axiosFun';
import API from "@/api/apiuri"
// import {Message, Notification} from "element-ui";

let strategyApi = API.strategyConfigApi;

// 查询策略是否改变
const policyStatusApi = () => {return reqGet("get", `/strategy/state/len`)};
// 清除策略数据
const clearDataApi = () => {return req("delete", `/strategy/state`)};


export default {
  distribution(callback) {
    return reqParamNoJson("POST", strategyApi.distribution, {},callback);
  },
  policyStatusApi,
  clearDataApi,
}




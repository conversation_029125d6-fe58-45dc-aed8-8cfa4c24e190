<template>
    <div class="container">
        <el-card v-show="showSearch" class="box-card" shadow="always">
            <el-form :inline="true" :show-message="false" label-width="60px" class="user-search comForm" style="text-align: left">
                <el-form-item label="IP协议：">
                    <el-select v-model="queryParams.mode" size="small">
                        <el-option :value="-1" label="全部"></el-option>
                        <el-option :value="1" label="IPV4"></el-option>
                        <el-option :value="2" label="IPV6"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="IP类型：">
                    <el-select v-model="queryParams.ipType" size="small">
                        <el-option :value="-1" label="全部"></el-option>
                        <el-option :value="1" label="单IP"></el-option>
                        <el-option :value="2" label="网段"></el-option>
                        <el-option :value="3" label="IP段"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="IP地址：">
                    <el-input size="small" v-model="queryParams.ipaddr" clearable></el-input>
                </el-form-item>
                <el-form-item style="background-color: transparent;">
                    <el-button class="comBtn com_send_btn" size="small" icon="el-icon-search" @click="refreshApp">搜索</el-button>
                    <el-button class="comBtn com_reset_btn" size="small" icon="el-icon-refresh" @click="Object.assign(queryParams,$options.data().queryParams)">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <div v-show="showSearch" style="padding: 10px"></div>

        <el-card class="box-card" shadow="always" style="padding-bottom: 10px">
            <!-- 操作方法 -->
            <el-form label-width="100px">
                <el-row>
                    <el-col :span="14" style="text-align: left">
                        <el-button class="comBtn com_add_btn" size="mini" type="success" @click="addOrEdit=true">新增</el-button>
                        <el-button class="comBtn com_del_btn" size="mini" type="danger" @click="batchDel">删除</el-button>
                        <Strategy :policyType="4"></Strategy>
                    </el-col>
                    <el-col :span="10">
                        <div style="text-align: right">
                            <el-button-group>
                                <el-button size="mini" icon="el-icon-search" @click="showAndSearch"></el-button>
                                <el-button size="mini" icon="el-icon-refresh-left" @click="refreshApp"></el-button>
                            </el-button-group>
                        </div>
                    </el-col>
                </el-row>
            </el-form>

            <div id="whiteList" style="padding-top: 10px">
                <createTable
                        :tableData="tableData"
                        :tableHeader="tableDataHeader"
                        :isPage="isPage"
                        :pageAttributes="pageAttr"
                        :current-change="currentChange"
                        :sizeChange="sizeChange"
                        :selection-change="handleSelectionChange"
                        tableRef="tableCot"
                >
                </createTable>
            </div>
        </el-card>

        <!--证书信息编辑-->
        <el-dialog :title="addOrEditTitle" :visible.sync="addOrEdit" width="600px" append-to-body :close-on-click-modal="false" @close="Object.assign(form, $options.data().form)">
            <el-form label-width="150px" ref="editForm" :model="form" size="medium" style="padding-right: 20px" :rules="rules">
                <el-form-item label="名称：">
                    <el-input v-model="form.whiteName" placeholder="请输入名称" maxlength="100"></el-input>
                </el-form-item>
                <el-form-item label="模式选择：">
                    <el-radio v-model="form.mode" :label="1" @change="changeMode">IPV4</el-radio>
                    <el-radio v-model="form.mode" :label="2" @change="changeMode">IPV6</el-radio>
                </el-form-item>
                <el-form-item label="IP范围：">
                    <el-select v-model="form.ipType">
                        <el-option label="单IP" :value="1"></el-option>
                        <el-option label="网段" :value="2"></el-option>
                        <el-option label="IP段" :value="3"></el-option>
                    </el-select>
                </el-form-item>


                <div v-if="form.mode === 1">
                    <el-form-item v-if="form.ipType === 1" label="IP地址：" prop="ipaddr" key="ipaddr">
                        <el-input v-model="form.ipaddr" placeholder="请输入IP地址"></el-input>
                    </el-form-item>
                    <div v-if="form.ipType === 2" style="display: flex">
                        <el-form-item label="IP地址：" prop="ipaddr" key="ipaddr">
                            <el-input v-model="form.ipaddr" placeholder="请输入IP地址"></el-input>
                        </el-form-item>
                        <span style="padding: 8px 5px 0">/</span>
                        <el-form-item label="" label-width="0px" prop="mask" key="mask">
                            <el-input v-model="form.mask" style="width: 100px" placeholder="掩码段"/>
                        </el-form-item>
                    </div>
                    <div v-if="form.ipType === 3" style="display: flex">
                        <el-form-item label="IP地址：" prop="ipaddr" key="ipaddr">
                            <el-input v-model="form.ipaddr" placeholder="请输入IP地址"></el-input>
                        </el-form-item>
                        <span style="padding: 8px 5px 0">-</span>
                        <el-form-item label="" label-width="0px" prop="ipaddr1" key="ipaddr1">
                            <el-input v-model="form.ipaddr1" placeholder="请输入IP地址"/>
                        </el-form-item>
                    </div>
                </div>

                <div v-if="form.mode === 2">
                    <el-form-item v-if="form.ipType === 1" label="IP地址：" prop="ipaddr" key="ipaddr">
                        <el-input v-model="form.ipaddr" placeholder="请输入IP地址"></el-input>
                    </el-form-item>
                    <div v-if="form.ipType === 2">
                        <el-form-item label="IP地址：" prop="ipaddr" key="ipaddr">
                            <el-input v-model="form.ipaddr" placeholder="请输入IP地址"></el-input>
                        </el-form-item>
                        <el-form-item label="前缀长度：" prop="mask" key="mask">
                            <el-input v-model="form.mask" placeholder="请输入前缀长度"/>
                        </el-form-item>
                    </div>
                    <div v-if="form.ipType === 3">
                        <el-form-item label="开始IP：" prop="ipaddr" key="ipaddr">
                            <el-input v-model="form.ipaddr" placeholder="请输入IP地址"></el-input>
                        </el-form-item>
                        <el-form-item label="结束IP：" prop="ipaddr1" key="ipaddr1">
                            <el-input v-model="form.ipaddr1" placeholder="请输入IP地址"/>
                        </el-form-item>
                    </div>
                </div>
            </el-form>

            <div slot="footer" class="dialog-footer">
                <el-button size="small" class="comBtn com_reset_btn" @click="closeWhiteListDia">取 消</el-button>
                <el-button size="small" class="comBtn com_send_btn" type="primary" @click="addOrEditF">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    // import whiteListMG from "@/api/whiteListMG";
    // import {dateFormat} from "@/utils/util";
    import Strategy from "@/components/strategy"


    export default {
        name: "wwite-list",
        components: {Strategy},
        data() {
            const ipv6toNum = (ipv6) => ipv6.split(':').reduce((total, num) => total + Number('0x' + num.toString(10)), 0);
            const isEqualIPAddress = (addr1, addr2) => {
                let res1 = [],
                    res2 = [];
                addr1 = addr1.split(".");
                addr2 = addr2.split(".");
                for (let i = 0, iLen = addr1.length - 1; i < iLen; i += 1) {
                    res1.push(parseInt(addr1[i]));
                    res2.push(parseInt(addr2[i]));
                }
                console.log(res1.join(".") === res2.join(".") && +addr1[3] > +addr2[3]);
                return res1.join(".") === res2.join(".") && +addr1[3] > +addr2[3];
            };

            const checkIPV4IPV6 = (rule, value, callback) => {
                // let a = /(^(?:(?:(?:[0-9A-Fa-f]{1,4}:){7}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}:[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){5}:([0-9A-Fa-f]{1,4}:)?[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:){0,2}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,3}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,4}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){5}:((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:)?((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,2}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,3}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:):([0-9A-Fa-f]{1,4}:){0,4}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(::([0-9A-Fa-f]{1,4}:){0,5}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|([0-9A-Fa-f]{1,4}::([0-9A-Fa-f]{1,4}:){0,5}[0-9A-Fa-f]{1,4})|(::([0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){1,7}:))$)|(^\[(?:(?:(?:[0-9A-Fa-f]{1,4}:){7}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}:[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){5}:([0-9A-Fa-f]{1,4}:)?[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:){0,2}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,3}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,4}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){5}:((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:)?((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,2}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,3}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:):([0-9A-Fa-f]{1,4}:){0,4}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(::([0-9A-Fa-f]{1,4}:){0,5}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|([0-9A-Fa-f]{1,4}::([0-9A-Fa-f]{1,4}:){0,5}[0-9A-Fa-f]{1,4})|(::([0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){1,7}:))\](?::(?:[0-9]|[1-9][0-9]{1,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5]))?$)/;
                // let a = /(^(?:(?:(?:[0-9A-Fa-f]{1,4}:){7}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}:[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){5}:([0-9A-Fa-f]{1,4}:)?[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:){0,2}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,3}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,4}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){5}:((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:)?((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,2}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,3}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:):([0-9A-Fa-f]{1,4}:){0,4}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(::([0-9A-Fa-f]{1,4}:){0,5}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|([0-9A-Fa-f]{1,4}::([0-9A-Fa-f]{1,4}:){0,5}[0-9A-Fa-f]{1,4})|(::([0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){1,7}:))$)|(^\[(?:(?:(?:[0-9A-Fa-f]{1,4}:){7}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}:[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){5}:([0-9A-Fa-f]{1,4}:)?[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:){0,2}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,3}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,4}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){5}:((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:)?((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,2}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,3}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:):([0-9A-Fa-f]{1,4}:){0,4}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(::([0-9A-Fa-f]{1,4}:){0,5}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|([0-9A-Fa-f]{1,4}::([0-9A-Fa-f]{1,4}:){0,5}[0-9A-Fa-f]{1,4})|(::([0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){1,7}:))\](?::(?:[0-9]|[1-9][0-9]{1,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5]))?$)/;
                let a = /(^(?:(?:(?:[0-9A-Fa-f]{1,4}:){7}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}:[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){5}:([0-9A-Fa-f]{1,4}:)?[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:){0,2}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,3}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,4}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){5}:((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:)?((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,2}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,3}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:):([0-9A-Fa-f]{1,4}:){0,4}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(::([0-9A-Fa-f]{1,4}:){0,5}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|([0-9A-Fa-f]{1,4}::([0-9A-Fa-f]{1,4}:){0,5}[0-9A-Fa-f]{1,4})|(::([0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){1,7}:))$)/;
                let reg = this.form.mode === 1 ? /^(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}$/ : a;

                if (!reg.test(value)) return callback(new Error("格式错误, 请重新输入!"));
                if (this.form.mode === 2 && this.form.ipType === 3) {
                    if (ipv6toNum(this.form.ipaddr) > ipv6toNum(this.form.ipaddr1) && this.form.ipaddr === value) return callback(new Error("起始IP不能大于结束IP!"));
                    if (ipv6toNum(this.form.ipaddr) > ipv6toNum(this.form.ipaddr1) && this.form.ipaddr1 === value) return callback(new Error("结束IP不能小于起始IP!"));
                }
                if (this.form.mode === 1 && this.form.ipType === 3) {
                    if (isEqualIPAddress(this.form.ipaddr, this.form.ipaddr1) && this.form.ipaddr === value) return callback(new Error("起始IP不能大于结束IP!"));
                    if (isEqualIPAddress(this.form.ipaddr, this.form.ipaddr1) && this.form.ipaddr1 === value) return callback(new Error("结束IP不能小于起始IP!"));
                }
                callback();
            };
            const checkPrefixLength = (rule, value, callback) => {
                let a = /^[0-9]{1,2}$/;
                let b = /^\+?[1-9]\d*$/;
                let reg = this.form.mode === 1 ? a : b;
                if (value === '') return callback(new Error(this.form.mode === 1 ? "掩码段不能为空！" : '前缀长度不能为空！'));
                if (!reg.test(value)) {
                    callback(new Error(this.form.mode === 1 ? "请输入0~32之间的数!" : '请输入1~128之间的整数!'));
                }
                if ((this.form.mode === 1 && (value < -1 || value > 32)) || (this.form.mode === 2 && (value < 1 || value > 128))) {
                    callback(new Error(this.form.mode === 1 ? "请输入0~32之间的数!" : '请输入1~128之间的整数!'));
                }
                callback();
            };
            return {
                showSearch: true,
                addOrEdit: false,
                addOrEditTitle: "添加规则",
                tableData: [],
                tableDataHeader: [],
                isPage: false,
                pageAttr: {total: 0},
                queryParams: {
                    pageNo: 1,
                    pageSize: 10,
                    ipType: -1,
                    ipaddr: "",
                    mode: ""
                },
                form: {
                    whiteName: '',
                    mode: 1,
                    id: "",
                    ipaddr: "",
                    ipaddr1: "",
                    ipType: 1,
                    operType: "add",
                    ids: "",
                    mask: "",
                },
                rules: {
                    ipaddr: [
                        {required: true, message: 'IP地址不能为空！', trigger: 'blur'},
                        {validator: checkIPV4IPV6, trigger: 'blur'}
                    ],
                    mask: [
                        // {required: true, message: '掩码段不能为空！', trigger: 'blur'},
                        // {required: true, trigger: 'blur'},
                        {required: true, validator: checkPrefixLength, trigger: 'blur'}
                    ],
                    ipaddr1: [
                        {required: true, message: 'IP地址不能为空！', trigger: 'blur'},
                        {validator: checkIPV4IPV6, trigger: 'blur'}
                    ]
                },
            }
        },
        methods: {
            // strategy() {
            //     strategyMG.distribution();
            // },
            changeMode() {
                this.form.ipType = 1;
                this.form.ipaddr = '';
                this.form.ipaddr1 = '';
                this.form.mask = '';
            },
            showAndSearch() {
                this.showSearch = !this.showSearch;
            },
            refreshApp: function () {
                this.tableData = [];
                let _this = this;
                this.$http.whiteListMG.list(this.queryParams).then(res => {
                    _this.tableData = res.data;
                    _this.isPage = false
                });
            },
            batchDel() {
                let _this = this;
                if (this.form.ids === "") {
                    _this.$alert("请选择删除项！");
                    return;
                }
                _this.$confirm('确定删除吗?', '删除确认', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.whiteListMG.delBatch(this.form.ids).then(res => {
                        _this.refreshApp()
                    });
                });
            },
            addOrEditF: function () {
                let _this = this;
                this.$refs["editForm"].validate((val) => {
                    if (val) {
                        let type = this.form.operType;
                        let ipType = this.form.ipType;
                        if (ipType === 2) {
                            this.form.ipaddr = this.form.ipaddr + "/" + this.form.mask;
                        }
                        if (ipType === 3) {
                            this.form.ipaddr = this.form.ipaddr + "-" + this.form.ipaddr1;
                        }
                        let res;
                        if (type === "add") {
                            res = _this.$http.whiteListMG.add(this.form);
                        } else {
                            res = _this.$http.whiteListMG.edit(this.form);
                        }
                        res.then(res => {
                            _this.addOrEdit = false;
                            _this.refreshApp();
                        });
                    }
                });

            },
            closeWhiteListDia() {
                this.addOrEdit = false;
                this.$refs['editForm'].clearValidate();
            },
            handleSelectionChange(val) {
                let delA = "";
                let length = val.length;
                val.forEach((item, index) => {
                    delA += item.id + ",";
                });
                if (length > 0) {
                    delA = delA.substr(0, delA.length - 1);
                }
                this.form.ids = delA;
            },
            currentChange: function (val) {
                this.queryParams.pageNo = val;
                this.refreshApp();
            },
            sizeChange: function (val) {
                this.queryParams.pageSize = val;
                this.refreshApp();
            }
        },
        created() {
            let _this = this;
            this.refreshApp();
            this.tableDataHeader = [{
                type: "select",
                label: "全选",
                width: "100",
            }, {
                type: "text_formatter",
                label: "名称",
                prop: "whiteName",
                // width: "200",
                formatter: function (value) {
                    console.log(value);
                    if (value) {
                        return value
                    } else {
                        return "-";
                    }
                }
            }, {
                type: "text_formatter",
                label: "IP协议",
                prop: "mode",
                // width: "100",
                formatter: function (value) {
                    console.log(value);
                    if (value === 1) {
                        return "IPV4"
                    } else if (value === 2) {
                        return "IPV6";
                    } else {
                        return "-";
                    }
                }
            }, {
                type: "normal",
                label: "IP地址",
                prop: "ipaddr",
            },
                //     {
                //     type: "normal",
                //     label: "内容",
                //     prop: "ipaddr",
                // },
                // {
                //     type: "text_formatter",
                //     label: "IP类型",
                //     prop: "ipType",
                //     width: "100",
                //     formatter: function (value, row) {
                //         if (value == 1) {
                //             return "单IP"
                //         } else if (value == 2) {
                //             return "网段";
                //         } else {
                //             return "IP段";
                //         }
                //     }
                // },
                {
                    type: "operation",
                    label: "操作",
                    width: "150",
                    tag: [
                        {
                            name: "修改",
                            operType: "update",
                            tagType: "el-button",
                            attributes: {
                                size: "mini",
                                type: "text",
                                icon: "el-icon-update"
                            },
                            callback: function (row) {
                                _this.$http.whiteListMG.queryById(row.id).then(res => {
                                    _this.form.id = row.id;
                                    _this.form.operType = "edit";
                                    _this.addOrEdit = true;
                                    _this.addOrEditTitle = "编辑规则";
                                    _this.form.whiteName = row.whiteName;
                                    _this.form.mode = row.mode;
                                    _this.form.ipaddr = row.ipaddr;
                                    _this.form.ipType = row.ipType;
                                    if (row.ipType == 2) {
                                        let attr = row.ipaddr.split("/");
                                        _this.form.ipaddr = attr[0];
                                        _this.form.mask = attr[1];
                                    } else if (row.ipType == 3) {
                                        let attr = row.ipaddr.split("-");
                                        _this.form.ipaddr = attr[0];
                                        _this.form.ipaddr1 = attr[1];
                                    }
                                });
                            }
                        },
                        {
                            name: "删除",
                            operType: "update",
                            tagType: "el-button",
                            attributes: {
                                size: "mini",
                                type: "text",
                                icon: "el-icon-delete"
                            },
                            callback: function (row) {
                                _this.$confirm('确定删除吗?', '删除确认', {
                                    confirmButtonText: '确定',
                                    cancelButtonText: '取消',
                                    type: 'warning'
                                }).then(() => {
                                    _this.$http.whiteListMG.del(row.id).then(res => {
                                        _this.refreshApp()
                                    });
                                });
                            }
                        }
                    ]
                }
            ];
        },
        mounted() {

        }
    }
</script>

<style lang="less" scoped>
    .container {
        padding: -10px;
    }

    .user-search .el-form-item {
        margin-bottom: 0;
        /*margin-top: 10px;*/
    }

</style>

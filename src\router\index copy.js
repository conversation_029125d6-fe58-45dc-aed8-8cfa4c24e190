import Vue from 'vue'
import Router from 'vue-router'
import login from "@/views/login";
import userLogin from "@/views/userLogin";
import home from "@/views/home";
import index from "@/views/index";

import {businessRouter} from './businessRouter'

//初始化
import init from "@/views/init/init";
//时间配置
import timeConfigure from "@/views/system/time-configure";
//备份策略
import backup from "@/views/system/backup";
//设备证书
import certificate from "@/views/system/certificate";
//系统自检
import selfCheckout from "@/views/system/self-checkout";
//服务检测
import serviceCheckout from "@/views/system/service-checkout";
//服务测试
import selfService from "@/views/system/self-checkout-list";
//网卡配置
// import networkCatConfigure from "@/views/system/network-cat-configure";
import networkCatConfigure from "@/views/system/networkCardCon";
//DNS配置
import dnsConfigure from "@/views/system/dns-configure";
//网络诊断
import networkDiag from "@/views/system/network-diag";
//管理员管理
import administratorManage from "@/views/system/administrator-manage";
//管理员角色
import administratorRole from "@/views/system/administrator-role";
//管理员策略
import administratorStrategy from "@/views/system/administrator-strategy";
//双机热备
import hotBackup from "@/views/system/hot-backup";
//密钥分发
import syncAppKey from "@/views/system/sync-app-key";
//密钥管理
import keysManage from "@/views/business/secretKey/keys-manage";
//应用管理
import appManage from "@/views/business/app-manage";
//证书管理
import certificateManage from "@/views/business/certificate-manage";
//证书链管理
import certificateChainManage from "@/views/business/certificate-chain-manage";
//签名验签配置
import signConfigure from "@/views/business/sign-configure";
import signConfigureOld from "@/views/business/sign-configure_old";
//签名验签测试
import signTest from "@/views/business/sign-test";
//操作审计
import operateAudit from "@/views/audit/audit-operate";
//系统审计
import systemAudit from "@/views/audit/audit-system";
//签名验签审计
import signAudit from "@/views/audit/audit-sign";
//日志配置
import logConfigure from "@/views/audit/log-configure";
//告警配置
import alarmConfigure from "@/views/system/alarm/alarm-configure";

import appList from "@/views/buss/app/appList";

import whiteList from "@/views/business/white-list";

import test from "@/views/buss/app/test";

import config from "@/views/oam/config";
//运维Log界面
import oamLog from "@/views/oam/log/log-configure";
//密码卡管理
import cryptoCard from "@/views/oam/cryptoCard";
//设备代管理
import deviceAgent from "@/views/oam/deviceAgent";
//关于
import about from "@/views/about/about";
//设备代管管理
import deviceEscrow from "@/views/oam/deviceEscrow";
//设备代管管理
import nodeManager from "@/views/cluster/node-manager";
//集群管理
import clusterManager from "@/views/cluster/cluster-manage";
//设备代管-设备信息
import nodeInfoManage from "@/views/cluster/node/node-info-manage";
//设备代管-设备服务信息
import nodeGoInfo from "@/views/cluster/node/node-go-info";

import timeStamp from "@/views/business/time_certificate";

import menuManager from "@/views/oam/menu";

import roleManager from "@/views/oam/roleManager";

Vue.use(Router);

export default new Router({
    routes: [
        {
            path: '/',
            name: 'login',
            component: login,
            hidden: true,
            meta: {
                requireAuth: false
            }
        },
        {
            path: '/initukey',
            name: '时间戳备份查看',
            component: () => import('@/views/initUKey'),
            meta: {
                requireAuth: false
            }
        },
        {
            path: '/root/login',
            name: 'userLogin',
            component: userLogin,
            hidden: true,
            meta: {
                requireAuth: false
            }
        },
        {
            path: '/init',
            name: 'init',
            component: init,
            hidden: true,
            meta: {
                requireAuth: false
            }
        },
        {
            path: '/index',
            name: '首页',
            component: index,
            iconCls: 'el-icon-tickets',
            children: [
                ...businessRouter, // 业务管理
                {
                    path: '/home',
                    name: '首页',
                    component: home,
                    meta: {
                        requireAuth: true
                    }
                },
                {
                    path: '/time-configure',
                    name: '时间配置',
                    component: timeConfigure,
                    meta: {
                        requireAuth: true
                    }
                },
                {
                    path: '/backup',
                    name: '备份策略',
                    component: backup,
                    meta: {
                        requireAuth: true
                    }
                },
                {
                    path: '/certificate',
                    name: '设备证书',
                    component: certificate,
                    meta: {
                        requireAuth: true
                    }
                },
                {
                    path: '/self-checkout',
                    name: '系统自检',
                    component: selfCheckout,
                    meta: {
                        requireAuth: true
                    }
                },
                // {
                //     path: '/service-checkout',
                //     name: '服务检测',
                //     component: serviceCheckout,
                //     meta: {
                //         requireAuth: true
                //     }
                // },
                // {
                //     path: '/self-service',
                //     name: '测试历史',
                //     component: selfService,
                //     meta: {
                //         requireAuth: true
                //     }
                // },
                {
                    path: '/network-cat-configure',
                    name: '网卡配置',
                    component: networkCatConfigure,
                    meta: {
                        requireAuth: true
                    }
                },
                {
                    path: '/dns-configure',
                    name: 'DNS配置',
                    component: dnsConfigure,
                    meta: {
                        requireAuth: true
                    }
                },
                {
                    path: '/network-diag',
                    name: '网络诊断',
                    component: networkDiag,
                    meta: {
                        requireAuth: true
                    }
                },
                {
                    path: '/administrator-manage',
                    name: '管理员管理',
                    component: administratorManage,
                    meta: {
                        requireAuth: true
                    }
                },
                {
                    path: '/administrator-role',
                    name: '管理员角色',
                    component: administratorRole,
                    meta: {
                        requireAuth: true
                    }
                },
                {
                    path: '/administrator-strategy',
                    name: '管理员策略',
                    component: administratorStrategy,
                    meta: {
                        requireAuth: true
                    }
                },
                {
                    path: '/hot-backup',
                    name: '双机热备',
                    component: hotBackup,
                    meta: {
                        requireAuth: true
                    }
                },
                {
                    path: '/sync-app-key',
                    name: '密钥分发',
                    component: syncAppKey,
                    meta: {
                        requireAuth: true
                    }
                },
                // {
                //     path: '/keys-manage',
                //     name: '密钥管理',
                //     component: keysManage,
                //     meta: {
                //         requireAuth: true
                //     }
                // }, 
                // {
                //     path: '/SM9Keys-manage',
                //     name: 'SM9密钥管理',
                //     component: () => import('@/views/business/secretKey/SM9-key-manage'),
                //     meta: {
                //         requireAuth: true
                //     }
                // },
                // {
                //     path: '/app-manage',
                //     name: '应用管理',
                //     component: appManage,
                //     meta: {
                //         requireAuth: true
                //     }
                // }, 
                // {
                //     path: '/timestamp-manage',
                //     name: '时间戳检索',
                //     component: () => import('@/views/business/timestampManage'),
                //     meta: {
                //         requireAuth: true
                //     }
                // },
                // {
                //     path: '/timestamp-check',
                //     name: '时间戳备份查看',
                //     component: () => import('@/views/business/timestamp-check'),
                //     meta: {
                //         requireAuth: false
                //     }
                // },
                // {
                //     path: '/certificate-manage',
                //     name: '证书管理',
                //     component: certificateManage,
                //     meta: {
                //         requireAuth: true
                //     }
                // },
                // {
                //     path: '/certificate-chain-manage',
                //     name: '证书链管理',
                //     component: certificateChainManage,
                //     meta: {
                //         requireAuth: true
                //     }
                // },
                // {
                //     path: '/white-list',
                //     name: '证书链管理',
                //     component: whiteList,
                //     meta: {
                //         requireAuth: true
                //     }
                // },
                // {
                //     path: '/time_certificate',
                //     name: '时间戳配置',
                //     component: timeStamp,
                //     meta: {
                //         requireAuth: true
                //     }
                // },
                // {
                //     path: '/sign-configure',
                //     name: '业务端口配置',
                //     component: signConfigure,
                //     meta: {
                //         requireAuth: true
                //     }
                // }
                , {
                    path: '/sign-configure-old',
                    name: '签名验签配置',
                    component: signConfigureOld,
                    meta: {
                        requireAuth: true
                    }
                },
                {
                    path: '/sign-test',
                    name: '签名验签测试',
                    component: signTest,
                    meta: {
                        requireAuth: true
                    }
                },
                {
                    path: '/operate-audit',
                    name: '操作审计',
                    component: operateAudit,
                    meta: {
                        requireAuth: true
                    }
                },
                {
                    path: '/system-audit',
                    name: '系统审计',
                    component: systemAudit,
                    meta: {
                        requireAuth: true
                    }
                },
                {
                    path: '/sign-audit',
                    name: '签名验签审计',
                    component: signAudit,
                    meta: {
                        requireAuth: true
                    }
                },
                {
                    path: '/log-configure',
                    name: '日志配置',
                    component: logConfigure,
                    meta: {
                        requireAuth: true
                    }
                },
                {
                    path: '/alarm-configure',
                    name: '告警配置',
                    component: alarmConfigure,
                    meta: {
                        requireAuth: true
                    }
                }, {
                    path: '/serviceManage',
                    name: '服务管理',
                    component: () => import('@/views/system/serviceManage'),
                    meta: {
                        requireAuth: true
                    }
                },
                {
                    path: '/config',
                    name: '字典表配置',
                    component: config,
                    meta: {
                        requireAuth: true
                    }
                }, {
                    path: '/oam-log',
                    name: '日志管理',
                    component: oamLog,
                    meta: {
                        requireAuth: true
                    }
                }, {
                    path: '/cryptoCard',
                    name: '密码卡管理',
                    component: cryptoCard,
                    meta: {
                        requireAuth: true
                    }
                },{
                    path: '/cardManage',
                    name: '密码卡管理',
                    component: () => import('@/views/oam/cryptoCard_old'),
                    meta: {
                        requireAuth: true
                    }
                },
                {
                    path: '/systemUpgrade',
                    name: '系统升级',
                    component: () => import('@/views/oam/systemUpgrade'),
                    meta: {
                        requireAuth: true
                    }
                },
                {
                    path: '/custom',
                    name: '个性化定制',
                    component: () => import('@/views/custom'),
                    meta: {
                        requireAuth: true
                    }
                },
                {
                    path: '/deviceAgent',
                    name: '设备代管理',
                    component: deviceAgent,
                    meta: {
                        requireAuth: true
                    }
                },
                {
                    path: '/about',
                    name: '关于',
                    component: about,
                    meta: {
                        requireAuth: true
                    }
                },
                {
                    path: '/deviceEscrow',
                    name: '设备代管理',
                    component: deviceEscrow,
                    meta: {
                        requireAuth: true
                    }
                },
                {
                    path: '/nodeManager',
                    name: '业务节点配置',
                    component: nodeManager,
                    meta: {
                        requireAuth: true
                    }
                },
                {
                    path: '/clusterManager',
                    name: '集群管理',
                    component: clusterManager,
                    meta: {
                        requireAuth: true
                    }
                },
                {
                    path: '/nodeInfoManage',
                    name: '集群详情',
                    component: nodeInfoManage,
                    meta: {
                        requireAuth: true
                    }
                },
                {
                    path: '/nodeGoInfo',
                    name: '服务详情',
                    component: nodeGoInfo,
                    meta: {
                        requireAuth: true
                    }
                },
                {
                    path: '/menuManager',
                    name: '菜单管理',
                    component: menuManager,
                    meta: {
                        requireAuth: true
                    }
                },
                {
                    path: '/roleManager',
                    name: '权限管理',
                    component: roleManager,
                    meta: {
                        requireAuth: true
                    }
                },
                // 密码主管    pwdSupervisor   el-icon-lock
                // 密码用户    passwordUser   el-icon-user
                {
                    path: '/pwdSupervisor',
                    name: '密码主管',
                    component: () => import('@/views/authManage/pwdSupervisor'),
                    meta: {
                        title: '密码主管',
                        requireAuth: true
                    }
                },
                // {
                //     path: '/passwordUser',
                //     name: '密码用户',
                //     component: () => import('@/views/authManage/passwordUser'),
                //     meta: {
                //         title: '密码用户',
                //         requireAuth: true
                //     }
                // },
            ]
        },
        {
            path: '/buss',
            name: '业务管理',
            component: index,
            iconCls: 'el-icon-tickets',
            children: [
                {
                    path: '/app',
                    name: '应用管理',
                    component: appList,
                    meta: {
                        requireAuth: true
                    }
                }, {
                    path: '/test',
                    name: '测试',
                    component: test,
                    meta: {
                        requireAuth: true
                    }
                }
            ]
        }
    ]
})

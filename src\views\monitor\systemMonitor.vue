<template>
  <div>
    <div v-if="system == ''">
      <el-row :gutter="20">
        <!--   cpu利用率   -->
        <el-col :span="12">
          <div>
            <el-card>
              <div slot="header" class="clearfix">
                <span>CPU使用率</span>
                <div style="float: right">
                  <el-button type="text" @click="moreClick('more', 'cpu')">
                    更多
                  </el-button>
                </div>
              </div>
              <el-row>
                <el-col :span="24"
                  ><div class="grid-content bg-purple-dark">
                    <lineGraph id="cpu" :get-data="cpudate"></lineGraph></div
                ></el-col>
              </el-row>
            </el-card>
          </div>
        </el-col>
        <!--   内存使用量   -->
        <el-col :span="12">
          <div>
            <el-card>
              <div slot="header" class="clearfix">
                <span>内存使用量</span>
                <div style="float: right">
                  <el-button type="text" @click="moreClick('more', 'memory')">
                    更多
                  </el-button>
                </div>
              </div>
              <el-row>
                <el-col :span="24"
                  ><div class="grid-content bg-purple-dark">
                    <lineGraph
                      id="memory"
                      :get-data="memorydate"
                    ></lineGraph></div
                ></el-col>
              </el-row>
            </el-card>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="20" style="margin-top: 15px">
        <!--   磁盘使用情况   -->
        <el-col :span="12">
          <div>
            <el-card>
              <div slot="header" class="clearfix">
                <span>磁盘使用情况</span>
                <div style="float: right">
                  <el-button type="text" @click="moreClick('more', 'disk')">
                    更多
                  </el-button>
                </div>
              </div>
              <el-row>
                <el-col :span="24"
                  ><div class="grid-content bg-purple-dark">
                    <lineGraph id="disk" :get-data="diskdate"></lineGraph></div
                ></el-col>
              </el-row>
            </el-card>
          </div>
        </el-col>
        <!--   网络吞吐量   -->
        <el-col :span="12">
          <div>
            <el-card>
              <div slot="header" class="clearfix">
                <span>网络吞吐量</span>
                <div style="float: right">
                  <el-button type="text" @click="moreClick('more', 'network')">
                    更多
                  </el-button>
                </div>
              </div>

              <el-row>
                <el-col :span="24"
                  ><div class="grid-content bg-purple-dark">
                    <lineGraph
                      id="network"
                      :get-data="networkdate"
                    ></lineGraph></div
                ></el-col>
              </el-row>
            </el-card>
          </div>
        </el-col>
      </el-row>
    </div>
    <div v-if="system == 'more'">
      <more ref="more" @personHandle="personHandle"></more>
    </div>
  </div>
</template>

<script>
import lineGraph from "./echarts/line-graph";
import more from "./echarts/more";
export default {
  name: "systemMonitor",
  components: { lineGraph, more },
  data() {
    return {
      cpudate: {},
      memorydate: {},
      diskdate: {},
      networkdate: {},
      value1: "",
      system: "",
      timer: null, //定时器名称
    };
  },
  beforeDestroy() {
    clearInterval(this.timer); // 清除定时器
    this.timer = null;
  },
  created() {
    this.cpuzhe();
    this.memoryzhe();
    this.diskzhe();
    this.networkzhe();
    this.setTime();
  },
  methods: {
    setTime() {
      //每隔一分钟运行一次保存方法
      this.timer = setInterval(() => {
        this.cpuzhe();
        this.memoryzhe();
        this.diskzhe();
        this.networkzhe();
      }, 3000);
    },
    networkzhe() {
      let opt = {
        setCode: "N_008",
      };
      this.linezhe("network", opt);
    },
    diskzhe() {
      let opt = {
        setCode: "N_007",
      };
      this.linezhe("disk", opt);
    },
    memoryzhe() {
      let opt = {
        setCode: "N_006",
      };
      this.linezhe("memory", opt);
    },
    cpuzhe() {
      let opt = {
        setCode: "N_005",
      };
      this.linezhe("cpu", opt);
    },
    linezhe(name, value) {
      var model = {
        opinionData: [],
        opinionDatay: [],
        opinionDatax: [],
        unit: "",
      };
      console.log(this.$http);
      this.$http.monitorApi.query(JSON.stringify(value)).then((res) => {
        let detail = res.data.detail[0];
        model.opinionDatax = detail.xAxis;
        model.opinionDatay = detail.point;
        model.unit = res.data.unit;
      });
      if (name == "cpu") {
        this.cpudate = model;
      } else if (name == "memory") {
        this.memorydate = model;
      } else if (name == "disk") {
        this.diskdate = model;
      } else if (name == "network") {
        this.networkdate = model;
      }
    },
    personHandle() {
      this.system = "";
    },
    moreClick(val, name) {
      this.system = val;
      let title = "";
      if (name == "cpu") {
        title = "CPU历史使用率";
      } else if (name == "memory") {
        title = "内存历史使用量";
      } else if (name == "disk") {
        title = "磁盘历史使用情况";
      } else if (name == "network") {
        title = "网络历史吞吐量";
      }
      let opt = {
        nav: "system",
        title: title,
        name: name,
      };
      this.$nextTick(() => {
        this.$refs.more.Detail(opt);
      });
    },
  },
};
</script>

<style scoped>
.el-button {
  padding: unset !important;
}
</style>

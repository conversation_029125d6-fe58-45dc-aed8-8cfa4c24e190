import {req} from './axiosFun';
import API from "@/api/apiuri"

let firewalldApi = API.firewalldApi;
export default {
  getList() {
    return req("GET", firewalldApi.getList, {}, function () {
    }, false);
  },
  add(params) {
    return req("POST", firewalldApi.add, params, function () {
    }, false);
  },
  addOneTime(params) {
    return req("POST", firewalldApi.addOneTime, params, function () {
    }, false);
  },
  delete(params) {
    return req("POST", firewalldApi.del, params, function () {
    }, false);
  },
}

<template>
  <el-dialog title="修改管理口" :visible.sync="editMgrShow" width="550px" append-to-body :close-on-click-modal="false"
             @close="closeEditMgrDia">
    <el-form ref="editMgrForm" :model="editMgrForm" :rules="rules" label-width="150px" size="medium">
      <el-form-item label="管理口：" prop="eth">
        <el-select size="small" v-model="editMgrForm.eth" placeholder="请选择" clearable>
          <el-option v-for="eth in networkList" v-if="eth.bondMode == null" :label="eth.name" :value="eth.name" :key="eth.name"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button class="comBtn com_reset_btn" size="small" @click="closeEditMgrDia">取 消</el-button>
      <el-button class="comBtn com_send_btn" size="small" @click="editMgrSubmitForm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
// import systemMG from "@/api/systemMG";

export default {
  props: ['networkList'],
  data() {
    return {
      editMgrShow: false,
      editMgrForm: {
        eth: ''
      },
      rules: {
        eth: [
          {required: true, message: "请选择管理口", trigger: "blur"}
        ]
      }
    }
  },
  methods: {
    initEditMgrFun() {
      for (let i = 0; i < this.networkList.length; i++) {
        if (this.networkList[i].defaultGw === 1) {
          this.editMgrForm.eth = this.networkList[i].name;
        }
      }
      this.editMgrShow = true;
    },
    editMgrSubmitForm() {
      let _this = this;
      this.$refs['editMgrForm'].validate(valid => {
        if (valid) {
          _this.$confirm('请确认是否要修改管理口, 修改后立刻生效!', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            closeOnClickModal: false,
            closeOnPressEscape: false,
            type: 'warning'
          }).then(() => {
            this.$http.systemMG.setDefaultGw(_this.editMgrForm.eth).then(({code, msg}) => {
              if (!code) {
                _this.$message.success(msg);
                _this.$parent.$parent.getNetworkList();
                _this.closeEditMgrDia()
              } else {
                if (code !== 500) this.$message.error(msg)
              }
            });
          });
        }
      })
    },
    closeEditMgrDia() {
      this.editMgrShow = false;
      this.$nextTick(() => {
        this.$refs["editMgrForm"].resetFields();
      })
    }
  }
}
</script>

<style scoped>

</style>

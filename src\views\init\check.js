export const iphone1 = (rule, value, callback) => {
    const iphone1 = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
    if (value == null) {
        value = ''
    }
    if (value != '') {
        if (!iphone1.test(value)) {
            callback("请输入正确的手机号");
            return false
        } else {
            callback();
        }
    } else {
        callback();
    }
};

export const validatorIp = (rule, value, callback) => {
    if (value !== "") {
        let ip = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
        if (!ip.test(value)) {
            callback(new Error('请输入正确的IP地址!'))
        } else {
            callback()
        }
    } else {
        callback();
    }
};
export const validatorGW = (rule, value, callback) => {
    if (value !== "") {
        let ip = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
        if (!ip.test(value)) {
            callback(new Error('请输入正确的网关地址!'))
        } else {
            callback()
        }
    } else {
        callback();
    }
};
export const validatorMask = (rule, value, callback) => {
    if (value !== "") {
        let mask = /^(254|252|248|240|224|192|128|0)\.0\.0\.0|255\.(254|252|248|240|224|192|128|0)\.0\.0|255\.255\.(254|252|248|240|224|192|128|0)\.0|255\.255\.255\.(254|252|248|240|224|192|128|0)$/;
        if (!mask.test(value)) {
            callback(new Error("请输入正确的掩码！"));
        } else {
            callback();
        }
    } else {
        callback();
    }
};
export const validatorName = (rule, value, callback) => {
    if (value !== "") {
        let mask = RegExp(/[(\ )(\~)(\!)(\@)(\#)(\$)(\%)(\^)(\&)(\*)(\()(\))(\-)(\+)(\=)(\[)(\])(\{)(\})(\|)(\\)(\;)(\:)(\')(\")(\，)(\。)(\/)(\<)(\>)(\?)(\)]+/);
        if (mask.test(value)) {
            callback(new Error("格式错误, 不能含有特殊字符！"));
        } else {
            callback();
        }
    } else {
        callback();
    }
};
export const checkIPV6 = (rule, value, callback) => {
    if (value !== "") {
        let IPV6 = /(^(?:(?:(?:[0-9A-Fa-f]{1,4}:){7}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}:[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){5}:([0-9A-Fa-f]{1,4}:)?[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:){0,2}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,3}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,4}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){5}:((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:)?((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,2}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,3}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:):([0-9A-Fa-f]{1,4}:){0,4}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(::([0-9A-Fa-f]{1,4}:){0,5}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|([0-9A-Fa-f]{1,4}::([0-9A-Fa-f]{1,4}:){0,5}[0-9A-Fa-f]{1,4})|(::([0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){1,7}:))$)/;
        if (!IPV6.test(value)) {
            callback(new Error('请输入正确的IP!'))
        } else {
            callback()
        }
    } else {
        callback();
    }
};
export const checkIPV6Mask = (rule, value, callback) => {
    if (value !== "") {
        let len = /^\+?[1-9]\d*$/;
        if (!len.test(value)) {
            callback(new Error('请输入1~128之间的整数!'));
        } else {
            callback();
        }
    } else {
        callback();
    }
};
<template>
  <div>

    <el-card class="box-card" shadow="always" style="padding-bottom: 10px">
      <br>
      <el-form label-width="160px" ref="testForm" :model="testForm">
        <el-row>
          <h3 class="title_back">服务检测</h3>
        </el-row>
        <el-row>
            <el-form-item  label="随机数检测：">
              <el-button class="comBtnDef com_send_btn" size="mini" @click="getRandom">获取随机数</el-button>
            </el-form-item>
        </el-row>
        <el-row>
          <h3 class="title_back">SM2证书</h3>
        </el-row>
        <el-row>
          <el-form-item  label="SM2私钥证书：">
            <el-input size="mini" type="textarea" rows="5" v-model="testForm.privateCert" name="privateCert" style="width: 400px"></el-input>
          </el-form-item>
          <el-form-item  label="SM2公钥证书：">
            <el-input size="mini" type="textarea" rows="5" v-model="testForm.publicCert" style="width: 400px"></el-input>
          </el-form-item>
          <el-button class="comBtnDef com_send_btn" size="mini" style="float: left; margin-top: 12px" @click="generateSm2Cert">生成证书</el-button>
        </el-row>
        <el-row>
          <el-form-item  label="SM2加密：">
            <el-input size="mini" v-model="testForm.sm2Encryption"></el-input>
            <el-button size="mini" class="comBtnDef com_send_btn" @click="sm2EncryptionInfo">加密</el-button>
            结果： <el-input size="mini" v-model="resultForm.sm2EncryptionResult"></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item  label="SM2解密：">
            <el-input size="mini" v-model="testForm.sm2Decryption"></el-input>
            <el-button size="mini" class="comBtnDef com_send_btn" @click="sm2DecryptionInfo">解密</el-button>
            结果： <el-input size="mini" v-model="resultForm.sm2DecryptionResult"></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item  label="SM2签名：">
            <el-input size="mini" v-model="testForm.sm2SignatureText"></el-input>
            <el-button size="mini" class="comBtnDef com_send_btn" @click="sm2Signature">签名</el-button>
            结果： <el-input size="mini" v-model="resultForm.sm2SignatureTextResult"></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item  label="SM2验签：">
            <el-input size="mini" v-model="testForm.sm2VerifyText"></el-input>
            <el-button size="mini" class="comBtnDef com_send_btn" @click="sm2Verify">验签</el-button>
            结果： <el-input size="mini" v-model="resultForm.sm2VerifyTextResult"></el-input>
          </el-form-item>
        </el-row>
        <!--<el-row>
          <el-form-item  label="SM2签名(预处理)：" prop="sm2SignaturePre">
            <el-input size="mini" v-model="testForm.sm2SignaturePre"></el-input>
            <el-button size="mini" type="primary" @click="sm2SignaturePreprocessing">签名</el-button>
            结果： <el-input size="mini" v-model="resultForm.sm2SignaturePreResult"></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item  label="SM2验签(预处理)：" prop="sm2VerifyPre">
            <el-input size="mini" v-model="testForm.sm2VerifyPre"></el-input>
            <el-button size="mini" type="primary" @click="sm2VerifyPreprocessing">验签</el-button>
            结果： <el-input size="mini" v-model="resultForm.sm2VerifyPreResult"></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item  label="sm2配对一致性：" prop="sm2KeyPair">
            <el-input size="mini" v-model="testForm.sm2KeyPair"></el-input>
            <el-button size="mini" type="primary" @click="sm2KeyPairFeneration">检测</el-button>
            结果： <el-input size="mini" v-model="resultForm.sm2KeyPairResult"></el-input>
          </el-form-item>
        </el-row>-->
        <el-row>
          <h3 class="title_back">SM3证书</h3>
        </el-row>
        <el-row>
          <el-form-item  label="SM3摘要：">
            <el-input size="mini" v-model="testForm.sm3Text"></el-input>
            <el-button size="mini" class="comBtnDef com_send_btn" @click="sm3Hash1">加密</el-button>
            结果： <el-input size="mini" v-model="resultForm.sm3TextResult"></el-input>
          </el-form-item>
        </el-row>
        <!--<el-row>
          <el-form-item  label="sm3摘要(带ID)：" prop="sm3TextAndId">
            <el-input size="mini" v-model="testForm.sm3TextAndId"></el-input>
            <el-button size="mini" class="comBtnDef com_send_btn" @click="sm3Hash2">加密</el-button>
            结果： <el-input size="mini" v-model="resultForm.sm3TextAndIdResult"></el-input>
          </el-form-item>
        </el-row>-->
        <el-row>
          <h3 class="title_back">SM4证书</h3>
        </el-row>
        <el-row>
          <el-form-item  label="SM4 Key：">
            <el-input size="mini" type="textarea" rows="5" v-model="testForm.sm4Key" style="width: 400px"></el-input>
          </el-form-item>
          <el-button size="mini" class="comBtnDef com_send_btn" style="float: left; margin-top: 12px" @click="generateSm4Cert">生成Key</el-button>
        </el-row>
        <el-row>
          <el-form-item  label="SM4（ECB）加密：">
            <el-input size="mini" v-model="testForm.sm4ECBEnc"></el-input>
            <el-button size="mini" class="comBtnDef com_send_btn" @click="sm4ECBEncryption">加密</el-button>
            结果： <el-input size="mini" v-model="resultForm.sm4ECBEncResult"></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item  label="SM4（ECB）解密：">
            <el-input size="mini" v-model="testForm.sm4ECBDec"></el-input>
            <el-button size="mini" class="comBtnDef com_send_btn" @click="sm4ECBDecryption">解密</el-button>
            结果： <el-input size="mini" v-model="resultForm.sm4ECBDecResult"></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item  label="SM4（CBC）加密：">
            <el-input size="mini" v-model="testForm.sm4CBCEnc"></el-input>
            <el-button size="mini" class="comBtnDef com_send_btn" @click="sm4CBCEncryption">加密</el-button>
            结果： <el-input size="mini" v-model="resultForm.sm4CBCEncResult"></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item  label="SM4（CBC）解密：">
            <el-input size="mini" v-model="testForm.sm4CBCDec"></el-input>
            <el-button size="mini" class="comBtnDef com_send_btn" @click="sm4CBCDecryption">解密</el-button>
            结果： <el-input size="mini" v-model="resultForm.sm4CBCDecResult"></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item  label="SM4（OFB）加密：">
            <el-input size="mini" v-model="testForm.sm4OFBEnc"></el-input>
            <el-button size="mini" class="comBtnDef com_send_btn" @click="sm4OFBEncryption">加密</el-button>
            结果： <el-input size="mini" v-model="resultForm.sm4OFBEncResult"></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item  label="SM4（OFB）解密：">
            <el-input size="mini" v-model="testForm.sm4OFBDec"></el-input>
            <el-button size="mini" class="comBtnDef com_send_btn" @click="sm4OFBDecryption">解密</el-button>
            结果： <el-input size="mini" v-model="resultForm.sm4OFBDecResult"></el-input>
          </el-form-item>
        </el-row>
        <!--<el-row>
          <el-form-item  label="CBC-MAC(SM4)：" prop="sm4MACText">
            <el-input size="mini" v-model="testForm.sm4MACText"></el-input>
            <el-button size="mini" class="comBtnDef com_send_btn" @click="sm4MAC">检测</el-button>
            结果： <el-input size="mini" v-model="resultForm.sm4MACTextResult"></el-input>
          </el-form-item>
        </el-row>-->
      </el-form>

      <!-- 结果返回 -->
      <el-dialog title="随机数列表" :visible.sync="resultVisible" width="35%" :before-close="closeResult"
                 :close-on-click-modal="false">
        <!--{{result}}-->
        <span v-text="result" style="white-space: pre-wrap;"></span>
        <div slot="footer" class="dialog-footer">
          <el-button size="small" class="comBtn com_reset_btn" @click="closeResult">关闭</el-button>
        </div>
      </el-dialog>
    </el-card>

  </div>
</template>

<script>
  // import checkMG from "@/api/checkMG";
  export default {
    name: "service-checkout",
    data() {
      return {
        result: '',
        testForm: {
          sm2Encryption: '',
          sm2Decryption: '',
          sm2SignatureText: '',
          sm2VerifyText: '',
          sm2SignaturePre: '',
          sm2VerifyPre: '',
          sm2KeyPair: '',
          sm3Text: '',
          sm3TextAndId: '',
          sm4ECBEnc: '',
          sm4ECBDec: '',
          sm4CBCEnc: '',
          sm4CBCDec: '',
          sm4OFBEnc: '',
          sm4OFBDec: '',
          sm4MACText: '',
          privateCert: '',
          publicCert: '',
          sm4Key: '',
        },
        //返回值
        resultForm: {
          sm2EncryptionResult: '',
          sm2DecryptionResult: '',
          sm2SignatureTextResult: '',
          sm2VerifyTextResult: '',
          sm2SignaturePreResult: '',
          sm2VerifyPreResult: '',
          sm2KeyPairResult: '',
          sm3TextResult: '',
          sm3TextAndIdResult: '',
          sm4ECBEncResult: '',
          sm4ECBDecResult: '',
          sm4CBCEncResult: '',
          sm4CBCDecResult: '',
          sm4OFBEncResult: '',
          sm4OFBDecResult: '',
          sm4MACTextResult: '',
        },
        resultVisible: false,
        sm2Data: '',
      }
    },
    methods: {
      //获取随机数
      getRandom() {
        this.$http.checkMG.random().then((res) => {
          const code = res.code;
          if (code === 0) {
            this.result = res.data;
            this.openResult();
            this.$message.success("操作成功！");
          } else {
            this.$message.error(res.msg);
          }
        });
      },
      generateSm2Cert() {
        this.testForm.privateCert = null;
        this.testForm.publicCert = null;
        this.$http.checkMG.generateSm2Certs().then((res) => {
          const code = res.code;
          if (code === 0) {
            this.testForm.privateCert = res.data.privateCert;
            this.testForm.publicCert = res.data.publicCert;
            this.$message.success("操作成功！");
          } else {
            this.$message.error(res.msg);
          }
        });
      },
      //sm2加密
      sm2EncryptionInfo() {
        this.resultForm.sm2EncryptionResult = null;
        var opt = {
          publicCert: this.testForm.publicCert,
          srcData: this.testForm.sm2Encryption
        };
        this.$http.checkMG.sm2Encryption(opt).then((res) => {
          const code = res.code;
          if (code === 0) {
            this.resultForm.sm2EncryptionResult = res.data;
            this.$message.success("操作成功！");
          } else {
            this.$message.error(res.msg);
          }
        })
      },
      //sm2解密
      sm2DecryptionInfo() {
        this.resultForm.sm2DecryptionResult = null;
        var opt = {
          privateCert: this.testForm.privateCert,
          encrypt: this.testForm.sm2Decryption
        };
        this.$http.checkMG.sm2Decryption(opt).then((res) => {
          const code = res.code;
          if (code === 0) {
            this.resultForm.sm2DecryptionResult = res.data;
            this.$message.success("操作成功！");
          } else {
            this.$message.error(res.msg);
          }
        })
      },
      //sm2签名
      sm2Signature() {
        this.resultForm.sm2SignatureTextResult = null;
        var opt = {
          privateCert: this.testForm.privateCert,
          srcData: this.testForm.sm2SignatureText
        };
        this.$http.checkMG.sm2Signature(opt).then((res) => {
          const code = res.code;
          if (code === 0) {
            this.resultForm.sm2SignatureTextResult = res.data;
            this.$message.success("操作成功！");
          } else {
            this.$message.error(res.msg);
          }
        })
      },
      //sm2验签
      sm2Verify() {
        this.resultForm.sm2VerifyTextResult = null;
        var opt = {
          publicCert: this.testForm.publicCert,
          srcData: this.testForm.sm2SignatureText,
          sign: this.testForm.sm2VerifyText
        };

        this.$http.checkMG.sm2Verify(opt).then((res) => {
          const code = res.code;
          if (code === 0) {
            this.resultForm.sm2VerifyTextResult = res.data;
            this.$message.success("操作成功！");
          } else {
            this.resultForm.sm2VerifyTextResult = false;
            this.$message.error(res.msg);
          }
        })

      },
      //sm2配对一致性
      sm2KeyPairFeneration() {
        this.resultForm.sm2KeyPairResult = null;
        this.$http.checkMG.sm2KeyPair().then((res) => {
          const code = res.code;
          if (code === 0) {
            this.resultForm.sm2KeyPairResult = res.data;
            this.$message.success("操作成功！");
          } else {
            this.$message.error(res.msg);
          }
        })
      },
      //sm3摘要
      sm3Hash1() {
        this.resultForm.sm3TextResult = null;
        const opt = {
          srcData: this.testForm.sm3Text
        };
        this.$http.checkMG.sm3Hash1(opt).then((res) => {
          const code = res.code;
          if (code === 0) {
            this.resultForm.sm3TextResult = res.data;
            this.$message.success("操作成功！");
          } else {
            this.$message.error(res.msg);
          }
        })
      },
      //sm3摘要（带ID）
      sm3Hash2() {
        this.resultForm.sm3TextAndIdResult = null;
        const opt = {
          srcData: this.testForm.sm3TextAndId
        };
        this.$http.checkMG.sm3Hash2(opt).then((res) => {
          const code = res.code;
          if (code === 0) {
            this.resultForm.sm3TextAndIdResult = res.data;
            this.$message.success("操作成功！");
          } else {
            this.$message.error(res.msg);
          }
        })
      },
      //生成sm4证书
      generateSm4Cert() {
        this.testForm.sm4Key = null;
        this.$http.checkMG.generateSm4Certs().then(res => {
          const code = res.code;
          if (code === 0) {
            this.testForm.sm4Key = res.data;
            this.$message.success("操作成功！");
          } else {
            this.$message.error(res.msg);
          }
        })
      },
      //SM4（ECB）加密
      sm4ECBEncryption() {
        this.resultForm.sm4ECBEncResult = null;
        var opt = {
          key: this.testForm.sm4Key,
          srcData: this.testForm.sm4ECBEnc
        };
        this.$http.checkMG.sm4ECBEncryption(opt).then((res) => {
          const code = res.code;
          if (code === 0) {
            this.resultForm.sm4ECBEncResult = res.data;
            this.$message.success("操作成功！");
          } else {
            this.$message.error(res.msg);
          }
        })
      },
      //SM4（ECB）解密
      sm4ECBDecryption() {
        this.resultForm.sm4ECBDecResult = null;
        var opt = {
          key: this.testForm.sm4Key,
          encrypt: this.testForm.sm4ECBDec
        };
        this.$http.checkMG.sm4ECBDecryption(opt).then((res) => {
          const code = res.code;
          if (code === 0) {
            this.resultForm.sm4ECBDecResult = res.data;
            this.$message.success("操作成功！");
          } else {
            this.$message.error(res.msg);
          }
        })
      },
      //SM4（CBC）加密
      sm4CBCEncryption() {
        this.resultForm.sm4CBCEncResult = null;
        var opt = {
          key: this.testForm.sm4Key,
          srcData: this.testForm.sm4CBCEnc
        };
        this.$http.checkMG.sm4CBCEncryption(opt).then((res) => {
          const code = res.code;
          if (code === 0) {
            this.resultForm.sm4CBCEncResult = res.data;
            this.$message.success("操作成功！");
          } else {
            this.$message.error(res.msg);
          }
        })
      },
      //SM4（CBC）解密
      sm4CBCDecryption() {
        this.resultForm.sm4CBCDecResult = null;
        var opt = {
          key: this.testForm.sm4Key,
          encrypt: this.testForm.sm4CBCDec
        };
        this.$http.checkMG.sm4CBCDecryption(opt).then((res) => {
          const code = res.code;
          if (code === 0) {
            this.resultForm.sm4CBCDecResult = res.data;
            this.$message.success("操作成功！");
          } else {
            this.$message.error(res.msg);
          }
        })
      },
      //SM4（OFB）加密
      sm4OFBEncryption() {
        this.resultForm.sm4OFBEncResult = null;
        var opt = {
          key: this.testForm.sm4Key,
          srcData: this.testForm.sm4OFBEnc
        };
        this.$http.checkMG.sm4OFBEncryption(opt).then((res) => {
          const code = res.code;
          if (code === 0) {
            this.resultForm.sm4OFBEncResult = res.data;
            this.$message.success("操作成功！");
          } else {
            this.$message.error(res.msg);
          }
        })
      },
      //SM4（OFB）解密
      sm4OFBDecryption() {
        this.resultForm.sm4OFBDecResult = null;
        var opt = {
          key: this.testForm.sm4Key,
          encrypt: this.testForm.sm4OFBDec
        };
        this.$http.checkMG.sm4OFBDecryption(opt).then((res) => {
          const code = res.code;
          if (code === 0) {
            this.resultForm.sm4OFBDecResult = res.data;
            this.$message.success("操作成功！");
          } else {
            this.$message.error(res.msg);
          }
        })
      },
      //CBC-MAC(SM4)
      sm4MAC() {
        this.resultForm.sm4MACTextResult = null;
        var opt = {
          key: this.testForm.sm4Key,
          srcData: this.testForm.sm4MACText
        };
        this.$http.checkMG.sm4MAC(opt).then((res) => {
          const code = res.code;
          if (code === 0) {
            this.resultForm.sm4MACTextResult = res.data;
            this.$message.success("操作成功！");
          } else {
            this.$message.error(res.msg);
          }
        })
      },
      closeResult() {
        this.result = '';
        this.resultVisible = false;
      },
      openResult() {
        // this.result = result;
        this.resultVisible = true;
      }
    }
  }
</script>

<style scoped lang="less">
  /deep/ .el-dialog__body {
    text-align: center;
    height: 600px;
    overflow: auto;
  }
  .el-form-item {
    float: left;
  }
  .el-input {
    width: 400px;
  }
  .el-button {
    margin-left: 50px;
    margin-right: 40px;
  }

  .title_back{
    float: left;
  }
  .title {
    float: left;
  }
</style>

/**
 * 系统管理 路由
 * */
const systemRouter = [
    {
        path: '/cryptoCard',
        name: '设备基本信息',
        component: () => import('@/views/oam/cryptoCard'),
        meta: {
            title: '设备基本信息',
            requireAuth: true
        }
    }, {
        path: '/serviceManage',
        name: '服务管理',
        component: () => import('@/views/system/serviceManage'),
        meta: {
            title: '服务管理',
            requireAuth: true
        }
    }, {
        path: '/time-configure',
        name: '时间配置',
        component: () => import('@/views/system/time-configure'),
        meta: {
            title: '时间配置',
            requireAuth: true
        }
    }, {
        path: '/backup',
        name: '策略备份',
        component: () => import('@/views/system/backup'),
        meta: {
            title: '策略备份',
            requireAuth: true
        }
    }, {
        path: '/about',
        name: '许可管理',
        component: () => import('@/views/about/about'),
        meta: {
            title: '许可管理',
            requireAuth: true
        }
    }, {
        path: '/certificate',
        name: '站点证书配置',
        component: () => import('@/views/system/certificate'),
        meta: {
            title: '站点证书配置',
            requireAuth: true
        }
    },

    // 管理员管理
    {
        path: '/administrator-manage',
        name: '管理员管理',
        component: () => import('@/views/system/administrator-manage'),
        meta: {
            title: '管理员管理',
            requireAuth: true
        }
    }, {
        path: '/administrator-role',
        name: '角色管理',
        component: () => import('@/views/system/administrator-role'),
        meta: {
            title: '角色管理',
            requireAuth: true
        }
    }, {
        path: '/administrator-strategy',
        name: '策略管理',
        component: () => import('@/views/system/administrator-strategy'),
        meta: {
            title: '策略管理',
            requireAuth: true
        }
    },

    // 权限管理
    {
        path: '/pwdSupervisor',
        name: '密码主管',
        component: () => import('@/views/authManage/pwdSupervisor'),
        meta: {
            title: '密码主管',
            requireAuth: true
        }
    }, {
        path: '/passwordUser',
        name: '密码用户',
        component: () => import('@/views/authManage/passwordUser'),
        meta: {
            title: '密码用户',
            requireAuth: true
        }
    },

    // 网络配置
    {
        path: '/network-cat-configure',
        name: '网卡配置',
        component: () => import('@/views/system/networkCardCon'), // new
        // component: () => import('@/views/system/network-cat-configure'), // old
        meta: {
            title: '网卡配置',
            requireAuth: true
        }
    },{
        path: '/network-vxlan',
        name: 'VXLAN配置',
        component: () => import('@/views/system/network-vxlan'),
        meta: {
            title: '网卡配置',
            requireAuth: true
        }
    },{
        path: '/network-diag',
        name: '网络诊断',
        component: () => import('@/views/system/network-diag'),
        meta: {
            title: '网络诊断',
            requireAuth: true
        }
    },

    // 高可用
    {
        path: '/sync-app-key',
        name: '密钥分发',
        component: () => import('@/views/system/sync-app-key'),
        meta: {
            title: '密钥分发',
            requireAuth: true
        }
    }, {
        path: '/hot-backup',
        name: '双机热备',
        component: () => import('@/views/system/hot-backup'),
        meta: {
            title: '双机热备',
            requireAuth: true
        }
    }, {
        path: '/clusterManager',
        name: '集群管理',
        component: () => import('@/views/cluster/cluster-manage'),
        meta: {
            title: '集群管理',
            requireAuth: true
        }
    }, {
        path: '/nodeInfoManage',
        name: '集群详情',
        component: () => import('@/views/cluster/node/node-info-manage'),
        meta: {
            title: '集群详情',
            requireAuth: true
        }
    }, {
        path: '/nodeManager',
        name: '业务节点配置',
        component: () => import('@/views/cluster/node-manager'),
        meta: {
            title: '业务节点配置',
            requireAuth: true
        }
    }, {
        path: '/nodeGoInfo',
        name: '服务详情',
        component: () => import('@/views/cluster/node/node-go-info'),
        meta: {
            requireAuth: true
        }
    },
    
    
    // 配置管理
    {
        path: '/deviceEscrow',
        name: '设备代管理',
        component: () => import('@/views/oam/deviceEscrow'),
        meta: {
            title: '设备代管理',
            requireAuth: true
        }
    }, {
        path: '/alarm-configure',
        name: '告警通知管理',
        component: () => import('@/views/system/alarm/alarm-configure'),
        meta: {
            title: '告警通知管理',
            requireAuth: true
        }
    }, {
        path: '/virtual-IP-config', // Virtual IP config
        name: '虚拟IP配置',
        component: () => import('@/views/system/virtualIPConfig'),
        meta: {
            title: '虚拟IP配置',
            requireAuth: true
        }
    },

];

export default { systemRouter }

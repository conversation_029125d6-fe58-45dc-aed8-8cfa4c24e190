<template>
  <div class="container viewLoading">
    <div>
      <el-card class="box-card" shadow="always" style="padding-bottom: 10px">
        <el-button
          class="comBtn com_send_btn"
          size="small"
          @click="addHandle"
        >新增
        </el-button
        >
        <el-table
          ref="portTable"
          class="comTab"
          :data="portList"
          stripe
          size="small"
          style="margin-top: 10px"
        >
          <el-table-column align="center" prop="index" label="序号" width="80">
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            prop="protocol"
            label="协议"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            align="center"
            prop="port"
            label="端口"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            align="center"
            prop="source"
            label="源地址"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column align="center" prop="action" label="动作">
            <template slot-scope="scope">
              <span v-if="scope.row.action == 'accept'">通过</span>
              <span v-if="scope.row.action == 'reject'">拒绝</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="mgr" label="作用域"
                           width="220">
            <template slot-scope="scope">
              <el-tag type="success">公共域</el-tag>
              <el-tag type="danger" v-if="scope.row.mgr" style="margin-left: 5px;">管理口</el-tag>
              <el-tag v-if="scope.row.work" style="margin-left: 5px;">业务口</el-tag>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="oneTime" label="临时">
            <template slot-scope="scope">
              <el-tag type="warning" v-if="scope.row.oneTime">是</el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            prop="handel"
            label="操作"
          >
            <template slot-scope="{ row }">
              <el-button
                :loading="row.loading"
                type="text"
                @click="deleteHandle(row)"
              >删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <!-- 创建管理员 -->
      <el-dialog title="添加规则" :visible.sync="addShow" width="500px" @click="closeDialog" :close-on-click-modal="false">
        <el-form :model="addForm" :rules="addRules" ref="addForm" label-width="100px" class="demo-ruleForm">
          <el-form-item label="协议:" prop="protocol">
            <el-select v-model="addForm.protocol" placeholder="请选择协议">
              <el-option label="TCP" value="tcp"></el-option>
              <el-option label="UDP" value="udp"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="端口:" prop="port">
            <el-input v-model="addForm.port"></el-input>
          </el-form-item>
          <el-form-item label="源地址:" prop="source">
            <el-input v-model="addForm.source"></el-input>
          </el-form-item>
          <el-form-item label="动作:" prop="action">
            <el-select v-model="addForm.action" placeholder="请选择动作">
              <el-option label="通过" value="accept"></el-option>
              <el-option label="拒绝" value="reject"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="作用域:" prop="zones">
            <el-checkbox-group v-model="addForm.zones">
              <el-checkbox label="public" disabled>公共域</el-checkbox>
              <el-checkbox label="mgr">管理口</el-checkbox>
              <el-checkbox label="work">业务口</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="时效:" prop="isOneTime">
            <el-radio-group v-model="addForm.isOneTime">
              <el-radio label="1">临时</el-radio>
              <el-radio label="0">永久</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button class="comBtn com_reset_btn" size="small" @click="closeDialog">取消</el-button>
          <el-button class="comBtn com_send_btn" size="small" type="primary" :loading="addLoading"
                     @click="addSubmit()">保存
          </el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import firewalldMG from "@/api/firewalldMG";

export default {
  name: "firewalld",
  data() {
    return {
      msg: null,
      addLoading: false,
      portList: [],
      addShow: false,
      addForm: {
        zones: ["public"],
        isOneTime: "1",
      },
      addRules: {
        protocol: [
          {required: true, message: "请选择协议", trigger: "change"},
        ],
        port: [
          {required: true, message: "请输入端口", trigger: "blur"},
        ],
        source: [
          {
            validator: function (rule, value, callback) {
              if (value === '' || value === undefined) {
                callback();
              }
              let reg =
                /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
              if (reg.test(value)) {
              } else {
                callback(new Error("请输入正确的源地址"));
              }
              // if (!this.checkIPv4(value)) {
              //   callback(new Error("请输入正确的源地址"));
              // }
              callback();
            }, trigger: 'blur'
          }
        ],
        action: [
          {required: true, message: "请选择动作", trigger: "change"},
        ],
        zones: [
          {required: true, message: "请选择作用域", trigger: "change"},
          {
            validator: function (rule, value, callback) {
              if (value.length < 1) {
                callback(new Error("请选择作用域"));
              }
              callback();
            }, trigger: 'blur'
          }
        ],
        isOneTime: [
          {required: true, message: "请选择时效", trigger: "change"},
        ],
      }
    };
  },
  methods: {
    addHandle() {
      if (this.addShow) {
        this.addShow = false;
      } else {
        this.addShow = true;
      }
      this.$refs["addForm"].clearValidate();
    },
    addSubmit() {
      this.$refs.addForm.validate(valid => {
        if (valid) {
          this.msg = this.$message({
            message: "正在添加中，请耐心等待...",
            type: "warning",
            duration: 0,
          });
          this.addForm.mgr = this.addForm.zones.includes("mgr");
          this.addForm.work = this.addForm.zones.includes("work");

          if (this.addForm.isOneTime === "1") {
            firewalldMG.addOneTime(JSON.stringify(this.addForm)).then(({code, data, msg}) => {
              this.msg.close();
              if (code === 0) {
                this.$message.success("添加成功");
                this.getPortList();
                this.closeDialog();
              } else {
                this.$message.error(msg);
              }
            }).catch(() => {
              this.msg.close();
            });
          } else {
            firewalldMG.add(JSON.stringify(this.addForm)).then(({code, data, msg}) => {
              this.msg.close();
              if (code === 0) {
                this.$message.success("添加成功");
                this.getPortList();
                this.closeDialog();
              } else {
                this.$message.error(msg);
              }
            }).catch(() => {
              this.msg.close();
            });
          }
        }
      });
    },
    getPortList() {
      firewalldMG.getList().then(({code, data, row, msg}) => {
        data.forEach((item, index) => {
          item.loading = false;
        });
        this.portList = data;
        this.total = row;
      }).catch((err) => {
        console.log(err);
      });
    },
    deleteHandle(row) {
      this.$confirm('确定要删除吗?', '信息', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        firewalldMG.delete(row).then(({code, data, row, msg}) => {
          if (code === 0) {
            this.$message.success("删除成功");
          }
          this.getPortList();
        }).catch((err) => {
          console.log(err);
        });
      })
    },
    closeDialog() {
      this.$refs["addForm"].clearValidate();
      this.addShow = false;
      this.addForm = {
        zones: ["public"],
        isOneTime: "1",
      };
    }
  },
  mounted() {
    this.getPortList();
  },
  created() {
  },
};
</script>

<template>
    <div>
        <el-form :model="creatDevForm" ref="creatDevForm" label-width="150px" :rules="rules">
            <h4>设备本地信息</h4>
            <el-form-item label="设备ID：" prop="devId">
                <el-input size="small" v-model="creatDevForm.devId" placeholder="请输入设备ID"></el-input>
            </el-form-item>
            <el-form-item label="" prop="province" class="upload_form_item">
                <el-upload
                        class="upload-demo"
                        :multiple='false'
                        :show-file-list="false"
                        accept='.key'
                        :headers="headerObj"
                        :before-upload="fileBeforeUpload"
                        :on-success="deviceKey"
                        action="/svs/csm/sys/import/private"
                        :auto-upload="true">
                    <el-button size="small" type="primary">导入设备加密密钥</el-button>
                </el-upload>

                <el-upload
                        class="upload-demo"
                        :multiple='false'
                        :show-file-list="false"
                        accept='.pem'
                        :headers="headerObj"
                        :before-upload="checkFile"
                        :on-success="deviceKey"
                        action="/svs/csm/sys/import/devSign"
                        :auto-upload="true">
                    <el-button size="small" type="primary">导入设备签名证书</el-button>
                </el-upload>

                <el-upload
                        class="upload-demo"
                        :multiple='false'
                        :show-file-list="false"
                        accept='.pem'
                        :headers="headerObj"
                        :before-upload="checkFile"
                        :on-success="deviceKey"
                        action="/svs/csm/sys/import/devEnc"
                        :auto-upload="true">
                    <el-button size="small" type="primary">导入设备加密证书</el-button>
                </el-upload>
            </el-form-item>

            <h4>上级平台信息</h4>
            <el-form-item label="上级管理平台ID：" prop="centerId">
                <el-input size="small" v-model="creatDevForm.centerId" placeholder="请输入上级管理平台ID"></el-input>
            </el-form-item>
            <el-form-item label="上级管理平台IP：" prop="centerIp">
                <el-input size="small" v-model="creatDevForm.centerIp" placeholder="请输入上级管理平台IP"></el-input>
            </el-form-item>
            <el-form-item label="上级管理平台端口：" prop="centerPort">
                <el-input size="small" v-model.number="creatDevForm.centerPort" placeholder="请输入上级管理平台端口"></el-input>
            </el-form-item>
            <el-form-item label="" prop="department" class="upload_form_item">
                <el-upload
                        class="upload-demo"
                        :multiple='false'
                        :show-file-list="false"
                        accept='.pem'
                        :headers="headerObj"
                        :before-upload="checkFile"
                        :on-success="deviceKey"
                        action="/svs/csm/sys/import/sysSign"
                        :auto-upload="true">
                    <el-button size="small" type="primary">导入上级签名证书</el-button>
                </el-upload>

                <el-upload
                        class="upload-demo"
                        :multiple='false'
                        :show-file-list="false"
                        accept='.pem'
                        :headers="headerObj"
                        :before-upload="checkFile"
                        :on-success="deviceKey"
                        action="/svs/csm/sys/import/sysEnc"
                        :auto-upload="true">
                    <el-button size="small" type="primary">导入上级加密证书</el-button>
                </el-upload>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer" style="text-align: center">
            <el-button class="comBtn com_reset_btn" size="small" @click="clearDialog">取 消</el-button>
            <el-button class="comBtn com_send_btn" size="small" type="primary" @click="requestCert('creatDevForm')">确定</el-button>
        </div>
    </div>
</template>

<script>
    import {getStore} from "@/utils/util";
    // import oamMG from "@/api/oamMG";
    import { checkDevId, isCheckIP, elValidatePort } from '@/utils/myValidate'

    export default {
        data() {
            return {
                paramsTit: '手动注册',
                token: '',
                headerObj: {
                    'token':'Bearer ' + getStore('token')
                },
                creatDevForm: {
                    devId: '', // 设备ID
                    centerId: '', // 上级管理平台ID
                    centerIp: '', // 上级管理平台IP
                    centerPort: null, // 上级管理平台端口
                },
                rules: {
                    devId: [
                        {required: true, message: '请输入设备ID', trigger: 'blur'},
                        {validator: checkDevId, trigger: 'blur'}
                    ],
                    centerId: [
                        {required: true, message: '请输入上级管理平台ID', trigger: 'blur'},
                        {validator: checkDevId, trigger: 'blur'}
                    ],
                    centerIp: [
                        {required: true, message: '请输入上级管理平台IP', trigger: 'blur'},
                        {validator: isCheckIP, trigger: 'blur'}
                    ],
                    centerPort: [
                        {required: true, message: '请输入上级管理平台端口', trigger: 'blur'},
                        {validator: elValidatePort, trigger: 'blur'}
                    ],
                }
            }
        },
        methods: {
            initDialog() {
                this.getPlatformMsgFun()
                // this.show = true;
            },
            // 生成设备证书申请
            // createDevCert() {},
            createDevCert(file) {
                let index = file.name.lastIndexOf(".");
                let ext = file.name.substr(index + 1);
                if (ext === 'cer') {
                    this.creatDevForm.caFile = file.raw
                } else {
                    this.$message.warning('文件格式错误, 请上传正确格式!')
                }
            },
            fileBeforeUpload(file){
                let index = file.name.lastIndexOf(".");
                let ext = file.name.substr(index + 1);
                if (ext !== 'key') {
                    this.$message.warning('文件格式错误, 请上传正确格式!');
                    return false
                }
            },
            checkFile(file){
                let index = file.name.lastIndexOf(".");
                let ext = file.name.substr(index + 1);
                if (ext !== 'pem') {
                    this.$message.warning('文件格式错误, 请上传正确格式!');
                    return false
                }
            },
            deviceKey(response, file, fileList) {
                const {code, msg} = response;
                if (!code) {
                    this.$message.success(msg || '上传成功!')
                } else {
                    this.$message.warning(msg || '上传失败!')
                }
            },
            // 申请证书
            requestCert(name) {
                this.$refs[name].validate(valid => {
                    if (valid) {
                        // 提交
                        this.$http.oamMG.submitDeviceInfo(JSON.stringify(this.creatDevForm)).then(({code, msg}) => {
                            if (!code) {
                                this.$message.success(msg || '操作成功!');
                                this.clearDialog()
                                this.getPlatformMsgFun();
                            } else {
                                this.$message.warning(msg)
                            }
                        })
                    }
                })
            },
            clearDialog() {
                // this.show = false;
                this.$refs["creatDevForm"].resetFields();
                Object.assign(this.$data.creatDevForm, this.$options.data().creatDevForm);
            },
            // 获取平台信息
            getPlatformMsgFun() {
                this.$http.oamMG.getDeviceInfo().then(({code, data, msg}) => {
                    // this.deviceInfo = res.data;
                    this.creatDevForm.devId = data.DEVID;
                    this.creatDevForm.centerId = data.CENTERID;
                    this.creatDevForm.centerIp = data.CENTERIP;
                    this.creatDevForm.centerPort = data.CENTERPORT
                })
            },
        },
        mounted() {
        }
    }
</script>

<style lang="less" scoped>
    .upload_form_item {
        /deep/ .el-form-item__content {
            display: flex;
            flex-wrap: wrap;
        }
    }

    .upload-demo {
        width: 195px;
        margin-right: 10px;

        /deep/ .el-upload {
            width: 100%;

            .el-button {
                width: 100%;
                border: 0 none;
                background-color: #0c4161;
            }
        }
    }
</style>

<template>
    <el-dialog title="修改" :visible.sync="editOpen" width="600px" append-to-body :close-on-click-modal="false">
        <el-form label-width="150px" ref="editCert" :model="editCert" :rules="rules">
            <el-form-item label="证书类型：">
                <span>{{ editCert.keyDesc === 'SM2' ? 'SM2' : 'RSA' }}</span>
            </el-form-item>
            <el-form-item label="证书用途：" prop="Usage">
                <el-checkbox-group :disabled="editCert.doubleCert === 1" v-model="editCert.Usage">
                    <el-checkbox :label="0">签名</el-checkbox>
                    <el-checkbox :label="3">加密</el-checkbox>
                </el-checkbox-group>
            </el-form-item>
            <el-form-item label="扩展密钥用途：" prop="">
                <el-checkbox-group v-model="editCert.Usage">
                    <el-checkbox :label="108">时间戳</el-checkbox>
                </el-checkbox-group>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button size="small" class="comBtn com_reset_btn" @click="editOpen=false">取 消</el-button>
            <el-button size="small" class="comBtn com_send_btn" @click="editSubmit">确 定</el-button>
        </div>
    </el-dialog>
</template>

<script>
    import bussMG from "@/api/bussMG";

    export default {
        data() {
            return {
                editOpen: false, // 修改
                editCert: {
                    id: "",
                    doubleCert: "",
                    keyDesc: "",
                    Usage: []
                },
                rules: {
                    Usage: [
                        {required: true, message: "请选择证书用途", trigger: "change"},
                        {
                            validator: function (rule, value, callback) {
                                if (value.indexOf(0) === -1 && value.indexOf(3) === -1) callback(new Error("请选择证书用途！"));
                                callback();
                            }, trigger: 'blur'
                        }
                    ]
                }
            }
        },
        methods: {
            initEditCertFun(row) {
                console.log(row);
                this.editCert.id = row.id;
                this.editCert.doubleCert = row.doubleCert;
                this.editCert.keyDesc = row.keyDesc;
                this.editCert.Usage = row.usage ? JSON.parse(row.usage) : [];
                this.editOpen = true;
            },
            // 修改
            editSubmit: function () {
                this.$refs["editCert"].validate((valid) => {
                    if (valid) {
                        let p = new FormData();
                        p.append('id', this.editCert.id);
                        p.append('keyDesc', this.editCert.keyDesc);
                        p.append('Usage', this.editCert.Usage);
                        bussMG.editCert(p).then(({code, msg}) => {
                            console.log(code, msg);
                            this.$parent.refreshCert();
                            this.editOpen = false;
                        });
                    }
                })
            }
        },
        created() {

        }
    }
</script>

<style scoped>

</style>

<template>
  <div class="container">
    <el-tabs type="border-card" v-model="activeName" @tab-click="handleClick">

      <el-tab-pane label="管理端" name="first">
        <manage-log ref="manageLog"/>
      </el-tab-pane>

      <el-tab-pane label="服务端" name="second">
        <service-log ref="serviceLog"/>
      </el-tab-pane>
    </el-tabs>

  </div>

</template>

<script>
import ManageLog from "@/views/oam/log/manage-log";
import ServiceLog from "@/views/oam/log/service-log";
export default {
  name: "log-configure",
  components: {ServiceLog, ManageLog},
  data() {
    return {
      activeName: "first",
    }
  },
  methods: {
    handleClick(tab, event) {
      if (tab.name === "first") {
        this.$refs.manageLog.searchRootLevel();
        this.$refs.manageLog.searchLoggerList();
      } else if (tab.name === "second") {
        this.$refs.serviceLog.getLogLevelFun();
      }
    }
  }
}
</script>

<style scoped>

.el-input {
  width: 50%;
}

</style>

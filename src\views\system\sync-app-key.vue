<template>
  <div>
    <el-card class="box-card" shadow="always" style="margin-bottom: 10px">
      <!-- 搜索筛选 -->
      <el-form :model="syncForm" :rules="rules" ref="syncForm" class="demo-ruleForm" label-width="150px">
        <div>
          <el-row>
            <h3 class="title_back">密钥分发</h3>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="10">
              <el-form-item label="主机地址:" prop="ip" :inline="true">
                <el-input placeholder="0.0.0.0:6090" v-model="syncForm.ip" class="input-with-select">
                  <el-select id="protocol" v-model="syncForm.protocol" slot="prepend" placeholder="请选择">
                    <el-option label="http://" value="http://"></el-option>
                    <el-option label="https://" value="https://"></el-option>
                  </el-select>
                </el-input>
              </el-form-item>
              <el-form-item>
                <el-button class="comBtn com_send_btn" size="small" type="primary" @click="syncAppKey">同步密钥</el-button>
                <el-button class="comBtn com_send_btn" size="small" type="primary" @click="testConnection">测试服务连通性
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </el-card>
    <el-card class="box-card" shadow="always" style="margin-bottom: 10px;min-height: 50vh;max-height: 60vh;overflow-y: auto;">
      <!-- 搜索筛选 -->
      <el-tabs type="border-card">
        <el-tab-pane label="应用密钥导入">
          <el-col :span="10">
            <el-descriptions class="margin-top" :column="1" title="当前密钥信息" border>
              <el-descriptions-item label-style="width: 30%">
                <template slot="label">
                  <i class="el-icon-s-order"></i>
                  类型
                </template>
                SM4
              </el-descriptions-item>
              <el-descriptions-item label-style="width: 30%">
                <template slot="label">
                  <i class="el-icon-location-outline"></i>
                  来源
                </template>
                {{ appKeyInfo.sourceInfo }}
                {{ appKeyInfo.source == 1 ? "(外部导入)" : "(系统默认)" }}
              </el-descriptions-item>
              <el-descriptions-item label-style="width: 30%">
                <template slot="label">
                  <i class="el-icon-date"></i>
                  启用时间
                </template>
                {{ appKeyInfo.createTime | formatDate }}
              </el-descriptions-item>
              <el-descriptions-item label-style="width: 30%">
                <template slot="label">
                  <i class="el-icon-download"></i>
                  接收应用密钥
                </template>
                <el-switch @change="changeReceiveAppKey"
                           v-model="isReceiveAppKey">
                </el-switch>
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
        </el-tab-pane>
        <el-tab-pane label="应用密钥导出">
          <el-button class="comBtn com_send_btn" size="mini" style="margin-bottom: 10px" @click="listKeyExportInfo">刷新</el-button>
          <create-table
            :tableData="tableData"
            :tableHeader="tableDataHeader"
            :isPage="true"
            :pageAttributes="{total: pageAttr.total, currentPage: pageParam.pageNo}"
            :size-change="sizeChange"
            :current-change="currentChange"
            :prev-click="prevClick"
            :next-click="nextClick"
          ></create-table>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>
<style>
#protocol {
  width: 90px;
}

.input-with-select .el-input-group__prepend {
  background-color: #fff;
}
</style>
<script>
// import systemMG from "@/api/systemMG";
import {dateFormat, getStore} from "@/utils/util";

export default {
  name: "hot-backup",
  data() {
    var validatorIp = (rule, value, callback) => {
      if (value !== "") {
        var ipPort = value.split(':');
        var ipValue = ipPort[0];
        var ip = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
        if (!ip.test(ipValue)) {
          callback(new Error('请输入正确的IP!'))
        } else if (ipPort.length == 2) {
          if (ipPort[1] <= 65535 && ipPort[1] > 0) {
            callback()
          } else {
            callback(new Error('请输入正确的端口!'))
          }
        } else {
          callback()
        }
      } else {
        callback();
      }
    };
    let _this = this;
    return {
      // rules 表单验证
      rules: {
        ip: [
          {required: true, message: '请输入目标服务地址', trigger: 'blur'},
          {validator: validatorIp, trigger: 'blur', required: true}
        ]
      },
      syncForm: {
        ip: '',
        protocol: "http://"
      },
      isReceiveAppKey: false,
      appKeyInfo: {
        sourceInfo: "",
        source: 0,
        createTime: ""
      },

      tableDataHeader: [
        {label: '序号', type: "index", width: '100'},
        {label: '证书序号', prop: 'certNo', type: "normal", width: '200'},
        {label: '使用者', prop: 'certDn', type: "normal"},
        {label: '日期', prop: 'createTime', type: "time", width: '200'}
      ],
      tableData: [],
      total: 0,
      isPage: true,
      pageAttr: {},
      // 分页参数
      pageParam: {
        pageNo: 1,
        pageSize: 10
      },
    }
  },
  filters: {
    formatDate(value) {
      return dateFormat("YYYY-mm-dd HH:MM:SS", new Date(value.time));
    }
  },
  methods: {
    // 每页显示条数改变
    sizeChange(res) {
      this.pageParam.pageSize = res
      this.listKeyExportInfo()
    },
    // 前往页
    currentChange(res) {
      this.pageParam.pageNo = res
      this.listKeyExportInfo()
    },
    // 上一页
    prevClick(res) {
      this.pageParam.pageNo = res
      this.listKeyExportInfo()
    },
    // 下一页
    nextClick(res) {
      this.pageParam.pageNo = res
      this.listKeyExportInfo()
    },
    testConnection() {
      this.$refs['syncForm'].validate((valid) => {
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '测试中，请稍等...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          this.$http.systemMG.testConnection({url: this.syncForm.protocol + this.syncForm.ip}).then(res => {
            if (res.code == 0) {
              this.$message.success("连接成功");
            }
            loading.close();
          }, err => {
            loading.close();
          })
        }
      });
    },
    getAppKeyInfo() {
      this.$http.systemMG.appKeyInfo().then((res) => {
        if (res.code == 0) {
          this.appKeyInfo = res.data;
          this.isReceiveAppKey = res.data.isReceive === "1";
        } else {
          this.$message.error(res.msg);
        }
      })
    },
    changeReceiveAppKey() {
      if (this.isReceiveAppKey) {
        this.$http.systemMG.turnOnReceiveAppKey().then(res => {
          this.getAppKeyInfo();
        }, err => {
          this.getAppKeyInfo();
        });
      } else {
        this.$http.systemMG.turnOffReceiveAppKey().then(res => {
          this.getAppKeyInfo();
        }, err => {
          this.getAppKeyInfo();
        });
      }
    },
    syncAppKey() {
      var _this = this;
      this.$refs['syncForm'].validate((valid) => {
        if (valid) {
          this.$confirm('确定要同步应用密钥到 '+_this.syncForm.ip+'?', '确定', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            const loading = _this.$loading({
              lock: true,
              text: '同步密钥中，请稍等...',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            });
            this.$http.systemMG.syncAppKey({url: _this.syncForm.protocol + _this.syncForm.ip}).then(res => {
              if (res.code == 0) {
                _this.$message.success(res.msg);
              } else {
                _this.$message.error(res.msg);
              }
              this.listKeyExportInfo();
              loading.close();
            }, err => {
              loading.close();
            })
          });
        }
      });
    },
    listKeyExportInfo() {
      let _this = this;
      _this.tableData = [];
      let r = this.$http.systemMG.listKeyExportInfo(this.pageParam);
      r.then(res => {
        _this.tableData = res.data;
        _this.pageAttr.total = res.row;
      });
    }
  },
  created() {
    this.getAppKeyInfo();
    this.listKeyExportInfo();
  }
}
</script>

<!-- 密码用户 zyft -->
<template>
    <el-card>
        <el-button class="comBtn com_send_btn" size="mini" :disabled="tableData.length !== 0" type="success"
            @click="addOperator">创建</el-button>
        <!-- 列表 -->
        <create-table style="margin-top: 10px" :tableData="tableData" :tableHeader="tableDataHeader"
            :isPage="false"></create-table>

        <!-- 创建管理员 -->
        <el-dialog title="创建密码用户" :visible.sync="addMangeShow" width="500px" @click="closeDialog"
            :close-on-click-modal="false">
            <!--<span>请插入操作员UKey</span>-->
            <span class="tip_txt" style="color: red;padding-bottom: 10px;display: inline-block">注：请插入将要新增的UKey!</span>
            <el-form :model="mangeForm" :rules="initRules" ref="mangeForm" label-width="100px" class="demo-ruleForm">
                <el-form-item label="口令：" prop="password">
                    <el-input v-model="mangeForm.password" clearable size="small" type="password"
                        placeholder="**********"></el-input>
                </el-form-item>
                <el-form-item label="确认口令：" prop="confirmPassword">
                    <el-input v-model="mangeForm.confirmPassword" clearable size="small" type="password"
                        placeholder="**********"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button class="comBtn com_reset_btn" size="small" @click="closeDialog">取消</el-button>
                <el-button class="comBtn com_send_btn" size="small" type="primary" :loading="addLoading"
                    @click="submitAddOperator('ruleForm')">保存</el-button>
            </div>
        </el-dialog>
    </el-card>
</template>

<script>
// import authManage from "../../api/authManage";

export default {
    name: 'passwordUser',
    data() {
        const validatePass = (rule, value, callback) => {
            // const highPass = /^(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])(?=.*[!"#$%&'()*+,\-@/\.])[0-9a-zA-Z!"#$%&'()*+,\-@/\\.]{8,16}$/;
            const highPass = /[^\u00-\uFF]{8,15}/;
            const spasePass = /\s+/g;
            if (value === '') {
                callback(new Error('请输入密码'));
            } else if (value !== '') {
                if (spasePass.test(value)) {
                    callback(new Error('口令不能包含空格'))
                }
                if (highPass.test(value)) {
                    // callback(new Error('密码格式为8-16位，包含大小写、数字和特殊字符!'));
                    callback(new Error('密码格式为8-15位，不包含中文!'));
                } else {
                    if (this.mangeForm.confirmPassword == null || this.mangeForm.confirmPassword === '') {
                        callback();
                    } else {
                        if (value !== this.mangeForm.confirmPassword) {
                            callback(new Error('两次输入密码不一致!'));
                        } else {
                            callback();
                        }
                    }
                }
            } else {
                callback();
            }
        };
        const validatePass2 = (rule, value, callback) => {
            if (value === '') {
                callback(new Error('请再次输入密码'));
            } else {
                if (value !== this.mangeForm.password) {
                    callback(new Error('两次输入密码不一致!'));
                } else {
                    callback();
                }
            }
        };
        let _this = this;
        return {
            addArr: [],
            activeNum: 0,
            addMangeShow: false,
            mangeForm: {
                keyIndex: 5,
                password: '',
                confirmPassword: '',
                // oldPassword: '',
            },
            loading: false,
            addLoading: false,
            indexArr: [], // 操作员索引
            tableData: [],
            tableDataHeader: [
                { label: '序号', type: "index", width: '60' },
                {
                    label: '名称', prop: 'name', type: "text_formatter",
                    formatter: function (value, row) {
                        return value
                    }
                },
                {
                    label: '角色', prop: 'role', type: "text_formatter",
                    formatter: function (value, row) {
                        return value
                        // if (row.role === 1) return "密码用户";
                    }
                },
                {
                    label: '描述', prop: 'desc', type: "text_formatter",
                    formatter: function () {
                        // return "通过登录密码用户，登录到工作态";
                        return "通过登录密码用户，登录到用户状态";
                    }
                },
            ],
            initRules: {
                password: [
                    { required: true, message: '请输入口令', trigger: 'blur' },
                    { min: 8, max: 15, message: '请输入8-15位字符', trigger: 'blur' },
                    { validator: validatePass, trigger: 'blur' }
                ],
                confirmPassword: [
                    { required: true, message: '请再次输入口令', trigger: 'blur' },
                    { validator: validatePass2, trigger: 'blur' }
                ],
            }
        }
    },
    methods: {
        // 获取操作员索引 OperatorIndex
        getOperatorIndex() {
            this.$http.authManage.OperatorIndex().then(({ code, data, msg }) => {
                if (!code) {
                    this.indexArr = data;
                } else {
                    this.$message({
                        message: msg,
                        type: 'error'
                    });
                }
            })
        },
        // 获取操作员列表
        getOperatorList() {
            this.tableData = [];
            this.$http.authManage.getDataList().then((res) => {
                let code = res.code;
                if (!code) {
                    this.tableData = res.data;
                } else {
                    this.$message({
                        message: res.msg,
                        type: 'error'
                    });
                }
            })
        },
        // 创建
        addOperator() {
            this.addMangeShow = true;
            // if (this.indexArr.length === 0) {
            //     this.$message.info('创建密码用户索引不足!')
            // } else {
            //     this.addMangeShow = true;
            // }
        },
        // 创建 密码用户
        submitAddOperator() {
            this.$refs["mangeForm"].validate((valid) => {
                if (!valid) return;
                this.addLoading = true;
                // authManage.addOperator(params).then(({code, data, msg}) => {
                this.$http.authManage.addOperator(this.mangeForm).then(({ code, data, msg }) => {
                    if (!code) {
                        this.$message.success(msg || '创建成功!');
                        this.getOperatorList();
                        // this.getOperatorIndex();
                        this.closeDialog();
                    } else if (code == 300007) {
                        this.$message({
                            message: msg || '创建密码用户失败!',
                            type: "warning",
                            customClass: 'messageIndex'
                        })
                    }
                    this.addLoading = false;
                }).catch(error => {
                    console.log(error);
                    this.addLoading = false;
                })
            })
        },
        closeDialog() {
            this.addMangeShow = false;
            this.mangeForm.password = '';
            this.mangeForm.confirmPassword = '';
            this.$refs["mangeForm"].clearValidate();
        },
        // 删除 delOperator
        delOperator(row) {
            this.$confirm('确定要删除吗?', '信息', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$http.authManage.delOperator(row.index).then(({ code, data, msg }) => {
                    if (!code) {
                        this.$message.success('删除成功，UKEY需清空后方可使用!');
                        this.getOperatorList();
                        // this.getOperatorIndex();
                    } else {
                        this.$message({
                            message: msg || '删除失败!',
                            type: "warning",
                            customClass: 'messageIndex'
                        })
                    }
                })
            })
        }
    },
    mounted() {
        // this.getOperatorIndex();
        this.getOperatorList()
    }
}
</script>

<style lang="less">
.messageIndex {
    z-index: 3000 !important;
}
</style>

<!-- 对称密钥 -->
<template>
    <div class="container">
        <el-card v-show="showSearch" class="box-card" shadow="always">
            <el-form :inline="true" :show-message="false" label-width="80px" class="user-search comForm" style="text-align: left">
                <el-form-item label="算法类型：">
                    <el-select size="small" v-model="queryParams.keyType">
                        <el-option label="全部" :value="-1"></el-option>
                        <el-option label="SM2" :value="2"></el-option>
                        <el-option label="RSA" :value="1" v-show="showRas"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="是否使用：">
                    <el-select size="small" v-model="queryParams.keyUse">
                        <el-option label="全部" :value="-1"></el-option>
                        <el-option label="已使用" :value="1"></el-option>
                        <el-option label="未使用" :value="0"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item style="background-color: transparent;">
                    <el-button class="comBtn com_send_btn" size="small" type="primary" icon="el-icon-search" @click="refreshApp">搜索</el-button>
                    <el-button class="comBtn com_reset_btn" size="small" type="primary" icon="el-icon-refresh"
                               @click="Object.assign(queryParams,$options.data().queryParams);refreshApp()">重置
                    </el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <div v-show="showSearch" style="padding: 5px"></div>
        <el-card class="box-card" shadow="always" style="padding-bottom: 10px">
            <!-- 操作方法 -->
            <el-form label-width="100px">
                <el-row>
                    <el-col :span="14" style="text-align: left">
                        <el-button class="comBtn com_send_btn" size="mini" type="success" @click="eopenWindow">生成密钥</el-button>
                        <el-button class="comBtn com_send_btn" size="mini" type="success" @click="ebatchOpenWindow">批量生成</el-button>
                        <el-button class="comBtn com_del_btn" size="mini" type="danger" @click="clearKey">清空所有未使用密钥</el-button>
                    </el-col>
                    <el-col :span="10">
                        <div style="text-align: right">
                            <el-button-group>
                                <el-button size="mini" icon="el-icon-search" @click="showAndSearch"></el-button>
                                <el-button size="mini" icon="el-icon-refresh-left" @click="refreshApp"></el-button>
                            </el-button-group>
                        </div>
                    </el-col>
                </el-row>
            </el-form>

            <div id="keyTable" style="padding-top: 10px">
                <createTable
                        :tableData="tableData"
                        :tableHeader="tableDataHeader"
                        :isPage="isPage"
                        :pageAttributes="{total: total, currentPage: queryParams.pageNum, pageSize: queryParams.pageSize}"
                        :current-change="currentChange"
                        :sizeChange="sizeChange"
                        tableRef="tableCot"
                >
                </createTable>
            </div>
        </el-card>

        <!-- 密钥生成 -->
        <el-dialog title="生成" :visible.sync="openWindow" width="350px" append-to-body :close-on-click-modal="false"
                   @close="Object.assign(form, $options.data().form)">
            <el-form label-width="100px;">
                <el-form-item label="加密算法:">
                    <el-select v-model="form.certType">
                        <el-option :value="1" label="RSA" v-show="showRas"></el-option>
                        <el-option :value="2" label="SM2"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="密钥长度:" v-if="form.certType == 1">
                    <el-select v-model="form.keyLength">
                        <el-option label="1024" :value="1024"></el-option>
                        <el-option label="2048" :value="2048"></el-option>
                        <el-option label="4096" :value="4096"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button class="comBtn com_reset_btn" @click="openWindow = false">取 消</el-button>
                <el-button class="comBtn com_send_btn" @click="generateSignKey" :loading="signKeyLoading">确 定</el-button>
            </div>
        </el-dialog>

        <!-- 密钥批量生成 -->
        <el-dialog title="批量生成" :visible.sync="batchOpenWindow" width="400px" append-to-body :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   :show-close="false">
            <el-form :model="form" :rules="rules" ref="form" label-width="85px">
                <el-form-item label="生成数量:" prop="num">
                    <el-input v-model="form.num" :placeholder="placeholder" clearable size="small"></el-input>
                </el-form-item>
                <el-form-item label="加密算法:">
                    <el-select v-model="form.certType" size="small" style="width: 100%">
                        <el-option :value="1" label="RSA" v-show="showRas"></el-option>
                        <el-option :value="2" label="SM2"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="密钥长度:" v-if="form.certType == 1">
                    <el-select v-model="form.keyLength" size="small" style="width: 100%">
                        <el-option label="1024" :value="1024"></el-option>
                        <el-option label="2048" :value="2048"></el-option>
                        <el-option label="4096" :value="4096"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="完成进度:" v-show="percentageShow" >
                    <el-progress style="margin-top: 12px" :text-inside="true" :stroke-width="15"
                                 :percentage="percentage"></el-progress>
                </el-form-item>
                <el-form-item label="结果:" v-show="showResult">
                    <span>成功：{{ successCount }}，失败：{{ failCount }}</span>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button size="small" class="comBtn com_reset_btn" :disabled="mulkeyLoading" @click="closeBatchDia">关 闭</el-button>
                <el-button size="small" class="comBtn com_send_btn" v-show="!showResult" @click="generateMulKey" :loading="mulkeyLoading">确 定</el-button>
            </div>
        </el-dialog>

        <!-- 清楚密钥进度条 -->
        <el-dialog title="清空密钥任务进度详情" :visible.sync="dshowWindows" width="350px" append-to-body
                   :close-on-click-modal="false"
                   @close="websock.close();dpercentage=0;">
            <el-form style="margin-top: -20px">
                <el-form-item label="清空密钥中，请稍后...">
                    <el-progress style="margin-top: 12px" :text-inside="true" :stroke-width="15" :percentage="dpercentage"></el-progress>
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>

<script>
    import extKeyMG from "@/api/extKeyMG";
    import {getParams} from "@/api/about";
    import {dateFormat, getStore, getWebSocketPre} from "@/utils/util";

    export default {
        name: "ext-keys-manage",
        data() {
            var _this = this;
            const checkNum = (rule, value, callback) => {
                console.log(value);
                console.log(isNaN(parseFloat(value)));
                console.log(!isFinite(value));
                if (isNaN(parseFloat(value)) && !isFinite(value)) {
                    callback(new Error('格式错误, 请输入正整数!'));
                }
                if (value === '') {
                    callback(new Error('请输入生成数量'));
                } else {
                    if (!_this.keyTotal) {
                        _this.keyTotal = 0;
                    }
                    if (_this.keyMaxCount == -1) {
                        callback();
                    }
                    let sum = _this.keyMaxCount - _this.keyTotal;
                    if (sum <= 0) {
                        callback(new Error('已超过密钥最大限制'));
                    }
                    if (value <= 0 || value > sum) {
                        callback(new Error('请输1~' + sum + "个"));
                    } else {
                        callback();
                    }
                }
            };

            return {
                showRas: true,
                keyMaxCount: 0,
                showSearch: true,
                placeholder: "",
                openWindow: false,
                batchOpenWindow: false,
                Height: 250,
                signKeyLoading: false,
                mulkeyLoading: false,
                initOpen: true,
                tableData: [],
                tableDataHeader: [],
                isPage: true,
                pageAttr: {},
                keyTotal: 0,
                queryParams: {
                    keyType: -1,
                    keyUse: -1,
                    pageNo: 1,
                    pageSize: 10
                },
                total: 0,
                form: {
                    certType: 2,
                    keyLength: 2048,
                    num: ""
                },
                wsServer: "",
                percentageShow: false,
                percentage: 0,
                dshowWindows: false,
                successCount: 0,
                failCount: 0,
                showResult: false,
                dpercentage: 0,
                rules: {
                    num: [
                        {required: true, message: '生成数量不能为空！', trigger: 'blur'},
                        {validator: checkNum, trigger: 'blur'}
                    ],
                }
            }
        },
        methods: {
            initWebSocket() { //初始化websocket
                let _this = this;
                this.$nextTick(() => {
                    if (!this.websock) {
                        this.websock = new WebSocket(_this.wsServer);
                        this.websock.onmessage = this.websocketonmessage;
                        this.websock.onopen = this.websocketonopen;
                        this.websock.onerror = this.websocketonerror;
                        this.websock.onclose = this.websocketclose;
                    } else {
                        if (this.websock.readyState != this.websock.CONNECTING) {
                            this.websock = new WebSocket(_this.wsServer);
                            this.websock.onmessage = this.websocketonmessage;
                            this.websock.onopen = this.websocketonopen;
                            this.websock.onerror = this.websocketonerror;
                            this.websock.onclose = this.websocketclose;
                        }
                    }
                });
            },
            websocketonopen(e, c, d) { //连接建立之后执行send方法发送数据
                console.log("open session")
            },
            websocketonerror() {//连接建立失败重连
                this.initWebSocket();
            },
            websocketonmessage(e, l, d) { //数据接收
                let message = JSON.parse(e.data);
                let type = message.type;
                let value = message.data;
                if (type == "GK") {
                    let number = parseFloat(value) / parseFloat(this.form.num);
                    number = number.toFixed(2)
                    this.percentage = parseInt(number * 100);
                } else if (type == "DK") {
                    let dkTotal = message.total;
                    let number = parseFloat(value) / parseFloat(dkTotal);
                    number = number.toFixed(2)
                    this.dpercentage = parseInt(number * 100);
                }
            },
            websocketsend(Data) {//数据发送
                this.websock.send(Data);
            },
            websocketclose(e) {  //关闭
                console.log('断开连接', e);
            },
            clearKey() {
                this.initWebSocket();
                let _this = this;
                this.$confirm('确定要清空所有未使用密钥?', '确定', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    _this.dshowWindows = true;
                    this.$http.extKeyMG.clearKey(function () {
                        _this.dpercentage = 100;
                        setTimeout(() => {
                            _this.dshowWindows = false;
                        }, 2000)

                    }).then(res => {
                        _this.refreshApp();
                    })
                });
            },
            generateSignKey() {
                // 生成密钥方法
                let _this = this;
                this.signKeyLoading = true;
                this.$http.extKeyMG.generateSignKey(this.form.certType, this.form.keyLength).then(res => {
                    _this.refreshApp();
                    _this.openWindow = false;
                    _this.signKeyLoading = false;
                }, error => {
                    _this.signKeyLoading = false;
                });
            },
            eopenWindow() {
                // 打开生成密钥窗口
                let _this = this;
                if (!_this.keyTotal) {
                    _this.keyTotal = 0;
                }
                let sum = _this.keyMaxCount - _this.keyTotal;
                if (_this.keyMaxCount != -1 && sum < 1) {
                    _this.openWindow = false;
                    _this.$message.info("密钥个数已达到上限！");
                    return;
                }
                _this.openWindow = true;
            },
            getLicenseParams() {
                let _this = this;
                this.$http.about.getParams().then(({code, data, msg}) => {
                    let _data = eval('(' + data.data + ')');
                    _this.keyMaxCount = _data.asymmetricNum;
                })
            },
            // 批量生成密钥方法
            generateMulKey() {
                let _this = this;
                this.$refs["form"].validate((valid) => {
                    if (valid) {
                        _this.mulkeyLoading = true;
                        _this.percentageShow = true;
                        // this.closeDisable = true;
                        this.$http.extKeyMG.generateMulKey(this.form.certType, this.form.keyLength, this.form.num).then(res => {//jingkl
                            _this.showResult = true;
                            _this.successCount = res.data.successCount;
                            _this.failCount = res.data.failCount;
                            _this.refreshApp();
                            _this.mulkeyLoading = false;
                            this.closeDisable = false;
                        }).catch(() => {
                            // this.closeDisable = false;
                            _this.showResult = true;
                            _this.mulkeyLoading = false;
                        });
                    }
                });
            },
            // 打开批量生成密钥窗口
            ebatchOpenWindow() {
                let _this = this;
                if (!_this.keyTotal) {
                    _this.keyTotal = 0;
                }
                // debugger;
                let sum = _this.keyMaxCount - _this.keyTotal;
                if (sum > 0) {
                    this.placeholder = "不能超过" + sum + "个";
                } else if(_this.keyMaxCount == -1) {
                    this.placeholder = "请输入生成密钥个数";
                } else {
                    _this.$message.info("密钥个数已达到上限！");
                    return;
                }
                this.batchOpenWindow = true;
                this.initWebSocket();
            },
            closeBatchDia() {
                // Object.assign(form, $options.data().form);
                this.batchOpenWindow = false;
                Object.assign(this.$data.form, this.$options.data().form);
                this.websock.close();
                this.percentageShow=false;
                this.percentage=0;
                this.showResult=false;
                this.successCount=0;
                this.failCount=0;
                this.$refs['form'].resetFields();
            },
            showAndSearch() {
                this.showSearch = !this.showSearch;
            },
            // 获取密钥列表
            refreshApp: function () {
                let _this = this;
                _this.tableData = [];
                let r = this.$http.extKeyMG.list(this.queryParams);
                r.then(res => {
                    if (this.queryParams.keyType == -1 && this.queryParams.keyUse == -1) {
                        _this.keyTotal = res.row;
                    } else {
                        let r1 = extKeyMG.currentCount({
                            keyType: -1,
                            keyUse: -1,
                            pageNo: 1,
                            pageSize: 10
                        });
                        r1.then(res => {
                            _this.keyTotal = res.data;
                        });
                    }
                    _this.tableData = res.data;
                    _this.total = res.row;
                    _this.isPage = res.row > 0 ? true : false;
                });
            },
            currentChange: function (val) {
                this.queryParams.pageNo = val;
                this.refreshApp();
            },
            sizeChange: function (val) {
                this.queryParams.pageSize = val;
                this.refreshApp();
            },
            refreshQueryParam() {
                this.queryParams.keyType = -1;
                this.queryParams.keyUse = -1;
                this.queryParams.pageNo = 1;
                this.queryParams.pageSize = 10;
            }
        },
        created() {
            this.getLicenseParams();
            this.wsServer = getWebSocketPre() + "/keyWebsocket/100";
            this.refreshApp();
            let _this = this;
            this.tableDataHeader = [
                {
                    type: "index",
                    label: "序号",
                    width: "100"
                }, {
                    type: "normal",
                    label: "索引号",
                    prop: "keyIndex",
                    width: "300"
                }, {
                    type: "text_formatter",
                    label: "算法",
                    prop: "keyType",
                    formatter: function (value, row) {
                        if (value == 1) {
                            return "RSA";
                        } else {
                            return "SM2";
                        }
                    }
                }, {
                    type: "text_formatter",
                    label: "是否使用",
                    prop: "keyUse",
                    html: true,
                    width: "100",
                    formatter: function (value, row) {
                        if (value) {
                            return "已使用"
                        } else {
                            return "<span style='color: green'>未使用</span>";
                        }
                    }
                }, {
                    type: "text_formatter",
                    label: "密钥长度",
                    prop: "keyLength",
                    width: "100",
                    formatter: function (value, row) {
                        if (row.keyType == 1) {
                            return value
                        } else {
                            return "-";
                        }
                    }
                }, {
                    type: "time",
                    label: "创建时间",
                    prop: "createTime",
                }, {
                    type: "operation",
                    label: "操作",
                    width: "150",
                    tag: [{
                        name: "清空密钥",
                        operType: "update",
                        tagType: "el-button",
                        attributes: {
                            size: "mini",
                            type: "text",
                            icon: "el-icon-delete"
                        },
                        isShow(row) {
                            if (row.keyUse == 0) return true
                            else return false;
                        },
                        callback: function (row) {
                            _this.$confirm('确定要清空密钥?', '确定', {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning'
                            }).then(() => {
                                _this.$http.extKeyMG.del(row.id).then(res => {
                                    _this.refreshApp();
                                });
                            });
                        }
                    }]
                }
            ]
        }
    }
</script>

<style lang="less" scoped>
    .container {
        padding: -10px;
    }

    .user-search .el-form-item {
        margin-bottom: 0;
        /*margin-top: 10px;*/
    }
</style>

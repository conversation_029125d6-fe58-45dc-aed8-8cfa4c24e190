<template>
    <div>
        <el-card>
            <el-button size="mini" class="comBtn com_send_btn" @click="networkBoundFun" :disabled="isYN">网口绑定</el-button>
            <el-button size="mini" class="comBtn com_send_btn" @click="editMgrFun" :disabled="isYN">修改管理口</el-button>
            <el-button size="mini" @click="getNetworkList">刷新</el-button>
            <!-- default-expand-all -->
            <el-table :data="networkList" row-key="name" class="comTab" :indent="0"  style="margin-top: 10px" default-expand-all :tree-props="{children: 'children', hasChildren: 'hasChildren'}">
                <el-table-column align="left" prop="index" label="序号" width="55">
    <!--            <el-table-column align="center" prop="index" label="序号" width="30">-->
                    <template slot-scope="{row, $index}">
                        <span>{{ $index + 1 }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" prop="name" label="网口名称" show-overflow-tooltip></el-table-column>
                <el-table-column align="center" prop="ipaddr" label="IPv4地址" min-width="100" show-overflow-tooltip></el-table-column>
                <el-table-column align="center" prop="netmask" label="掩码" show-overflow-tooltip>
                    <template slot-scope="{row}">
                        <span v-if="row.netmask === 'null' || !row.netmask">-</span>
                        <!--<span v-if="row.optical === 0">电口</span>-->
                        <span v-else>{{row.netmask}}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" prop="gateway" label="IPv4网关" show-overflow-tooltip></el-table-column>
                <el-table-column align="center" prop="ipv6addr" label="IPv6地址" min-width="120" show-overflow-tooltip></el-table-column>
                <el-table-column align="center" prop="ipv6mask" label="前缀" show-overflow-tooltip></el-table-column>
                <el-table-column align="center" prop="ipv6Defaultgw" label="IPv6网关" show-overflow-tooltip></el-table-column>
                <!-- <el-table-column align="center" prop="optical" label="类型" min-width="60">
                    <template slot-scope="{row}">
                        <span v-if="row.optical === 1">光口</span>
                        <span v-else-if="row.optical === 0">电口</span>
                        <span v-else>-</span>
                    </template>
                </el-table-column> -->
                <el-table-column align="center" prop="status" label="状态" min-width="50">
                    <template slot-scope="{row}">
                        <el-button v-if="row.isLinked === 1" type="success" circle></el-button>
                        <el-button v-else type="info" circle></el-button>
                    </template>
                </el-table-column>
            <el-table-column align="center" prop="status" label="绑定模式" show-overflow-tooltip>
                <template slot-scope="{row}">
                <span v-if="row.bondMode === 1">主备(bond1)</span>
                <span v-else-if="row.bondMode === 0">均衡(bond0)</span>
                <span v-else>-</span>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="status" label="用途">
                <template slot-scope="{row}">
                <span v-if="row.defaultGw === 1">管理口</span>
                <span v-else>业务口</span>
                </template>
            </el-table-column>
                <el-table-column align="center" prop="handel" label="操作" min-width="160">
                    <template slot-scope="{row, $index}">
                        <el-button v-if="row.bond !== 1" type="text" icon="el-icon-edit" @click="editHandle(row, $index)" :disabled="isYN">修改</el-button>
                        <el-button v-if="row.bondMode != null" type="text" icon="el-icon-link" @click="unbindHandle(row)" :disabled="isYN">解绑</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <network-bound-dia ref="networkBoundDia" :networkList="networkList"></network-bound-dia>
            <edit-manager-cart-dia ref="editManagerCartDia" :networkList="networkList"></edit-manager-cart-dia>

            <el-dialog title="修改" :visible.sync="editShow" width="550px" append-to-body :close-on-click-modal="false" @close="closeEditDia">
                <el-form ref="editForm" :model="editForm" :rules="rules" label-width="150px" size="medium">
                    <el-form-item label="协议类型：">
                    <!--<el-input size="small" v-model="netForm.ip" placeholder="请输入IP地址"></el-input>-->
                        <el-select v-model="editForm.ipType" size="small" style="width: 100%">
                            <el-option label="IPv4 & IPv6" value="all" key="all" ></el-option>
                            <el-option label="IPv4" value="ipv4" key="ipv4"></el-option>
                            <el-option label="IPv6" value="ipv6" key="ipv6"></el-option>
                        </el-select>
                    </el-form-item>
                    <div v-if="editForm.ipType === 'ipv4' || editForm.ipType === 'all'">
                        <el-form-item label="IPv4地址：" prop="ipaddr">
                            <el-input size="small" v-model="editForm.ipaddr" @blur="checkIp(editForm.ipaddr, editForm.gateway, editForm.netmask, currentIndex, 0 )" style="width: 100%" clearable/>
                        </el-form-item>
                        <el-form-item label="IPv4子网掩码：" prop="netmask">
                            <el-input size="small" v-model="editForm.netmask" clearable/>
                        </el-form-item>
                        <el-form-item label="IPv4网关地址：" prop="gateway">
                            <el-input size="small" v-model="editForm.gateway" @blur="isEqualIPAddress(editForm.ipaddr, editForm.gateway, editForm.netmask, currentIndex, 2 )" clearable/>
                        </el-form-item>
                    </div>
                    <div v-if="editForm.ipType === 'ipv6' || editForm.ipType === 'all'">
                        <el-form-item label="IPv6地址：" prop="ipv6addr">
                            <el-input size="small" v-model="editForm.ipv6addr" style="width: 100%" clearable/>
                        </el-form-item>
                        <el-form-item label="IPv6前缀：" prop="ipv6mask">
                        <el-input size="small" v-model="editForm.ipv6mask" clearable/>
                        </el-form-item>
                        <el-form-item label="IPv6网关地址：" prop="ipv6Defaultgw">
                        <el-input size="small" v-model="editForm.ipv6Defaultgw" clearable/>
                        </el-form-item>
                    </div>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button class="comBtn com_reset_btn" size="small" @click="closeEditDia">取 消</el-button>
                    <el-button class="comBtn com_send_btn" size="small" @click="editSubmitForm">确 定</el-button>
                </div>
            </el-dialog>
        </el-card>
        <el-card style="margin-top: 10px">
            <div slot="header">
                <span>DNS管理</span>
            </div>
            <el-form label-width="150px" style="margin-top: 10px" :model="dnsFrom" ref="dnsForm" :rules="dnsRules">
                <el-form-item label="DNS服务器：" prop="dns">
                    <el-input size="small" v-model="dnsFrom.dns" placeholder="请输入DNS服务器地址" style="width: 300px; margin-right: 20px;" :disabled="isYN"></el-input>
                    <el-button class="comBtn com_send_btn" size="small" @click="setDNSadrr" :disabled="isYN">确 定</el-button>
                </el-form-item>
            </el-form>
        </el-card>
    </div>
</template>

<script>
    // import systemMG from "@/api/systemMG";
    import networkBoundDia from './components/networkBoundDia'
    import EditManagerCartDia from "@/views/system/components/editManagerCartDia";

    export default {
        components: {EditManagerCartDia, networkBoundDia },
        data() {
            const validatorDNS = (rule, value, callback) => {
                if (value !== "") {
                    let ip = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
                    if (!ip.test(value)) {
                        callback(new Error('请输入正确的DNS!'))
                    } else {
                        callback()
                    }
                } else {
                    callback();
                }
            };
            const validatorIp = (rule, value, callback) => {
                if (value !== "") {
                    let ip = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
                    if (!ip.test(value)) {
                        callback(new Error('请输入正确的IP!'))
                    } else {
                        callback()
                    }
                } else {
                    callback();
                }
            };
            const validatorMask = (rule, value, callback) => {
                if (value !== "") {
                    let mask = /^(254|252|248|240|224|192|128|0)\.0\.0\.0|255\.(254|252|248|240|224|192|128|0)\.0\.0|255\.255\.(254|252|248|240|224|192|128|0)\.0|255\.255\.255\.(254|252|248|240|224|192|128|0)$/;
                    if (!mask.test(value)) {
                        callback(new Error("请输入正确的子网掩码！"));
                    } else {
                        callback();
                    }
                } else {
                    callback();
                }
            };
            const validatorGW = (rule, value, callback) => {
                if (value !== "") {
                    let ip = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
                    if (!ip.test(value)) {
                        callback(new Error('请输入正确的网关地址!'))
                    } else {
                        callback()
                    }
                } else {
                    callback();
                }
            };
            const checkIPV6 = (rule, value, callback) => {
                if (value !== "") {
                    // let IPV6 = /(^(?:(?:(?:[0-9A-Fa-f]{1,4}:){7}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}:[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){5}:([0-9A-Fa-f]{1,4}:)?[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:){0,2}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,3}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,4}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){0,5}:((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(::([0-9A-Fa-f]{1,4}:){0,5}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|([0-9A-Fa-f]{1,4}::([0-9A-Fa-f]{1,4}:){0,5}[0-9A-Fa-f]{1,4})|(::([0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){1,7}:))$)|(^\[(?:(?:(?:[0-9A-Fa-f]{1,4}:){7}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}:[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){5}:([0-9A-Fa-f]{1,4}:)?[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:){0,2}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,3}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,4}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){0,5}:((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(::([0-9A-Fa-f]{1,4}:){0,5}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|([0-9A-Fa-f]{1,4}::([0-9A-Fa-f]{1,4}:){0,5}[0-9A-Fa-f]{1,4})|(::([0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){1,7}:))\](?::(?:[0-9]|[1-9][0-9]{1,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5]))?$)/;
                    // let IPV6 = /(^(?:(?:(?:[0-9A-Fa-f]{1,4}:){7}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}:[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){5}:([0-9A-Fa-f]{1,4}:)?[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:){0,2}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,3}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,4}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){5}:((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:)?((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,2}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,3}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:):([0-9A-Fa-f]{1,4}:){0,4}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(::([0-9A-Fa-f]{1,4}:){0,5}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|([0-9A-Fa-f]{1,4}::([0-9A-Fa-f]{1,4}:){0,5}[0-9A-Fa-f]{1,4})|(::([0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){1,7}:))$)|(^\[(?:(?:(?:[0-9A-Fa-f]{1,4}:){7}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}:[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){5}:([0-9A-Fa-f]{1,4}:)?[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:){0,2}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,3}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,4}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){5}:((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:)?((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,2}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,3}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:):([0-9A-Fa-f]{1,4}:){0,4}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(::([0-9A-Fa-f]{1,4}:){0,5}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|([0-9A-Fa-f]{1,4}::([0-9A-Fa-f]{1,4}:){0,5}[0-9A-Fa-f]{1,4})|(::([0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){1,7}:))\](?::(?:[0-9]|[1-9][0-9]{1,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5]))?$)/;
                    let IPV6 = /(^(?:(?:(?:[0-9A-Fa-f]{1,4}:){7}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}:[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){5}:([0-9A-Fa-f]{1,4}:)?[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:){0,2}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,3}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,4}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){6}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){5}:((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){4}:([0-9A-Fa-f]{1,4}:)?((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){3}:([0-9A-Fa-f]{1,4}:){0,2}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:){2}:([0-9A-Fa-f]{1,4}:){0,3}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(([0-9A-Fa-f]{1,4}:):([0-9A-Fa-f]{1,4}:){0,4}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|(::([0-9A-Fa-f]{1,4}:){0,5}((\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b)\.){3}(\b((25[0-5])|(1\d{2})|(2[0-4]\d)|(\d{1,2}))\b))|([0-9A-Fa-f]{1,4}::([0-9A-Fa-f]{1,4}:){0,5}[0-9A-Fa-f]{1,4})|(::([0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4})|(([0-9A-Fa-f]{1,4}:){1,7}:))$)/;
                    if (!IPV6.test(value)) {
                        callback(new Error('请输入正确的IP!'))
                    } else {
                        callback()
                    }
                } else {
                    callback();
                }
            };
            const checkIPV6Mask = (rule, value, callback) => {
                if (value !== "") {
                    let len = /^\+?[1-9]\d*$/;
                    if (len.test(value) && value > 0 && value <= 128) {
                        callback();
                    } else {
                        callback(new Error('请输入1~128之间的整数!'));
                    }
                } else {
                    callback();
                }
            };
            return {
                isYN: false,
                isPage: false,
                editShow: false,
                currentIndex: '',
                dnsFrom: {
                    dns: ''
                },
                editForm: {
                    name: '',
                    ipType: '',
                    ipaddr: '',
                    netmask: '',
                    gateway: '',
                    ipv6addr: '',
                    ipv6mask: '',
                    ipv6Defaultgw: ''
                },
                networkList: [],
                dnsRules: {
                    dns: [
                        {required: true, message: "请输入DNS服务器地址", trigger: "blur"},
                        {validator: validatorDNS, trigger: 'blur'}
                    ]
                },
                rules: {
                    ipaddr: [
                        {required: true, message: "请输入IPv4地址", trigger: "blur"},
                        {validator: validatorIp, trigger: 'blur'}
                    ],
                    netmask: [
                        {required: true, message: "请输入IPv4子网掩码", trigger: "blur"},
                        {validator: validatorMask, trigger: 'blur'}
                    ],
                    gateway: [
                        {required: true, message: "请输入IPv4网关地址", trigger: "blur"},
                        {validator: validatorGW, trigger: 'blur'}
                    ],
                    ipv6addr: [
                        {required: true, message: "请输入正确的IPv6地址", trigger: "blur"},
                        {validator: checkIPV6, trigger: 'blur'}
                    ],
                    ipv6mask: [
                        {required: true, message: "请输入正确的前缀长度", trigger: "blur"},
                        {validator: checkIPV6Mask, trigger: 'blur'}
                    ],
                    ipv6Defaultgw: [
                        {required: true, message: "请输入正确的网关地址", trigger: "blur"},
                        {validator: checkIPV6, trigger: 'blur'}
                    ]
                }
            }
        },
        methods: {
            // 获取列表
            getNetworkList() {
                this.$http.systemMG.networkListApi().then(({code, data, msg}) => {
                    if (!code) {
                        this.networkList = data
                    } else {
                        // this.$message.warning(msg)
                    }
                })
            },
            getDnsInfo(){
                this.$http.systemMG.getDns().then(({code, data, msg}) => {
                    if (!code) {
                        this.dnsFrom.dns = data
                    } else {
                        this.$message.warning(msg)
                    }
                })
            },
            // networkBoundDia
            networkBoundFun() {
                this.$refs.networkBoundDia.initNetworkBoundFun()
            },
            // 修改
            editHandle(row, index) {
                console.log(row, index);
                row.ipType="all";
                this.editShow = true;
                this.currentIndex = index;
                this.editForm = {...row};
            },
            // 解绑
            unbindHandle(row) {
                this.$confirm('请确认是否要解除绑定, 解绑后立刻生效!', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    closeOnClickModal: false,
                    closeOnPressEscape: false,
                    type: 'warning'
                }).then(() => {
                    this.$http.systemMG.unBond(JSON.stringify(row)).then(({code, data, msg}) => {
                        if (!code) {
                            this.$message.success('配置成功！');
                            // 调用重启设备方法
                            // this.restartDeviceFun();
                            this.getNetworkList();
                        } else {
                            // this.$message.warning(msg);
                        }
                        this.closeEditDia();
                    });
                });
            },
            checkIp(ip, gw, mask, index, num) {
                this.networkList.map((item, i) => {
                    if (ip === item.ipaddr && index !== i) {
                        console.log(this.$refs['editForm'].fields[num]);
                        this.$nextTick(() => {
                            this.$refs['editForm'].fields[num].validateMessage = 'IP重复, 请重新输入!';
                            this.$refs['editForm'].fields[num].validateState = 'error';
                        });
                    }
                });
                // console.log(this.$refs['networkForm'].fields[num].validateState);
                // if (this.$refs['networkForm'][index].fields[num].validateState !== 'error') this.isEqualIPAddress(ip, gw, mask, index, num)
                this.isEqualIPAddress(ip, gw, mask, index, num);
            },
            /**
             * [isEqualIPAddress 判断两个IP地址是否在同一个网段]
             * @param {[String]} addr1 [ip地址]
             * @param {[String]} addr2 [网关地址]
             * @param {[String]} mask [子网掩码]
             * @param {[Number]} index [动态表单索引]
             * @param {[Number]} num [表单项索引]
             * @return {Boolean} [true or false]
             */
            isEqualIPAddress(addr1, addr2, mask, index, num) {
                console.log(addr1, addr2, mask, index, num);
                // let num = index === 0 ? 0 : 2;
                console.log(this.$refs['editForm'].fields[num].validateState);
                // if (this.$refs['networkForm'][index].fields[num].validateState === 'error' ) return false;
                if (!this.checkIpOrGw(addr2) || !this.checkIpOrGw(addr1)) return false;
                if (!addr1 || !addr2 || !mask) {
                    console.log("各参数不能为空");
                    return false;
                }
                var
                    res1 = [],
                    res2 = [];
                addr1 = addr1.split(".");
                addr2 = addr2.split(".");
                mask = mask.split(".");
                for (var i = 0, ilen = addr1.length; i < ilen; i += 1) {
                    res1.push(parseInt(addr1[i]) & parseInt(mask[i]));
                    res2.push(parseInt(addr2[i]) & parseInt(mask[i]));
                }
                if (res1.join(".") == res2.join(".")) {
                    console.log("在同一个网段");
                    let n = num === 0 ? 2 : 0;
                    this.$refs['editForm'].fields[n].validateState = 'success';
                    return true;
                } else {
                    console.log("不在同一个网段");
                    this.$nextTick(() => {
                        this.$refs['editForm'].fields[num].validateState = 'error';
                        this.$refs['editForm'].fields[num].validateMessage = '网关和IP不在同一网段, 请重新输入!';
                    });
                    return false;
                }
            },
            checkIpOrGw(value) {
                let ip = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
                return !ip.test(value);

                // if (!ip.test(value)) {
                    // this.$nextTick(() => {
                    //     this.$refs['networkForm'].fields[num].validateState = 'error';
                    //     this.$refs['networkForm'].fields[num].validateMessage = txt;
                    // });
                    // return false
                    // callback(new Error('请输入正确的网关地址!'))
                // } else {
                //     return true
                    // callback()
                // }
            },
            setDNSadrr() {
                this.$refs['dnsForm'].validate((valid) => {
                    if (valid) {
                        this.$http.systemMG.setDns(JSON.stringify(this.dnsFrom)).then(({code, data, msg}) => {
                            if (!code) {
                                this.$message.success(msg);
                            } else {
                                this.$message.warning(msg)
                            }
                        })
                    }
                });
            },
            editSubmitForm() {
                this.$refs['editForm'].validate(valid => {
                    if (valid) {
                        this.$confirm('请确保网络配置的正确性, 确定后生效!', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            closeOnClickModal: false,
                            closeOnPressEscape: false,
                            type: 'warning'
                        }).then(() => {
                            this.$http.systemMG.setNetwork(JSON.stringify(this.editForm)).then(({code, data, msg}) => {
                                // console.log(code, data, msg);
                                if (!code) {
                                    this.$message.success('配置成功！');
                                    // 调用重启设备方法
                                    // this.restartDeviceFun();
                                    this.getNetworkList()
                                } else {
                                    // this.$message.warning(msg)
                                }
                                this.closeEditDia()
                            })
                        });
                    }
                })
            },
            closeEditDia() {
                Object.assign(this.editForm, this.$options.data().editForm);
                this.$refs['editForm'].clearValidate();
                this.editShow = false
            },
            // editManagerCartDia
            editMgrFun() {
              this.$refs.editManagerCartDia.initEditMgrFun()
            },
            isVsm() {
                this.$http.initMG.isVSM().then(res => {
                    if (res.code === 0) {
                        if(res.data == "true"){
                            this.isYN = true;
                        }
                    }
                })
            },
        },
        mounted() {
            this.isVsm();
            this.getNetworkList();
            this.getDnsInfo();
        }
    }
</script>

<style lang="less" scoped>
    /deep/.el-table [class*=el-table__row--level] .el-table__expand-icon {
        float: right;
    }
    /deep/.el-button.is-circle {
        padding: 7px;
        margin-bottom: 8px;
        cursor: default;
    }
</style>

import {reqheaders, req, reqParams, noAuthreq, reqParamNo<PERSON>son, uploadReq, reqCommon} from './axiosFun';
import API from "@/api/apiuri"

let extKeyApi = API.extKeyApi;
export default {
  list(param) {
    return reqParam<PERSON>o<PERSON><PERSON>("POST", extKeyApi.list, param);
  },
  del(id) {
    return reqParam<PERSON>o<PERSON><PERSON>("GET", extKeyApi.del + "/" + id, {});
  },
  generateSignKey(certType, keyLength) {
    return reqParamNo<PERSON>son("POST", extKeyApi.generateSignKey, {certType: certType, keyLength: keyLength});
  },
  generateMulKey(certType, keyLength, count) {
    return reqParamNo<PERSON>son("POST", extKeyApi.generateMulKey, {count: count, certType: certType, keyLength: keyLength});
  },
  getMaxCount(){
    return reqParam<PERSON><PERSON><PERSON><PERSON>("POST", extKeyApi.getMaxCount, {});
  },
  clear<PERSON>ey(callback) {
    return reqParamNo<PERSON>son("GET", extKeyApi.clearKey, {},callback);
  },
  currentCount(param) {
    return reqParamNoJson("POST", extKeyApi.getCurrentCount, param);
  }
}

import Api from "@/api/apiuri";

import {req, reqC<PERSON>mon, reqParamNo<PERSON>son, reqFormData} from "./axiosFun";
const systemApi = Api.systemApi;

export default {
  /**
   * 查询 设置 是否无访态
   * */
  // 设置状态
  setStatus(params) {
    return req("get", '/business/svs/switchAccessState?code=' + params);
  },
  // 获取是否无访态
  getStatus() {
    return req("get", '/business/svs/getConfStatus');
  },
  // 设备登录
  devLogin(params) {
    return req("POST", '/system/devRegister', params);
  },
  // 设备登出
  devLoginout() {
    return req("POST", '/system/devLogout');
  },
  // 设备状态
  devStatusApi() {
    return req("get", '/business/svs/getStatus');
  },

  /**
   * 系统配置
   */
  // 网卡配置 /system/network/select
  networkListApi() {
    return req("get", systemApi.networkList);
  },
  getVXLANList(params) {
    return req("post", "/ics/vsm/vxlan/page", params);
  },
  addVXLAN(params) {
    return req("post", "/ics/vsm/vxlan/add", params);
  },
  editVXLAN(params) {
    return req("post", "/ics/vsm/vxlan/edit", params);
  },
  // 删除VXlan
  delVXLAN(params) {
    return req("get", '/ics/vsm/vxlan/delete/' + params);
  },
  bindableList() {
    return req("get", systemApi.bindableList)
  },
  setDns(params) {
    return req("post", systemApi.setDns, params);
  },
  getDns() {
    return req("get", systemApi.getDns);
  },
  setNetwork(params) {
    return req("post", systemApi.setNetwork, params);
  },
  setBond(params) {
    return req("post", systemApi.setBond, params);
  },
  getBondStatus() {
    return req("get", systemApi.getBondStatus);
  },
  restartDevApi() {
    return req("post", systemApi.restartDev);
  },
  unBond(params) {
    return req("post", systemApi.unBond, params);
  },



  // 获取配置的网口
  allEth() {
    return req("get", systemApi.allEth);
  },
  // 获取空网口
  getEmptyEth() {
    return req("post", systemApi.queryNetwork);
  },
  // 设置网卡
  setEth(eth) {
    return req("post", systemApi.setEth, eth);
  },
  //查询默认网关
  queryDefGW() {
    return req("post", systemApi.queryDefGW)
  },
  //设置默认网关
  setDefGW(wg) {
    return req("post", systemApi.setDefGW,wg)
  },
  //设置网卡状态
  setNetCardStatus(status) {
    return req("post", systemApi.setNetCardStatus, status);
  },
  //查询当前时间
  queryTime() {
    return req("get", systemApi.quertCurrentTime);
  },
  // 设置ntp
  setNtpOrDate(date) {
    return req("post", systemApi.setTime, date)
  },
  // 查询当前ntp设置
  queryNTP() {
    return req("get", systemApi.queryNtp)
  },
  // 分页查询日志
  pageAudit(page) {
    return req("post", systemApi.queryPageAudit, page)
  },
  // 审计
  reportAudit(ids) {
    return req("post", "/audit/markAudit", ids)
  },
  exportAuditExcel(search) {
    return reqCommon("post", systemApi.exportAuditExcel, search, {responseType: 'blob' })
  },
  // 网络诊断
  diagNetWork(diag) {
    return req("post", systemApi.diagNetWork, diag)
  },
  // 系统使用情况
  getOnlineNum() {
    return req("get", systemApi.onlineNum)
  },
  // 系统基本配置
  getSystemInfo() {
    return req("get", systemApi.systemInfo)
  },
  // 系统基本信息
  getProjectInfo() {
    return req("get", systemApi.projectInfo)
  },
  // 获取webSocket地址
  getWebSocketPath() {
    return req("get", systemApi.getWsIp)
  },
  // 获取webSocket地址
  getWebSocketPathgetWsIpNoSid() {
    return req("get", systemApi.getWsIpNoSid)
  },
  // 重启Nginx服务
  reStartNginx() {
    return reqParamNoJson("post", systemApi.rebootNginx, {}, function(){}, true)
  },
  // 设置防火墙
  setFirewall() {
    return req("post", systemApi.rebootNginx)
  },
  //重启服务
  rebootService() {
    return reqParamNoJson("post", systemApi.rebootService, {}, function(){}, false);
  },
  // 关机设备
  shutdownDevice() {
    return reqParamNoJson("post", systemApi.shutdown, {},function(){},false)
  },
  //重启设备
  rebootDevice() {
    return reqParamNoJson("post", systemApi.reboot, {}, function(){}, false)
  },
  selfChecking() {
    return req("post", systemApi.selfCheck)
  },
  //DNS列表
  allDNS() {
    return req("get", systemApi.allDns)
  },
  // 添加DNS
  addDNS(dns) {
    return req("post", systemApi.addDns, dns)
  },
  // 修改DNS
  updateDNS(dns) {
    return req("put", systemApi.updateDns, dns)
  },
  // 删除DNS
  deleteDNS(id) {
    return req("delete", systemApi.deleteDns + '/' + id)
  },
  // 校验NTP地址
  verifyNtp(ntp) {
    return req("post", systemApi.verifyNtp, ntp)
  },
  // 校验DN地址
  verifyDN(dn) {
    return req("post", systemApi.verifyDN, dn)
  },
  // 设置默认网关所在网口
  setDefaultGw(name) {
    return req("post", systemApi.setDefaultGw, name)
  },


  /**
   * 设备证书
   */
  // 设备证书列表
  quertSslCertList() {
    return req("get", systemApi.sslCertList)
  },
  // 更新设备证书
  // uploadSslCert(param) {
  //   return req("post", systemApi.uploadSslCert, param)
  // },
  uploadSslCert(param) {
    return reqFormData("post", systemApi.uploadSslCert, param)
  },
  // 下载证书
  downloadSslCert(id) {
    return req("get", systemApi.downloadCert + "/" + id)
  },
  // 启用证书
  enableSslCert(id) {
    return req("put", systemApi.enableSslCert + "/" + id)
  },
  // 清除证书
  clearSslCert(id) {
    return req("delete", systemApi.clearSslCert + "/" + id)
  },

  /**
   * 高可用
   */
  //查询热备组
  queryBackupGroup() {
    return req("post", systemApi.queryGroup)
  },
  //添加热备组
  addBackupGroup(params) {
    return req("post", systemApi.addGroup, params)
  },
  //开启热备组
  startBackupGroup(params) {
    return req("post", systemApi.startGroup, params)
  },
  //手动同步
  manualBackupSave(params) {
    return req("post", systemApi.manual, params)
  },
  //监控状态
  queryBackupStatus() {
    return req("post", systemApi.queryStatus)
  },

  //打开接收密钥开关
  turnOnReceiveAppKey() {
    return req("post", '/sign/system/keySync/receiveAppKey')
  },
  //关闭接收密钥开关
  turnOffReceiveAppKey() {
    return req("post", '/sign/system/keySync/unReceiveAppKey')
  },
  //测试链接状态
  testConnection(param) {
    return reqParamNoJson("post", '/sign/system/keySync/testConnection', param)
  },
  //测试链接状态
  syncAppKey(param) {
    return reqParamNoJson("post", '/sign/system/keySync/syncAppKeyTo', param)
  },
  //密钥导出记录列表
  listKeyExportInfo(param) {
    return reqParamNoJson("post", '/sign/system/keySync/exportInfo', param)
  },
  //应用密钥信息
  appKeyInfo() {
    return reqParamNoJson("post", '/sign/system/keySync/appKeyInfo')
  }

}

<template>
    <el-dialog title="生成用户密钥" :visible.sync="show" width="550px" append-to-body :close-on-click-modal="false" @close="closeDialog">
        <el-form label-width="110px" :model="createForm" :rules="userRules" ref="createForm">
            <el-form-item label="密钥类型:" prop="keyType">
                <el-radio-group v-model="createForm.keyType" @change="changeKeyType">
                    <el-radio :label="1">签名</el-radio>
                    <el-radio :label="2">加密</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="主密钥索引：" prop="masterId">
                <el-select v-model="createForm.masterId" placeholder="请选择主密钥索引" size="small" style="width: 100%">
                    <el-option v-for="item in masterIndexArr" :key="item.id" :label="item.id" :value="item.id"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="用户标识：" prop="userID">
                <el-input size="small" v-model="createForm.userID" placeholder="请输入用户标识" maxlength="255" show-word-limit></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button size="small" class="comBtn com_reset_btn" @click="closeDialog">取 消</el-button>
            <el-button size="small" class="comBtn com_send_btn" @click="exportUserKeyFun" :loading="loading">确 定</el-button>
        </div>
    </el-dialog>
</template>

<script>
    // import keyManage from "../../../api/symmetryMG";
    export default {
        data() {
            return {
                show: false,
                createForm: {
                    keyType: 1,
                    masterId: '',
                    userID: ''
                },
                masterIndexArr: [],
                indexArr: [],
                userRules: {
                    // keyIndex: [{required: true, message: '请输入主密钥索引', trigger: 'blur'}],
                    masterId: [{required: true, message: '请选择主密钥索引', trigger: 'change'}],
                    userID: [{required: true, message: '请输入用户标识', trigger: 'blur'}]
                },
                loading: false
            }
        },
        methods: {
            initCreateUserKey(indexArr) {
                this.show = true;
                this.indexArr = indexArr;
                this.changeKeyType(1);
                this.$nextTick(() => {
                    this.$refs["createForm"].clearValidate();
                })
            },
            changeKeyType(val) {
                this.createForm.masterId = '';
                this.$nextTick(() => {
                    this.$refs["createForm"].clearValidate();
                });
                this.masterIndexArr = this.indexArr.filter(res => {
                    return res.keyType === val
                });
            },
            exportUserKeyFun() {
                this.$refs["createForm"].validate((valid) => {
                    if (valid) {
                        // 调用后端接口
                        this.loading = true;
                        let p = new FormData();
                        p.append('keyType', this.createForm.keyType);
                        p.append('masterId', this.createForm.masterId);
                        p.append('userId', this.createForm.userID);
                        this.$http.symmetryMG.createUserKey(p).then(({code, data, msg}) => {
                            console.log(code, data, msg);
                            this.loading = false;
                            if (code !== 0) return this.$message.warning(msg);
                            this.$message.success(msg || '生成用户密钥成功!');
                            this.$emit('parentHandle')
                        }).catch(() => {
                            this.loading = false;
                        });
                        this.closeDialog()
                    }
                })
            },
            closeDialog() {
                this.show = false;
                this.$nextTick(() => {
                    this.$refs.createForm.resetFields();
                })
            }
        }
    }
</script>

<style lang="less" scoped>
    .myUpload_form_item {
        margin-bottom: 5px;
    }
    /deep/ .el-input .el-input__count .el-input__count-inner {
        background: #f0f0f0;
        padding: 5px 5px;
    }
</style>
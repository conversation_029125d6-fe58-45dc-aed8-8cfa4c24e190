<template>
    <div>
        <el-card v-show="showSearch" class="box-card" shadow="always" style="padding-bottom: 10px; margin-bottom: 10px">
            <el-form :inline="true" :model="searchPage" class="user-search comForm" size="small">
                <el-form-item label="用户名">
                    <el-input size="small" v-model="searchPage.userName" clearable></el-input>
                </el-form-item>
                <el-form-item label="级别">
                    <el-select size="small" v-model="searchPage.logLevel" placeholder="请选择" clearable>
                        <el-option v-for="item in levelOptions" :label="item.label" :value="item.value" :key="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="动作">
                    <el-input size="small" v-model="searchPage.action" clearable></el-input>
                </el-form-item>
                <el-form-item label="模块">
                    <el-input size="small" v-model="searchPage.object" clearable></el-input>
                </el-form-item>
                <el-form-item label="结果">
                    <el-select size="small" v-model="searchPage.result" placeholder="请选择" clearable>
                        <el-option v-for="item in resultOptions" :label="item.label" :value="item.value" :key="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="时间范围">
                    <el-date-picker
                            v-model="searchPage.startsTimes" clearable
                            type="datetimerange" size="small"
                            format="yyyy - MM - dd HH:mm:ss"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期">
                    </el-date-picker>
                </el-form-item>

                <el-form-item style="background-color: transparent;">
                    <el-button class="comBtn com_send_btn" size="small" icon="el-icon-search" @click="searchAudit">搜索</el-button>
                    <el-button class="comBtn com_reset_btn" size="small" icon="el-icon-refresh" @click="cleanSearchPage">重置</el-button>

                </el-form-item>
            </el-form>
        </el-card>
        <el-card class="box-card" shadow="always" style="padding-bottom: 10px">
            <el-button class="comBtn com_del_btn" size="mini" icon="el-icon-download" @click="exportAudit">导出</el-button>
            <el-button class="comBtn com_send_btn" size="mini" icon="el-icon-check" @click="auditLog(null,2,null)">批量审计</el-button>
            <div style="text-align: right; float: right; margin-bottom: 10px">
                <el-button-group>
                    <el-button size="mini" icon="el-icon-search" @click="showAndSearch"></el-button>
                    <el-button size="mini" icon="el-icon-refresh-left" @click="searchAudit"></el-button>
                </el-button-group>
            </div>

            <el-table size="small" class="comTab" :data="tableData" highlight-current-row v-loading="loading" element-loading-text="拼命加载中" style="width: 100%;" @selection-change="handleSelectionChange">
                <el-table-column align="center" type="selection" width="55"></el-table-column>
                <el-table-column align="center" type="index" label="序号" width="60" show-overflow-tooltip :resizable="false"></el-table-column>
                <el-table-column prop="userName" label="用户名" align="center" show-overflow-tooltip :resizable="false"></el-table-column>
                <el-table-column prop="sourceIp" label="源IP" align="center" show-overflow-tooltip :resizable="false"></el-table-column>
                <el-table-column prop="crateTime" label="时间" align="center" show-overflow-tooltip :resizable="false"></el-table-column>
                <el-table-column prop="action" label="动作" align="center" show-overflow-tooltip :resizable="false"></el-table-column>
                <el-table-column prop="object" label="模块" align="center" show-overflow-tooltip :resizable="false"></el-table-column>
                <el-table-column prop="result" label="结果" align="center" show-overflow-tooltip :resizable="false">
                    <template slot-scope="{row}">
                        <span v-if="row.result === 'success'">成功</span>
                        <span v-else>失败</span>
                    </template>
                </el-table-column>
                <el-table-column prop="logLevel" label="级别" align="center" show-overflow-tooltip :resizable="false">
                    <template slot-scope="{row}">
                        <span v-if="row.logLevel === 1">信息</span>
                        <span v-if="row.logLevel === 2">警告</span>
                        <span v-if="row.logLevel === 3">错误</span>
                    </template>
                </el-table-column>
                <el-table-column prop="audited" label="审计状态" align="center" show-overflow-tooltip :resizable="false">
                    <template slot-scope="{row}">
                        <span v-if="row.audited== '0'">未审计</span>
                        <span v-else>已审计</span>
                    </template>
                </el-table-column>
                <el-table-column prop="verified" label="是否篡改" align="center" show-overflow-tooltip :resizable="false" v-if="signStatus">
                    <template slot-scope="{row}">
                        <span v-if="row.verified== -1">是</span>
                        <span v-else-if="row.verified== 0">-</span>
                        <span v-else>否</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="操作" show-overflow-tooltip :resizable="false" width="150">
                    <template slot-scope="{$index, row}">
                        <el-button type="text" icon="el-icon-document" @click="searchDetail(row)">详细信息</el-button>
                        <el-button type="text" icon="el-icon-check" @click="auditLog([row.id],1,row)">审计</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页组件 -->
            <div style="text-align: right;margin-top: 10px">
                <el-pagination
                        background
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page.sync="searchPage.pageNum"
                        :page-size="searchPage.pageSize"
                        :total="total"
                        :page-sizes="[10, 20, 30, 40]"
                        layout="total, sizes, prev, pager, next, jumper">
                </el-pagination>
            </div>
        </el-card>

        <!-- 详细信息 -->
        <el-dialog class="detailInfo" title="详细信息" :visible.sync="searchVisible" :before-close='closeDialog' :close-on-click-modal="false" width="700px">
            <el-descriptions class="margin-top" :column="1" border>
                <el-descriptions-item label="级别">{{searchForm.logLevel === 1 ? '信息' : searchForm.logLevel === 2 ? '警告' : '错误'}}</el-descriptions-item>
                <el-descriptions-item label="源IP">{{searchForm.sourceIp}}</el-descriptions-item>
                <el-descriptions-item label="用户名">{{ searchForm.userName }}</el-descriptions-item>
                <el-descriptions-item label="动作">{{searchForm.action}}</el-descriptions-item>
                <el-descriptions-item label="模块">{{searchForm.object}}</el-descriptions-item>
                <!--<el-descriptions-item label="审计状态">{{searchForm.audited == 0 ? '未审计' : '已审计'}}</el-descriptions-item>-->
                <el-descriptions-item label="访问详情">{{searchForm.detailInfo}}</el-descriptions-item>
            </el-descriptions>
        </el-dialog>
    </div>
</template>

<script>
    import systemMG from "../../api/systemMG";
    import logConfMG from "@/api/logConfMG"
    // import Pagination from '../../components/my-pagination'

    export default {
        name: "audit-operate",
        // components: {Pagination},
        data() {
            return {
                total: 0,
                tableData: [],
                showSearch: true,
                signStatus: true,
                searchVisible: false,
                loading: false,
                searchForm: {
                    logLevel: '',
                    sourceIp: '',
                    userName: '',
                    action: '',
                    object: '',
                    audited: '',
                    detailInfo: ''
                },
                resultOptions: [
                    {value: 'success', label: '成功'},
                    {value: 'fail', label: '失败'}
                ],
                levelOptions: [
                    {value: 3, label: '错误'},
                    {value: 2, label: '警告'},
                    {value: 1, label: '信息'}
                ],
                selectionArr: [],
                searchPage: {
                    pageNum: 1,
                    pageSize: 10,
                    logType: "1",
                    userName: '',
                    result: '',
                    logLevel: null,
                    startsTimes: [],
                    startTime: '',
                    endTime: '',
                    appId: "1,5",
                    object: "",
                    action: "",
                },
            }
        },
        methods: {
            handleSelectionChange(val) {
                this.selectionArr = val;
            },
            showAndSearch() {
                this.showSearch = !this.showSearch;
            },
            cleanSearchPage() {
                this.searchPage.pageNum = 1;
                this.searchPage.pageSize = 10;
                this.searchPage.logType = "1";
                this.searchPage.userName = null;
                this.searchPage.result = null;
                this.searchPage.logLevel = null;
                this.searchPage.action = null;
                this.searchPage.object = null;
                this.searchPage.startsTimes = [];
                this.searchPage.startTime = null;
                this.searchPage.endTime = null;
                this.pageAudit(this.searchPage);
            },
            searchDetail(row) {
                this.searchForm.detailInfo = row.detailInfo;
                this.searchForm.logLevel = row.logLevel;
                this.searchForm.sourceIp = row.sourceIp;
                this.searchForm.userName = row.userName;
                this.searchForm.action = row.action;
                this.searchForm.object = row.object;
                // this.searchForm.audited = row.audited;
                this.searchVisible = true;
            },

            getSignStatus() {
                // let url = process.env.NODE_ENV === 'production' ? '/api/log' : 'http://***********:10242';
                logConfMG.logSign().then(({code, data, msg}) => {
                    // if (code !== 0) return this.$message.warning(msg);
                    console.log(code, data, msg);
                    this.signStatus = data.audit.sign === 1;
                    console.log(this.signStatus);
                    this.pageAudit(this.searchPage);
                })
            },
            auditLog(ids, type, row) {
                this.$confirm(type === 1 ? '是否审计?' : '是否批量审计?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                let params = {
                    // appId: type === 1 ? row.appId : "1",
                    logType: type === 1 ? row.logType : "1",
                    ids: type === 1 ? ids : this.selectionArr.map(item => item.id)
                };
                systemMG.reportAudit(JSON.stringify(params)).then((res) => {
                    if (!res.code) {
                        this.$message.success(type === 1 ? '审计成功' : '批量审计成功');
                        this.pageAudit(this.searchPage);
                    } else {
                        this.$message.warning(res.msg)
                    }
                })

            }).catch(() => {
                this.$message.info('已取消')
            });
            },
            // 分页查询
            pageAudit(params) {
                this.tableData = [];
                systemMG.pageAudit(params).then((res) => {
                    if (!res.code) {
                        let dataObj = JSON.parse(res.data);
                        console.log(dataObj);
                        if (!dataObj.code) {
                            this.tableData = dataObj.data.list;
                            // 分页赋值
                            this.total = dataObj.data.total;
                        } else {
                            // this.$message.warning(dataObj.message)
                        }
                    } else {
                        this.$message.warning(res.msg)
                    }
                })
            },
            searchAudit() {
                if (!this.searchPage.startsTimes) {
                    this.searchPage.startTime = '';
                    this.searchPage.endTime = ''
                } else {
                    this.searchPage.startTime = this.searchPage.startsTimes[0];
                    this.searchPage.endTime = this.searchPage.startsTimes[1]
                }
                this.searchPage.pageNum = 1;
                this.pageAudit(this.searchPage);
            },
            closeDialog() {
                this.searchVisible = false;
                this.searchForm.detailInfo = null;
                this.searchForm.logLevel = null;
                this.searchForm.sourceIp = null;
                this.searchForm.userName = null;
                this.searchForm.action = null;
                this.searchForm.object = null;
            },
            getCurrentDataTime() {
                let date = new Date();
                return date.getFullYear() + "-" +
                    (date.getMonth() + 1) + "-" +
                    date.getDate()+" " +
                    date.getHours() + ":" +
                    date.getMinutes() + ":" +
                    date.getSeconds()
            },
            exportAudit() {
                systemMG.exportAuditExcel(this.searchPage).then((res) => {

                    let currentTime = this.getCurrentDataTime();
                    const fileName = "操作日志" + currentTime + ".xls";
                    let blob = new Blob([res], {
                        type: 'application/vnd.ms=excel'
                    });
                    let link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    link.download = fileName;
                    link.click();
                    //释放内存
                    window.URL.revokeObjectURL(link.href)

                })
            },
            // 分页插件事件
            handleSizeChange(val) {
                this.searchPage.pageSize = val;
                this.pageAudit(this.searchPage)
            },
            handleCurrentChange(val) {
                this.searchPage.pageNum = val;
                this.pageAudit(this.searchPage)
            },

        },
        created() {
            // this.pageAudit();
            this.getSignStatus();
        }
    }
</script>

<style lang="less" scoped>
    /deep/ .el-descriptions-item__label.is-bordered-label {
        width: 130px;
    }

    /deep/ .el-pagination__total {
        float: left;
    }

    .user-search {
        float: left;
    }

    .detailInfo {
        text-align: left;
    }
</style>

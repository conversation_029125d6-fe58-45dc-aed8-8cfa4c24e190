/**
* 分页组件
*/ 
<template>
  <el-pagination class="page-box" @size-change="handleSizeChange" @current-change="handleCurrentChange" background :current-page.sync="childMsg.currentPage" :page-sizes="[10, 20, 30, 40]" :page-size="childMsg.pageSize"
                 layout="total, sizes, prev, pager, next, jumper" :total="childMsg.total">
  </el-pagination>
</template>
<script>
export default {
  name: 'Pagination',
  props: ['childMsg'],
  data() {
    return {
      pageparm: {
        currentPage: this.childMsg.currentPage,
        pageSize: this.childMsg.pageSize
      }
    }
  },
  created() {},
  methods: {
    handleSizeChange(val) {
      console.log(val);
      /**
       * 子传父
       * 参数1 父元素方法
       * 参数2 数据
       */
      this.pageparm.pageSize = val;
      setTimeout(() => { this.$emit('callFather', this.pageparm) }, 10)
      // this.$emit('callFather', this.pageparm)
    },
    handleCurrentChange(val) {
      /**
       * 子传父
       * 参数1 父元素方法
       * 参数2 数据
       */
      console.log(val);
      this.pageparm.currentPage = val;
      this.$emit('callFather', this.pageparm)
    }
  }
}
</script>

<style lang="less" scoped>
.page-box {
  text-align: right;
  margin: 10px auto;
  /deep/ .el-pagination__total {
    float: left;
  }
  /deep/.el-pagination__sizes .el-select .el-input--mini .el-input__inner {
    height: 28px;
  }
  /*/deep/.el-pager li:not(.disabled).active {*/
    /*background-color: #70b5e0;*/
  /*}*/
}
</style>

<template>
  <div>
    <div v-if="system == ''">
      <el-row :span="24" :gutter="20">
        <el-col
          v-for="(item, index) in monitorList"
          :span="12"
          :key="index"
          :class="{ 'm-top': index != 0 && index != 1 }"
        >
          <div>
            <el-card>
              <div slot="header" class="clearfix">
                <span>{{ getTitleByKey(item) }}</span>
                <div style="float: right">
                  <el-button type="text" @click="historyClick('history', item)">
                    历史业务量
                  </el-button>
                  <el-button type="text" @click="moreClick('more', item)">
                    更多
                  </el-button>
                </div>
              </div>
                <el-row>
                  <el-col :span="24">
                    <div class="grid-content bg-purple-dark">
                      <lineGraph
                        :id="item"
                        :get-data="getDataByKey(item)"
                      ></lineGraph>
                    </div>
                  </el-col>
                </el-row>
            </el-card>
          </div>
        </el-col>
      </el-row>
    </div>
    <div v-if="system == 'more'">
      <more ref="more" @personHandle="personHandle"></more>
    </div>
    <div v-if="system == 'history'">
      <barGraph ref="historyName" @personHandle="personHandle"></barGraph>
    </div>
  </div>
</template>

<script>
import lineGraph from "./echarts/line-graph";
import more from "./echarts/more";
import barGraph from "./echarts/bar-graph";
export default {
  name: "operationMonitor",
  components: { lineGraph, barGraph, more },
  data() {
    return {
      monitorList: [],
      sighData: {},
      asymData: {},
      symData: {},
      timestampData: {},
      envelopeData: {},
      digestData: {},
      macData: {},
      csdSignData: {},
      csdDecData: {},
      system: "",
      timer: null, //定时器名称
    };
  },
  beforeDestroy() {
    clearInterval(this.timer); // 清除定时器
    this.timer = null;
  },
  created() {
    this.$http.monitorApi.chartList().then((res) => {
      this.monitorList = res.data;
      this.fetchDatas(res.data);
      this.setTime();
    });
  },
  methods: {
    getTitleByKey(key) {
      switch (key) {
        case "TAG.SIGN":
          return "签名验签";
        case "TAG.ASYM":
          return "非对称加解密";
        case "TAG.SYM":
          return "对称加解密";
        case "TAG.TIMESTAMP":
          return "时间戳签名";
        case "TAG.ENVELOPE":
          return "数字信封";
        case "TAG.DIGEST":
          return "消息摘要";
        case "TAG.MAC":
          return "消息认证码(MAC)";
        case "TAG.CSD.SIGN":
          return "协同签名";
        case "TAG.CSD.DEC":
          return "协同解密";
        default:
          return "未知";
      }
    },
    getDataByKey(key) {
      switch (key) {
        case "TAG.SIGN":
          return this.sighData;
        case "TAG.ASYM":
          return this.asymData;
        case "TAG.SYM":
          return this.symData;
        case "TAG.TIMESTAMP":
          return this.timestampData;
        case "TAG.ENVELOPE":
          return this.envelopeData;
        case "TAG.DIGEST":
          return this.digestData;
        case "TAG.MAC":
          return this.macData;
        case "TAG.CSD.SIGN":
          return this.csdSignData;
        case "TAG.CSD.DEC":
          return this.csdDecData;
        default:
          return {};
      }
    },
    getApiByKey(key) {
      switch (key) {
        case "TAG.SIGN":
          return "N_028";
        case "TAG.ASYM":
          return "N_029";
        case "TAG.SYM":
          return "N_030";
        case "TAG.TIMESTAMP":
          return "N_031";
        case "TAG.ENVELOPE":
          return "N_032";
        case "TAG.DIGEST":
          return "N_033";
        case "TAG.MAC":
          return "N_034";
        case "TAG.CSD.SIGN":
          return "N_035";
        case "TAG.CSD.DEC":
          return "N_036";
        default:
          return "N_0";
      }
    },
    fetchDatas(monitorList) {
      if (monitorList.length > 0) {
        monitorList.forEach((ele) => {
          let opt = {
            setCode: this.getApiByKey(ele),
          };
          this.linezhe(ele, opt);
        });
      }
    },
    setTime() {
      this.timer = setInterval(() => {
        this.fetchDatas(this.monitorList);
      }, 30000);
    },
    linezhe(name, value) {
      var model = {
        opinionData: [],
        opinionDatay: [],
        opinionDatax: [],
        unit: "",
      };
      this.$http.monitorApi.query(JSON.stringify(value)).then((res) => {
        let detail = res.data.detail[0];
        model.opinionDatax = detail.xAxis;
        model.opinionDatay = detail.point;
        model.unit = res.data.unit;
      });
      if (name == "TAG.SIGN") {
        this.sighData = model;
      }
      if (name == "TAG.ASYM") {
        this.asymData = model;
      }
      if (name == "TAG.SYM") {
        this.symData = model;
      }
      if (name == "TAG.TIMESTAMP") {
        this.timestampData = model;
      }
      if (name == "TAG.ENVELOPE") {
        this.envelopeData = model;
      }
      if (name == "TAG.DIGEST") {
        this.digestData = model;
      }
      if (name == "TAG.MAC") {
        this.macData = model;
      }
      if (name == "TAG.CSD.SIGN") {
        this.csdSignData = model;
      }
      if (name == "TAG.CSD.DEC") {
        this.csdDecData = model;
      }
    },
    personHandle() {
      this.system = "";
    },
    historyClick(val, name) {
      this.system = "history";
      let title = this.getTitleByKey(name) + "历史业务量";
      let opt = {
        nav: "history",
        title: title,
        name: name,
      };
      this.$nextTick(() => {
        this.$refs.historyName.Detail(opt);
      });
    },
    moreClick(val, name) {
      this.system = "more";
      let title = this.getTitleByKey(name) + "监控";
      let opt = {
        nav: "business",
        title: title,
        name: name,
      };
      this.$nextTick(() => {
        this.$refs.more.Detail(opt);
      });
    },
  },
};
</script>

<style scoped>
.m-top {
  margin-top: 20px;
}

.el-button {
  padding: unset !important;
}
</style>

<template>
  <div>
    <div class="tableTitle"><span class="midText" v-show="msg">{{ msg }}</span></div>
    <div style="padding: 5px"></div>
  </div>
</template>

<script>
export default {
  name: "DividingLine",
  props: {
    msg: {
      type: String,
      default: undefined
    }
  }
}
</script>

<style scoped>
.tableTitle {
  border-bottom: 1px #cccccc dashed;
}

.midText {
  position: absolute;
  left: 50%;
  background-color: #ffffff;
  padding: 0 15px;
  transform: translateX(-50%) translateY(-50%);
}
</style>

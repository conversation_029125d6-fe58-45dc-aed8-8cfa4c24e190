/**
* 系统管理 系统升级
* 20211221 修改
*/
<style lang="less" scoped>
@import "../../assets/css/commons.less";
</style>
<template>
  <div>
    <el-card>
      <el-form :inline="true" :model="formInline" class="search_form comForm">
        <el-form-item label="升级包名称">
          <el-input size="small" v-model="formInline.updatePackageName" placeholder="请输入升级包名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="升级结果">
          <el-select v-model="formInline.updateResult" placeholder="请选择升级结果" clearable size="small">
            <el-option v-for="item in options1" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="起止时间">
          <el-date-picker
            size="small"
            v-model="createtime" clearable
            type="datetimerange"
            format="yyyy - MM - dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item style="background-color: transparent">
          <el-button size="small" class="comBtn com_send_btn" type="primary" icon="el-icon-search" @click="search">搜 索</el-button>
          <el-button size="small" class="comBtn com_reset_btn" icon="el-icon-refresh" @click="Reset">重 置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <el-card style="margin-top: 10px">
      <!-- 升级记录列表 v-loading="loading" element-loading-text="拼命加载中"  -->
      <el-button icon="el-icon-upload2" class="comBtn com_send_btn" size="small" type="primary" @click="upgradeHandel">升 级</el-button>
      <el-table size="small" :data="listData" highlight-current-row class="comTab" style="margin-top: 10px">
        <el-table-column align="center" prop="index" label="序号" width="50">
          <template slot-scope="scope">
            <span>{{ scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="updatePackageName" label="升级包名称" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="beforeVersion" label="升级前版本" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="presentVersion" label="升级包版本" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" prop="updateResult" label="升级结果" width="130px">
          <template slot-scope="{ row }">
                        <span class="update_result">{{ row.updateResult == null ? '进行中' : (row.updateResult ? '失败' : '成功') }}
                            <el-button v-if="row.updateResult != null" size="mini" @click="downloadReportFun(row)" class="download_report_btn" icon="el-icon-download"></el-button>
                        </span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="createdAt" label="开始时间" show-overflow-tooltip width="120px"></el-table-column>
        <el-table-column align="center" prop="stopAt" label="结束时间" show-overflow-tooltip width="120px"></el-table-column>
        <el-table-column align="center" prop="updateResult" label="进度" width="230px">
          <template slot-scope="{ row }">
            <el-progress v-if="row.updateResult == null" :text-inside="true" :stroke-width="20"
                         :percentage="row.step>=0? row.step * 25: 0"></el-progress>
            <span v-else>/</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="describe" label="描述" show-overflow-tooltip></el-table-column>
      </el-table>
      <!-- 分页组件 -->
      <Pagination v-bind:child-msg="pageParams" @callFather="callFather"></Pagination>
    </el-card>
    <!-- 上传升级包 -->
    <el-dialog title="升级" :visible.sync="editFormVisible" width="550px" @close="closeDialog()">
      <el-form label-width="120px" :model="editForm" :rules="rules" ref="editForm">
        <el-form-item label="升级包上传" prop="fileList1">
          <el-upload
            class="upload-demo"
            drag
            action=""
            accept=".zip"
            :on-remove="handleRemove1"
            :on-change="handleChange1"
            :limit="1"
            :auto-upload="false"
            :file-list="editForm.fileList1"
            multiple>
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">只能上传zip文件，且不超过5G</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="升级原因" prop="upgradeReason">
          <el-input type="textarea" v-model="editForm.upgradeReason" auto-complete="off" :autosize="{ minRows: 3}"
                    maxlength="150" show-word-limit></el-input>
          <div class="el-upload__tip"><span style="color: red">*</span>注：升级将重启服务，请升级后重新登录</div>
        </el-form-item>
      </el-form>
      <!-- append-to-body -->
      <el-dialog
        width="550px"
        @closed="dialogClearData"
        title="升级包上传中，请稍等……"
        :visible.sync="innerVisible"
        append-to-body
        :show-close="false"
      >
      </el-dialog>
      <div slot="footer" class="dialog-footer">
        <el-button class="comBtn com_reset_btn" size="small" @click="closeDialog">取 消</el-button>
        <el-button class="comBtn com_send_btn" size="small" type="primary" @click="submitForm('editForm')">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import {update, queryRecord} from '@/api/jzx'
// import {logOUt} from '@/api/trz'
import {getStore} from '@/utils/util'
import {exportDevLog} from '@/utils/exportExcel'
// import oamMG from "@/api/oamMG";
// import userMG from "@/api/userMG";
import Pagination from '@/components/my-pagination'
// import {getversion} from '@/api/about'

export default {
  data() {
    return {
      url: '',
      WebSocket: null,
      upgradeText: '',
      active: -1,
      // updateType: '',
      // 升级类型
      options: [
        {value: 'WEB', label: 'WEB'},
        {value: '后台服务', label: '后台服务'}
      ],
      // 升级结果
      options1: [
        {value: 0, label: '成功'},
        {value: 1, label: '失败'}
      ],
      editFormVisible: false, // 系统升级弹窗
      innerVisible: false, // 系统升级弹窗
      loading: true,
      editForm: {
        updateType: '',
        upgradeReason: '',
        fileList1: [],
      },
      // rules表单验证
      rules: {
        updateType: [
          {required: true, message: '请选择升级包类型', trigger: 'change'}
        ],
        upgradeReason: [
          {required: true, message: '请输入升级原因', trigger: 'blur'}
        ],
        fileList1: [
          {required: true, message: '请选择文件', trigger: 'change'}
        ],
      },
      createtime: '',
      formInline: {
        page: 1,
        size: 10,
        username: getStore("userName"),
        updatePackageName: '',
        updatePackageType: '',
        updateResult: '',
        // deviceIp: '',
        beginAt: '',
        endAt: '',
      },
      listData: [], // 升级记录
      // 无效变量
      // upgradeData: [
      //   {sysName: 'test_001'}
      // ],
      uploadForm: new FormData(),
      // 分页参数
      pageParams: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      }
    }
  },
  // 注册组件
  components: {Pagination},
  created() {
    this.$http.about.getversion();
    // this.url = 'http://**********:8087';
    // this.url = process.env.NODE_ENV === 'production' ? '/common' : '***********:8087';  // 系统管理单独部署
    // this.url = '/common';
    this.getUpgradeRecordFun(this.formInline)
  },
  methods: {
    // 获取升级记录  upgradeRecord
    getUpgradeRecordFun(parameter) {
      this.$http.oamMG.upgradeApi(this.url, parameter).then(({code, msg, rows, total}) => {
        if (!code) {
          this.listData = rows;
          // 分页赋值
          this.pageParams.currentPage = this.formInline.page;
          this.pageParams.pageSize = this.formInline.size;
          this.pageParams.total = total
        } else {
          this.$message.warning(msg)
        }
      })
    },
    // 搜索事件
    search() {
      if (this.createtime !== []) {
        this.formInline.beginAt = this.createtime[0];
        this.formInline.endAt = this.createtime[1]
      }
      this.formInline.page = 1;
      this.pageParams.currentPage = 1;
      this.getUpgradeRecordFun(this.formInline);
      this.$http.about.getversion();
    },
    // 重置
    Reset() {
      this.createtime = '';
      Object.assign(this.$data.formInline, this.$options.data().formInline);
      this.formInline.size = this.pageParams.pageSize;
      this.pageParams.currentPage = 1;
      this.getUpgradeRecordFun(this.formInline)
    },
    // 下载日志
    downloadReportFun(row) {
      exportDevLog("get", `/common/upgrade/log/${row.id}/${getStore("userName")}`)
      // exportDevLog("get",`http://***********:8087/upgrade/log/${row.id}`)
    },
    handleRemove1(file, fileList) {
      console.log(file, fileList);
      this.editForm.fileList1 = fileList;
      this.uploadForm.delete('file');
    },
    handleChange1(file, fileList) {
      console.log(file, fileList);
      if (file.size == "0") {
        this.$message.error('选择文件大小不能为0！');
        this.editForm.fileList1 = [];
        return false
      } else if (file.size > "5368709120") {
        this.$message.error('选择文件大小不能超过5G！');
        this.editForm.fileList1 = [];
        return false
      } else if (file.name.split(".")[file.name.split(".").length - 1] != "zip") {
        this.$message.error('请上传zip格式！');
        this.editForm.fileList1 = [];
        return false
      } else {
        this.editForm.fileList1.push(file);
        this.$refs.editForm.validateField('fileList1');
        this.uploadForm.append('file', file.raw);
      }
    },
    /**
     * 升级操作
     * */
    upgradeHandel() {
      this.editFormVisible = true;
      this.$nextTick(() => {
        this.$refs["editForm"].clearValidate();
      })
    },
    // 分页插件事件
    callFather(param) {
      this.formInline.page = param.currentPage;
      this.formInline.size = param.pageSize;
      this.getUpgradeRecordFun(this.formInline)
    },
    // loading
    dialogLoading() {
      this.dialogLoadingInstance = this.$loading({
        visible: true,
        text: '升级包上传中...',
        spinner: 'el-icon-loading',
        target: document.querySelector('.el-dialog--center')
      })
    },
    initWebSocket() { //初始化weosocket protocol
      // let host = window.location.host;  // 系统管理单独部署
      let dic = {
        "http:": "ws://",
        "https:": "wss://",
        "HTTP:": "ws://",
        "HTTPS:": "wss://"
      };
      let protocol = window.location.protocol; /* 获取协议 */
      let host = process.env.NODE_ENV === 'production' ? window.location.host : '**********:8087';  // 系统管理单独部署
      // const wsUri = dic[protocol] + host + "/wsupgrade/" + getStore("userId");
      const wsUri = dic[protocol] + host + "/upgrade/wsupgrade/98";
      this.WebSocket = new WebSocket(wsUri);
      this.WebSocket.onmessage = this.websocketOnmessage;
      this.WebSocket.onopen = this.websocketOnopen;
      this.WebSocket.onerror = this.websocketOnerror;
      this.WebSocket.onclose = this.websocketClose;
    },
    websocketOnopen() { //连接建立之后执行send方法发送数据
      // this.websocketSend('');
      this.loading = true
    },
    websocketOnerror() {//连接建立失败重连
      // this.initWebSocket();
    },
    websocketOnmessage(e) { //数据接收
      // let data = eval('(' + e.data + ')');
      console.log(JSON.parse(e.data));
      // this.upgradeText.push(e.data)
      this.upgradeText = JSON.parse(e.data);
      this.active = this.upgradeText.code
    },
    websocketSend(Data) {//数据发送
      this.WebSocket.send(Data);
    },
    websocketClose(e) {  //关闭
      // console.log('断开连接', e);
      // this.WebSocket.onclose()
    },
    // 提交升级包
    submitForm(editData) {
      this.$refs[editData].validate(valid => {
        if (valid) {
          this.dialogLoading();
          // this.innerVisible = true;
          // this.initWebSocket();
          if (!this.uploadForm.get('upgradeReason')) this.uploadForm.append('upgradeReason', this.editForm.upgradeReason);
          this.uploadForm.append('username', getStore("userName"));
          this.$http.oamMG.updateApi(this.url, this.uploadForm).then(({code, data, msg}) => {
            console.log(code, data, msg);
            if (code === 0) {
              this.dialogLoadingInstance.close();
              this.closeDialog();
              this.upgradeSuccess();
              // this.editFormVisible = false;
              // this.editForm.fileList1 = [];
              // this.uploadForm.delete('file');
              // this.closeDialog();
              this.$message.success('上传成功！升级结束后，请重新登陆！')
              // this.$message.success(msg);
            } else {
              this.upgradeError();
              this.dialogLoadingInstance.close();
              this.$message.warning(msg);
              // this.closeDialog();
              // backup_error=10 #备份失败返回码
              // update_error=20 #升级失败返回码
              // roolback_error=30 #回滚失败返回码
              // this.$message.warning(msg === '10' ? '备份失败' : msg === '20' ? '升级失败' : msg === '30' ? '回滚失败' : msg)
            }
          }).catch(() => {
            this.WebSocket.close();
            this.upgradeError()
            // this.dialogLoadingInstance.close();
          })
        }
      })
    },
    // 升级成功
    upgradeSuccess() {
      this.loading = false;
      // this.$confirm('升级完成，请重新登陆!', '提示', {
      //   confirmButtonText: '确定',
      //   // cancelButtonText: '取消',
      //   closeOnClickModal: false,
      //   closeOnPressEscape: false,
      //   showClose: false,
      //   showCancelButton: false,
      //   type: 'success'
      // }).then(() => {
      //   // window.sessionStorage.clear();
      //   // this.$router.push({path: '/login'});
      //   this.logoutEvent()
      // })
    },
    // 退出登录
    logoutEvent() {
      this.$http.userMG.loginOut(getStore("token")).then(res => {
        if (!res.code) {
          //如果请求成功就让他2秒跳转路由
          setTimeout(() => {
            window.sessionStorage.clear();
            this.$store.commit('SET_IS_SHOW_MENU', false);
            clearInterval(this.$store.state.commonData.checkTimer);
            this.$router.push({path: '/login'});
            this.$message.success('已退出登录!')
          }, 1000)
        } else {
          this.$message.error(res.msg);
        }
      })
    },
    // 升级失败
    upgradeError() {
      this.loading = false;

      // this.innerVisible = false;
      this.closeDialog();
    },
    dialogClearData() {
      this.innerVisible = false;
      this.upgradeText = '';
    },
    // 关闭编辑、增加弹出框
    closeDialog() {
      // this.WebSocket.close();
      // this.WebSocket.onclose();
      if(this.WebSocket!=null){
        this.WebSocket.close();
        this.WebSocket.onclose();
      }
      this.getUpgradeRecordFun(this.formInline);
      this.innerVisible = false;
      this.editForm.fileList1 = [];
      this.editForm.updateType = '';
      this.editForm.upgradeReason = '';
      this.uploadForm.delete('file');
      this.editFormVisible = false
    }
  },
  destroyed() {
    // this.WebSocket.close()
    //离开路由之后断开websocket连接
  },
}
</script>
<style lang="less">
.el-tooltip__popper {
    /*white-space: normal;*/
    word-break: break-all;
    max-width: 600px;
}

</style>
<style lang="less" scoped>
/deep/.el-tooltip__popper {
  max-width: 300px !important;
}

.update_result {
  overflow: hidden;
  display: block;
  height: 30px;
  line-height: 30px;
}

.update_result:hover {
  .download_report_btn {
    display: block;
  }
}

.download_report_btn {
  position: absolute;
  top: 15%;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 30px;
  display: none;
}
.progress {
  display: inline-block;
  width: 30px;
  height: 15px;
  margin-right: 6px;
  background-color: #EEEEEE;
}
.setActive {
  background-color: #00ff26;
}
.upgrade_text {
  font-size: 15px;
  margin-bottom: 10px;
  .el-button {
    margin: 0;
    padding: 0;
    color: #606266;
  }
}
/deep/ .el-steps {
  .el-step__line {
    display: none;
  }
  .el-step__head.is-wait {
    border-color: #EEEEEE;
  }
  .el-step__icon.is-text {
    border-radius: 0;
    border-width: 8px;
    height: 15px;
    width: 55px;
  }
  .el-step__head.is-process {
    border-color: darkorange;
  }
  .el-step__icon-inner {
    display: none;
  }
}
</style>

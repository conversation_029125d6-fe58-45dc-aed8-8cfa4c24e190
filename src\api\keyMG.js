import {reqheaders, req, reqParams, noAuthreq, reqParamNo<PERSON>son, uploadReq, reqCommon} from './axiosFun';
import API from "@/api/apiuri"

let keyApi = API.keyApi;
export default {
  list(param) {
    return reqParam<PERSON><PERSON><PERSON><PERSON>("POST", keyApi.list, param);
  },
  del(id) {
    return reqParam<PERSON>o<PERSON><PERSON>("GET", keyApi.del + "/" + id, {});
  },
  generateSign<PERSON>ey(certType, keyLength) {
    return reqParamNo<PERSON>son("POST", keyApi.generateSignKey, {certType: certType, keyLength: keyLength});
  },
  generateM<PERSON><PERSON><PERSON>(certType, keyLength, count) {
    return reqParamNo<PERSON>son("POST", keyApi.generateMulKey, {count: count, certType: certType, keyLength: keyLength});
  },
  keyUnUseInfo() {
    return reqParam<PERSON><PERSON><PERSON><PERSON>("POST", keyApi.keyUnUseInfo, {});
  },
  synCard(callback) {
    return reqParamNo<PERSON>son("POST", keyApi.synCard, {},callback);
  },
  init(callback) {
    return reqParamNoJson("GET", keyApi.init, {},callback);
  },getKeyList(certType,keyLength,callback){
    return reqParamNoJson("POST", keyApi.getKeyIndex, {certType:certType,keyLength:keyLength},callback);
  },
  getKeyManageType(callback) {
    return reqParamNoJson("GET", keyApi.getKeyManageType, {},callback);
  }
}

/**
 *   generateSignKey:"/business/key/generateSignKey",
 generateMulKey:"/business/key/generateMulKey",
 keyUnUseInfo:"/business/key/keyUnUseInfo"
 */





import {reqheaders, req, reqParams, noAuthreq, reqParamNo<PERSON><PERSON>} from './axiosFun';

export default {
  appList(param) {
    return reqheaders("post", "/business/app/list", param, {"Content-Type": "application/x-www-form-urlencoded;charset=utf-8"});
  }, addApp(param) {
    return req("post", "/business/app/add", param);
  }, del(param) {
    return reqheaders("post", "/business/app/del", param, {"Content-Type": "application/x-www-form-urlencoded;charset=utf-8"});
  }, update(param) {
    return req("post", "/business/app/update", param);
  }, queryCertUseStatistics(id) {
    return reqParams("post", "/business/app/queryCertUseStatistics", {appId: id});
  }, bindAppAsCert(appId, ids) {
    return reqParamNoJson("post", "/business/app/bindAppAsCert", {appId: appId, ids: ids});
  }, unbindAppAsCert(appId, ids) {
    return reqParamNoJson("post", "/business/app/unbindAppAsCert", {appId: appId, ids: ids});
  }, bindCertApp(param) {
    return reqParamNoJson("post", "/business/app/listByCertId", param);
  }, checkCode(appCode, id) {
    return reqParamNoJson("post", "/business/app/checkAppcode", {appCode: appCode, id: id});
  },
  checkAppName(appName, id) {
    return reqParamNoJson("post", "/business/app/checkAppName", {appName: appName, id: id});
  }, list(appName, certId,type) {
    return reqParamNoJson("post", "/business/app/appList", {appName: appName, certIds: certId,type:type});
  },
  appPage(param) {
    return reqParamNoJson("post", "/business/app/appPage", param);
  }
}





<template>
  <div>
    <el-form :inline="true" :model="formInline">
      <el-form-item label="交易量">
        <el-select
          v-model="formInline.totalNum"
          placeholder="请选择"
          size="small"
          clearable
        >
          <el-option
            v-for="(item, index) in options"
            :key="item.value"
            :label="item.label"
            :value="item.label"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="失败笔数">
        <el-select
          v-model="formInline.failNum"
          placeholder="请选择"
          size="small"
          clearable
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.label"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="类型">
        <el-select
          v-model="formInline.type"
          placeholder="请选择"
          size="small"
          clearable
        >
          <el-option
            v-for="item in typedata"
            :key="item.dictLabel"
            :label="item.dictLabel"
            :value="item.dictValue"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="算法">
        <el-select
          v-model="formInline.algorithm"
          placeholder="请选择"
          size="small"
          clearable
        >
          <el-option
            v-for="item in algorithmdata"
            :key="item.dictLabel"
            :label="item.dictLabel"
            :value="item.dictLabel"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="加密模式">
        <el-select
          v-model="formInline.model"
          placeholder="请选择"
          size="small"
          clearable
        >
          <el-option
            v-for="item in modeldata"
            :key="item.dictLabel"
            :label="item.dictLabel"
            :value="item.dictLabel"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="时间">
        <el-date-picker
          clearable
          v-model="formInline.Time"
          type="daterange"
          size="small"
          format="yyyy - MM - dd "
          value-format="yyyy-MM-dd "
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          size="small"
          type="primary"
          icon="el-icon-search"
          @click="search"
          >搜索</el-button
        >
        <el-button size="small" @click="reset">重置</el-button>
      </el-form-item>
    </el-form>
    <!--列表-->

    <el-table
      size="small"
      class="comTab"
      :data="listData"
      highlight-current-row
      ref="multipleTable"
      element-loading-text="拼命加载中"
      style="width: 100%"
    >
      <el-table-column type="index" width="60" label="序号" :resizable="false">
      </el-table-column>
      <el-table-column
        align="center"
        prop="endpoint"
        :resizable="false"
        label="应用标识/IP地址"
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="total_num"
        :resizable="false"
        label="交易量"
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="delay"
        :resizable="false"
        label="平均响应时间"
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="create_time"
        :resizable="false"
        label="记录时间"
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="succ_num"
        :resizable="false"
        label="成功笔数"
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="fail_num"
        :resizable="false"
        label="失败笔数"
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="type"
        :resizable="false"
        label="类型"
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="model"
        :resizable="false"
        label="加密模式"
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="algorithm"
        :resizable="false"
        label="算法"
      >
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <Pagination
      v-bind:child-msg="pageparm"
      @callFather="callFather"
    ></Pagination>
  </div>
</template>

<script>
import Pagination from '@/components/my-pagination'
export default {
  name: "tabledata",
  data() {
    return {
      typedata: [],
      algorithmdata: [],
      modeldata: [],
      formInline: {
        setCode: "",
        pageNum: 1,
        pageSize: 10,
        totalNum: "",
        failNum: "",
        type: "",
        algorithm: "",
        model: "",
        Time: "",
        conditions: [],
      },
      options: [
        {
          value: "1",
          label: "0-100",
        },
        {
          value: "2",
          label: "100-1000",
        },
        {
          value: "3",
          label: ">=1000",
        },
      ],
      listData: [], //用户数据

      // 分页参数
      pageparm: {
        currentPage: 1,
        pageSize: 10,
        total: 10,
      },
    };
  },
  // 注册组件
  components: {
    Pagination,
  },
  /**
   * 数据发生改变
   */

  watch: {},

  /**
   * 创建完毕
   */

  created() {
    // this.getdata(this.formInline)
    this.getdictType("dict_key_type");
    this.getdictType("dict_key_algo");
    this.getdictType("dict_key_model");
  },

  /**
   * 里面的方法只有被调用才会执行
   */

  methods: {
    Detail(val) {
      this.formInline.setCode = val;
      this.getdictType("dict_key_type");
      this.getdictType("dict_key_algo");
      this.getdictType("dict_key_model");
      this.getdata(this.formInline);
    },
    getdictType(item) {
      this.$http.monitorApi.dictType(item).then((res) => {
        if (item == "dict_key_type") {
          this.typedata = res.data;
        } else if (item == "dict_key_algo") {
          this.algorithmdata = res.data;
        } else if (item == "dict_key_model") {
          this.modeldata = res.data;
        }
      });
    },
    reset() {
      this.formInline.type = "";
      this.formInline.model = "";
      this.formInline.totalNum = "";
      this.formInline.failNum = "";
      this.formInline.algorithm = "";
      this.formInline.Time = "";
      this.formInline.conditions = [];
      this.formInline.pageNum = 1;
      console.log(this.formInline);
      this.getdata(this.formInline);
    },
    // 获取角色列表
    getdata(parameter) {
      /***
       * 调用接口，注释上面模拟数据 取消下面注释
       */
      if (this.formInline.totalNum != "") {
        if (this.formInline.totalNum.indexOf("-") != "-1") {
          this.formInline.conditions.push(
            {
              paramName: "totalNumUp",
              paramValue: this.formInline.totalNum.split("-")[1],
            },
            {
              paramName: "totalNumDown",
              paramValue: this.formInline.totalNum.split("-")[0],
            }
          );
        } else {
          this.formInline.conditions.push(
            {
              paramName: "totalNumUp",
              paramValue: "",
            },
            {
              paramName: "totalNumDown",
              paramValue: this.formInline.totalNum.substring(2),
            }
          );
        }
      }
      if (this.formInline.failNum != "") {
        if (this.formInline.failNum.indexOf("-") != "-1") {
          this.formInline.conditions.push(
            {
              paramName: "failNumUp",
              paramValue: this.formInline.failNum.split("-")[1],
            },
            {
              paramName: "failNumDown",
              paramValue: this.formInline.failNum.split("-")[0],
            }
          );
        } else {
          this.formInline.conditions.push(
            {
              paramName: "failNumUp",
              paramValue: "",
            },
            {
              paramName: "failNumDown",
              paramValue: this.formInline.failNum.substring(2),
            }
          );
        }
      }
      if (this.formInline.type != "") {
        this.formInline.conditions.push({
          paramName: "type",
          paramValue: this.formInline.type,
        });
      }
      if (this.formInline.algorithm != "") {
        this.formInline.conditions.push({
          paramName: "algorithm",
          paramValue: this.formInline.algorithm,
        });
      }
      if (this.formInline.model != "") {
        this.formInline.conditions.push({
          paramName: "model",
          paramValue: this.formInline.model,
        });
      }
      if (this.formInline.Time != "") {
        this.formInline.conditions.push(
          {
            paramName: "startTime",
            paramValue: this.formInline.Time[0],
          },
          {
            paramName: "endTime",
            paramValue: this.formInline.Time[1],
          }
        );
      }

      this.$http.monitorApi.queryList(JSON.stringify(parameter)).then((res) => {
        this.loading = false;
        if (res.code != "200") {
          this.$message({
            type: "error",
            message: res.msg,
          });
        } else {
          this.listData = res.data.detail;
          // 分页赋值
          this.pageparm.currentPage = this.formInline.pageNum;
          this.pageparm.pageSize = this.formInline.pageSize;
          this.pageparm.total = res.data.total;
        }
      });
    },
    // 分页插件事件
    callFather(parm) {
      this.formInline.pageNum = parm.currentPage;
      this.formInline.pageSize = parm.pageSize;
      this.getdata(this.formInline);
    },
    // 搜索事件
    search() {
      this.formInline.pageNum = 1;
      this.getdata(this.formInline);
      this.formInline.conditions = [];
    },
  },
};
</script>

<style scoped>
.user-search {
  margin-top: 20px;
}
.userRole {
  width: 100%;
}
</style>

/**
 * 监控管理 路由
 * */
const monitorRouter = [
  {
    path: "/systemMonitor",
    name: "系统监控",
    component: () => import("@/views/monitor/systemMonitor"),
    meta: {
      title: "系统监控",
      requireAuth: true,
    },
  },
  {
    path: "/operationMonitor",
    name: "业务监控",
    component: () => import("@/views/monitor/operationMonitor"),
    meta: {
      title: "业务监控",
      requireAuth: true,
    },
  },
  {
    path: "/serviceMonitor",
    name: "服务监控",
    component: () => import("@/views/monitor/serviceMonitor"),
    meta: {
      title: "服务监控",
      requireAuth: true,
    },
  },
];

export default { monitorRouter };

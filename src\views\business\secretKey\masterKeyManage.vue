<template>
    <div>
        <el-card>
            <el-form :inline="true" class="comForm">
                <el-form-item label="密钥索引：" style="margin-bottom: 0" size="small">
                    <el-input size="small" v-model="queryParams.index" clearable placeholder="请输入密钥索引"></el-input>
                </el-form-item>
                <el-form-item label="密钥类型：" style="margin-bottom: 0" size="small">
                    <el-select v-model="queryParams.keyType" size="small">
                        <el-option value="" label="全部"></el-option>
                        <el-option :value="1" label="签名主密钥"></el-option>
                        <el-option :value="2" label="加密主密钥"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item style="background-color:transparent; margin-bottom: 0" size="small">
                    <el-button class="comBtn com_send_btn" size="small" type="primary" icon="el-icon-search" @click="refreshHandle">搜索</el-button>
                    <el-button class="comBtn com_reset_btn" size="small" type="primary" icon="el-icon-refresh" @click="resetHandle">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card style="margin-top: 10px">
            <div style="margin-bottom: 10px">
                <el-button class="comBtn com_send_btn" size="small" type="primary" icon="el-icon-plus" @click="createSignEncKey(1)">生成签名主密钥</el-button>
                <el-button class="comBtn com_send_btn" size="small" type="primary" icon="el-icon-plus" @click="createSignEncKey(2)">生成加密主密钥</el-button>
                <el-button class="comBtn com_send_btn" size="small" type="primary" icon="el-icon-upload2" @click="importMasterKey">导入主密钥</el-button>
                <el-button class="comBtn com_send_btn" size="small" type="primary" icon="el-icon-download" @click="downloadCertFun">下载证书</el-button>
                <Strategy :policyType="5" btnSize="small"></Strategy>
            </div>
            <!-- :selectionChange="handleSelectionChange"  -->
            <createTable
                    :tableData="masterList"
                    :tableHeader="tableHeader"
                    :isPage="isPage"
                    :pageAttributes="{total: total, currentPage: queryParams.pageNo, pageSize: queryParams.pageSize}"
                    :current-change="currentChange"
                    :sizeChange="sizeChange"
            >
            </createTable>
        </el-card>
        <import-master-key ref="importMasterKey" @parentHandle="getMasterKeyList"></import-master-key>
        <export-master-key ref="exportMasterKey"></export-master-key>
    </div>
</template>

<script>
    // import keyManage from "../../../api/symmetryMG";
    import importMasterKey from "./importMasterKeyDia"
    import exportMasterKey from "./exportMasterKeyDia"
    import Strategy from "@/components/strategy/index";
    export default {
        components: {importMasterKey, exportMasterKey, Strategy},
        data() {
            let _this = this;
            return {
                queryParams: {
                    index: '',
                    keyType: '',
                    pageNo: 1,
                    pageSize: 10,
                },
                masterList: [],
                keyIdArray: '',
                tableHeader: [
                    // {type: "select", label: ""},
                    {type: "index", label: "序号"},
                    {type: "normal", label: "索引", prop: "id"},
                    {type: "text_formatter", label: "密钥类型", prop: "keyType",
                        formatter(value) {
                            return value === 1 ? "签名主密钥" : value === 2 ? "加密主密钥" : "-";
                        }
                    },
                    {type: "time", label: "创建时间", prop: "createTime"},
                    {
                        type: "operation",
                        label: "操作",
                        width: "150",
                        tag: [
                            {
                                name: "导出",
                                operType: "put",
                                tagType: "el-button",
                                attributes: {
                                    size: "mini",
                                    type: "text",
                                    icon: "el-icon-download"
                                },
                                callback: function (row) {
                                    _this.exportMasterKey(row.id);
                                }
                            }, {
                                name: "删除",
                                operType: "del",
                                tagType: "el-button",
                                attributes: {
                                    size: "mini",
                                    type: "text",
                                    icon: "el-icon-delete"
                                },
                                callback: function (row) {
                                    _this.deleteMasterKey(row.id);
                                }
                            }
                        ]
                    }
                ],
                isPage: true,
                total: 0,
            }
        },
        methods: {
            initMasterKeyManage() {
                this.getMasterKeyList()
            },
            refreshHandle() {
                this.getMasterKeyList()
            },
            resetHandle() {
                Object.assign(this.queryParams, this.$options.data().queryParams);
                this.getMasterKeyList()
            },
            // 获取列表
            getMasterKeyList() {
                this.$http.symmetryMG.masterList(this.queryParams).then(({code, data, msg, row}) => {
                    console.log(code, data, msg, row);
                    if (code !== 0) return this.$message.warning(msg);
                    this.masterList = data;
                    this.total = row
                })
            },
            // 多选
            handleSelectionChange(val) {
                console.log(val);
                let delA = "";
                let length = val.length;
                val.forEach((item) => {
                    delA += item.id + ",";
                });
                if (length > 0) {
                    delA = delA.substr(0, delA.length - 1);
                }
                this.keyIdArray = delA;
                console.log(this.keyIdArray)
            },
            // 生成签名加密主密钥 1 => 签名  2=> 加密
            createSignEncKey(keyType) {
                this.$http.symmetryMG.createMasterKey({keyType: keyType}).then(res => {
                    console.log(res);
                    this.getMasterKeyList()
                })
            },
            // 导入主密钥
            importMasterKey() {
                this.$refs.importMasterKey.initImportMaster()
            },
            // 导出主密钥
            exportMasterKey(id) {
                console.log(id);
                this.$refs.exportMasterKey.initExportMaster(id)
            },
            // 批量导出主密钥
            downloadCertFun() {
                this.$http.symmetryMG.downloadApi().then(res => {
                    // console.log(res);
                    console.log(Object.prototype.toString.call(res));
                    console.log(Object.prototype.toString.call(res) === '[object Object]');
                    if (Object.prototype.toString.call(res) === '[object Object]') {
                        this.$message.warning(res.msg);
                        return
                    }
                    let blob = new Blob([res], {
                        type: 'application/force-download'
                    });
                    // let fileName = Date.parse(new Date()) + '.cer';
                    let fileName = String(new Date().getTime()) + '.cer';
                    if (window.navigator.msSaveOrOpenBlob) {
                        navigator.msSaveBlob(blob, fileName)
                    } else {
                        let link = document.createElement('a');
                        link.href = window.URL.createObjectURL(blob);
                        link.download = fileName;
                        link.click();
                        //释放内存
                        window.URL.revokeObjectURL(link.href)
                    }
                })
            },
            deleteMasterKey(id) {
                console.log(id);
                this.$confirm('确定删除吗?', '删除确认', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.$http.symmetryMG.delMasterKey(id).then(({code, data, msg}) => {
                        console.log(code, data, msg);
                        if (code !== 0) return this.$message.warning(msg);
                        this.$message.success('删除成功!');
                        this.getMasterKeyList()
                    })
                }).catch(() =>{})
            },
            // 分页操作
            currentChange(val) {
                this.queryParams.pageNo = val;
                this.getMasterKeyList();
            },
            sizeChange(val) {
                this.queryParams.pageSize = val;
                this.getMasterKeyList();
            },
        },
        mounted() {
            this.getMasterKeyList()
        }
    }
</script>

<style lang="less" scoped>
    .svg-icon {
        width: 12px;
        height: 12px;
    }
</style>

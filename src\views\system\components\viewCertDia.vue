<template>
    <el-dialog title="证书信息" :visible.sync="searchCertVisible" width="600px" :close-on-click-modal="false">
        <el-descriptions class="margin-top" :column="1" border>
            <el-descriptions-item label="签发者">{{ certDetail.issuerDn }}</el-descriptions-item>
            <el-descriptions-item label="签发给">{{ certDetail.dn }}</el-descriptions-item>
            <el-descriptions-item label="序列号">{{ certDetail.serialNumber }}</el-descriptions-item>
            <el-descriptions-item label="有效期">{{ certDetail.validity }}</el-descriptions-item>
            <el-descriptions-item label="证书类型">{{ certDetail.secretKey }}</el-descriptions-item>
            <el-descriptions-item label="哈希算法">{{ certDetail.digestEncAlg == 0 ? '' : certDetail.digestEncAlg }}</el-descriptions-item>
            <el-descriptions-item label="证书指纹（SHA1）">{{ certDetail.fingerprintSha1 }}</el-descriptions-item>
            <el-descriptions-item label="证书指纹（MD5）">{{ certDetail.fingerprintMd5 }}</el-descriptions-item>
        </el-descriptions>
        <div slot="footer" class="dialog-footer">
            <el-button class="comBtnDef com_reset_btn" size="small" @click="closeCert">关闭</el-button>
        </div>
    </el-dialog>
</template>

<script>
export default {
    name: "viewCertDia",
    data() {
        return {
            searchCertVisible: false,
            certDetail: {}
        }
    },
    methods: {
        // 关闭查看证书
        initViewCertFun(row) {
            this.searchCertVisible = true;
            this.certDetail = row
        },
        closeCert() {
            this.searchCertVisible = false;
        },
    }
}
</script>

<style lang="less" scoped>
/deep/ .el-descriptions-item__label.is-bordered-label {
    width: 150px;
}
</style>

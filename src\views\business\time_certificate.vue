<template>
  <div>
    <el-card class="box-card" shadow="always" style="padding-bottom: 10px">
      <div style="margin-bottom: 10px">
        <el-button class="comBtn com_send_btn" size="mini" @click="list()">刷新</el-button>
        <Strategy :policyType="1"></Strategy>
      </div>
      <createTable
        :tableData="tableData"
        :tableHeader="tableDataHeader"
        :isPage="isPage"
      >
      </createTable>
    </el-card>


    <!--证书信息-->
    <el-dialog title="证书信息" :visible.sync="queryCertInfo" width="600px" append-to-body :close-on-click-modal="false"
               @close="Object.assign(certEntityInfo,$options.data().certEntityInfo)">
      <el-descriptions class="margin-top" :column="1" border>
        <el-descriptions-item label="签发者">{{certEntityInfo.issuer}}</el-descriptions-item>
        <el-descriptions-item label="签发给">{{certEntityInfo.dn}}</el-descriptions-item>
        <el-descriptions-item label="序列号">{{ certEntityInfo.sn }}</el-descriptions-item>
        <el-descriptions-item label="有效期">{{certEntityInfo.dateStr}}</el-descriptions-item>
        <el-descriptions-item label="证书类型">{{certEntityInfo.keyDesc}}</el-descriptions-item>
        <el-descriptions-item label="哈希算法">{{certEntityInfo.digestEncAlg}}</el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button class="comBtn com_send_btn" type="primary" @click="queryCertInfo=false">确 认</el-button>
      </div>
    </el-dialog>

    <select-time-cert ref="selectTimeCert"></select-time-cert>
  </div>
</template>

<script>
// import timeStampMG from "@/api/timeStampMG";
import selectTimeCert from "@/views/business/components/selectTimeCert"
// import {dateFormat} from "@/utils/util";
// import bussMG from "@/api/bussMG";
// import caMg from "@/api/caMG";
import Strategy from "@/components/strategy"

export default {
  name: "certificate",
  components: {selectTimeCert,Strategy},
  data() {
    let _this = this;
    return {
      isPage: false,
      tableData: [],
      queryCertInfo:false,
      certType: "",
      certEntityInfo:{},
      tableDataHeader: [
        {
          label: '证书类型',
          prop: 'certType',
          type: "text_formatter",
          // width: '150',
          formatter: function (value, row) {
            if (value == 1) return "RSA";
            else if (value == 2) return "SM2";
            else return "-"
          }
        },
        {
          label: '证书ID', prop: 'certId', type: "text_formatter", formatter(value) {
            return (value == 0 || value == "" || value == null) ? "" : value;
          }
        },
        {label: '证书主题', prop: 'dn', type: "normal"},
        {label: '序列号', prop: 'sn', type: "normal"},
        {label: '生效时间', prop: 'startTime', type: "time"},
        {label: '失效时间', prop: 'endTime', type: "time"},
        {label: '密钥索引', prop: 'keyIndex', type: "normal"},
        {
          label: "操作",
          prop: "1",
          type: "operation",
          width: "180",
          tag: [
            {
              name: "绑定证书",
              operType: "put",
              tagType: "el-button",
              attributes: {
                size: "mini",
                type: "text",
                icon: "el-icon-edit"
              },
              isShow: function (row) {
                const certId = row.certId;
                if (certId == 0 || certId == "" || certId == null) {
                  return true;
                } else {
                  return false;
                }
              },
              callback: function (row, opts, event) {
                _this.$refs.selectTimeCert.loadSelectTimeCert(row);
              }
            },
            {
              name: "查看证书",
              operType: "get",
              tagType: "el-button",
              attributes: {
                size: "mini",
                type: "text",
                icon: "el-icon-search"
              },
              isShow: function (row) {
                const certId = row.certId;
                if (certId == 0 || certId == "" || certId == null) {
                  return false;
                } else {
                  return true;
                }
              },
              callback: function (row, opts, event) {
                _this.$http.bussMG.queryById(row.certId).then(res => {
                  _this.certEntityInfo = res.data;
                  _this.queryCertInfo = true;
                });
              }
            },
            {
              name: "解绑",
              operType: "put",
              tagType: "el-button",
              attributes: {
                size: "mini",
                type: "text",
                icon: "el-icon-edit"
              },
              isShow: function (row) {
                const certId = row.certId;
                if (certId == 0 || certId == "" || certId == null) {
                  return false;
                } else {
                  return true;
                }
              },
              callback: function (row, opts, event) {
                _this.$confirm('您确定要解绑证书！', '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                }).then(() => {
                  _this.unbindCert(row);
                });
              }
            }
          ]
        }
      ],

      loading: false,

    }
  },
  created() {
    this.list();
  },
  methods: {

    // 获取列表
    list() {
      this.rasTableData = [];
      this.sm2TableData = [];

      this.$http.timeStampMG.list().then(({code, data, msg}) => {
        if (code == '0') {
          this.tableData = data;
        } else {
          this.$message.error(msg);
        }
      })
    },
    unbindCert(row) {
      let _this = this;
      let param = {id: row.id, certId: row.certId};
      this.$http.timeStampMG.unBindCert(param).then(rdata => {
        if (rdata.code == 0) {
          _this.$message.success("解绑成功！")
          _this.list();
        } else {
          _this.$message.error(rdata.message)
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
/deep/ .el-divider--horizontal {
  margin: 5px 0 10px;
}

/deep/ .el-descriptions-item__label.is-bordered-label {
  width: 130px;
}

.admin-button {
  margin-top: 20px;
  margin-bottom: 20px;
  float: left;
}
</style>

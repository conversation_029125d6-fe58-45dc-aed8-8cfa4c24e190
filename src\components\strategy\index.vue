<template>
    <el-tooltip class="item" effect="dark" content="将配置下发签名服务端" placement="top-start">
        <el-button class="comBtn com_send_btn" :size="btnSize" :loading="loading" type="warning" @click="strategy()">
            <svg-icon class="svg-icon"  icon-class="send_policy"></svg-icon>
            下发策略
        </el-button>
    </el-tooltip>
</template>
<script>
    import strategyMG from "@/api/strategyMG";
    import {Message} from "element-ui";

    export default {
        name: "Strategy",
        props: {
            policyType: {
                type: Number,
                default: null
            },
            btnSize: {
                type: String,
                default: 'mini'
            }
        },
        data() {
            return {
                loading: false
            }
        },
        methods: {
            strategy() {
                this.loading = true;
                let _this = this;
                strategyMG.distribution(function () {
                    _this.loading = false;
                }).then(res => {
                    let code = res.code;
                    if (code === 0) {
                        Message({
                            message: "策略下发成功！",
                            showClose: true,
                            type: 'success'
                        });
                    } else {
                        Message({
                            message: res.msg,
                            showClose: true,
                            type: 'error'
                        });
                    }
                    this.$store.dispatch("clearPolicyDataFun")
                });
            }
        }
    }
</script>

<style lang="less" scoped>
    .svg-icon {
        width: 12px !important;
        height: 12px !important;
    }
</style>

//.marginBottom{
//  margin-bottom: 14px;
//}
//.back{
//  background: #fff;
//}
/* forme 表单样式 */
.el-message-box__btns {
    .el-button--primary {
        border: 0 none;
        background-color: #395a7a;
    }
}

.messageIndex {
    z-index: 3000 !important;
}


.comForm.el-form--inline .el-form-item {
    padding: 0 10px;
    margin: 0 10px 10px 0;
    border-radius: 5px;
    background-color: #f0f0f0;
    .el-form-item__label {
        padding: 0;
    }
    &:last-child {
        margin-right: 0;
    }
}

/* el-card */
//.page_header .el-card__body {
//    width: 100%;
//    display: flex;
//    justify-content: space-between;
//
//    .el-form-item {
//        margin-bottom: 0;
//    }
//}

/* table 表格 */
.comTab.el-table {
    .el-table__header {
        tr {
            background: #eaedf3;
            th {
                padding: 8px 0;
                color: rgb(96, 98, 102);
                font-size: 14px;
                background: #eaedf3;
                &.is-leaf {
                    border: 0 none;
                }
            }
        }
    }
    .el-table__body {
        td {
            height: 40px;
            padding: 0;
            border: 0 none;
        }
        .el-table__row {
            background-color: #fafafa;
        }
        tr.el-table__row--striped td {
            border: 0 none;
            background: #f4f4f4;
        }
    }
    .el-table__body {
        border-collapse: separate;
        border-spacing: 0 8px;
        table-layout: auto !important;
    }
}
/* button */
.el-button {
     //height: 35px;
     //padding: 5px 12px;
 }
.el-button--text {
    //color: #395a7a;
}

.comBtn {
    //height: 25px;
    //padding: 5px 12px;
    border: none 0;
    color: #fff;
    font-size: 12px;
    min-width: 80px;
    //background-color: #2690d8;
}
.comBtnDef {
    //height: 25px;
    //padding: 5px 12px;
    border: none 0;
    color: #fff;
    font-size: 12px;
    //min-width: 80px;
    //background-color: #2690d8;
}

// 查询
.com_query_btn.el-button {
    background-color: #409EFF;
    &:hover, &:active, &:focus {
        color: #fff;
        background-color: #409EFF;
    }
}
.com_query_btn.is-disabled {
    background-color: rgba(64, 158, 255, 0.55);
    &:hover {
        background-color: rgba(64, 158, 255, 0.55);
    }
}
// 重置
.com_reset_btn.el-button {
    color: #395a7a;
    background-color: #f0f0f0;
    &:hover, &:active, &:focus {
        //color: #fff;
        background-color: #d5d5d5;
    }
}

// 发送按钮
.com_send_btn.el-button {
    background-color: #395a7a;
    &:hover, &:active, &:focus {
        color: #fff;
        background-color: #314363;
    }
}

// 删除
.com_del_btn.el-button {
    background-color:#bd6464;
    &:hover, &:active, &:focus {
        color: #fff;
        background-color: #ae5757;
    }
}
// 编辑
.com_edit_btn.el-button {
    background-color: #2690d8;
    &:hover, &:active, &:focus {
        color: #fff;
        background-color: #2673b9;
    }
}

// 添加
.com_add_btn.el-button {
    color: #395a7a;
    background-color: #f0f0f0;
    &:hover, &:active, &:focus {
        color: #395a7a;
        background-color: #d5d5d5;
    }
}
// 批量删除
.com_batch_btn.el-button {
    color: #bf6868;
    background-color: #f0f0f0;
    &:hover, &:active, &:focus {
        //color: #fff;
        background-color: #d5d5d5;
    }
}

//.el-button--primary.is-active, .el-button--primary:active {
//    background: #2690d8;
//    border: none 0;
//}

/* select 下拉框 */
.comSel .el-input__inner {
    height: 35px;
    border: 0 none;
    background-color: #f0f0f0;
}

/* input 输入框 */
/* .comInp  */
.el-input__inner {
    //height: 35px;
    border: 0 none !important;
    background-color: #f0f0f0 !important;
}
///deep/.el-input--small .el-input__inner {
//    border: 0 none !important;
//    background-color: #f0f0f0 !important;
//}
//
.el-range-editor .el-range-input {
    background-color: #f0f0f0;
}
//
///deep/.el-textarea__inner {
//    border: 0 none;
//    background-color: #f0f0f0;
//}


/* pagination */
//.comPagination {
//    text-align: right;
//}
//
//.el-pagination.is-background .btn-next, .el-pagination.is-background .btn-prev {
//    background-color: transparent;
//}

/* element-ui tabs 切换 */
//.comTabs.el-tabs .el-tabs__header {
//    margin: 0;
//}
//
//.el-tabs--card > .el-tabs__header .el-tabs__nav {
//    border: none 0;
//}

//.comTabs/deep/.el-tabs .el-tabs__item {
    //min-width: 120px;
    //text-align: center;
    //color: #fff;
    //background-image: linear-gradient(to left, #324d68, #4d7aa9); /* 自右向左 */
//}

//.comTabs.el-tabs {
//    .el-tabs__nav {
//        .el-tabs__item {
//            min-width: 120px;
//            text-align: center;
//            color: #fff;
//            background-image: linear-gradient(to left, #324d68, #4d7aa9); /* 自右向左 */
//        }
//
//        .el-tabs__item:first-child {
//            border-top-left-radius: 5px;
//        }
//
//        .el-tabs__item.is-active {
//            color: #333;
//            border: 0 none;
//            background: #fff;
//        }
//
//        .el-tabs__item:last-child {
//            border-top-right-radius: 5px;
//        }
//    }
//}


.comTabs.el-tabs {
  .el-tabs__nav {
    .el-tabs__item {
      min-width: 120px;
      text-align: center;
      color: #333;
      border-radius: 0;
    }
    .el-tabs__item.is-active {
      color: #1887ee;
      font-weight: 600;
      border-bottom: 2px solid #1887ee;
      background: #fff;
    }

    .el-tabs__item:last-child {
      margin-right: 0;
    }
  }
}

/* 自定义 tabs 切换 */
.custom_tabs_con {
    height: 40px;
    line-height: 40px;
    //margin-bottom: 15px;
    //border-bottom: 2px solid #24447a;

    .custom_tabs_title {
        display: inline-block;
        width: 90px;
        font-size: 14px;
        color: #fff;
        text-align: center;
        margin-right: 2px;
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
        cursor: pointer;
        background-image: linear-gradient(to left, #324d68, #4d7aa9); /* 自右向左 */
    }
    .active {
        color: #333;
        background: #fff;
    }
}

/* el-card */
//.el-card {
//    border: none 0;
//}

.comCard .el-card {
    border-top-left-radius: 0;
}

// 查询条件样式
.query_con {
    margin-bottom: 15px;
    /deep/ .el-card__body {
        display: flex;
        justify-content: space-between;
    }

    // 操作按钮
    .handle_btn {
        position: relative;
        min-width: 180px;
        .but_con {
            position: absolute;
            bottom: 11px;
            right: 0;
            .el-button {
                margin-left: 0;
            }
        }
    }
}


.el-table--scrollable-x .el-table__body-wrapper {
    //滚动条凹槽的颜色，还可以设bai置du边框属性 
    &::-webkit-scrollbar-track-piece {
        border-radius: 5px;
        background-color: #bfbfbf;
    }
    
    //滚动条的宽度 
    &::-webkit-scrollbar {
        width: 9px;
        height: 9px;
    }
    
    //滚动条的设置 
    &::-webkit-scrollbar-thumb {
        border-radius: 5px;
        background-color: #dddddd;
        background-clip: padding-box;
        min-height: 28px;
    }
}
<template>
    <el-card>
        <el-form ref="customForm" class="custom_form" :model="customForm" label-width="130px" :rules="rules">
            <el-row style="width: 80%;">
                <el-col :span="12" >
                    <el-form-item label="产品名称：">
                        <el-input size="small" v-model="customForm.productName" :disabled="true"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="产品型号：">
                        <el-input size="small" v-model="customForm.productModel" :disabled="true"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="产品规格：">
                        <el-input size="small" v-model="customForm.productSpec" :disabled="true"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="产品版本：">
                        <el-input size="small" v-model="customForm.productVersion" :disabled="true"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="名称：" prop="displayName">
                        <el-input size="small" v-model="customForm.displayName" placeholder="请输入名称" maxlength="10" auto-complete="off" show-word-limit></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="型号：" prop="displayModel">
                        <el-input size="small" v-model="customForm.displayModel" placeholder="请输入型号" maxlength="20" auto-complete="off" show-word-limit></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="规格：" prop="displaySpec">
                        <el-input size="small" v-model="customForm.displaySpec" placeholder="请输入规格" maxlength="20" auto-complete="off" show-word-limit></el-input>
                    </el-form-item>

                </el-col>
                <el-col :span="12">
                    <el-form-item label="版本：" prop="displayVersion">
                        <el-input size="small" v-model.trim="customForm.displayVersion" placeholder="请输入版本" maxlength="50" auto-complete="off" show-word-limit></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="设备厂商：" prop="companyName">
                        <el-input size="small" v-model="customForm.companyName" placeholder="请输入公司名称" maxlength="50" auto-complete="off" show-word-limit></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="厂商简称：" prop="shortName">
                        <!-- data -->
                        <el-input size="small" v-model="customForm.shortName" placeholder="请输入公司简称" auto-complete="off" show-word-limit></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-form-item label="LOGO：" prop="logo">
                <el-upload
                        style="display: inline-block"
                        list-type="picture"
                        :on-change="changeImage"
                        :multiple='false'
                        :show-file-list="false"
                        accept='.jpg, .jpeg, .png'
                        action=""
                        :auto-upload="false">
                    <!--<div class="icon_tip_con">-->
                    <div class="upload_img">
                        <img v-if="customForm.logo" :src="customForm.logo" alt="">
                        <!--<img v-else src="../assets/img/pic_icon.png" alt="">-->
                        <i v-else slot="default" class="el-icon-plus"></i>
                    </div>
                    <!--<i slot="default" class="el-icon-plus">上传</i>-->
                </el-upload>
                <span v-if="customForm.logo" class="remove_btn" @click.stop="handleRemove('logo')">
                    <i class="el-icon-delete"></i>删除
                </span>
                <span class="upload_tip">注：请上传.jpg、.jpeg、.png格式，且文件大小不超过1M！<br/>推荐上传图片尺寸为 38*38 像素</span>
            </el-form-item>
            <el-form-item label="登录背景：" prop="backgroundLogin">
                <el-upload
                        style="display: inline-block"
                        list-type="picture"
                        :on-change="changeBgImage"
                        :multiple='false'
                        :show-file-list="false"
                        accept='.jpg, .jpeg, .png'
                        action=""
                        :auto-upload="false">
                    <!--<div class="icon_tip_con">-->
                    <div class="upload_bg_img">
                        <img v-if="customForm.backgroundLogin" :src="customForm.backgroundLogin" alt="">
                        <!--<img v-else src="../assets/img/pic_icon.png" alt="">-->
                        <i v-else slot="default" class="el-icon-plus"></i>
                    </div>
                </el-upload>
                <span v-if="customForm.backgroundLogin" class="remove_btn" @click.stop="handleRemove('bg')">
                    <i class="el-icon-delete"></i>删除
                </span>
                <span class="upload_tip">注：请上传.jpg、.jpeg、.png格式，且文件大小不超过2M！<br />推荐上传图片尺寸为 1920*1080 像素</span>
            </el-form-item>
            <div style="margin-left: 120px;">
                <el-button class="comBtn com_send_btn" size="small" @click="saveCustom('customForm')">保 存</el-button>
                <el-button size="small" @click="restoreDefaultFun">恢复默认</el-button>
            </div>
        </el-form>
    </el-card>
</template>

<script>
    // import customGM from "@/api/customMG";

    export default {
        name: "custom",
        data() {
            let _this = this;
            const checkShortName = (rule, value, callback) => {
                if (value === '') return callback(new Error('请输入公司简称'));
                let checkTrim = /^[^\s]*$/;
                if (!checkTrim.test(value)) return callback(new Error('输入的内容不能包含空格!'));
                _this.checkShortNameFun(value, callback)
            };
            const checkTrim = (rule, value, callback) => {
                let checkTrim = /^[^\s]*$/;
                if (!checkTrim.test(value)) return callback(new Error('输入的内容不能包含空格!'));
                callback()
            };
            return {
                customForm: {
                    productName: '',
                    productModel: '',
                    productSpec: '',
                    productVersion: '',
                    displayName: '',
                    displayModel: '',
                    displaySpec: '',
                    displayVersion: '',
                    copyrightInfo: '',
                    companyName: '',
                    shortName: '', // 1表示可以，2表示不可以
                    logo: '',
                    // logoUrl: null,
                    // bgUrl: null,
                    backgroundLogin: '',
                },
                logoFile: null,
                bgFile: null,
                fileList: [],
                disabled: false,
                imageUrl: null,
                rules: {
                    displayName: [
                        // {required: true, min: 2, max: 16, message: '请输入设备名称, 长度在2到16个字符', trigger: 'blur'},
                        {required: true, message: '请输入产品名称, 最大为10个字符', trigger: 'blur'},
                        // {validator: checkTrim, trigger: 'blur'}
                    ],
                    displayModel: [
                        {required: true, message: '请输入产品型号', trigger: 'blur'},
                        // {validator: checkTrim, trigger: 'blur'}
                    ],
                    displaySpec: [
                        {required: true, message: '请输入产品规格', trigger: 'blur'},
                        // {validator: checkTrim, trigger: 'blur'}
                    ],
                    displayVersion: [
                        {required: true, message: '请输入产品版本', trigger: 'blur'},
                        // {validator: checkTrim, trigger: 'blur'}
                    ],
                    copyrightInfo: [
                        {required: true, message: '请输入版权信息', trigger: 'blur'}
                    ],
                    companyName: [
                        {required: true, message: '请输入公司名称', trigger: 'blur'},
                        // {validator: checkTrim, trigger: 'blur'}
                    ],
                    shortName: [
                        {required: true, message: '请输入公司简称', trigger: 'blur'},
                        {validator: checkShortName, trigger: 'blur'}
                    ],
                    logo: [
                        {required: true, message: '请上传logo', trigger: 'change'}
                    ],
                    backgroundLogin: [
                        {required: true, message: '请上传背景图片', trigger: 'change'}
                    ],
                }
            }
        },
        methods: {
            checkShortNameFun(shortName, callback) {
                this.$http.customMG.checkShortName({shortName: shortName}).then((res) => {
                    console.log(res);
                    const code = res.data;
                    if (code === 1) {
                        callback();
                    } else {
                        callback('每个汉字等于两字节, 总长度不能大于16字节!');
                    }
                })
            },
            changeImage(file) {
                console.log(file);
                let index = file.name.lastIndexOf(".");
                let ext = file.name.substr(index + 1);
                console.log(ext);
                if ((ext === 'jpg' || ext === 'jpeg' || ext === 'png') && file.size / 1024 < 1024) {
                    this.$refs.customForm.validateField('logo');
                    let reader = new FileReader();
                    reader.readAsDataURL(file.raw); // 一定要传入file格式
                    reader.onload = () => {
                        this.customForm.logo = reader.result;
                        this.$refs.customForm.validateField('logo');
                    };
                    reader.onerror = function (error) {
                        console.log("Error: ", error);
                        this.$message.warning(error)
                    };

                } else {
                    this.$message.warning('文件格式错误, 请上传.jpg、.jpeg、.png格式，且文件大小不超过1M！')
                }

            },
            handleRemove(str) {
                console.log(str);
                str === 'logo' ? this.customForm.logo = null : this.customForm.backgroundLogin = null
            },
            changeBgImage(file) {
                let index = file.name.lastIndexOf(".");
                let ext = file.name.substr(index + 1);
                if ((ext === 'jpg' || ext === 'jpeg' || ext === 'png') && file.size / 1024 < 1024*2) {
                    // debugger;
                    let reader = new FileReader();
                    reader.readAsDataURL(file.raw); // 一定要传入file格式
                    reader.onload = () => {
                        // console.log(reader.result);
                        this.customForm.backgroundLogin = reader.result;
                        this.$refs.customForm.validateField('backgroundLogin');
                    };
                    reader.onerror = function (error) {
                        console.log("Error: ", error);
                        this.$message.warning(error)
                    };
                } else {
                    this.$message.warning('文件格式错误, 请上传.jpg、.jpeg、.png格式，且文件大小不超过2M！')
                }
            },
            // 转base64
            toBase64(file) {
                let reader = new FileReader();
                reader.readAsDataURL(file); // 一定要传入file格式
                reader.onload = () => {
                    // console.log("file 转 base64结果：" + reader.result);
                    return reader.result
                };
                reader.onerror = function (error) {
                    console.log("Error: ", error);
                    this.$message.warning(error)
                };
            },
            saveCustom(name) {
                this.$refs[name].validate(valid => {
                    if (valid) {
                        console.log(this.customForm);
                        this.$http.customMG.updateConfig(this.customForm).then(({code, msg, data}) => {
                            console.log(code, msg, data);
                            if (!code) {
                                this.$message.success(msg);
                                this.$store.dispatch("getCurrConfigFun").then(data => {
                                    document.title = data.productName;
                                    this.customForm = JSON.parse(JSON.stringify(data))
                                })
                            } else {
                                this.$message.warning(msg)
                            }
                        })
                    }
                })
            },
            restoreDefaultFun() {
                this.$http.customMG.restoreDefault().then(res => {
                    console.log(res);
                    this.$refs.customForm.clearValidate()
                    if (!res.code) {
                        this.$message.success(res.msg);
                        this.$store.dispatch("getCurrConfigFun").then(data => {
                            document.title = data.productName;
                            this.customForm = JSON.parse(JSON.stringify(data))
                        })
                    } else {
                        this.$message.warning(res.msg);
                    }
                })
            }
        },
        mounted() {
            console.log(this.$http.customMG);
            this.$store.dispatch("getCurrConfigFun").then(data => {
                this.customForm = JSON.parse(JSON.stringify(data))
            })
        }
    }
</script>

<style lang="less" scoped>
    /deep/ .el-input .el-input__count .el-input__count-inner {
        background-color: transparent;
    }
    .custom_form {
        display: inline-block;
    }

    .remove_btn {
        cursor: pointer;

        &:hover {
            color: #1887ee;
        }
    }

    .upload_img {
        display: inline-block;
        width: 43px;
        height: 43px;
        padding: 5px;
        border-radius: 5px;
        border: 1px dashed #c0ccda;
        background-color: #fbfdff;

        img {
            width: 100%;
            height: 100%;
            vertical-align: middle;
        }
    }

    .upload_bg_img {
        position: relative;
        display: inline-block;
        width: 192px;
        height: 108px;
        padding: 5px;
        border-radius: 5px;
        border: 1px dashed #c0ccda;
        background-color: #fbfdff;

        img {
            width: 100%;
            height: 100%;
            vertical-align: middle;
        }

        /*img:hover .remove_con {*/
        /*display: block;*/
        /*}*/


        i {
            font-size: 16px;
            line-height: 108px;
        }

        /*.remove_con {*/
        /*!*display: none;*!*/
        /*position: absolute;*/
        /*left: 0;*/
        /*right: 0;*/
        /*width: 100%;*/
        /*background-color: rgba(0,0,0,.23);*/
        /*}*/

        /*&::after {*/
        /*content: "注：推荐上传图片尺寸为 38*38 像素";*/
        /*color: #ae5757;*/
        /*}*/
    }

    .upload_tip {
        display: inline-block;
        width: 400px;
        line-height: 15px;
        color: #ccc;
        margin-left: 15px;
        vertical-align: middle;
    }

    .hide.el-upload--picture-card {
        display: none;
    }
</style>

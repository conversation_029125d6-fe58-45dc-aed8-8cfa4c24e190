<template>
  <div class="container">
    <el-card v-show="showSearch" class="box-card" shadow="always" style="padding-bottom: 10px">
      <div>
        <el-form :inline="true" :model="queryParams" class="user-search comForm">
          <el-form-item label="key">
            <el-input size="small" v-model="queryParams.key" clearable></el-input>
          </el-form-item>
          <el-form-item label="描述">
            <el-input size="small" v-model="queryParams.des" clearable></el-input>
          </el-form-item>
          <el-form-item label="是否内置">
            <el-select size="small" v-model="queryParams.builtIn" placeholder="请选择" clearable>
              <el-option v-for="item in builtInOptions" :label="item.label" :value="item.value"
                :key="item.value"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item style="background-color: transparent;">
            <el-button class="comBtn com_send_btn" size="mini" icon="el-icon-search" @click="showAndSearch">搜索</el-button>
            <el-button class="comBtn com_reset_btn" size="mini" icon="el-icon-refresh" @click="refresh">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <div v-show="showSearch" style="padding: 10px"></div>

    <el-card class="box-card" shadow="always" style="padding-bottom: 10px">
      <el-form label-width="100px">
        <el-row>
          <el-col :span="14" style="text-align: left">
            <el-button class="comBtn com_add_btn" size="mini" type="primary" @click="addConfig = true">添加</el-button>
          </el-col>
          <el-col :span="10">
            <div style="text-align: right">
              <el-button-group>
                <el-button size="mini" icon="el-icon-search" @click="showAndSearch"></el-button>
                <el-button size="mini" icon="el-icon-refresh-left" @click="refresh()"></el-button>
              </el-button-group>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div id="config" style="padding-top: 10px">
        <createTable :tableData="tableData" :tableHeader="tableDataHeader" :isPage="isPage" :pageAttributes="pageAttr"
          :current-change="currentChange" :sizeChange="sizeChange">
        </createTable>
      </div>
    </el-card>

    <el-dialog title="添加配置项" :visible.sync="addConfig" width="800px"
      @close="Object.assign(configFrom, $options.data().configFrom);">
      <el-form label-width="120px" ref="configFrom" :model="configFrom">
        <el-form-item label="key:">
          <el-input label="key" v-model="configFrom.key"></el-input>
        </el-form-item>
        <el-form-item label="value:">
          <el-input label="key" v-model="configFrom.value"></el-input>
        </el-form-item>
        <el-form-item label="描述:">
          <el-input v-model="configFrom.des"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button class="comBtn com_reset_btn" size="mini" @click="addConfig = false;">取 消</el-button>
        <el-button class="comBtn com_send_btn" size="mini" @click="addConfigE();">确定</el-button>
      </div>
    </el-dialog>


    <el-dialog title="修改配置项" :visible.sync="editConfig" width="800px"
      @close="Object.assign(configFrom, $options.data().configFrom);">
      <el-form label-width="120px" ref="configFrom" :model="configFrom">
        <el-form-item label="key:">
          <el-input label="key" v-model="configFrom.key"></el-input>
        </el-form-item>
        <el-form-item label="value:">
          <el-input label="key" v-model="configFrom.value"></el-input>
        </el-form-item>
        <el-form-item label="描述:">
          <el-input v-model="configFrom.des"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button class="comBtn com_reset_btn" size="mini" @click="editConfig = false;">取 消</el-button>
        <el-button class="comBtn com_send_btn" size="mini" @click="editConfigE();">确定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="查看" :visible.sync="queryConfig" width="800px"
      @close="Object.assign(configFrom, $options.data().configFrom);">
      <el-form label-width="120px" ref="configFrom" :model="configFrom">
        <el-form-item label="key:">
          <el-input label="key" v-model="configFrom.key"></el-input>
        </el-form-item>
        <el-form-item label="value:">
          <el-input label="key" v-model="configFrom.value"></el-input>
        </el-form-item>
        <el-form-item label="描述:">
          <el-input v-model="configFrom.des"></el-input>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
// import configMG from "@/api/configMG";
export default {
  name: "backup",
  data() {
    return {
      urule: {
        "backUpFileSize": [
          { required: true, message: "请上传备份文件", trigger: "change" },
        ],
        "password": [
          { required: true, message: "请输入上传文件密码", trigger: "blur" },
        ]
      },
      brule: {
        "password": [
          { required: true, message: "请输入备份文件密码", trigger: "blur" },
        ]
      },
      rrule: {
        "password": [
          { required: true, message: "请输入密码", trigger: "blur" },
        ]
      },
      addConfig: false,
      editConfig: false,
      showSearch: true,
      queryConfig: false,
      tableData: [],
      tableDataHeader: [],
      pageAttr: {},
      isPage: false,
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        key: "",
        des: "",
        builtIn: -1
      },
      configFrom: {
        id: "",
        key: "",
        value: "",
        des: ""
      },
      builtInOptions: [
        { value: '-1', label: '全部' },
        { value: '1', label: '内置' },
        { value: '0', label: '非内置' }
      ],
    }
  },
  methods: {
    showAndSearch() {
      this.showSearch = !this.showSearch;
    },
    currentChange: function (val) {
      this.queryParams.pageNo = val;
      this.refresh();
    },
    sizeChange: function (val) {
      this.queryParams.pageSize = val
      this.refresh()
    },
    refresh() {
      let _this = this;
      this.$http.configMG.list(this.queryParams).then(res => {
        _this.tableData = res.data;
        _this.isPage = res.row > 0 ? true : false;
        _this.pageAttr.total = res.row;
      });
    },
    addConfigE() {
      let _this = this;
      this.addConfig = true;
      this.$http.configMG.add(this.configFrom).then(res => {
        this.addConfig = false;
        _this.refresh();
      });
    },
    editConfigE() {
      let _this = this;
      this.editConfig = true;
      this.$http.configMG.edit(this.configFrom).then(res => {
        this.editConfig = false;
        _this.refresh();
      });
    }
  },
  created() {
    if (!this.$store.state.isShowMenu) this.$router.push({ path: '/home' });
    let _this = this;
    this.refresh();
    this.tableDataHeader = [
      {
        type: "index",
        label: "序号",
        width: "100"
      },
      {
        type: "normal",
        label: "key",
        prop: "key"
      },
      {
        type: "normal",
        label: "value",
        prop: "value"
      },
      {
        type: "normal",
        label: "描述",
        prop: "des"
      },
      {
        type: "text_formatter",
        label: "内置",
        prop: "builtIn",
        formatter: function (value, row) {
          if (row.builtIn == 1) {
            return "内置";
          } else {
            return "自定义";
          }
        }
      },
      {
        type: "operation",
        label: "操作",
        tag: [
          {
            name: "编辑",
            operType: "update",
            tagType: "el-button",
            attributes: {
              size: "mini",
              type: "text",
              icon: "el-icon-edit"
            },
            callback: function (row) {
              _this.$http.configMG.queryById(row.id).then(res => {
                _this.configFrom = res.data;
                _this.editConfig = true;
                _this.configFrom.id = row.id;
              });
            }
          },
          {
            name: "删除",
            operType: "update",
            tagType: "el-button",
            attributes: {
              size: "mini",
              type: "text",
              icon: "el-icon-edit"
            },
            callback: function (row) {
              _this.$confirm('确定删除吗?', '删除确认', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                _this.$http.configMG.del(row.id).then(res => {
                  _this.refresh();
                });
              })
            },
            isShow(row) {
              if (row.builtIn == 1) {
                return false;
              } else {
                return true;
              }
            }
          },
          {
            name: "查看",
            operType: "update",
            tagType: "el-button",
            attributes: {
              size: "mini",
              type: "text",
              icon: "el-icon-edit"
            },
            callback: function (row) {
              _this.$http.configMG.queryById(row.id).then(res => {
                _this.configFrom = res.data;
                _this.queryConfig = true;
              });
            }
          }
        ]
      }
    ]
  }
}
</script>

<style scoped>
.el-checkbox {
  text-align: left;
}

.upload-demo {
  text-align: left;
}

.checkleft {
  text-align: left;
}

body .el-scrollbar__wrap {
  overflow-x: hidden;
}
</style>



/**
* 左边菜单
*/
<template>
    <!-- background-color="#24447a" -->
    <el-menu default-active="2" :collapse="collapsed" @select="check" collapse-transition router
        :default-active="$route.path" active-text-color="#fff" background-color="#24447a" unique-opened
        class="el-menu-vertical-demo" text-color="#fff">
        <div class="logoBox">
            <div v-show="!collapsed" class="logo_con">
                <img style="height: 38px;" :src="this.$store.state.customConfig.logo" alt="">
                <!--<div class="sys_name" v-show="$store.state.isShowMenu">{{ this.$store.state.customConfig.productName || sysName }}</div>-->
                <div class="sys_name">{{ this.$store.state.customConfig.productName || sysName }}</div>
            </div>
            <div v-show="collapsed" class="logo_con">
                <img style="height: 38px;margin-top: 10px" :src="this.$store.state.customConfig.logo || imgSrc" alt="">
            </div>
        </div>
        <div v-for="menu in allmenu" class="el-sub">
            <el-menu-item :index="menu.menuUrl" :key="menu.id" v-if="menu.menus == null">
                <i class="el-icon-menu-style" :class="menu.menuClass"></i>
                <span slot="title">{{ menu.menuName }}</span>
            </el-menu-item>
            <!-- $store.state.isShowMenu -->
            <el-submenu :key="menu.id" :index="menu.menuName"
                v-if="menu.menus != null && (!menu.advancedOps || $store.state.isShowMenu)">
                <template slot="title">
                    <!-- <i class="iconfont" :class="menu.menuClass"></i> -->
                    <i :class="menu.menuClass"></i>
                    <span slot="title">{{ menu.menuName }}</span>
                </template>

                <el-menu-item v-for="menu2 in menu.menus" :index="menu2.menuUrl" :key="menu2.id"
                    v-if="menu2.menus == null">
                    <i :class="menu2.menuClass"></i>
                    <span slot="title">{{ menu2.menuName }}</span>
                </el-menu-item>

                <el-submenu v-for="menu2 in menu.menus" :key="menu2.id" :index="menu2.menuName"
                    v-if="menu2.menus != null">
                    <template slot="title">
                        <!-- <i class="iconfont" :class="menu2.menuClass"></i> -->
                        <i :class="menu2.menuClass"></i>
                        <span class="spanright">{{ menu2.menuName }}</span>
                    </template>
                    <el-menu-item-group>
                        <el-menu-item v-for="chmenu in menu2.menus" :index="chmenu.menuUrl" :key="chmenu.id">
                            <!-- <i class="iconfont" :class="chmenu.menuClass"></i> -->
                            <i :class="chmenu.menuClass"></i>
                            <span class="spanright">{{ chmenu.menuName }}</span>
                        </el-menu-item>
                    </el-menu-item-group>
                </el-submenu>
            </el-submenu>
        </div>
    </el-menu>
</template>
<script>

export default {
    name: 'leftnav',
    data() {
        return {
            sysName: '签名验签服务器',
            // allmenu: []
            logClass: "openimgclass",
            imgSrc: 'data:image/png;base64,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',
        }
    },
    props: ['allmenu', "collapsed"],
    methods: {
        check(val) {
            console.log(val);
        }
    },
    // mounted() {
    //     this.$store.dispatch("setAllMenu", JSON.parse(getStore("menus")));
    // },
    watch: {
        collapsed(val, oldVal) {
            this.logClass = val ? "clossImgClass" : "openimgclass";
        }
    }
}
</script>
<style lang="less" scoped>
.asideshow {
    .el-menu-vertical-demo {
        background-color: transparent;

        /deep/ .el-menu-item {
            height: 48px;
            line-height: 48px;
            margin: 10px 15px;
            border-radius: 10px;
            background: url("../assets/img/el_menu/menu_button_normal.png") no-repeat;
            background-size: 100%;

            &:hover {
                background: url("../assets/img/el_menu/menu_button_press.png") no-repeat;
                background-size: 100%;
            }

            &.is-active {
                background: url("../assets/img/el_menu/menu_button_press.png") no-repeat;
                background-size: 100%;
            }
        }

        /* 有子菜单 */

        .el-sub {
            .el-submenu.is-active .el-submenu__title {
                border-bottom: 1px solid #6482b7;
            }

            /deep/ .el-submenu__title {
                height: 48px;
                line-height: 48px;
                padding-left: 20px !important;
                margin-bottom: 5px;
                background: url("../assets/img/el_menu/menu_button_normal.png") no-repeat;
                background-size: 100% 100%;
                background-color: transparent !important;

                &:hover {
                    border-radius: 10px;
                    -webkit-border-radius: 10px;
                    -moz-border-radius: 10px;
                    -ms-border-radius: 10px;
                    -o-border-radius: 10px;
                    background: url("../assets/img/el_menu/menu_button_press.png") no-repeat;
                    background-size: 100% 100%;
                }
            }

            &>.el-submenu {
                margin: 10px 15px 0;
                background: #304e81;
                border-radius: 10px;

                /deep/ .el-menu {
                    padding: 5px 10px;
                    background-color: transparent !important;

                    .el-menu-item {
                        height: 40px;
                        line-height: 40px;
                        min-width: 150px;
                        margin: 5px;
                        padding-left: 20px !important;
                        background: none;
                        background-color: transparent !important;

                        &:hover {
                            background-color: #9bc913 !important;
                        }

                        &.is-active {
                            background-color: #9bc913 !important;
                            background-size: 100%;
                        }
                    }
                }
            }

            /deep/ .el-submenu .el-menu {
                background: #304e81;
                border-radius: 10px;

                .el-menu-item-group__title {
                    padding: 0;
                }
            }
        }
    }
}
</style>
<style lang="less">
.el-menu-vertical-demo:not(.el-menu--collapse) {
    width: 240px;
    min-height: 400px;
}

.el-menu-vertical-demo:not(.el-menu--collapse) {
    border: none;
    text-align: left;
}

.el-menu-item-group__title {
    padding: 0;
}

.el-menu-bg {
    background-color: #1f2d3d !important;
}

.el-menu {
    border: none;
}

.logo_con {
    display: flex;
    justify-content: center;
    align-items: center;

    .sys_name {
        margin-left: 20px;
        font-size: 18px;
        color: #fff;
        font-weight: bolder;
        white-space: nowrap
    }
}

.logoBox {
    height: 60px;
    line-height: 60px;
    color: #9d9d9d;
    font-size: 20px;
    text-align: center;
    /*padding: 20px 0;*/
}

/*.logoimg {*/
/*height: 40px;*/
/*}*/

/*.spanright {*/
/*margin-left: 20px;*/
/*}*/
// .el-icon-menu-style {
//     font-size: 15px !important;
//     margin-left: -5px;
// }

//     .el-icon-s-home {
//         font-size: 15px !important;
//         margin-left: -5px;
//    }


.el-menu--collapse>.el-sub>.el-submenu>.el-submenu__title span {
    height: 0;
    width: 0;
    overflow: hidden;
    visibility: hidden;
    display: inline-block;
}

.el-menu--collapse>.el-sub>.el-submenu>.el-submenu__title .el-submenu__icon-arrow {
    display: none !important;
}

.common_icon(@url: '../assets/img/dc_active.png') {
    height: 16px;
    background: url(@url) center no-repeat;
    /*使用自己的图片来替换*/
    background-size: contain;
}

.el-icon-virtualIP {
    .common_icon('../assets/img/virtual_IP_icon.png');
}

.el-menu-item.is-active .el-icon-virtualIP {
    .common_icon('../assets/img/virtual_IP_active.png');
}

.el-icon-terminal {
    .common_icon('../assets/img/terminal_icon.png');
}

.el-menu-item.is-active .el-icon-terminal {
    .common_icon('../assets/img/terminal_active.png');
}

/*策略配置*/
.el-icon-policy {
    .common_icon('../assets/img/policy_icon.png');
}

.el-menu-item.is-active .el-icon-policy {
    .common_icon('../assets/img/policy_active.png');
}

/*业务端口配置*/
.el-icon-port {
    .common_icon('../assets/img/port_icon.png');
}

.el-menu-item.is-active .el-icon-port {
    .common_icon('../assets/img/port_active.png');
}

/*服务端密钥*/
.el-icon-server-key {
    .common_icon('../assets/img/server_key_icon.png');
}

.el-menu-item.is-active .el-icon-server-key {
    .common_icon('../assets/img/server_key_active.png');
}

/*客户端密钥*/
.el-icon-client-key {
    .common_icon('../assets/img/client_key_icon.png');
}

.el-menu-item.is-active .el-icon-client-key {
    .common_icon('../assets/img/client_key_active.png');
}

/*应用服务端密钥*/
.el-icon-app-server-key {
    .common_icon('../assets/img/app_server_key_icon.png');
}

.el-menu-item.is-active .el-icon-app-server-key {
    .common_icon('../assets/img/app_server_key_active.png');
}

/*配置管理*/
.el-icon-config-manage {
    .common_icon('../assets/img/config_manage_icon.png');
}

.el-menu-item.is-active .el-icon-config-manage {
    .common_icon('../assets/img/config_manage_active.png');
}

/*管理平台接入配置*/
.el-icon-cut-in {
    .common_icon('../assets/img/cut_in_icon.png');
}

.el-menu-item.is-active .el-icon-cut-in {
    .common_icon('../assets/img/cut_in_active.png');
}

/*日志配置*/
.el-icon-log-config {
    .common_icon('../assets/img/log_config_icon.png');
}

.el-menu-item.is-active .el-icon-log-config {
    .common_icon('../assets/img/log_config_active.png');
}

/*协同签名配置*/
.el-icon-cooperative-sign {
    .common_icon('../assets/img/cooperative_icon.png');
}

.el-menu-item.is-active .el-icon-cooperative-sign {
    .common_icon('../assets/img/cooperative_active.png');
}

/*集群管理*/
.el-icon-cluster {
    .common_icon('../assets/img/cluster_icon.png');
}

.el-menu-item.is-active .el-icon-cluster {
    .common_icon('../assets/img/cluster_active.png');
}

/*消息处理*/
.el-icon-msg {
    .common_icon('../assets/img/msg_icon.png');
}

.el-menu-item.is-active .el-icon-msg {
    .common_icon('../assets/img/msg_active.png');
}

/*对称密钥管理*/
.el-icon-symmetric {
    .common_icon('../assets/img/symmetric_icon.png');
}

.el-menu-item.is-active .el-icon-symmetric {
    .common_icon('../assets/img/symmetric_active.png');
}

/*非对称密钥管理*/
.el-icon-asymmetric {
    .common_icon('../assets/img/asymmetric_icon.png');
}

.el-menu-item.is-active .el-icon-asymmetric {
    .common_icon('../assets/img/asymmetric_active.png');
}

/*密钥备份与恢复*/
.el-icon-backupRec {
    .common_icon('../assets/img/backupRec_icon.png');
}

.el-menu-item.is-active .el-icon-backupRec {
    .common_icon('../assets/img/backupRec_active.png');
}

/*业务端口配置*/
.el-icon-bussPort {
    .common_icon('../assets/img/buss_port_icon.png');
}

.el-menu-item.is-active .el-icon-bussPort {
    .common_icon('../assets/img/buss_port_active.png');
}

/*证书绑定*/
.el-icon-certBound {
    .common_icon('../assets/img/cert_bound_icon.png');
}

.el-menu-item.is-active .el-icon-certBound {
    .common_icon('../assets/img/cert_bound_active.png');
}

/*时间戳检索*/
.el-icon-timeQuery {
    .common_icon('../assets/img/time_query_icon.png');
}

.el-menu-item.is-active .el-icon-timeQuery {
    .common_icon('../assets/img/time_query_active.png');
}

/*密码机管理*/
.el-icon-crypto {
    .common_icon('../assets/img/crypto_icon.png');
}

.el-menu-item.is-active .el-icon-crypto {
    .common_icon('../assets/img/crypto_active.png');
}

/*协同签名管理*/
.el-icon-synergia {
    .common_icon('../assets/img/synergia_icon.png');
}

.el-menu-item.is-active .el-icon-synergia {
    .common_icon('../assets/img/synergia_active.png');
}

/*协同密钥管理*/
.el-icon-synergiaKey {
    .common_icon('../assets/img/synergiaKey_icon.png');
}

.el-menu-item.is-active .el-icon-synergiaKey {
    .common_icon('../assets/img/synergiaKey_active.png');
}

// 系统管理-证书链
.el-icon-certChain {
    .common_icon('../assets/img/cert_chain_icon.png');
}

.el-menu-item.is-active .el-icon-certChain {
    .common_icon('../assets/img/cert_chain_active.png');
}
</style>

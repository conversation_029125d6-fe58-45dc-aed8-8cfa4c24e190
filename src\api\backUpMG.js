import {reqheaders, req, reqParams, noAuthreq, reqParamNo<PERSON>son, reqCommon, uploadReq} from './axiosFun';
import apiuri from "@/api/apiuri";
import de from "element-ui/src/locale/lang/de";

const backUpApi = apiuri.backupApi;
export default {
  list(param) {
    return reqParam<PERSON>o<PERSON><PERSON>("post", backUpApi.list, param);
  },
  del(id) {
    return reqParam<PERSON>o<PERSON><PERSON>("get", backUpApi.del + "/" + id, {});
  },
  batchDel(ids) {
    return reqParamNo<PERSON><PERSON>("post", backUpApi.batchDel, {batchIds: ids});
  },
  backup(param, calback) {
    return reqParamNo<PERSON>son("post", backUpApi.backup, param, calback);
  },
  reduce(param, calback) {
    return reqParamNo<PERSON><PERSON>("post", backUpApi.reduce, param, calback);
  }, upload(param, callBack) {
    return uploadReq("POST", backUpApi.upload, param, callBack)
  },
  getBackUpInfo(start, callBack) {
    return reqParamNoJson("post", backUpApi.getBackUpInfo, {start: start}, callBack);
  }
}




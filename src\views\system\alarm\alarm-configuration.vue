<template>
  <div>
    <el-row :gutter="15">
      <el-col :span="8">
        <div class="div_around">
          <div class="div_bottom">
            <p style="margin-left: 2%">告警管理</p>
          </div>
          <el-row style="height: 140px">
            <el-form label-width="150px" style="margin-top: 10px">
              <el-form-item label="是否开启告警:">
                <el-switch
                  v-model="openSwitch"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                  :active-value="1"
                  :inactive-value="0"
                ></el-switch>
              </el-form-item>
              <el-form-item label="告警内容:">
                <el-checkbox-group v-model="contentList">
                  <el-checkbox label="system">系统</el-checkbox>
                  <el-checkbox v-if="!virtual" label="service">业务</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-form>
          </el-row>
          <div class="div_top">
            <el-row>
              <el-button
                class="comBtn com_send_btn"
                size="small"
                @click="setAlarm"
                >保存</el-button
              >
            </el-row>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="div_around">
          <div class="div_bottom">
            <p style="margin-left: 2%">通知管理</p>
          </div>
          <el-row style="height: 140px">
            <el-form
              label-width="150px"
              style="margin-top: 10px"
              ref="serviceAlarmForm"
            >
              <el-form-item label="告警通知方式:">
                <el-checkbox-group v-model="modeList">
                  <el-checkbox label="email">邮件</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-form>
          </el-row>
          <div class="div_top">
            <el-row>
              <el-button
                class="comBtn com_send_btn"
                size="small"
                @click="setNotice"
                >保存</el-button
              >
            </el-row>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import alarmMG from "@/api/alarmMG";

export default {
  name: "host-alarm",
  data() {
    return {
      virtual: true,
      openSwitch: 0,
      contentList: [],
      modeList: [],
    };
  },
  methods: {
    //设置告警配置
    setAlarm() {
      if (this.openSwitch == 1 && this.contentList.length == 0) {
        this.$message.warning("请选择告警内容");
        return;
      }
      const data = {
        status: this.openSwitch,
        content: this.contentList.toString(),
      };
      alarmMG.setAlarmStatus(data).then((res) => {
        let code = res.code;
        if (code == 0) {
          this.$message.success("设置成功");
        }
      });
    },
    setNotice() {
      const data = {
        noticeType: this.modeList.toString(),
      };
      alarmMG.setNoticeType(data).then((res) => {
        let code = res.code;
        if (code == 0) {
          this.$message.success("设置成功");
        }
      });
    },
    getAlarmInfos() {
      alarmMG.configure().then((res) => {
        let code = res.code;
        if (code == 0) {
          this.openSwitch = res.data.status || 0;
          this.virtual = res.data.virtual;
          if (res.data.content) {
            this.contentList = res.data.content.split(",") || [];
          }
          if (res.data.noticeType) {
            this.modeList = res.data.noticeType.split(",") || [];
          }
        }
      });
    },
  },
  mounted() {
    this.getAlarmInfos();
  },
};
</script>

<style scoped>
.div_around {
  border: 1px solid #cccccc;
}

.div_top {
  border-top: 1px solid #cccccc;
  padding: 10px 15px;
  display: flex;
  justify-content: flex-end;
}

.div_bottom {
  border-bottom: 1px solid #cccccc;
}
</style>

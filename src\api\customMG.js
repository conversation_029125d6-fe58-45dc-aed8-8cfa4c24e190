import {req, reqGet, reqParams} from './axiosFun';

// 获取当前配置
const currentConfig = () => {return reqGet("get", "/custom/made/enable")};
// 更新配置
const updateConfig = (params) => {return req("put", "/custom/made/renew", params)};
// 恢复默认
const restoreDefault = (params) => {return req("post", "/custom/made/recover", params)};
// 校验公司简称
// const restoreDefault = (params) => {return req("post", "/custom/made/recover", params)};
const checkShortName = (params) => {return reqParams("post", "/custom/made/checkShortName", params);};
// 查询策略是否改变

export default {
  currentConfig,
  updateConfig,
  restoreDefault,
  checkShortName,
}
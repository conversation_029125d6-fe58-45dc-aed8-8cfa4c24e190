import Api from "@/api/apiuri";

import {req, reqC<PERSON>mon, reqParamNoJson, reqFormData, reqParams} from "./axiosFun";

const timeStampApi = Api.timeStampApi;

export default {

  /**
   * 系统配置
   */
  // 网卡配置 /timestamp/list
  list() {
    return req("get", timeStampApi.list);
  },
  listCert(params) {
    return reqParams("post", timeStampApi.cerList, params);
  },
  bindCert(params) {
    return reqParams("post", timeStampApi.bindCert, params);
  },
  unBindCert(params) {
    return reqParams("post", timeStampApi.unBindCert, params);
  }
}

